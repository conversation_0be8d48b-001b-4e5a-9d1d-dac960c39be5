import os
import sys
import re
import json
import requests
import time
from typing import List, Dict, Tuple

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置工作目录为项目根目录，以便找到config.yaml
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入VideoLingo的配置工具
from core.utils import load_key

# 支持的模型配置
AVAILABLE_MODELS = {
    "deepseek-v3": {
        "name": "deepseek-ai/DeepSeek-V3",
        "cost": "付费 (￥2/M输入 + ￥8/M输出)",
        "quality": "🌟🌟🌟🌟🌟 极高质量",
        "speed": "⚡ 中等速度",
        "description": "顶级推理模型，翻译质量最佳"
    },
    "qwen2.5-7b": {
        "name": "Qwen/Qwen2.5-7B-Instruct", 
        "cost": "💰 完全免费",
        "quality": "🌟🌟🌟🌟 高质量", 
        "speed": "⚡⚡ 较快速度",
        "description": "免费高质量模型，性价比极高"
    },
    "glm-4-9b": {
        "name": "THUDM/glm-4-9b-chat",
        "cost": "💰 完全免费", 
        "quality": "🌟🌟🌟🌟 高质量",
        "speed": "⚡⚡ 较快速度", 
        "description": "免费智谱AI模型，中文理解优秀"
    },
    "qwen2.5-coder": {
        "name": "Qwen/Qwen2.5-Coder-7B-Instruct",
        "cost": "💰 完全免费",
        "quality": "🌟🌟🌟 良好质量", 
        "speed": "⚡⚡⚡ 快速",
        "description": "免费代码优化模型，技术内容翻译佳"
    }
}

# 确保out目录存在
def ensure_output_dir():
    """确保输出目录存在"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    out_dir = os.path.join(script_dir, "out")
    os.makedirs(out_dir, exist_ok=True)
    return out_dir

class SubtitleEntry:
    """字幕条目类"""
    def __init__(self, index: int, start_time: str, end_time: str, text: str):
        self.index = index
        self.start_time = start_time
        self.end_time = end_time
        self.text = text.strip()
    
    def __str__(self):
        return f"{self.index}\n{self.start_time} --> {self.end_time}\n{self.text}\n"

def parse_srt_file(srt_file_path: str) -> List[SubtitleEntry]:
    """解析SRT字幕文件"""
    print(f"📄 解析SRT文件: {srt_file_path}")
    
    if not os.path.exists(srt_file_path):
        raise FileNotFoundError(f"SRT文件不存在: {srt_file_path}")
    
    subtitles = []
    
    with open(srt_file_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 按空行分割每个字幕条目
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        if not block.strip():
            continue
            
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
            
        try:
            index = int(lines[0])
            time_line = lines[1]
            text = '\n'.join(lines[2:])
            
            # 解析时间戳
            time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
            if time_match:
                start_time = time_match.group(1)
                end_time = time_match.group(2)
                subtitles.append(SubtitleEntry(index, start_time, end_time, text))
        except (ValueError, IndexError) as e:
            print(f"⚠️ 跳过无效的字幕条目: {block[:50]}...")
            continue
    
    print(f"✅ 解析完成，共{len(subtitles)}条字幕")
    return subtitles

def select_translation_model():
    """选择翻译模型"""
    print("🤖 可用的翻译模型：")
    print("=" * 80)
    
    model_keys = list(AVAILABLE_MODELS.keys())
    for i, key in enumerate(model_keys, 1):
        model = AVAILABLE_MODELS[key]
        print(f"{i}. {model['name']}")
        print(f"   💰 费用: {model['cost']}")
        print(f"   {model['quality']}")
        print(f"   {model['speed']}")
        print(f"   📝 说明: {model['description']}")
        print("-" * 60)
    
    print(f"\n默认使用当前配置的模型: {load_key('api.model')}")
    choice = input(f"\n请选择模型 (1-{len(model_keys)}, 直接回车使用默认): ").strip()
    
    if choice == "":
        return load_key("api.model")  # 使用配置文件中的默认模型
    
    try:
        choice_idx = int(choice) - 1
        if 0 <= choice_idx < len(model_keys):
            selected_key = model_keys[choice_idx]
            selected_model = AVAILABLE_MODELS[selected_key]
            print(f"\n✅ 已选择: {selected_model['name']}")
            print(f"💰 费用: {selected_model['cost']}")
            return selected_model['name']
        else:
            print("❌ 无效选择，使用默认模型")
            return load_key("api.model")
    except ValueError:
        print("❌ 无效输入，使用默认模型")
        return load_key("api.model")

def estimate_translation_cost(subtitles: List[SubtitleEntry], model_name: str):
    """估算翻译成本"""
    total_chars = sum(len(subtitle.text) for subtitle in subtitles)
    
    # 粗略估算token数量（中文约1.5字符/token，英文约4字符/token）
    estimated_input_tokens = total_chars / 3  # 平均估算
    estimated_output_tokens = estimated_input_tokens * 0.8  # 中文通常比英文短一些
    
    print(f"\n📊 成本估算:")
    print(f"   📝 字幕条数: {len(subtitles)}")
    print(f"   📝 总字符数: {total_chars}")
    print(f"   🔢 预估输入token: {estimated_input_tokens:.0f}")
    print(f"   🔢 预估输出token: {estimated_output_tokens:.0f}")
    
    if "DeepSeek-V3" in model_name:
        input_cost = (estimated_input_tokens / 1000000) * 2.0  # ￥2/M tokens
        output_cost = (estimated_output_tokens / 1000000) * 8.0  # ￥8/M tokens
        total_cost = input_cost + output_cost
        print(f"   💰 预估费用: ￥{total_cost:.4f} (约{total_cost*1000:.2f}分)")
    else:
        print(f"   💰 预估费用: 免费 🎉")

def translate_text_with_llm(text: str, context: str = "", model_override: str = None) -> str:
    """使用LLM翻译文本"""
    api_key = load_key("api.key")
    base_url = load_key("api.base_url")
    model = model_override or load_key("api.model")  # 允许覆盖模型
    target_language = load_key("target_language")
    
    if not api_key or not base_url or not model:
        raise ValueError("API配置不完整，请检查config.yaml中的api设置")
    
    # 构建翻译提示词
    system_prompt = f"""你是一个专业的视频字幕翻译专家。请将以下英文字幕翻译成{target_language}。

翻译要求：
1. 保持原意准确，语言流畅自然
2. 适合作为视频字幕，简洁明了
3. 保持专业术语的准确性
4. 符合中文表达习惯

请只返回翻译结果，不要包含其他解释。"""

    user_prompt = f"请翻译以下英文字幕：\n\n{text}"
    
    if context:
        user_prompt = f"上下文：{context}\n\n" + user_prompt
    
    # 准备API请求
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "temperature": 0.3,
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(f"{base_url}/chat/completions", headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        translation = result["choices"][0]["message"]["content"].strip()
        
        return translation
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
        raise
    except KeyError as e:
        print(f"❌ API响应格式错误: {e}")
        print(f"响应内容: {response.text}")
        raise

def translate_subtitles(subtitles: List[SubtitleEntry], selected_model: str) -> List[SubtitleEntry]:
    """翻译字幕列表"""
    print(f"🌐 使用模型 {selected_model} 开始翻译{len(subtitles)}条字幕...")
    
    translated_subtitles = []
    
    for i, subtitle in enumerate(subtitles, 1):
        print(f"🔄 翻译第{i}/{len(subtitles)}条字幕...")
        
        # 获取上下文（前一条字幕）
        context = ""
        if i > 1:
            context = subtitles[i-2].text
        
        try:
            # 翻译文本（传入选择的模型）
            translated_text = translate_text_with_llm(subtitle.text, context, selected_model)
            
            # 创建翻译后的字幕条目
            translated_subtitle = SubtitleEntry(
                subtitle.index,
                subtitle.start_time,
                subtitle.end_time,
                translated_text
            )
            translated_subtitles.append(translated_subtitle)
            
            print(f"✅ 原文: {subtitle.text[:50]}...")
            print(f"✅ 译文: {translated_text[:50]}...")
            
            # 避免API频率限制
            time.sleep(0.5)
            
        except Exception as e:
            print(f"❌ 翻译失败: {e}")
            # 如果翻译失败，保留原文
            translated_subtitles.append(subtitle)
    
    return translated_subtitles

def save_srt_file(subtitles: List[SubtitleEntry], output_path: str):
    """保存SRT字幕文件"""
    print(f"💾 保存翻译后的字幕: {output_path}")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for subtitle in subtitles:
            f.write(str(subtitle) + '\n')
    
    print(f"✅ 中文字幕保存完成: {output_path}")

def main():
    print("🚀 开始字幕翻译流程...")
    
    # 确定输入和输出路径
    out_dir = ensure_output_dir()
    input_srt = os.path.join(out_dir, "output.srt")
    output_srt = os.path.join(out_dir, "output_chinese.srt")
    
    print(f"📂 输入文件: {input_srt}")
    print(f"📂 输出文件: {output_srt}")
    
    try:
        # 1. 解析英文SRT文件
        english_subtitles = parse_srt_file(input_srt)
        
        # 2. 选择翻译模型
        selected_model = select_translation_model()
        
        # 3. 显示成本估算
        estimate_translation_cost(english_subtitles, selected_model)
        
        # 4. 确认是否继续
        print(f"\n🤔 确认使用模型 {selected_model} 进行翻译吗？")
        confirm = input("输入 'y' 或直接回车继续，其他任意键取消: ").strip().lower()
        if confirm not in ['', 'y', 'yes']:
            print("❌ 用户取消翻译")
            return
        
        # 5. 翻译字幕
        chinese_subtitles = translate_subtitles(english_subtitles, selected_model)
        
        # 6. 保存中文SRT文件
        save_srt_file(chinese_subtitles, output_srt)
        
        print("🎉 字幕翻译完成！")
        print(f"📄 英文字幕: {input_srt}")
        print(f"📄 中文字幕: {output_srt}")
        print(f"🤖 使用模型: {selected_model}")
        
    except Exception as e:
        print(f"❌ 翻译流程失败: {e}")
        raise

if __name__ == "__main__":
    main() 