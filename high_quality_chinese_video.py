#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高质量中文配音视频合成器
使用原始的完整翻译而非分割版本
"""

import os
import re
import asyncio
from moviepy import VideoFileClip, AudioFileClip
from pydub import AudioSegment
import shutil

# 导入TTS功能
import edge_tts

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                # 尝试解析第一行为数字索引
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 检查时间行格式
                if ' --> ' in time_line:
                    # 解析时间
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
                else:
                    print(f"⚠️  跳过无效时间格式: {time_line}")
            except ValueError:
                print(f"⚠️  跳过无效索引: {lines[0]}")
                continue
    
    return subtitles

async def generate_high_quality_tts(text: str, output_path: str, voice: str = "zh-CN-YunxiNeural"):
    """生成高质量TTS音频"""
    try:
        communicate = edge_tts.Communicate(text, voice)
        await communicate.save(output_path)
        return True
    except Exception as e:
        print(f"❌ TTS生成失败: {str(e)}")
        return False

async def create_high_quality_chinese_video():
    """创建高质量中文配音视频"""
    
    # 文件路径
    video_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/input/test_30s.mp4"
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt"
    audio_dir = "out/audio_high_quality"
    output_path = "chinese_video_high_quality.mp4"
    
    print("🎬 开始创建高质量中文配音视频...")
    print(f"📝 使用原始翻译文件: {srt_path}")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(srt_path):
        print(f"❌ 字幕文件不存在: {srt_path}")
        return False
    
    try:
        # 创建音频输出目录
        os.makedirs(audio_dir, exist_ok=True)
        
        # 读取字幕文件
        subtitles = read_srt_file(srt_path)
        print(f"📝 读取到 {len(subtitles)} 条字幕")
        
        # 过滤出30秒内的字幕
        subtitles_30s = [s for s in subtitles if s[1] < 30]  # 开始时间小于30秒
        print(f"📝 30秒内的字幕: {len(subtitles_30s)} 条")
        
        # 生成高质量TTS音频
        print("🎵 开始生成高质量TTS音频...")
        for i, (index, start_time, end_time, text) in enumerate(subtitles_30s):
            audio_file = os.path.join(audio_dir, f"subtitle_{index:03d}.mp3")
            
            if not os.path.exists(audio_file):
                print(f"🔄 [{i+1}/{len(subtitles_30s)}] 生成: {text[:50]}...")
                success = await generate_high_quality_tts(text, audio_file)
                if success:
                    print(f"✅ 完成: subtitle_{index:03d}.mp3")
                else:
                    print(f"❌ 失败: subtitle_{index:03d}.mp3")
            else:
                print(f"⏭️  跳过已存在: subtitle_{index:03d}.mp3")
        
        # 加载原视频
        print("📹 加载原视频...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        print(f"📊 原视频时长: {original_duration:.2f}s")
        
        # 创建静音背景音轨
        print("🎵 创建合成音频...")
        composite_audio = AudioSegment.silent(duration=int(original_duration * 1000))
        
        # 处理每个字幕的音频
        successful_audio_count = 0
        for i, (index, start_time, end_time, text) in enumerate(subtitles_30s):
            audio_file = os.path.join(audio_dir, f"subtitle_{index:03d}.mp3")
            
            if os.path.exists(audio_file):
                try:
                    # 加载TTS音频（MP3格式）
                    tts_audio = AudioSegment.from_mp3(audio_file)
                    tts_duration = len(tts_audio) / 1000.0
                    
                    # 计算字幕时间段长度
                    subtitle_duration = end_time - start_time
                    
                    print(f"🔄 [{i+1}/{len(subtitles_30s)}] 处理: {text[:50]}... (时长: {tts_duration:.2f}s)")
                    
                    # 调整音频时长以匹配字幕时间段
                    if tts_duration > subtitle_duration:
                        # 需要加速
                        speed_factor = tts_duration / subtitle_duration
                        # 限制最大加速倍数为2.0，避免语音过快
                        if speed_factor > 2.0:
                            speed_factor = 2.0
                            print(f"  ⚠️  限制加速倍数为2.0x")
                        tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                        print(f"  🔄 音频加速 {speed_factor:.2f}x")
                    elif tts_duration < subtitle_duration * 0.8:
                        # 如果音频明显短于字幕时间，可以稍微减速
                        speed_factor = tts_duration / (subtitle_duration * 0.9)
                        if speed_factor < 0.8:
                            speed_factor = 0.8
                        tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                        print(f"  🔄 音频减速 {speed_factor:.2f}x")
                    
                    # 将TTS音频插入到指定时间位置
                    start_ms = int(start_time * 1000)
                    
                    # 确保不超出总时长
                    if start_ms + len(tts_audio) > len(composite_audio):
                        tts_audio = tts_audio[:len(composite_audio) - start_ms]
                    
                    # 叠加音频
                    composite_audio = composite_audio.overlay(tts_audio, position=start_ms)
                    successful_audio_count += 1
                    
                except Exception as e:
                    print(f"❌ 处理音频失败 {audio_file}: {str(e)}")
                    continue
            else:
                print(f"⚠️  音频文件不存在: {audio_file}")
        
        print(f"✅ 成功处理 {successful_audio_count}/{len(subtitles_30s)} 个音频文件")
        
        # 将AudioSegment转换为临时文件
        temp_audio_path = "temp_high_quality_audio.wav"
        print("💾 导出合成音频...")
        composite_audio.export(temp_audio_path, format="wav")
        
        # 加载合成音频
        print("🎧 加载合成音频到MoviePy...")
        new_audio = AudioFileClip(temp_audio_path)
        
        # 设置视频的新音轨
        print("🎬 合成最终视频...")
        final_video = video.with_audio(new_audio)
        
        # 输出视频
        print(f"💾 保存视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac'
        )
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        
        # 释放资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"🎉 高质量中文配音视频创建成功！")
        print(f"📁 输出文件: {output_path}")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            print(f"📊 文件大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建视频失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = asyncio.run(create_high_quality_chinese_video())
    if success:
        print("✅ 任务完成！")
    else:
        print("❌ 任务失败！")

if __name__ == "__main__":
    main() 