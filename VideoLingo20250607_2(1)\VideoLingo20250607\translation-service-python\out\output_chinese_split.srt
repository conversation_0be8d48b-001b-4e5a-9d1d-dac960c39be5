1
00:00:00,009 --> 00:00:02,120
大家好，

2
00:00:02,120 --> 00:00:04,232
欢迎收看Phaser Editor V4速成课程。

3
00:00:04,232 --> 00:00:06,343
今天我们将学习如何快速轻松地构建Phaser 3游戏，

4
00:00:06,343 --> 00:00:08,455
无需手动编写大量代码。

5
00:00:08,897 --> 00:00:13,431
大家好，

6
00:00:13,431 --> 00:00:17,965
欢迎来到Phaser Editor V4速成课程。

7
00:00:17,965 --> 00:00:22,499
如果你是Phaser或游戏开发的新手，

8
00:00:22,499 --> 00:00:27,032
来对地方了。

9
00:00:27,032 --> 00:00:31,566
Phaser Editor V4是一款强大的可视化HTML5游戏开发工具，

10
00:00:31,566 --> 00:00:36,100
让你专注于创作而非迷失在代码中。

11
00:00:36,100 --> 00:00:37,885
如果你是Phaser或游戏开发的新手，

12
00:00:37,885 --> 00:00:39,670
那你就来对地方了。

13
00:00:39,670 --> 00:00:41,455
Phaser Editor V4是一款强大的可视化HTML5游戏开发工具，

14
00:00:41,455 --> 00:00:43,240
让你专注于创作而无需深陷代码之中。

15
00:00:43,240 --> 00:00:45,025
在本视频中，

16
00:00:45,025 --> 00:00:46,810
我将一步步指导你完成项目设置、编辑器使用以及制作简单游戏的全过程。

17
00:00:46,810 --> 00:00:48,595
今天我们将涵盖以下内容：什么是Phaser Editor？

18
00:00:48,595 --> 00:00:50,380
如何快速创建全新项目。

19
00:00:50,380 --> 00:00:52,165
我们将概览工作台和用户界面中的各种视图。

20
00:00:52,165 --> 00:00:53,951
了解编辑器如何根据你在图形界面中的操作自动生成代码。

21
00:00:54,170 --> 00:00:56,355
要跟着操作，

22
00:00:56,355 --> 00:00:58,539
请确保你已安装Phaser Editor v4。

23
00:00:58,539 --> 00:01:00,723
我会在视频描述中附上文档链接，

24
00:01:00,723 --> 00:01:02,908
提供更详细的设置说明。

25
00:01:02,908 --> 00:01:05,092
在开始之前，

26
00:01:05,092 --> 00:01:07,276
请注意：本教程使用的是Phaser Editor v4。

27
00:01:07,276 --> 00:01:09,461
虽然具备JavaScript和Phaser 3框架基础会更有帮助，

28
00:01:09,461 --> 00:01:11,645
但即使你是完全的新手，也能轻松跟上。

29
00:01:11,645 --> 00:01:13,829
本视频不会讲解安装步骤，不过别担心，

30
00:01:13,829 --> 00:01:16,014
我已经在描述中附上了文档和资源链接。

31
00:01:16,014 --> 00:01:18,198
现在让我们正式开始。

32
00:01:18,198 --> 00:01:20,382
在这个快速入门教程中，

33
00:01:20,382 --> 00:01:22,567
我们将创建一个非常基础的Phaser 3游戏。

34
00:01:22,995 --> 00:01:25,436
这个游戏的目标是帮助我们学习使用Phaser Editor创建Phaser 3游戏的基础知识。

35
00:01:25,436 --> 00:01:27,878
在我们的游戏中，

36
00:01:27,878 --> 00:01:30,320
我们将创建一个简单的标题场景，

37
00:01:30,320 --> 00:01:32,762
玩家可以点击进入下一个场景，

38
00:01:32,762 --> 00:01:35,204
这样我们就能学习场景切换。

39
00:01:35,204 --> 00:01:37,645
然后我们会学习如何让玩家（也就是我们的恐龙）在游戏中移动，

40
00:01:37,645 --> 00:01:40,087
并与障碍物发生碰撞。

41
00:01:40,087 --> 00:01:42,529
我们还将实现拾取食物的功能，

42
00:01:42,529 --> 00:01:44,971
了解如何添加不同种类的食物道具。

43
00:01:44,971 --> 00:01:47,413
此外，

44
00:01:47,413 --> 00:01:49,854
我们还会学习加载精灵图集和创建基础动画的方法。

45
00:01:49,956 --> 00:01:53,257
这是一款非常基础的游戏，

46
00:01:53,257 --> 00:01:56,558
但它能帮助我们掌握使用Phaser Editor创建Phaser 3游戏的基本方法。

47
00:01:56,558 --> 00:01:59,859
那么什么是Phaser Editor？

48
00:01:59,859 --> 00:02:03,160
简单来说，

49
00:02:03,160 --> 00:02:06,462
Phaser Editor是一款旨在加速Phaser 3游戏开发的工具。

50
00:02:06,462 --> 00:02:09,763
它能协助构建游戏场景、管理资源资产，

51
00:02:09,763 --> 00:02:13,064
甚至自动生成代码。

52
00:02:13,064 --> 00:02:16,365
更正式的定义是：Phaser Editor是一款强大的可视化开发工具，

53
00:02:16,365 --> 00:02:19,667
专为使用Phaser游戏引擎创建2D游戏而设计。

54
00:02:19,836 --> 00:02:23,586
凭借直观的界面和丰富的功能集，

55
00:02:23,586 --> 00:02:27,336
它能让各级开发者快速轻松地为桌面和移动平台创建高质量游戏。

56
00:02:27,336 --> 00:02:31,086
无论你是初学者还是资深开发者，

57
00:02:31,086 --> 00:02:34,836
Phaser Editor都能提升你的Phaser 3游戏开发体验。

58
00:02:35,331 --> 00:02:38,830
Phaser编辑器的主要功能包括场景编辑器，

59
00:02:38,830 --> 00:02:42,328
这是一个可视化工具，

60
00:02:42,328 --> 00:02:45,826
能让你通过摆放图片和其他游戏对象来创建Phaser场景或关卡。

61
00:02:45,826 --> 00:02:49,325
该工具支持拖放各类Phaser游戏对象到场景中，

62
00:02:49,325 --> 00:02:52,823
如图片、文本等多种元素。

63
00:02:52,823 --> 00:02:56,321
场景预览直接由Phaser引擎渲染，

64
00:02:56,321 --> 00:02:59,819
因此你可以确信所见即所得——编辑界面展示的就是最终游戏效果。

65
00:03:00,794 --> 00:03:03,913
接下来是物理编辑器。

66
00:03:03,913 --> 00:03:07,032
这个工具能让你轻松创建和修改街机物理游戏对象。

67
00:03:07,032 --> 00:03:10,151
你可以直观地调整游戏对象的物理体，

68
00:03:10,151 --> 00:03:13,270
无需再通过代码手动操作。

69
00:03:13,270 --> 00:03:16,389
还能通过图形界面添加碰撞检测和重叠检查等功能。

70
00:03:17,175 --> 00:03:20,826
接下来是我们的动画编辑器。

71
00:03:20,826 --> 00:03:24,478
这款工具可以让你手动或自动从纹理帧创建精灵动画，

72
00:03:24,478 --> 00:03:28,130
你不再需要为各种动画动态生成JSON文件了。

73
00:03:28,130 --> 00:03:31,782
通过图形界面，

74
00:03:31,782 --> 00:03:35,434
你可以修改帧率、延迟、重复、时间缩放等配置参数。

75
00:03:35,434 --> 00:03:39,086
紧接着是资源包编辑器。

76
00:03:39,258 --> 00:03:43,008
接下来是我们的动画编辑器。

77
00:03:43,008 --> 00:03:46,759
这个工具可以让你手动或自动从纹理帧创建精灵动画，

78
00:03:46,759 --> 00:03:50,510
无需再为各种动画动态生成JSON文件。

79
00:03:50,510 --> 00:03:54,261
通过图形界面，

80
00:03:54,261 --> 00:03:58,012
你可以修改帧率、延迟、重复、时间缩放等配置参数。

81
00:03:58,658 --> 00:04:01,386
最后要介绍的是预制件系统。

82
00:04:01,386 --> 00:04:04,113
预制件是Phaser编辑器的核心概念，

83
00:04:04,113 --> 00:04:06,841
简而言之，

84
00:04:06,841 --> 00:04:09,568
它能让您创建可重复使用的游戏对象，

85
00:04:09,568 --> 00:04:12,295
统一控制其外观、属性和设置，并集中存储。

86
00:04:12,295 --> 00:04:15,023
之后您就可以通过该预制件创建多个对象实例。

87
00:04:15,023 --> 00:04:17,750
现在让我们打开Phaser编辑器开始创建项目。

88
00:04:17,750 --> 00:04:20,477
启动编辑器后，

89
00:04:20,477 --> 00:04:23,205
点击"新建项目"来创建一个全新的Phaser编辑器项目。

90
00:04:23,205 --> 00:04:25,932
在接下来的界面中，

91
00:04:25,932 --> 00:04:28,660
您可以为项目选择初始模板。

92
00:04:28,985 --> 00:04:32,579
这样就会搭建好你的Phaser 3游戏项目框架。

93
00:04:32,579 --> 00:04:36,173
编辑器安装过程中已内置了三个离线项目模板。

94
00:04:36,173 --> 00:04:39,767
而入门示例和更高级的框架范例需要从网上下载。

95
00:04:39,767 --> 00:04:43,361
我们将使用这个基础的JavaScript模板开始。

96
00:04:43,361 --> 00:04:46,955
选择模板后，

97
00:04:46,955 --> 00:04:50,549
系统会提示你选择项目在电脑上的保存位置。

98
00:04:50,549 --> 00:04:54,143
你需要新建一个文件夹，

99
00:04:54,143 --> 00:04:57,737
我准备将其命名为"Hello World"。

100
00:04:58,574 --> 00:04:59,990
创建新项目文件夹后，

101
00:05:00,043 --> 00:05:09,906
创建新项目文件夹后，

102
00:05:09,906 --> 00:05:19,769
你需要打开这个项目文件夹，

103
00:05:19,769 --> 00:05:29,632
此时Phaser编辑器会自动生成项目模板 并搭建运行游戏所需的全部本地文件。

104
00:05:29,956 --> 00:05:33,627
打开项目文件夹后，

105
00:05:33,627 --> 00:05:37,298
Phaser编辑器会自动配置项目模板，

106
00:05:37,298 --> 00:05:40,969
并搭建本地运行游戏所需的所有项目文件。

107
00:05:40,969 --> 00:05:44,641
初始化完成后，您会看到欢迎页面。

108
00:05:44,641 --> 00:05:48,312
从这里我们可以选择"打开场景"，

109
00:05:48,312 --> 00:05:51,983
从项目模板包含的两个场景中任选其一。

110
00:05:51,983 --> 00:05:55,654
现在让我们打开关卡场景，

111
00:05:55,654 --> 00:05:59,326
编辑器界面随即会更新显示当前的Phaser场景及其包含的游戏对象。

112
00:06:00,009 --> 00:06:07,491
当我们切换到不同的编辑器时，

113
00:06:07,491 --> 00:06:14,974
这里会实时更新显示对应编辑器的内容。

114
00:06:14,974 --> 00:06:22,456
比如在编辑资源包时，

115
00:06:22,456 --> 00:06:29,939
就能看到组成该资源包的所有资源。

116
00:06:30,298 --> 00:06:34,996
而当我选中这个图像游戏对象时，

117
00:06:34,996 --> 00:06:39,695
右侧面板会实时更新，

118
00:06:39,695 --> 00:06:44,394
此时显示的就是该游戏对象的所有属性参数。

119
00:06:44,394 --> 00:06:49,092
我们可以进行诸如修改变量名、调整游戏对象位置等多项操作。

120
00:06:49,092 --> 00:06:53,791
根据当前所处的编辑器不同，

121
00:06:53,791 --> 00:06:58,490
右侧面板会动态显示所选对象的对应属性。

122
00:06:58,728 --> 00:07:00,108
而如果我选择这个图像游戏对象，

123
00:07:00,108 --> 00:07:01,488
面板就会更新，

124
00:07:01,488 --> 00:07:02,868
现在我们就能看到游戏对象的所有属性。

125
00:07:02,868 --> 00:07:04,248
我们可以修改变量名称，

126
00:07:04,248 --> 00:07:05,629
调整游戏对象的位置等等。

127
00:07:05,629 --> 00:07:07,009
根据你当前所处的编辑器不同，

128
00:07:07,009 --> 00:07:08,389
面板会实时显示所选对象的对应属性。

129
00:07:08,389 --> 00:07:09,769
屏幕底部是文件视图区，

130
00:07:09,769 --> 00:07:11,150
这里会列出项目的所有文件。

131
00:07:11,150 --> 00:07:12,530
和其他文件编辑器一样，

132
00:07:12,530 --> 00:07:13,910
你可以进行添加新文件、删除、重命名甚至移动文件等基本操作。

133
00:07:13,910 --> 00:07:15,290
当你打开某个文件时，

134
00:07:15,290 --> 00:07:16,671
它会在集成开发环境中打开以供编辑。

135
00:07:16,671 --> 00:07:18,051
和Phaser编辑器中的其他对象一样，

136
00:07:18,051 --> 00:07:19,431
选中文件时检查器面板会同步显示该文件的相关信息。

137
00:07:19,431 --> 00:07:20,811
旁边是积木视图区，

138
00:07:20,811 --> 00:07:22,192
这里会显示当前编辑器中可用于构建对象的元素组件。

139
00:07:22,500 --> 00:07:27,167
当我们打开场景编辑器时，

140
00:07:27,167 --> 00:07:31,834
这里会显示可添加到场景中的各类Phaser 3内置游戏对象类型。

141
00:07:31,834 --> 00:07:36,500
同时也会展示已加载的图片或其他资源，

142
00:07:36,500 --> 00:07:41,168
我们可以将这些元素直接拖入场景中。

143
00:07:41,168 --> 00:07:45,835
若当前激活的是其他编辑器（如资源包编辑器），

144
00:07:45,835 --> 00:07:50,502
则会显示可导入资源包的各种文件类型。

145
00:07:50,740 --> 00:08:00,177
你可以更改场景的默认布局、选择主题颜色等。

146
00:08:00,177 --> 00:08:09,615
旁边是联系Phaser Studio团队的支持聊天按钮。

147
00:08:09,615 --> 00:08:19,052
左上角有几个创建新内容的快捷方式：播放按钮可运行项目并在浏览器中查看；快速对话框用于打开场景；还有在Visual Studio Code中打开项目的选项——点击该按钮会以当前活动文件在VS Code中打开你的项目。

148
00:08:19,257 --> 00:08:21,775
当你点击该按钮时，

149
00:08:21,775 --> 00:08:24,293
如果本地已安装VS Code，

150
00:08:24,293 --> 00:08:26,811
它就会自动打开，

151
00:08:26,811 --> 00:08:29,328
你可以查看构成项目的代码。

152
00:08:29,328 --> 00:08:31,846
最后在屏幕顶部，

153
00:08:31,846 --> 00:08:34,364
我们会看到一些随当前编辑器变化的不同工具。

154
00:08:34,364 --> 00:08:36,882
例如在欢迎界面时，

155
00:08:36,882 --> 00:08:39,399
这里不会显示任何工具；在资源包编辑器中，

156
00:08:39,399 --> 00:08:41,917
会出现导入文件的相关选项；而现在处于场景编辑器时，

157
00:08:41,917 --> 00:08:44,435
则会显示场景编辑工具。

158
00:08:44,435 --> 00:08:46,952
场景编辑器的第一个工具是平移工具。

159
00:08:47,174 --> 00:08:56,526
移动工具允许你选中游戏对象后，

160
00:08:56,526 --> 00:09:05,878
在场景中拖动它，

161
00:09:05,878 --> 00:09:15,230
这会实时更新该游戏对象的X和Y坐标位置。

162
00:09:15,501 --> 00:09:18,471
这个工具可以修改游戏对象的原点位置。

163
00:09:18,471 --> 00:09:21,440
默认情况下，

164
00:09:21,440 --> 00:09:24,409
大多数游戏对象的原点位于中心位置，

165
00:09:24,409 --> 00:09:27,379
即X和Y轴均为0.5。

166
00:09:27,379 --> 00:09:30,348
原点是Phaser在场景中放置游戏对象的基准点，

167
00:09:30,348 --> 00:09:33,317
它决定了游戏对象的起始定位位置。

168
00:09:33,317 --> 00:09:36,287
我们看到的X和Y坐标值都是基于这个原点计算的。

169
00:09:36,287 --> 00:09:39,256
比如当我把原点移到左上角，

170
00:09:39,256 --> 00:09:42,225
并将位置坐标设为(0,0)时...

171
00:09:42,481 --> 00:09:44,232
这样你就能修改游戏对象的原点位置。

172
00:09:44,232 --> 00:09:45,983
默认情况下，

173
00:09:45,983 --> 00:09:47,734
大多数游戏对象的原点位于中心位置，

174
00:09:47,734 --> 00:09:49,485
因此X和Y值都是0.5。

175
00:09:49,485 --> 00:09:51,235
这个原点决定了Phaser如何在场景中放置游戏对象，

176
00:09:51,235 --> 00:09:52,986
它会将该点作为游戏对象的起始定位点。

177
00:09:52,986 --> 00:09:54,737
我们在这里设置的X和Y坐标值，

178
00:09:54,737 --> 00:09:56,488
就是基于这个原点来计算的。

179
00:09:56,488 --> 00:09:58,239
如果我将原点移到左上角，

180
00:09:58,239 --> 00:09:59,990
然后把位置坐标设为0,0，

181
00:10:00,009 --> 00:10:01,844
现在可以看到，

182
00:10:01,844 --> 00:10:03,680
系统将这个点作为我们游戏对象的起始点。

183
00:10:03,680 --> 00:10:05,515
如果我把原点放回中间位置，

184
00:10:05,515 --> 00:10:07,351
就能看到当前坐标归零，

185
00:10:07,351 --> 00:10:09,186
系统将其作为游戏对象的原点。

186
00:10:09,186 --> 00:10:11,022
接下来介绍区域选择工具，

187
00:10:11,022 --> 00:10:12,857
通过拖拽划定区域可以批量选中游戏对象。

188
00:10:12,857 --> 00:10:14,693
最后是平移工具，

189
00:10:14,693 --> 00:10:16,529
只需在场景中点击拖动即可自由调整视口视角。

190
00:10:16,529 --> 00:10:18,364
如需查看快捷键，

191
00:10:18,364 --> 00:10:20,200
将鼠标悬停在工具图标上就会显示对应快捷操作。

192
00:10:20,200 --> 00:10:22,035
值得一提的是，

193
00:10:22,035 --> 00:10:23,871
图形界面中大多数属性选项在悬停时都会显示工具提示，

194
00:10:23,871 --> 00:10:25,706
说明该功能在Phaser编辑器中的具体用法。

195
00:10:29,019 --> 00:10:32,090
在我们操作项目并移动场景中的物体时，

196
00:10:32,090 --> 00:10:35,162
Phaser编辑器实际上正在为我们的Phaser项目动态生成代码。

197
00:10:35,162 --> 00:10:38,233
要查看这些代码，

198
00:10:38,233 --> 00:10:41,304
我们需要打开与关卡相关联的.js文件。

199
00:10:41,304 --> 00:10:44,376
在图形界面中，当我们为项目创建新文件时，

200
00:10:44,376 --> 00:10:47,447
通常会产生两个文件。

201
00:10:47,739 --> 00:10:51,814
在我们操作项目并移动场景中的元素时，

202
00:10:51,814 --> 00:10:55,889
Phaser编辑器实际上正在为我们的Phaser项目动态生成代码。

203
00:10:55,889 --> 00:10:59,965
要查看这些代码，

204
00:10:59,965 --> 00:11:04,041
我们需要打开与关卡关联的.js文件。

205
00:11:04,041 --> 00:11:08,116
在图形界面中创建新项目文件时，

206
00:11:08,116 --> 00:11:12,192
通常会产生两个文件：

207
00:11:12,721 --> 00:11:40,759
首先会看到test.scene文件被创建
现在场景已显示在图形界面中
同时test.js文件也已生成

208
00:11:40,929 --> 00:11:44,076
首先我们会看到test.scene文件被创建，

209
00:11:44,076 --> 00:11:47,222
现在GUI中显示了场景，

210
00:11:47,222 --> 00:11:50,368
test.js文件也已生成。

211
00:11:50,368 --> 00:11:53,514
生成的代码主要分为两部分：第一部分是编译器生成的代码，

212
00:11:53,514 --> 00:11:56,661
第二部分是可供添加自定义代码的安全区域。

213
00:11:56,661 --> 00:11:59,807
在整个代码中，

214
00:11:59,807 --> 00:12:02,953
我们将看到这两种不同的代码块：一种是"编译代码开始/结束"标记，

215
00:12:02,953 --> 00:12:06,100
另一种是"用户代码开始/结束"标记。

216
00:12:06,442 --> 00:12:09,578
在这里，

217
00:12:09,578 --> 00:12:12,714
我们能看到与关卡场景中游戏对象绑定的代码。

218
00:12:12,714 --> 00:12:15,850
目前有两个游戏对象：一个是这个图像游戏对象，

219
00:12:15,850 --> 00:12:18,986
另一个是文本游戏对象。

220
00:12:18,986 --> 00:12:22,122
代码中对应两个变量：dino指向我们的图像游戏对象，

221
00:12:22,122 --> 00:12:25,258
welcome则指向文本游戏对象。

222
00:12:25,258 --> 00:12:28,394
它们各自拥有X和Y坐标位置，

223
00:12:28,394 --> 00:12:31,530
比如图像坐标显示为329×235——当我们选中图像游戏对象时，

224
00:12:31,530 --> 00:12:34,667
就能看到这个位置数值与之对应。

225
00:12:34,855 --> 00:12:38,074
如果我拖动这个恐龙到这里，

226
00:12:38,074 --> 00:12:41,293
可以看到属性值已经改变。

227
00:12:41,293 --> 00:12:44,513
回到level.js文件，

228
00:12:44,513 --> 00:12:47,733
会发现代码也同步更新了。

229
00:12:47,733 --> 00:12:50,952
在项目中添加自定义代码时，这点很重要，

230
00:12:50,952 --> 00:12:54,172
要确保代码放在正确位置，避免被覆盖。

231
00:12:56,596 --> 00:12:58,729
要在本地运行和测试游戏，

232
00:12:58,729 --> 00:13:00,862
你需要启动一个开发服务器。

233
00:13:00,862 --> 00:13:02,995
默认情况下，

234
00:13:02,995 --> 00:13:05,128
Phaser编辑器自带了一个内置的HTTP服务器可以实现这个功能。

235
00:13:05,128 --> 00:13:07,261
我们使用的是基础JavaScript项目模板。

236
00:13:07,261 --> 00:13:09,394
如果点击左上角的这个播放按钮，

237
00:13:09,394 --> 00:13:11,527
就会启动开发服务器，

238
00:13:11,527 --> 00:13:13,660
并在你的网页浏览器中打开游戏。

239
00:13:13,660 --> 00:13:15,793
启动成功后，

240
00:13:15,793 --> 00:13:17,926
你会看到本地主机端口显示为editor external，

241
00:13:17,926 --> 00:13:20,059
这里就能看到我们的Phaser游戏了。

242
00:13:20,059 --> 00:13:22,192
当开发服务器运行时，

243
00:13:22,192 --> 00:13:24,325
如果你在场景中移动游戏对象并点击保存，

244
00:13:24,325 --> 00:13:26,458
再回到浏览器查看时

245
00:13:26,764 --> 00:13:29,693
要本地运行和测试游戏，

246
00:13:29,693 --> 00:13:32,621
您需要启动开发服务器。

247
00:13:32,621 --> 00:13:35,549
默认情况下，

248
00:13:35,549 --> 00:13:38,478
Phaser编辑器提供了内置的HTTP服务器来实现这一功能。

249
00:13:38,478 --> 00:13:41,406
我们使用的是基础JavaScript项目模板。

250
00:13:41,406 --> 00:13:44,334
点击左上角的播放按钮，

251
00:13:44,334 --> 00:13:47,263
就会启动开发服务器并在浏览器中打开您的游戏。

252
00:13:47,263 --> 00:13:50,191
启动后，

253
00:13:50,191 --> 00:13:53,119
您会看到显示本地主机端口及编辑器外部链接，

254
00:13:53,119 --> 00:13:56,048
这里就能看到我们的Phaser游戏了。

255
00:13:56,288 --> 00:13:59,373
这样你就有选项可以启动开发服务器，

256
00:13:59,373 --> 00:14:02,458
然后就能在浏览器中运行游戏了。

257
00:14:02,458 --> 00:14:05,544
Phaser框架支持多种输入类型，

258
00:14:05,544 --> 00:14:08,629
包括键盘、鼠标、游戏手柄，

259
00:14:08,629 --> 00:14:11,715
甚至在移动设备上的触摸操作。

260
00:14:11,918 --> 00:14:14,658
我们将首先了解的是点击事件。

261
00:14:14,658 --> 00:14:17,398
在Phaser游戏中，

262
00:14:17,398 --> 00:14:20,138
点击事件通常是一种让玩家直接与场景或游戏对象互动的机制，

263
00:14:20,138 --> 00:14:22,878
点击时会触发游戏中的某些操作。

264
00:14:22,878 --> 00:14:25,617
默认情况下，

265
00:14:25,617 --> 00:14:28,357
这个Phaser模板已支持点击事件功能。

266
00:14:28,357 --> 00:14:31,097
现在我们切换到运行游戏的浏览器窗口，

267
00:14:31,097 --> 00:14:33,837
如果点击恐龙游戏对象，

268
00:14:33,837 --> 00:14:36,576
就能看到这里的文本内容被更新了。

269
00:14:36,765 --> 00:14:40,636
其运作原理是：当你在场景编辑器中选择一个游戏对象时，

270
00:14:40,636 --> 00:14:44,507
可以为其定义点击区域。

271
00:14:44,507 --> 00:14:48,377
点击区域就是玩家实际可点击的游戏对象范围，

272
00:14:48,377 --> 00:14:52,248
这样我们就能监听点击事件及其他各类事件。

273
00:14:52,248 --> 00:14:56,119
点击区域支持多种形状选择，默认情况下，

274
00:14:56,119 --> 00:14:59,990
建议对大多数图像类游戏对象使用矩形作为点击区域。

275
00:15:00,009 --> 00:15:04,158
其运作原理是：当你在场景编辑器中选择一个游戏对象时，

276
00:15:04,158 --> 00:15:08,307
可以为该对象定义点击区域。

277
00:15:08,307 --> 00:15:12,456
点击区域就是玩家实际可以点击的游戏对象区域，

278
00:15:12,456 --> 00:15:16,606
我们可以监听该点击事件及其他各类事件类型。

279
00:15:16,606 --> 00:15:20,755
点击区域支持多种形状选择，默认情况下，

280
00:15:20,755 --> 00:15:24,904
图像类游戏对象通常建议使用矩形。

281
00:15:25,177 --> 00:15:28,330
当我们查看dino1的定义处时，

282
00:15:28,330 --> 00:15:31,483
可以看到这里的游戏对象。

283
00:15:31,483 --> 00:15:34,636
原版恐龙与新恐龙的关键区别之一 就是这个setInteractive方法。

284
00:15:34,636 --> 00:15:37,788
要让Phaser框架为游戏对象启用输入功能，

285
00:15:37,788 --> 00:15:40,941
我们必须调用这个方法。

286
00:15:40,941 --> 00:15:44,094
而Phaser编辑器在我们定义碰撞区域后，

287
00:15:44,094 --> 00:15:47,247
会自动添加相关代码。

288
00:15:47,247 --> 00:15:50,399
现在我们已经添加了这个矩形碰撞区域，

289
00:15:50,860 --> 00:15:54,361
回到level.js文件，

290
00:15:54,361 --> 00:15:57,862
我们会看到setInteract方法已被调用，

291
00:15:57,862 --> 00:16:01,363
并使用了我们定义的矩形参数。

292
00:16:01,363 --> 00:16:04,863
实现点击事件的第二个关键点是需要监听该事件并作出响应。

293
00:16:04,863 --> 00:16:08,364
在这个create方法下方，

294
00:16:08,364 --> 00:16:11,865
模板自带的代码已经添加了对此事件的监听。

295
00:16:11,865 --> 00:16:15,365
Phaser框架内置了多种可触发的事件类型

296
00:16:15,538 --> 00:16:17,226
如果我们回到level.js文件，

297
00:16:17,226 --> 00:16:18,914
现在可以看到setInteract方法已被调用，

298
00:16:18,914 --> 00:16:20,603
并传入了我们定义的矩形参数。

299
00:16:20,603 --> 00:16:22,291
实现点击事件的第二步是需要监听该事件并作出响应。

300
00:16:22,291 --> 00:16:23,979
在这个create方法底部，

301
00:16:23,979 --> 00:16:25,668
模板自带的代码已经添加了对此事件的监听。

302
00:16:25,668 --> 00:16:27,356
Phaser框架内置了多种系统事件，

303
00:16:27,356 --> 00:16:29,044
可以响应各种不同的操作。

304
00:16:29,044 --> 00:16:30,733
当我们监听输入事件时，

305
00:16:30,733 --> 00:16:32,421
可能是pointer down（指针按下）、pointer up（指针抬起）等多种类型。

306
00:16:32,421 --> 00:16:34,109
现在我们可以提供一个回调函数，

307
00:16:34,109 --> 00:16:35,798
在事件触发时执行特定操作。

308
00:16:35,798 --> 00:16:37,486
这个例子很好地展示了如何基于Phaser Editor生成的代码进行功能扩展。

309
00:16:37,486 --> 00:16:39,174
我们既能添加事件监听器，

310
00:16:39,174 --> 00:16:40,863
也能修改在场景编辑器中创建的其他游戏对象。

311
00:16:40,863 --> 00:16:42,551
例如，我们直接复制这段代码块，

312
00:16:42,551 --> 00:16:44,240
然后将其粘贴到指定位置。

313
00:16:44,633 --> 00:16:48,877
And we're going to listen for the pointer down event on our dyno1 variable. So we're going to do this. We'll do dyno1. We'll do on pointer down. But instead of saying hello world,

314
00:16:48,877 --> 00:16:53,121
we'll just say hi. So if we come back to our level scene,

315
00:16:53,121 --> 00:16:57,365
in order for us to use and run that code,

316
00:16:57,365 --> 00:17:01,610
we need to update our variable scope. So for the JavaScript code that's created,

317
00:17:01,610 --> 00:17:05,854
that's tied to our variable definition over here in our inspector. So by default,

318
00:17:05,854 --> 00:17:10,098
when we add in our new game objects,

319
00:17:10,098 --> 00:17:14,343
the scope will always be local to our class where it's created.

320
00:17:14,701 --> 00:17:19,428
这意味着我们只能在这段编辑器创建方法中添加的代码本地上下文中使用它。

321
00:17:19,428 --> 00:17:24,154
也就是说，除非我们更改该属性，

322
00:17:24,154 --> 00:17:28,882
否则无法在类的其他任何地方使用它。

323
00:17:29,121 --> 00:17:34,523
如果我们看一下原始的dyno对象，

324
00:17:34,523 --> 00:17:39,926
可以看到它的作用域是类级别的。

325
00:17:39,926 --> 00:17:45,328
因此我们需要在这里更新新dyno变量的作用域。

326
00:17:45,328 --> 00:17:50,731
让我们修改这个设置，将其设为类变量。

327
00:17:50,731 --> 00:17:56,134
这意味着这个变量将对我们这个Level类中的所有逻辑代码都可用。

328
00:17:56,442 --> 00:17:59,569
来看一个例子，如果我们不做修改，

329
00:17:59,569 --> 00:18:02,696
游戏中就会出现错误。

330
00:18:02,696 --> 00:18:05,823
保存更改后，回到浏览器刷新游戏。

331
00:18:05,823 --> 00:18:08,950
我们需要打开开发者工具，

332
00:18:08,950 --> 00:18:12,077
右键选择"检查"，切换到控制台查看日志。

333
00:18:12,077 --> 00:18:15,204
现在就能看到与level.js生成代码相关的报错信息，

334
00:18:15,204 --> 00:18:18,331
提示"无法读取未定义的属性"。

335
00:18:18,331 --> 00:18:21,459
这是因为在关卡代码中

336
00:18:21,766 --> 00:18:23,806
来看一个例子，如果我们不做修改，

337
00:18:23,806 --> 00:18:25,846
游戏中就会出现错误。

338
00:18:25,846 --> 00:18:27,886
保存更改后返回浏览器，刷新游戏界面。

339
00:18:27,886 --> 00:18:29,926
接着打开开发者工具，

340
00:18:29,926 --> 00:18:31,966
右键点击选择"检查"，

341
00:18:31,966 --> 00:18:34,006
切换到控制台查看日志。

342
00:18:34,006 --> 00:18:36,046
现在我们会发现level.js文件生成的代码报错了，

343
00:18:36,046 --> 00:18:38,086
提示"无法读取未定义的属性"。

344
00:18:38,086 --> 00:18:40,126
这是因为在关卡代码中，

345
00:18:40,673 --> 00:18:43,461
正如我提到的，我们可以监听多种内置事件。

346
00:18:43,461 --> 00:18:46,249
在Phaser框架的官方文档网站上，

347
00:18:46,249 --> 00:18:49,038
列出了所有支持的事件类型，

348
00:18:49,038 --> 00:18:51,826
目前我们重点关注的是Phaser输入事件。

349
00:18:51,826 --> 00:18:54,615
针对输入系统，

350
00:18:54,615 --> 00:18:57,403
我们支持诸如点击场景中的对象、拖拽对象等操作。

351
00:18:57,403 --> 00:19:00,191
还可以实现将对象拖放到另一个对象上的功能。

352
00:19:00,191 --> 00:19:02,980
我们能够监听指针按下（pointer down）、指针抬起（pointer up）以及指针悬停（pointer over）等事件。

353
00:19:02,980 --> 00:19:05,768
通过这些功能，

354
00:19:05,768 --> 00:19:08,557
我们可以创建类似按钮的交互效果——比如当鼠标悬停在按钮上时，

355
00:19:08,984 --> 00:19:11,423
如我所言，我们可以监听多种内置事件。

356
00:19:11,423 --> 00:19:13,861
在Phaser框架文档网站上，

357
00:19:13,861 --> 00:19:16,299
列出了所有支持的事件类型，

358
00:19:16,299 --> 00:19:18,738
目前我们重点关注的是Phaser输入事件。

359
00:19:18,738 --> 00:19:21,176
针对输入系统，

360
00:19:21,176 --> 00:19:23,615
我们支持诸如点击场景中的对象并拖拽、将对象放置到另一个对象上等操作。

361
00:19:23,615 --> 00:19:26,053
还能监听指针按下、抬起以及悬停事件，

362
00:19:26,053 --> 00:19:28,491
这样就能实现按钮效果——比如鼠标悬停时施加特效，

363
00:19:28,491 --> 00:19:30,930
移开时样式变化等等。

364
00:19:31,152 --> 00:19:33,555
接下来我们要看的是键盘输入事件。

365
00:19:33,555 --> 00:19:35,958
我们将添加对方向键的监听功能，

366
00:19:35,958 --> 00:19:38,361
然后让场景中的游戏对象随之移动。

367
00:19:38,361 --> 00:19:40,764
在内置模块中添加键盘事件支持时，

368
00:19:40,764 --> 00:19:43,167
输入类型是其中之一。

369
00:19:43,167 --> 00:19:45,570
展开这个选项后，

370
00:19:45,570 --> 00:19:47,974
我们可以选择为场景添加键盘按键。

371
00:19:47,974 --> 00:19:50,377
把这个模块拖到场景中，

372
00:19:50,377 --> 00:19:52,780
会看到新增了一个名为"input"的文件夹。

373
00:19:52,780 --> 00:19:55,183
默认情况下变量名是"keyboard key"，

374
00:19:55,183 --> 00:19:57,586
我们把它改成"up key"。

375
00:19:57,586 --> 00:19:59,990
现在针对我们的...

376
00:20:00,009 --> 00:20:02,219
接下来我们要探讨的输入类型是键盘事件。

377
00:20:02,219 --> 00:20:04,430
我们将添加对方向键的监听功能，

378
00:20:04,430 --> 00:20:06,640
然后在场景中移动游戏对象。

379
00:20:06,640 --> 00:20:08,851
在内置模块中添加键盘事件支持时，

380
00:20:08,851 --> 00:20:11,062
输入类型是其中之一。

381
00:20:11,062 --> 00:20:13,272
展开这个选项后，

382
00:20:13,272 --> 00:20:15,483
我们可以选择向场景添加键盘按键。

383
00:20:15,483 --> 00:20:17,694
把这个模块拖到场景中，

384
00:20:17,694 --> 00:20:19,904
会看到新增了一个名为"input"的文件夹。

385
00:20:19,904 --> 00:20:22,115
默认情况下变量名是"keyboard key"，

386
00:20:22,115 --> 00:20:24,326
我们将其改为"up key"。

387
00:20:24,923 --> 00:20:26,936
它默认的作用域是类级别，

388
00:20:26,936 --> 00:20:28,950
我们需要保持这个设置，

389
00:20:28,950 --> 00:20:30,963
因为我们将在场景的update方法中监听输入并作出响应。

390
00:20:30,963 --> 00:20:32,977
接下来我们可以选择要监听的按键，

391
00:20:32,977 --> 00:20:34,991
这里我们选择上方向键。

392
00:20:34,991 --> 00:20:37,005
选定后保存场景，

393
00:20:37,005 --> 00:20:39,018
切换到level.js文件，

394
00:20:39,018 --> 00:20:41,032
可以看到在编辑器create方法中已经创建了上方向键变量，

395
00:20:41,032 --> 00:20:43,046
并且我们正在监听输入插件。

396
00:20:43,385 --> 00:20:48,443
这里的方法是Phaser场景类自带的内置方法之一。

397
00:20:48,443 --> 00:20:53,501
这个方法会在每次游戏更新时被调用。

398
00:20:53,501 --> 00:20:58,559
现在我们可以在这个方法中检查按键是否被按下。

399
00:20:58,559 --> 00:21:03,617
我们只需添加一个if判断语句：如果上方向键处于按下状态，

400
00:21:03,617 --> 00:21:08,675
就表示玩家当前正按着这个键。

401
00:21:09,136 --> 00:21:12,415
现在我们可以选择一个游戏对象，

402
00:21:12,415 --> 00:21:15,694
并根据按键状态来更新它的位置。

403
00:21:15,694 --> 00:21:18,973
让我们引用恐龙游戏对象及其Y属性。

404
00:21:18,973 --> 00:21:22,252
当我们按下上键时，

405
00:21:22,252 --> 00:21:25,531
希望游戏对象在场景中向上移动。

406
00:21:25,531 --> 00:21:28,810
在场景坐标系中，左上角的位置是(0,

407
00:21:28,810 --> 00:21:32,088
0) - X轴为0，Y轴也为0。

408
00:21:32,381 --> 00:21:35,499
当我们向右移动时，场景中的X值会增加。

409
00:21:35,499 --> 00:21:38,618
向左移动则会减小X值。

410
00:21:38,618 --> 00:21:41,737
向下移动会增加Y值，

411
00:21:41,737 --> 00:21:44,856
而向上移动则需要减小Y值。

412
00:21:44,856 --> 00:21:47,975
这意味着当按下上键时，

413
00:21:47,975 --> 00:21:51,094
我们实际上要从Y值中减去一个数值。

414
00:21:51,094 --> 00:21:54,213
这里我们直接减去4。

415
00:21:54,213 --> 00:21:57,332
保存代码后，确保场景已保存，

416
00:21:57,332 --> 00:22:00,451
回到浏览器刷新场景即可。

417
00:22:00,759 --> 00:22:04,014
当我们向右移动时，这会增加x值。

418
00:22:04,014 --> 00:22:07,270
向左移动则会减小x值。

419
00:22:07,270 --> 00:22:10,525
向下移动时，y值会增加。

420
00:22:10,525 --> 00:22:13,781
而向上移动时，y值需要递减。

421
00:22:13,781 --> 00:22:17,036
这意味着当按下上方向键时，

422
00:22:17,036 --> 00:22:20,292
我们需要从y值中减去一个数值。

423
00:22:20,292 --> 00:22:23,547
这里我们直接使用"-=4"来实现。

424
00:22:23,547 --> 00:22:26,803
保存代码后，确保场景已保存，

425
00:22:26,803 --> 00:22:30,058
让我们回到浏览器刷新场景。

426
00:22:30,298 --> 00:22:33,512
切换到level.js文件，

427
00:22:33,512 --> 00:22:36,726
现在我们要做同样的事情，

428
00:22:36,726 --> 00:22:39,941
但这次要引用我们创建的其他按键。

429
00:22:39,941 --> 00:22:43,155
快速查看编辑器中的create方法，

430
00:22:43,155 --> 00:22:46,370
可以看到其他按键已经根据我们指定的键码定义好了。

431
00:22:46,370 --> 00:22:49,585
现在回到update方法，

432
00:22:49,585 --> 00:22:52,799
使用else if语句，这里复制一下。

433
00:22:52,799 --> 00:22:56,013
如果是上方向键，如果是下方向键被按下，

434
00:22:56,237 --> 00:22:59,861
现在我们要继续增加Y值。

435
00:22:59,861 --> 00:23:03,485
让我们复制这段完整代码，

436
00:23:03,485 --> 00:23:07,109
然后处理左右方向键。

437
00:23:07,109 --> 00:23:10,733
如果左键按下，

438
00:23:10,733 --> 00:23:14,357
我们要减少X值；如果右键按下，

439
00:23:14,357 --> 00:23:17,981
我们要增加X值。

440
00:23:17,981 --> 00:23:21,605
保存后回到浏览器，

441
00:23:21,605 --> 00:23:25,230
现在按方向键就能在场景中移动游戏对象了。

442
00:23:28,761 --> 00:23:33,546
现在我们可以通过键盘事件在场景中移动物体了。

443
00:23:33,546 --> 00:23:38,329
接下来我们要看看如何利用物理系统让游戏对象之间产生互动。

444
00:23:38,329 --> 00:23:43,114
Phaser Editor默认支持Phaser的街机物理系统，

445
00:23:43,507 --> 00:23:50,418
现在我们可以通过键盘事件在场景中移动物体。

446
00:23:50,418 --> 00:23:57,328
接下来我们要学习的是如何利用物理系统让游戏对象之间产生互动。

447
00:23:57,328 --> 00:24:04,240
Phaser Editor默认支持Phaser的街机物理系统，

448
00:24:04,497 --> 00:24:06,924
为了让游戏支持物理效果，

449
00:24:06,924 --> 00:24:09,352
我们需要更新游戏配置，

450
00:24:09,352 --> 00:24:11,780
告诉Phaser我们想使用哪种物理系统。

451
00:24:11,780 --> 00:24:14,207
在我们的游戏配置中，

452
00:24:14,207 --> 00:24:16,635
这些设置会放在这个主.js文件里。

453
00:24:16,635 --> 00:24:19,063
在代码内部，

454
00:24:19,063 --> 00:24:21,491
我们会新建一个Phaser游戏实例，

455
00:24:21,491 --> 00:24:23,918
并为游戏提供配置参数。

456
00:24:23,918 --> 00:24:26,346
在这里面，我们可以指定要使用的物理系统。

457
00:24:26,346 --> 00:24:28,774
如果我们添加physics属性，

458
00:24:28,774 --> 00:24:31,201
它将是一个对象。

459
00:24:31,527 --> 00:24:41,014
我们需要添加的第一个属性是default。

460
00:24:41,014 --> 00:24:50,502
这个字符串属性用于指定默认使用的物理系统，

461
00:24:50,502 --> 00:24:59,990
这里我们选择"arcade"。

462
00:25:00,009 --> 00:25:02,016
我们要添加的第一个属性是default（默认设置）。

463
00:25:02,016 --> 00:25:04,023
这将是一个字符串，

464
00:25:04,023 --> 00:25:06,030
用于指定我们默认使用的物理系统。

465
00:25:06,030 --> 00:25:08,038
我们选择arcade（街机模式）。

466
00:25:08,038 --> 00:25:10,045
接下来，我们提供正在使用的物理系统属性，

467
00:25:10,045 --> 00:25:12,052
这里同样是arcade。

468
00:25:12,052 --> 00:25:14,059
现在我们要为其提供配置。

469
00:25:14,059 --> 00:25:16,067
首先设置debug（调试）属性为true（开启），

470
00:25:16,067 --> 00:25:18,074
这将显示物理体的轮廓边框，

471
00:25:18,074 --> 00:25:20,081
方便我们在设计游戏时使用物理系统。

472
00:25:20,081 --> 00:25:22,088
游戏完成后记得将其设为false（关闭）。

473
00:25:22,327 --> 00:25:26,432
接下来，我们可以定义默认的重力参数，

474
00:25:26,432 --> 00:25:30,536
它将作用于X和Y属性。

475
00:25:30,536 --> 00:25:34,640
这里我们把两个值都设为零，然后保存设置。

476
00:25:34,640 --> 00:25:38,744
既然已经为游戏启用了物理系统，

477
00:25:38,744 --> 00:25:42,848
现在需要更新游戏对象来实际应用它。

478
00:25:42,848 --> 00:25:46,952
具体有几种实现方式：第一种是为现有游戏对象添加物理刚体——通过这个刚体组件我们就能检测碰撞。

479
00:25:47,396 --> 00:25:50,671
这就是物理体，我们用它来检测碰撞。

480
00:25:50,671 --> 00:25:53,946
第二种方法是我们可以更改默认对象类型。

481
00:25:53,946 --> 00:25:57,220
当我们拖入一个图像时，

482
00:25:57,220 --> 00:26:00,495
默认会是图像游戏对象。

483
00:26:00,495 --> 00:26:03,769
如果我们点击恐龙图像，

484
00:26:03,769 --> 00:26:07,044
可以将其改为街机图像。

485
00:26:07,044 --> 00:26:10,318
街机图像是图像游戏对象的扩展，

486
00:26:10,318 --> 00:26:13,593
但具有更多与物理相关的属性。

487
00:26:13,593 --> 00:26:16,867
快速看一下检查器面板，

488
00:26:17,567 --> 00:26:21,568
现在我们将游戏对象类型更新为街机图像后，

489
00:26:21,568 --> 00:26:25,570
查看面板会发现默认已带有街机物理体。

490
00:26:25,570 --> 00:26:29,572
保存后重新打开网页浏览器，刷新页面，

491
00:26:29,572 --> 00:26:33,573
就能看到恐龙周围出现了紫色方框。

492
00:26:33,573 --> 00:26:37,575
这个紫色方框代表的就是街机物理体，

493
00:26:37,575 --> 00:26:41,576
默认会采用当前图像的宽高作为碰撞体积。

494
00:26:41,903 --> 00:26:47,520
在我们将游戏对象类型更新为"街机图像"后，

495
00:26:47,520 --> 00:26:53,137
查看面板时会发现默认已附带一个街机物理体。

496
00:26:53,137 --> 00:26:58,755
保存后重新打开网页浏览器并刷新，

497
00:26:58,755 --> 00:27:04,372
现在可以看到恐龙周围出现了紫色方框。

498
00:27:04,372 --> 00:27:09,990
这个紫色方框代表的就是我们的街机物理体。

499
00:27:10,094 --> 00:27:13,116
我们将执行替换操作，点击保存后返回，

500
00:27:13,116 --> 00:27:16,138
现在我们的两个游戏对象都已启用物理系统。

501
00:27:16,138 --> 00:27:19,161
就像处理输入事件一样，

502
00:27:19,161 --> 00:27:22,183
在设置好物理碰撞体后，

503
00:27:22,183 --> 00:27:25,205
我们需要告知编辑器要检测哪些碰撞对象。

504
00:27:25,205 --> 00:27:28,228
我们可以检测与单个游戏对象的碰撞，

505
00:27:28,228 --> 00:27:31,250
也可以检测与一组游戏对象的碰撞，

506
00:27:31,250 --> 00:27:34,272
甚至还能检测与画布元素边界的碰撞。

507
00:27:34,272 --> 00:27:37,295
要实现这些，请先选择玩家控制的恐龙角色。

508
00:27:37,449 --> 00:27:41,186
接下来，我们进入街机物理引擎的碰撞面板。

509
00:27:41,186 --> 00:27:44,923
这里有许多属性会影响物理效果在物体上的应用。

510
00:27:44,923 --> 00:27:48,660
我们只需勾选"碰撞世界边界"这个选项。

511
00:27:48,660 --> 00:27:52,397
保存后回到浏览器，

512
00:27:52,397 --> 00:27:56,134
现在如果尝试将游戏对象移出画布元素外，

513
00:27:56,134 --> 00:27:59,871
就能看到物理系统会阻止它越界。

514
00:27:59,871 --> 00:28:03,608
要检测游戏对象之间的碰撞，

515
00:28:03,608 --> 00:28:07,346
我们需要使用所谓的碰撞器。

516
00:28:07,567 --> 00:28:10,941
接下来，我们进入街机物理引擎的碰撞面板。

517
00:28:10,941 --> 00:28:14,316
这里有许多属性会影响物理效果如何作用于我们的物体。

518
00:28:14,316 --> 00:28:17,691
我们只需勾选这个"碰撞世界边界"的选项。

519
00:28:17,691 --> 00:28:21,066
保存后回到浏览器，

520
00:28:21,066 --> 00:28:24,440
现在如果我们尝试将游戏对象移出画布范围，

521
00:28:24,440 --> 00:28:27,815
就会发现物理系统会阻止它越界。

522
00:28:27,815 --> 00:28:31,190
要检测游戏对象之间的碰撞，

523
00:28:31,190 --> 00:28:34,565
我们需要使用所谓的碰撞器。

524
00:28:35,194 --> 00:28:37,699
刷新页面后，

525
00:28:37,699 --> 00:28:40,204
如果我们尝试将一个游戏对象移动到另一个游戏对象上，

526
00:28:40,204 --> 00:28:42,709
会发现碰撞检测没有生效。

527
00:28:42,709 --> 00:28:45,214
这是因为要让碰撞系统正常工作，

528
00:28:45,214 --> 00:28:47,719
我们必须使用物理系统来移动游戏对象。

529
00:28:47,719 --> 00:28:50,224
目前在我们的level.js文件中，

530
00:28:50,224 --> 00:28:52,729
我们只是手动设置x和y坐标值，

531
00:28:52,729 --> 00:28:55,234
通过增减这些数值来移动对象，

532
00:28:55,234 --> 00:28:57,739
而没有实际检测是否发生了碰撞。

533
00:28:58,028 --> 00:29:01,276
让我们回顾一下，

534
00:29:01,276 --> 00:29:04,525
如果尝试将游戏对象移动到另一个游戏对象上，

535
00:29:04,525 --> 00:29:07,774
会发现碰撞检测失效了。

536
00:29:07,774 --> 00:29:11,023
这是因为要让碰撞系统正常工作，

537
00:29:11,023 --> 00:29:14,272
我们必须使用物理系统来移动游戏对象。

538
00:29:14,272 --> 00:29:17,521
目前在我们的level.js文件中，

539
00:29:17,521 --> 00:29:20,770
我们直接手动设置了x和y值，

540
00:29:20,770 --> 00:29:24,019
并在没有检查实际碰撞的情况下进行增减操作。

541
00:29:24,343 --> 00:29:27,083
根据我们当前操作的属性，选择X轴或Y轴。

542
00:29:27,083 --> 00:29:29,824
在向上按键时，由于涉及Y轴，

543
00:29:29,824 --> 00:29:32,564
我们将设置velocityY。

544
00:29:32,564 --> 00:29:35,305
现在可以设定我们想要的移动速度值，

545
00:29:35,305 --> 00:29:38,046
这里设为-200。

546
00:29:38,046 --> 00:29:40,786
就像之前处理Y轴那样，

547
00:29:40,786 --> 00:29:43,527
向上移动时设为负值，向下则设为正值。

548
00:29:43,527 --> 00:29:46,267
复制这段代码，粘贴后改为+200。

549
00:29:46,267 --> 00:29:49,008
在设置Y值前，先注释掉这里的代码。

550
00:29:49,008 --> 00:29:51,749
保存修改。

551
00:29:52,432 --> 00:29:54,951
如果我们刷新场景，来到恐龙下方，

552
00:29:54,951 --> 00:29:57,470
现在尝试向上移动，

553
00:29:57,470 --> 00:29:59,990
就会看到我们正在推动另一个游戏对象。

554
00:30:00,009 --> 00:30:03,330
当我们按下上下键一次时，

555
00:30:03,330 --> 00:30:06,652
会看到物体持续朝该方向移动，

556
00:30:06,652 --> 00:30:09,974
因为我们设置了速度值。

557
00:30:09,974 --> 00:30:13,296
实际上物理系统会多次调用更新方法。

558
00:30:13,296 --> 00:30:16,617
一旦为游戏对象设置了速度，

559
00:30:16,617 --> 00:30:19,939
除非我们修改物理参数，

560
00:30:19,939 --> 00:30:23,261
否则速度将保持不变，物体会持续来回移动。

561
00:30:23,261 --> 00:30:26,583
因此我们需要告诉Phaser框架：当按键未被按住时，

562
00:30:26,583 --> 00:30:29,904
就不应该更新这个速度值。

563
00:30:30,587 --> 00:30:33,230
为了解决这个问题，

564
00:30:33,230 --> 00:30:35,872
我们只需在这个条件判断后添加一个else代码块。

565
00:30:35,872 --> 00:30:38,513
当上下方向键都没有被按下时，

566
00:30:38,513 --> 00:30:41,155
我们将速度重置为零。

567
00:30:41,155 --> 00:30:43,798
现在刷新后，

568
00:30:43,798 --> 00:30:46,440
松开按键时游戏对象就会停止移动。

569
00:30:46,440 --> 00:30:49,082
这样我们就能在碰撞时推动另一个游戏对象了。

570
00:30:49,082 --> 00:30:51,724
接下来让我们对x轴属性做同样的修改。

571
00:30:51,724 --> 00:30:54,365
这里复制这段代码，先注释掉原有部分，

572
00:30:54,365 --> 00:30:57,008
粘贴新代码，

573
00:30:57,008 --> 00:30:59,650
然后使用setVelocityX方法。

574
00:31:00,280 --> 00:31:03,506
我们复制这段代码，粘贴过来。

575
00:31:03,506 --> 00:31:06,731
这里要改成正值。

576
00:31:06,731 --> 00:31:09,957
最后添加else语句块，用来重置数值。

577
00:31:09,957 --> 00:31:13,182
把这里改成x轴，然后保存。

578
00:31:13,182 --> 00:31:16,407
趁现在，

579
00:31:16,407 --> 00:31:19,633
我们可以考虑把重复出现的数值提取出来，

580
00:31:19,633 --> 00:31:22,858
存储在文件的一个属性或变量中会更合理。

581
00:31:22,858 --> 00:31:26,084
这样只需在一处修改，而不用多处改动。

582
00:31:26,781 --> 00:31:30,497
我们复制这段代码。粘贴过来。

583
00:31:30,497 --> 00:31:34,213
然后将其设为正值。

584
00:31:34,213 --> 00:31:37,929
最后添加else代码块。接着重置它。

585
00:31:37,929 --> 00:31:41,645
把这个改成x。保存一下。

586
00:31:41,645 --> 00:31:45,361
既然在这里，

587
00:31:45,361 --> 00:31:49,077
我们可以考虑这个值被重复使用了多次。

588
00:31:49,077 --> 00:31:52,793
把它存储在文件的一个属性或变量中会更合理。

589
00:31:52,793 --> 00:31:56,509
这样只需在一个地方修改，不必多处改动。

590
00:31:56,713 --> 00:31:59,131
好的，我们来保存并测试修改效果。

591
00:31:59,131 --> 00:32:01,549
我回到浏览器刷新页面，

592
00:32:01,549 --> 00:32:03,967
现在依然可以像之前那样移动游戏对象，

593
00:32:03,967 --> 00:32:06,384
但这次我们是通过物理系统实现的。

594
00:32:06,384 --> 00:32:08,802
这样一来，我们就能进行碰撞检测，

595
00:32:08,802 --> 00:32:11,220
并与其他游戏对象互动了。

596
00:32:11,645 --> 00:32:14,564
好的，

597
00:32:14,564 --> 00:32:17,482
现在需要注意一点：当我们碰到另一个游戏对象时，

598
00:32:17,482 --> 00:32:20,400
实际上是我们这个物理游戏对象的碰撞力作用在了那个对象上。

599
00:32:20,400 --> 00:32:23,318
由于没有任何阻力使其停下或减速，

600
00:32:23,318 --> 00:32:26,236
那个游戏对象也会持续移动。

601
00:32:26,236 --> 00:32:29,154
而我们从未更新过那个游戏对象，

602
00:32:29,154 --> 00:32:32,072
所以它不会受到场景边界限制，

603
00:32:32,072 --> 00:32:34,990
这就是为什么它能离开场景而我们的游戏对象不能。

604
00:32:34,990 --> 00:32:37,908
如果我们想让那个游戏对象真正移动起来，

605
00:32:37,908 --> 00:32:40,826
可以在编辑器里选择Dino 2进行设置。

606
00:32:41,252 --> 00:32:43,519
好的，

607
00:32:43,519 --> 00:32:45,785
这里需要注意一点：当我们碰到另一个游戏对象时，

608
00:32:45,785 --> 00:32:48,051
实际上是我们这个物理游戏对象在碰撞时对那个对象施加了力。

609
00:32:48,051 --> 00:32:50,317
由于没有任何阻力，

610
00:32:50,317 --> 00:32:52,583
那个游戏对象也会持续移动。

611
00:32:52,583 --> 00:32:54,850
而我们从未更新过那个游戏对象，

612
00:32:54,850 --> 00:32:57,116
所以它不会离开场景，

613
00:32:57,116 --> 00:32:59,382
这就是为什么它能离开而我们的游戏对象不能。

614
00:32:59,382 --> 00:33:01,648
如果我们想让那个游戏对象真正移动起来，

615
00:33:01,648 --> 00:33:03,914
可以在编辑器中选择Dino 2，

616
00:33:07,671 --> 00:33:10,994
除了用物理系统检测游戏对象间的碰撞外，

617
00:33:10,994 --> 00:33:14,318
我们还能用它来检测重叠。

618
00:33:14,318 --> 00:33:17,642
重叠功能允许游戏对象穿过其他对象，

619
00:33:17,642 --> 00:33:20,966
同时仍能通过物理系统获知何时发生了重叠。

620
00:33:20,966 --> 00:33:24,289
这非常适合游戏中的拾取物机制，

621
00:33:24,289 --> 00:33:27,613
让玩家能触碰并拾取物品。

622
00:33:27,613 --> 00:33:30,937
当我们需要设置事件触发区域，

623
00:33:30,937 --> 00:33:34,261
或是让玩家通过特定区域激活游戏事件时，

624
00:33:34,261 --> 00:33:37,585
重叠检测就是理想选择。

625
00:33:37,788 --> 00:33:40,753
在我们的游戏中，

626
00:33:40,753 --> 00:33:43,719
假设有一只饥饿的恐龙想要吃食物。

627
00:33:43,719 --> 00:33:46,683
我们需要实现拾取功能。

628
00:33:46,683 --> 00:33:49,648
为此，让我们回到场景编辑器，

629
00:33:49,648 --> 00:33:52,614
添加一个新的街机图像游戏对象。

630
00:33:52,614 --> 00:33:55,578
拖动街机图像块到场景中，选择食物素材。

631
00:33:55,578 --> 00:33:58,544
选中图像后——哦，图像有点大，

632
00:33:58,544 --> 00:34:01,509
我们把它缩小到0.4倍。

633
00:34:02,635 --> 00:34:06,266
现在我们的食物道具已经缩小了一些，

634
00:34:06,266 --> 00:34:09,897
接下来要为玩家和需要重叠的对象添加碰撞器。

635
00:34:09,897 --> 00:34:13,527
从Arcade模块中，

636
00:34:13,527 --> 00:34:17,158
我们再往场景里添加一个碰撞器。

637
00:34:17,158 --> 00:34:20,789
在这个碰撞器上，选择我们的恐龙角色。

638
00:34:20,789 --> 00:34:24,419
然后第二个对象选择我们的Arcade图片道具。

639
00:34:24,419 --> 00:34:28,050
现在碰撞器这里还有个重叠选项可用。

640
00:34:28,050 --> 00:34:31,681
如果我们点击这个并保存，回到浏览器界面，

641
00:34:32,072 --> 00:34:34,863
现在发生的情况是，

642
00:34:34,863 --> 00:34:37,655
尽管这个食物对象具有物理属性，

643
00:34:37,655 --> 00:34:40,447
当我们与之碰撞时，

644
00:34:40,447 --> 00:34:43,239
实际上并不会推动或移动它，

645
00:34:43,239 --> 00:34:46,030
而只是与其重叠。

646
00:34:46,030 --> 00:34:48,822
为了处理这种情况，

647
00:34:48,822 --> 00:34:51,614
我们需要监听玩家与游戏对象碰撞时的回调函数。

648
00:34:51,614 --> 00:34:54,406
在这里，我们可以提供想要调用的回调函数。

649
00:34:54,406 --> 00:34:57,198
那么对于这个回调函数，

650
00:34:57,198 --> 00:34:59,989
我们现在可以在我们的

651
00:35:00,009 --> 00:35:04,275
现在的情况是：虽然这个食物对象具有物理实体，

652
00:35:04,275 --> 00:35:08,541
但当我们与之碰撞时，

653
00:35:08,541 --> 00:35:12,807
实际上并没有移动或推动它，

654
00:35:12,807 --> 00:35:17,072
而是与之发生了重叠。

655
00:35:17,072 --> 00:35:21,338
为了处理这种情况，

656
00:35:21,338 --> 00:35:25,605
我们需要监听玩家与游戏对象碰撞时的回调函数。

657
00:35:25,605 --> 00:35:29,871
这里我们可以提供一个想要调用的回调函数。

658
00:35:30,195 --> 00:35:33,009
我们想要调用的关卡类方法。

659
00:35:33,009 --> 00:35:35,823
为此，我们需要引用当前场景的上下文，

660
00:35:35,823 --> 00:35:38,637
然后调用eatFruit方法。

661
00:35:38,637 --> 00:35:41,451
保存后，我们需要在类中添加这个方法。

662
00:35:41,451 --> 00:35:44,265
现在进入level.js文件，

663
00:35:44,265 --> 00:35:47,079
在update方法下方添加一个新方法，

664
00:35:47,079 --> 00:35:49,893
命名为eatFruit。

665
00:35:49,893 --> 00:35:52,706
好的，在这个方法内部，

666
00:35:52,706 --> 00:35:55,521
我们先简单写个console.log打印"hit"。

667
00:35:55,521 --> 00:35:58,335
这样做的目的是为了观察碰撞效果。

668
00:35:58,831 --> 00:36:02,844
既然我们已经确认碰撞检测生效了，

669
00:36:02,844 --> 00:36:06,857
接下来就需要清理食物游戏对象，

670
00:36:06,857 --> 00:36:10,871
将其从场景中移除。

671
00:36:10,871 --> 00:36:14,884
为此，在碰撞回调函数中，

672
00:36:14,884 --> 00:36:18,898
我们会接收到两个关键参数——它们正是发生重叠碰撞的两个游戏对象实例。

673
00:36:19,224 --> 00:36:25,008
现在既然看到这个功能已经生效，

674
00:36:25,008 --> 00:36:30,793
我们就要清理食物游戏对象并将其从场景中移除。

675
00:36:30,793 --> 00:36:36,578
具体操作是：在碰撞器中调用碰撞回调函数时，

676
00:36:36,578 --> 00:36:42,363
会接收到两个参数，

677
00:36:42,363 --> 00:36:48,148
分别是发生重叠碰撞的两个游戏对象。

678
00:36:48,643 --> 00:36:50,692
根据我们在这里添加这些对象的顺序，

679
00:36:50,692 --> 00:36:52,741
这将决定参数的排列顺序。

680
00:36:52,741 --> 00:36:54,790
由于第一个对象是恐龙，

681
00:36:54,790 --> 00:36:56,839
第一个参数将引用我们的恐龙图像游戏对象，

682
00:36:56,839 --> 00:36:58,888
第二个参数则引用食物对象。

683
00:36:58,888 --> 00:37:00,938
现在来到level.js文件，

684
00:37:00,938 --> 00:37:02,987
让我们为方法添加这两个参数：先添加dino，

685
00:37:02,987 --> 00:37:05,036
再添加food。

686
00:37:05,036 --> 00:37:07,085
好的，在console.log下方，

687
00:37:07,085 --> 00:37:09,134
我们来引用food变量。

688
00:37:09,134 --> 00:37:11,183
对于这个食物游戏对象，

689
00:37:11,183 --> 00:37:13,233
我们需要禁用其物理体。

690
00:37:13,695 --> 00:37:16,082
如果我们执行food.destroy，

691
00:37:16,082 --> 00:37:18,469
这将通知Phaser销毁该游戏对象并清理它。

692
00:37:18,469 --> 00:37:20,856
好的，如果我们在浏览器中刷新，

693
00:37:20,856 --> 00:37:23,244
当与食物物品重叠时，

694
00:37:23,244 --> 00:37:25,631
我们会看到它从场景中移除了。

695
00:37:25,631 --> 00:37:28,018
好的，所以在我们的游戏中，

696
00:37:28,018 --> 00:37:30,406
吃完一个食物后，恐龙仍然饥饿。

697
00:37:30,406 --> 00:37:32,793
那么让我们在游戏中再添加一个食物拾取物。

698
00:37:32,793 --> 00:37:35,180
从我们的积木中，选择一个街机图像。

699
00:37:35,180 --> 00:37:37,568
我们将选择我们的食物物品。

700
00:37:37,960 --> 00:38:06,766
我们能看到食物现在恢复了原始大小
现在需要手动调整尺寸来匹配另一个食物
随着在场景中添加更多游戏选项实例
我们将不得不重复这种逐个修改设置的模式
但假设我们做完这些后
突然又想更改某个食物的其他设置
比如说想给它添加一个帐篷效果来改变颜色
举个例子 假设我想这样做 呃

701
00:38:07,244 --> 00:38:09,981
哦，我们看到食物道具现在恢复了原始尺寸。

702
00:38:09,981 --> 00:38:12,719
现在需要手动调整它的大小以匹配另一个食物道具。

703
00:38:12,719 --> 00:38:15,456
随着我们在场景中添加更多游戏选项实例，

704
00:38:15,456 --> 00:38:18,194
我们将不得不重复这种逐个修改设置的繁琐操作。

705
00:38:18,194 --> 00:38:20,931
假设我们完成调整后，

706
00:38:20,931 --> 00:38:23,669
突然又想修改某个食物道具的其他属性——比如给它添加帐篷并改变颜色。

707
00:38:23,669 --> 00:38:26,407
举个例子，我打算这样做...

708
00:38:26,596 --> 00:38:29,513
这里的蓝色部分。

709
00:38:29,513 --> 00:38:32,431
如果我复制这个颜色并应用到所有帐篷上，

710
00:38:32,431 --> 00:38:35,349
现在我们就有了独特的物品，

711
00:38:35,349 --> 00:38:38,267
但还需要把这个颜色应用到其他游戏对象上。

712
00:38:38,267 --> 00:38:41,185
为了避免重复操作，

713
00:38:41,185 --> 00:38:44,103
我们可以使用Phaser编辑器中的预制体(prefab)功能。

714
00:38:44,103 --> 00:38:47,021
预制体可以让我们创建一个游戏对象所有属性设置的快照，

715
00:38:47,021 --> 00:38:49,939
然后基于这个预制体创建多个实例。

716
00:38:49,939 --> 00:38:52,857
这样当我们修改预制体时，

717
00:38:52,857 --> 00:38:55,775
所有相关游戏对象都会同步更新。

718
00:38:56,237 --> 00:39:03,643
这是我们为某个游戏对象设置的所有属性的快照。

719
00:39:03,643 --> 00:39:11,049
然后我们可以基于这个预设体创建实例。

720
00:39:11,049 --> 00:39:18,455
之后任何时候我们对预设体做出修改，

721
00:39:18,455 --> 00:39:25,862
都会同步到所有相关游戏对象上。

722
00:39:26,014 --> 00:39:28,751
这将创建一个继承自该游戏对象类型的类。

723
00:39:28,751 --> 00:39:31,488
通过这种方式，

724
00:39:31,488 --> 00:39:34,225
我们可以直接修改该游戏对象。

725
00:39:34,225 --> 00:39:36,963
现在我们可以将这些更改应用到场景中。

726
00:39:36,963 --> 00:39:39,700
举个例子，

727
00:39:39,700 --> 00:39:42,437
假设我们不喜欢食物上的帐篷图案。

728
00:39:42,437 --> 00:39:45,175
让我们把它重置回白色。

729
00:39:45,175 --> 00:39:47,912
如果我们复制这个设置，应用到数值上，

730
00:39:47,912 --> 00:39:50,649
保存后食物就会变回紫色。

731
00:39:50,649 --> 00:39:53,387
这时我们切换到关卡场景...

732
00:39:53,608 --> 00:39:54,246
这只是一个继承该游戏对象类型的类。

733
00:39:54,246 --> 00:39:54,884
通过这种方式，

734
00:39:54,884 --> 00:39:55,522
我们可以直接修改那个游戏对象。

735
00:39:55,522 --> 00:39:56,160
现在就能将这些改动应用到场景中。

736
00:39:56,160 --> 00:39:56,798
举个例子，

737
00:39:56,798 --> 00:39:57,437
假设我们不喜欢食物上的那个色调，

738
00:39:57,437 --> 00:39:58,075
可以把它重置回白色。

739
00:39:58,075 --> 00:39:58,713
如果我们复制这个值并应用到参数中，

740
00:39:58,713 --> 00:39:59,351
保存后食物就会变回紫色，

741
00:39:59,351 --> 00:39:59,989
这时切换到关卡场景——

742
00:40:00,009 --> 00:40:02,358
我们看到食物道具变成了紫色。

743
00:40:02,358 --> 00:40:04,707
这时预制件的优势就显现出来了——我们在此处所做的任何修改，

744
00:40:04,707 --> 00:40:07,056
都能应用到所有场景中的实例上。

745
00:40:07,056 --> 00:40:09,405
现在如果需要多个食物拾取物，

746
00:40:09,405 --> 00:40:11,754
不必再拖拽街机图像，

747
00:40:11,754 --> 00:40:14,104
直接使用这个新的预制件方块即可。

748
00:40:14,104 --> 00:40:16,453
拖入食物图像后，

749
00:40:16,453 --> 00:40:18,802
可以看到它与其他游戏对象实例尺寸一致，

750
00:40:18,802 --> 00:40:21,151
且继承了所有相同的属性设置。

751
00:40:21,151 --> 00:40:23,500
现在我们可以向场景中拖入多个实例。

752
00:40:23,500 --> 00:40:25,849
若想统一修改所有实例，

753
00:40:25,849 --> 00:40:28,199
只需调整食物预制件即可。

754
00:40:28,438 --> 00:40:31,812
假设我们希望食物尺寸更大些。

755
00:40:31,812 --> 00:40:35,186
将缩放比例设为1.2, 1.2。

756
00:40:35,186 --> 00:40:38,561
保存后返回场景，

757
00:40:38,561 --> 00:40:41,936
可以看到所有游戏对象实例都已更新。

758
00:40:41,936 --> 00:40:45,310
现在我将撤销对预制体和缩放比例的修改，

759
00:40:45,310 --> 00:40:48,685
恢复为0.4。

760
00:40:48,685 --> 00:40:52,059
预制体另一个很酷的功能是：当我们创建这些实例时，

761
00:40:52,059 --> 00:40:55,434
属性面板上会出现一个小锁图标。

762
00:40:55,657 --> 00:40:59,145
假设我们希望食物看起来更大些。

763
00:40:59,145 --> 00:41:02,634
那么我们把缩放比例设为1.2。

764
00:41:02,634 --> 00:41:06,122
保存后回到场景，

765
00:41:06,122 --> 00:41:09,611
会发现所有游戏对象实例都已同步修改。

766
00:41:09,611 --> 00:41:13,099
现在我要把预制体的缩放值复原回0.4。

767
00:41:13,099 --> 00:41:16,588
预制体还有个很酷的特性：当我们创建实例时，

768
00:41:16,588 --> 00:41:20,077
属性面板上会出现一个小锁图标。

769
00:41:20,349 --> 00:41:24,325
这意味着当我们锁定这个属性时，

770
00:41:24,325 --> 00:41:28,301
创建的类实例将使用该类的默认属性。

771
00:41:28,301 --> 00:41:32,277
解锁后，

772
00:41:32,277 --> 00:41:36,253
实例则会采用我们在图形界面中设置的数值。

773
00:41:36,253 --> 00:41:40,229
这就是为什么我们能在场景中移动这个对象，

774
00:41:40,229 --> 00:41:44,206
而其他对象保持不动——因为它们也处于解锁状态。

775
00:41:44,445 --> 00:41:47,100
这样我们就可以对实例的任何属性进行修改。

776
00:41:47,100 --> 00:41:49,756
比如我想把这个食物对象放大些，

777
00:41:49,756 --> 00:41:52,412
可以设为0.6。

778
00:41:52,412 --> 00:41:55,068
其他实例会保持原有尺寸不变。

779
00:41:55,068 --> 00:41:57,724
我们还能调整透明度或色调等属性，

780
00:41:57,724 --> 00:42:00,379
都不会影响其他实例。

781
00:42:00,379 --> 00:42:03,035
预制体功能非常强大，

782
00:42:03,035 --> 00:42:05,691
特别适合需要创建多个相同游戏对象的情况。

783
00:42:05,691 --> 00:42:08,347
好了，

784
00:42:08,347 --> 00:42:11,003
现在我们已经往场景里添加了多个预制体。

785
00:42:11,003 --> 00:42:13,659
如果回到浏览器测试一下，

786
00:42:14,085 --> 00:42:18,500
现在的情况应该是：我们可以吃掉最初的那个食物，

787
00:42:18,500 --> 00:42:22,914
但新增的那些食物却无法拾取。

788
00:42:22,914 --> 00:42:27,328
原因在于我们的碰撞检测目前只针对最初添加的那个食物游戏对象。

789
00:42:27,328 --> 00:42:31,743
我们需要做的是检测玩家与所有食物游戏对象之间的碰撞。

790
00:42:31,743 --> 00:42:36,157
因此，必须传入一个游戏对象组或数组，

791
00:42:36,157 --> 00:42:40,572
用于检查与哪些对象可能发生碰撞。

792
00:42:40,811 --> 00:42:43,513
为了传递一组需要检测碰撞的游戏对象，

793
00:42:43,513 --> 00:42:46,215
我们可以使用另一个内置功能块。

794
00:42:46,215 --> 00:42:48,918
在"内置块"菜单下，进入"组"选项，

795
00:42:48,918 --> 00:42:51,620
向游戏场景添加一个列表。

796
00:42:51,620 --> 00:42:54,323
这将新增一个名为"列表"的板块。

797
00:42:54,323 --> 00:42:57,025
列表功能可以创建数组结构，

798
00:42:57,025 --> 00:42:59,728
你可以在数组中添加任意需要的游戏对象，

799
00:42:59,728 --> 00:43:02,430
这些对象就能在我们的代码中使用了。

800
00:43:02,430 --> 00:43:05,133
我将这个变量命名为"food"然后保存。

801
00:43:05,133 --> 00:43:07,835
现在跳转到level.js文件，

802
00:43:07,835 --> 00:43:10,538
就能看到类中新增了一个名为food的属性。

803
00:43:10,896 --> 00:43:13,848
因此，

804
00:43:13,848 --> 00:43:16,800
在列表下方我们可以看到food数组目前是空的。

805
00:43:16,800 --> 00:43:19,752
现在要向列表中添加对象，

806
00:43:19,752 --> 00:43:22,704
我们可以先选中一个游戏对象，

807
00:43:22,704 --> 00:43:25,656
然后从属性下拉菜单中选择"list"选项。

808
00:43:25,656 --> 00:43:28,608
选择"food"后保存，

809
00:43:28,608 --> 00:43:31,560
再回到level.js文件，

810
00:43:31,560 --> 00:43:34,512
就能看到我们的食物预设项已被添加到food数组中，

811
00:43:34,512 --> 00:43:37,465
后续可以在代码中引用这些对象。

812
00:43:37,875 --> 00:43:40,990
在检视面板中，我们选择列表功能，

813
00:43:40,990 --> 00:43:44,105
现在可以选择要添加对象的食物列表。

814
00:43:44,105 --> 00:43:47,221
你也可以同时选择多个游戏对象进行添加。

815
00:43:47,221 --> 00:43:50,336
保存后回到碰撞器设置，

816
00:43:50,336 --> 00:43:53,451
这次为对象2选择食物列表而非特定食物实例。

817
00:43:53,451 --> 00:43:56,567
点击选择后保存，返回浏览器刷新页面。

818
00:43:56,567 --> 00:43:59,682
现在当我们拾取食物对象时，

819
00:43:59,682 --> 00:44:02,797
可以看到每个都能被拾取，

820
00:44:02,797 --> 00:44:05,913
回调方法也被成功触发。

821
00:44:09,188 --> 00:44:11,997
接下来我们要为游戏实现计分功能。

822
00:44:11,997 --> 00:44:14,806
当饥饿的恐龙拾取并吃掉食物时，

823
00:44:14,806 --> 00:44:17,614
我们会增加分数，并将分数显示给玩家看。

824
00:44:17,614 --> 00:44:20,423
要实现这个功能，

825
00:44:20,423 --> 00:44:23,233
我们需要在场景中添加一个新的文本游戏对象。

826
00:44:23,233 --> 00:44:26,041
在内置类型的字符串选项下，

827
00:44:26,041 --> 00:44:28,850
我们添加文本组件。

828
00:44:28,850 --> 00:44:31,659
将这个文本游戏对象的变量命名为"score"。

829
00:44:31,659 --> 00:44:34,469
然后将其定位在屏幕左上角，

830
00:44:34,469 --> 00:44:37,277
并更新文本内容为"分数"。

831
00:44:37,670 --> 00:44:48,829
然后我们将初始值设为0 把字体大小调整为48像素 并在游戏边缘留出一些间距 位置方面，

832
00:44:48,829 --> 00:44:59,989
我们向右移动10像素 再向下移动10像素 保存后返回浏览器刷新 就能看到分数显示对玩家可见了 现在当我们拾取食物对象时 需要实时更新这个分数显示

833
00:45:00,009 --> 00:45:02,534
更新我们这里的文本字符串。

834
00:45:02,534 --> 00:45:05,059
现在转到level.js文件，

835
00:45:05,059 --> 00:45:07,585
首先添加一个新变量来记录玩家分数。

836
00:45:07,585 --> 00:45:10,110
在自定义代码区域（之前定义玩家速度的地方），

837
00:45:10,110 --> 00:45:12,636
我们添加score变量，

838
00:45:12,636 --> 00:45:15,161
并设置默认值为0。

839
00:45:15,161 --> 00:45:17,687
然后在eat fruit方法中，

840
00:45:17,687 --> 00:45:20,212
删除console.log语句，

841
00:45:20,212 --> 00:45:22,738
在销毁游戏对象后增加分数。

842
00:45:22,738 --> 00:45:25,263
具体操作是：引用score变量，

843
00:45:25,263 --> 00:45:27,789
然后执行加1操作（score += 1）。

844
00:45:28,523 --> 00:45:33,680
更新字符串中的文本显示。

845
00:45:33,680 --> 00:45:38,837
现在我们来到level.js文件，

846
00:45:38,837 --> 00:45:43,994
首先添加一个新变量来记录玩家分数。

847
00:45:43,994 --> 00:45:49,151
在自定义代码区域（之前定义玩家速度的地方），

848
00:45:49,151 --> 00:45:54,309
我们添加score变量并设置默认值为0。

849
00:45:54,717 --> 00:45:58,983
在我们更新分数变量后，

850
00:45:58,983 --> 00:46:03,249
实际上需要更新分数文本。

851
00:46:03,249 --> 00:46:07,515
对于分数文本，

852
00:46:07,515 --> 00:46:11,782
我们需要引用已创建的分数游戏对象。

853
00:46:11,782 --> 00:46:16,048
当前我们的分数游戏对象作用域设置为局部，

854
00:46:16,048 --> 00:46:20,314
现在让我们将其改为类作用域。

855
00:46:20,314 --> 00:46:24,581
这样我们就能为类添加一个可供引用的属性。

856
00:46:24,974 --> 00:46:28,073
现在我们要把分数值加进去。

857
00:46:28,073 --> 00:46:31,172
所以我们写上this.score。

858
00:46:31,172 --> 00:46:34,272
好的，保存后回到浏览器。

859
00:46:34,272 --> 00:46:37,371
现在当我们拾取食物时，哦，

860
00:46:37,371 --> 00:46:40,470
出现了一个错误。

861
00:46:40,470 --> 00:46:43,570
提示说：无法读取未定义的set text属性？

862
00:46:43,570 --> 00:46:46,669
问题在于，当我们在方法内部时，

863
00:46:46,669 --> 00:46:49,768
当前试图引用的this指向的是level场景的作用域。

864
00:46:50,195 --> 00:46:53,971
现在我们只需要添加分数值。

865
00:46:53,971 --> 00:46:57,747
我们使用this.score来操作。

866
00:46:57,747 --> 00:47:01,524
好的，保存后回到浏览器。

867
00:47:01,524 --> 00:47:05,300
当我们拾取食物时，哎呀，出现了一个错误。

868
00:47:05,300 --> 00:47:09,077
提示说：无法读取未定义的set text属性？

869
00:47:09,077 --> 00:47:12,853
问题在于，在这个方法内部，

870
00:47:12,853 --> 00:47:16,630
我们试图引用level scene的this作用域。

871
00:47:17,159 --> 00:47:20,840
因此我们尝试在这个类中递增分数和更新分数文本属性。

872
00:47:20,840 --> 00:47:24,522
但在关卡场景中，如果查看碰撞器，

873
00:47:24,522 --> 00:47:28,203
当我们在此处调用回调时，

874
00:47:28,203 --> 00:47:31,885
默认会丢失调用来源的上下文。

875
00:47:31,885 --> 00:47:35,567
举个例子，

876
00:47:35,567 --> 00:47:39,248
在执行disable body前，

877
00:47:39,248 --> 00:47:42,930
我先用console.log输出这个值。

878
00:47:42,930 --> 00:47:46,612
回到浏览器，让我们试着拾取一个食物道具。

879
00:47:46,971 --> 00:47:51,234
现在我们应该能够拾取场景中的所有游戏对象了。

880
00:47:51,234 --> 00:47:55,498
既然已经为游戏添加了计分机制，

881
00:47:55,498 --> 00:47:59,762
我想花点时间谈谈场景分层和渲染顺序的问题。

882
00:47:59,762 --> 00:48:04,025
当我们移动场景中的游戏对象时，

883
00:48:04,025 --> 00:48:08,289
可能已经注意到恐龙游戏对象会显示在两个UI元素的文字后面。

884
00:48:08,289 --> 00:48:12,553
这是因为默认情况下，

885
00:48:12,553 --> 00:48:16,817
Phaser会按照我们将游戏对象添加到场景中的顺序进行渲染。

886
00:48:17,159 --> 00:48:21,061
现在我们应该能够选中场景中的所有游戏对象了。

887
00:48:21,061 --> 00:48:24,965
既然已经为游戏添加了计分机制，

888
00:48:24,965 --> 00:48:28,867
我想花点时间谈谈场景分层和渲染顺序的问题。

889
00:48:28,867 --> 00:48:32,771
当我们移动场景中的游戏对象时，

890
00:48:32,771 --> 00:48:36,673
可能已经注意到恐龙游戏对象会显示在两个UI元素的文字后面。

891
00:48:36,673 --> 00:48:40,577
这是因为默认情况下，

892
00:48:40,577 --> 00:48:44,480
Phaser会按照我们向场景添加对象的顺序进行渲染。

893
00:48:44,786 --> 00:48:48,696
使用Phaser编辑器时，

894
00:48:48,696 --> 00:48:52,606
这取决于游戏对象在层级结构中的添加顺序。

895
00:48:52,606 --> 00:48:56,517
由于分数文本和欢迎文本都位于恐龙对象上方，

896
00:48:56,517 --> 00:49:00,427
它们会渲染在玩家游戏对象之上。

897
00:49:00,427 --> 00:49:04,338
若要调整显示顺序，

898
00:49:04,338 --> 00:49:08,248
我们需要改变层级结构中这些对象的排列。

899
00:49:08,248 --> 00:49:12,159
例如，如果我选中恐龙对象，

900
00:49:12,704 --> 00:49:15,422
因此还有其他方法可以控制渲染顺序。

901
00:49:15,422 --> 00:49:18,141
其中一种方式是通过游戏对象的深度值来实现。

902
00:49:18,141 --> 00:49:20,859
在Phaser编辑器中，

903
00:49:20,859 --> 00:49:23,577
更简便的管理方法是使用"图层"功能。

904
00:49:23,577 --> 00:49:26,296
图层可以将我们的游戏对象分组管理，

905
00:49:26,296 --> 00:49:29,015
然后通过调整图层的上下顺序，

906
00:49:29,015 --> 00:49:31,733
就能控制它们在游戏中的显示层级。

907
00:49:31,733 --> 00:49:34,452
让我们实际操作一下：在"内置模块"下的"分组"选项中，

908
00:49:34,452 --> 00:49:37,170
拖入一个"图层"组件，

909
00:49:37,170 --> 00:49:39,889
这将在场景中添加一个新的图层。

910
00:49:40,195 --> 00:49:43,022
因此，我们可以将这个图层命名为"UI"。

911
00:49:43,022 --> 00:49:45,850
对于图层管理，

912
00:49:45,850 --> 00:49:48,678
我们需要将游戏对象实际添加至该图层中。

913
00:49:48,678 --> 00:49:51,506
默认情况下，

914
00:49:51,506 --> 00:49:54,334
所有游戏对象都会有一个父级容器，

915
00:49:54,334 --> 00:49:57,162
这个父级默认就是场景本身。

916
00:49:57,162 --> 00:49:59,989
我们可以在对象属性中

917
00:50:00,333 --> 00:50:03,251
如果我选中这里的欢迎文本，

918
00:50:03,251 --> 00:50:06,169
右键点击后可以选择"父级"，

919
00:50:06,169 --> 00:50:09,087
然后执行"移动到父级"操作。

920
00:50:09,087 --> 00:50:12,005
这样就能为游戏对象选择一个新的父级对象。

921
00:50:12,005 --> 00:50:14,923
这里我们将选择UI图层作为父级。

922
00:50:14,923 --> 00:50:17,841
在层级结构中，

923
00:50:17,841 --> 00:50:20,759
这会将游戏对象嵌套在该图层下方，

924
00:50:20,759 --> 00:50:23,677
表明该游戏对象的父级就是这里的这个游戏对象。

925
00:50:23,677 --> 00:50:26,595
我们可以对分数文本进行同样的操作：选择"父级"→"移动到父级"，

926
00:50:26,595 --> 00:50:29,514
然后指定UI图层即可。

927
00:50:29,786 --> 00:50:32,188
现在由于层级关系，

928
00:50:32,188 --> 00:50:34,591
我们的UI层位于其他游戏对象之上。

929
00:50:34,591 --> 00:50:36,994
这两个游戏对象将始终显示在其他对象前面。

930
00:50:36,994 --> 00:50:39,396
我们可以将剩余的游戏对象添加到另一个图层，

931
00:50:39,396 --> 00:50:41,799
这样就能轻松进行排序和移动。

932
00:50:41,799 --> 00:50:44,202
但如果我们回到浏览器视图，

933
00:50:44,202 --> 00:50:46,604
现在将游戏对象移动到文字上方时，

934
00:50:46,604 --> 00:50:49,007
可以看到它显示在文字后面。

935
00:50:49,007 --> 00:50:51,410
对于UI界面来说，

936
00:50:51,410 --> 00:50:53,813
游戏对象就显示在其下方了。

937
00:50:56,851 --> 00:50:59,442
在我们的游戏中，

938
00:50:59,442 --> 00:51:02,034
饥饿的恐龙除了水果还想吃点别的。

939
00:51:02,034 --> 00:51:04,626
为此，我们需要向项目添加新素材。

940
00:51:04,626 --> 00:51:07,217
要在Phaser编辑器中添加素材，

941
00:51:07,217 --> 00:51:09,809
一种方法是点击这里的素材文件夹，

942
00:51:09,809 --> 00:51:12,401
然后在检查器面板中会看到添加文件的选项。

943
00:51:12,401 --> 00:51:14,993
我们可以直接将文件拖放到这里，

944
00:51:14,993 --> 00:51:17,585
它们就会被复制到项目中。

945
00:51:17,891 --> 00:51:20,991
我们可以点击这里的按钮浏览文件。

946
00:51:20,991 --> 00:51:24,091
最后，如果右键点击文件夹，

947
00:51:24,091 --> 00:51:27,191
还能选择"添加文件"选项，

948
00:51:27,191 --> 00:51:30,291
同样会弹出添加文件的对话框。

949
00:51:30,291 --> 00:51:33,392
我决定给我的恐龙准备一个汉堡——这里有个汉堡素材是精灵表。

950
00:51:33,392 --> 00:51:36,492
视频简介里会提供这个汉堡精灵表的下载链接。

951
00:51:36,492 --> 00:51:39,592
特别感谢Cast Creates Games这位创作者，

952
00:51:39,592 --> 00:51:42,692
他制作了这个超棒的精灵表并在itch.io上免费分享，

953
00:51:42,692 --> 00:51:45,793
下方简介中也会附上链接。

954
00:51:46,748 --> 00:51:49,259
好的，

955
00:51:49,259 --> 00:51:51,770
目前我们只是往项目文件夹里添加了一个新素材。

956
00:51:51,770 --> 00:51:54,281
要想在Phaser编辑器里使用这个素材，

957
00:51:54,281 --> 00:51:56,792
并让它能在Phaser游戏中生效，

958
00:51:56,792 --> 00:51:59,303
我们需要告诉Phaser编辑器如何处理它。

959
00:51:59,303 --> 00:52:01,814
当我们加载完这个新精灵表后，

960
00:52:01,814 --> 00:52:04,326
就会在文件的检查器面板里看到新增的选项。

961
00:52:04,786 --> 00:52:07,357
好的，选中文件后，这里会出现资产包条目。

962
00:52:07,357 --> 00:52:09,929
这就是Phaser编辑器识别我们刚添加文件的方式。

963
00:52:09,929 --> 00:52:12,501
由于我们导入的是精灵表，

964
00:52:12,501 --> 00:52:15,072
需要选择这个选项。

965
00:52:15,072 --> 00:52:17,644
若只需导入普通图片，

966
00:52:17,644 --> 00:52:20,216
可选择"作为图片导入"。

967
00:52:20,216 --> 00:52:22,787
选择导入精灵表时，

968
00:52:22,787 --> 00:52:25,359
系统不会询问要添加到哪个资产包，

969
00:52:25,359 --> 00:52:27,931
我们暂时选择assetpack.json文件。

970
00:52:27,931 --> 00:52:30,503
完成操作后，编辑器就会立即更新。

971
00:52:30,639 --> 00:52:33,469
因为我们已在资源包中存储了对此的引用。

972
00:52:33,469 --> 00:52:36,298
现在如果我们选择在资源包中打开汉堡精灵表，

973
00:52:36,298 --> 00:52:39,128
它就会打开我们的资源包JSON文件。

974
00:52:39,128 --> 00:52:41,959
在这个视图中，这是我们的资源包编辑器。

975
00:52:41,959 --> 00:52:44,789
通过这个编辑器，

976
00:52:44,789 --> 00:52:47,619
我们可以看到项目所知道的所有文件

977
00:52:47,789 --> 00:52:52,028
既然我们已经将引用存储在了资源包中。

978
00:52:52,028 --> 00:52:56,267
现在如果在资源包中选择打开汉堡精灵表，

979
00:52:56,267 --> 00:53:00,507
就会打开我们的资源包JSON文件。

980
00:53:00,507 --> 00:53:04,746
在这个视图中，

981
00:53:04,746 --> 00:53:08,986
可以看到我们的资源包编辑器。

982
00:53:08,986 --> 00:53:13,225
通过这个编辑器，

983
00:53:13,225 --> 00:53:17,465
我们可以查看项目所知道的所有文件

984
00:53:17,755 --> 00:53:20,819
当前它正在加载我们的恐龙图像和刚添加的汉堡精灵表。

985
00:53:20,819 --> 00:53:23,883
我们的资源包JSON文件，

986
00:53:23,883 --> 00:53:26,947
本质上是一个JSON配置文件，

987
00:53:26,947 --> 00:53:30,011
编辑器和Phaser引擎将据此了解如何加载资源。

988
00:53:30,011 --> 00:53:33,076
例如，

989
00:53:33,076 --> 00:53:36,140
右键点击资源包JSON文件选择用文本编辑器打开，

990
00:53:36,140 --> 00:53:39,204
就能看到原始JSON配置内容。

991
00:53:39,204 --> 00:53:42,268
可以看到这里定义了两个文件：一个是汉堡精灵表及其解析参数（因为是精灵表类型），

992
00:53:42,268 --> 00:53:45,333
另一个是恐龙PNG图片（作为普通图像直接加载）。

993
00:53:46,253 --> 00:53:51,840
现在我们可以保持配置不变。

994
00:53:51,840 --> 00:53:57,427
如果精灵尺寸不同，

995
00:53:57,427 --> 00:54:03,014
就需要在这里更新帧的高度设置。

996
00:54:03,014 --> 00:54:08,601
让我们关闭资产包的JSON文件，

997
00:54:08,601 --> 00:54:14,188
回到关卡场景。

998
00:54:14,905 --> 00:54:18,993
要在游戏中使用汉堡素材，

999
00:54:18,993 --> 00:54:23,081
我们新建一个预制体实例。

1000
00:54:23,081 --> 00:54:27,169
在源文件夹上右键点击，选择新建，

1001
00:54:27,169 --> 00:54:31,258
创建一个预制体文件。

1002
00:54:31,258 --> 00:54:35,346
我们将它命名为"汉堡预制体"，点击创建。

1003
00:54:35,346 --> 00:54:39,434
这会打开默认的预制体视图界面，

1004
00:54:39,434 --> 00:54:43,523
帮我们建立预制体实例。

1005
00:54:43,847 --> 00:54:45,192
要在游戏中使用我们的汉堡，

1006
00:54:45,192 --> 00:54:46,537
让我们创建一个新的预制体实例。

1007
00:54:46,537 --> 00:54:47,882
从源文件夹右键点击，选择新建，

1008
00:54:47,882 --> 00:54:49,228
为预制体创建新的预制体文件。

1009
00:54:49,228 --> 00:54:50,573
我们将它命名为"burger prefab"，

1010
00:54:50,573 --> 00:54:51,918
点击创建。

1011
00:54:51,918 --> 00:54:53,263
这将打开默认的预制体视图，

1012
00:54:53,263 --> 00:54:54,608
设置我们的预制体实例。

1013
00:54:54,608 --> 00:54:55,954
现在需要告诉Phaser编辑器我们想使用什么作为基础。

1014
00:54:55,954 --> 00:54:57,299
根据我们拖入预制体场景的方块，

1015
00:54:57,299 --> 00:54:58,644
这将被用作根节点，

1016
00:54:58,644 --> 00:54:59,989
也就是我们预制体要继承的对象。

1017
00:55:00,009 --> 00:55:03,270
X轴设为3，Y轴也设为3。

1018
00:55:03,270 --> 00:55:06,531
然后把它移回原点(0,0)位置，

1019
00:55:06,531 --> 00:55:09,792
这样它就会出现在左上角。

1020
00:55:09,792 --> 00:55:13,053
好的，最后我们要给汉堡添加碰撞体。

1021
00:55:13,053 --> 00:55:16,315
我们需要把这个图像类型改为物理图像 - 选择Arcade Image类型，

1022
00:55:16,315 --> 00:55:19,576
点击替换。现在就有了物理实体。

1023
00:55:19,576 --> 00:55:22,837
保存后回到关卡场景，拖入一个汉堡实例，

1024
00:55:22,837 --> 00:55:26,098
替换掉这里的食物道具。

1025
00:55:26,098 --> 00:55:29,360
删除原有食物，把我们的汉堡添加到这里。

1026
00:55:30,161 --> 00:55:32,405
最后，我们只需将其添加到列表中。

1027
00:55:32,405 --> 00:55:34,649
需要把这个加入食物物品清单，

1028
00:55:34,649 --> 00:55:36,893
虽然这是个不同的预设体，

1029
00:55:36,893 --> 00:55:39,137
但它仍是Phaser游戏对象，

1030
00:55:39,137 --> 00:55:41,381
所以玩家应该能拾取它。

1031
00:55:41,381 --> 00:55:43,625
现在切换到浏览器，刷新场景，

1032
00:55:43,625 --> 00:55:45,869
就能看到新汉堡出现在场景中，

1033
00:55:45,869 --> 00:55:48,114
带有物理实体，当玩家拾取时，分数会增加。

1034
00:55:50,896 --> 00:55:53,517
除了汉堡素材外，

1035
00:55:53,517 --> 00:55:56,138
我们还要为游戏添加另一个素材——障碍方块。

1036
00:55:56,138 --> 00:55:58,759
这样玩家就有可以碰撞的障碍物了。

1037
00:55:58,759 --> 00:56:01,380
不过在添加之前，

1038
00:56:01,380 --> 00:56:04,002
我们先清理一下场景：删掉第二只恐龙，

1039
00:56:04,002 --> 00:56:06,623
移除这里的第二个文本，然后保存。

1040
00:56:06,623 --> 00:56:09,244
接着切换到level.js文件，

1041
00:56:09,244 --> 00:56:11,865
由于我们删除了一些游戏对象，

1042
00:56:11,865 --> 00:56:14,486
需要清理那些引用这些对象的手动代码，

1043
00:56:14,486 --> 00:56:17,108
比如这个dino1就可以删掉了。

1044
00:56:17,789 --> 00:56:20,247
既然我们不再需要更新这里的文本，

1045
00:56:20,247 --> 00:56:22,706
这部分也可以删除了。

1046
00:56:22,706 --> 00:56:25,165
最后，由于移除了文本，

1047
00:56:25,165 --> 00:56:27,624
我们把恐龙的点击区域也去掉，

1048
00:56:27,624 --> 00:56:30,083
这样它就不再可点击了。

1049
00:56:30,083 --> 00:56:32,542
保存后，我们来添加另一个素材。

1050
00:56:32,542 --> 00:56:35,000
进入素材文件夹，

1051
00:56:35,000 --> 00:56:37,459
第二个素材我们将使用Kenny提供的像素平台积木块中的一个方块。

1052
00:56:37,459 --> 00:56:39,918
我把这个方块拖到文件里来，

1053
00:56:39,918 --> 00:56:42,377
这里我选用的是单个方块的图片。

1054
00:56:42,377 --> 00:56:44,836
现在当检查器更新时，

1055
00:56:44,836 --> 00:56:47,295
我们要将其作为单张图片导入。

1056
00:56:47,568 --> 00:56:50,177
对于这个资源，

1057
00:56:50,177 --> 00:56:52,786
我们还需要选择对应的资源包JSON文件。

1058
00:56:52,786 --> 00:56:55,396
现在查看资源包JSON文件时，

1059
00:56:55,396 --> 00:56:58,005
应该能在图片部分看到新加载的图像。

1060
00:56:58,005 --> 00:57:00,614
让我们回到关卡场景，拖入一个瓦片实例。

1061
00:57:00,614 --> 00:57:03,224
现在我们已经把方块放进了场景，

1062
00:57:03,224 --> 00:57:05,833
它看起来很小，所以需要放大尺寸。

1063
00:57:05,833 --> 00:57:08,442
我将把X和Y属性值都调整为5倍。

1064
00:57:08,442 --> 00:57:11,052
实际上我们还需要为它添加物理实体，

1065
00:57:11,052 --> 00:57:13,661
所以进入类型设置，选择图片类型，

1066
00:57:13,661 --> 00:57:16,271
将其更改为街机图像模式，然后点击替换。

1067
00:57:16,612 --> 00:57:19,399
我们需要创建多个方块实例，

1068
00:57:19,399 --> 00:57:22,186
所以先制作一个预设体。

1069
00:57:22,186 --> 00:57:24,973
右键点击选择"创建预设体"，

1070
00:57:24,973 --> 00:57:27,760
在源文件夹下命名为"方块预设"。

1071
00:57:27,760 --> 00:57:30,548
现在有了预设体，让我们创建几个实例。

1072
00:57:30,548 --> 00:57:33,335
我只需在场景中拖拽一个，

1073
00:57:33,335 --> 00:57:36,122
再复制粘贴出另一个。

1074
00:57:36,122 --> 00:57:38,909
这里再放一个，把玩家角色移到这边。

1075
00:57:38,909 --> 00:57:41,697
最后再添加一个方块。

1076
00:57:42,260 --> 00:57:55,553
好的，

1077
00:57:55,553 --> 00:58:08,847
现在我们已经有了四个方块 需要让它们能与玩家角色发生碰撞 就像之前处理食物那样 我们新建一个列表来存放这些物体

1078
00:58:09,309 --> 00:58:13,224
好的，现在我们有四个方块了，

1079
00:58:13,224 --> 00:58:17,139
需要让它们能与玩家发生碰撞。

1080
00:58:17,139 --> 00:58:21,054
就像处理食物那样，

1081
00:58:21,054 --> 00:58:24,969
我们先新建一个列表来存放这些对象。

1082
00:58:24,969 --> 00:58:28,884
在"group block"下方添加一个新列表，

1083
00:58:28,884 --> 00:58:32,799
命名为"walls"。

1084
00:58:32,799 --> 00:58:36,715
然后把所有四个方块都添加进这个墙壁列表。

1085
00:58:37,090 --> 00:58:39,839
如果我们保存后返回场景，刷新页面，

1086
00:58:39,839 --> 00:58:42,588
现在当恐龙试图穿过方块时，就无法实现了。

1087
00:58:42,588 --> 00:58:45,338
我们的角色体型有点大，

1088
00:58:45,338 --> 00:58:48,087
让我们把恐龙调小一些。

1089
00:58:48,087 --> 00:58:50,837
点击恐龙对象，将缩放比例调整为0.75。

1090
00:58:50,837 --> 00:58:53,586
好的，保存并刷新后，

1091
00:58:53,586 --> 00:58:56,336
可以看到恐龙变小了些，

1092
00:58:56,336 --> 00:58:59,085
但所有功能仍正常运行。

1093
00:59:01,971 --> 00:59:06,783
目前我们的游戏看起来有点静态，

1094
00:59:06,783 --> 00:59:11,595
虽然有可以移动的游戏对象，

1095
00:59:11,595 --> 00:59:16,407
但缺少动画效果。

1096
00:59:16,407 --> 00:59:21,220
之前导入的汉堡素材是一个精灵图集，

1097
00:59:21,220 --> 00:59:26,032
包含多帧图像可以制作汉堡待机和移动的动画。

1098
00:59:26,032 --> 00:59:30,844
为了让游戏更有趣，我们将添加汉堡动画。

1099
00:59:31,067 --> 00:59:35,887
现在我们的游戏看起来有点静态，

1100
00:59:35,887 --> 00:59:40,708
虽然有可以移动的游戏对象，

1101
00:59:40,708 --> 00:59:45,528
但缺少动画效果。

1102
00:59:45,528 --> 00:59:50,348
当我们导入汉堡素材时，由于它是精灵图集，

1103
00:59:50,348 --> 00:59:55,169
我们有一系列帧可以用来创建汉堡待机和移动的动画。

1104
00:59:55,169 --> 00:59:59,989
为了让游戏更生动，我们将添加汉堡动画。

1105
01:00:00,009 --> 01:00:02,843
我们当前处于创建动画的选项界面。

1106
01:00:02,843 --> 01:00:05,678
点击"构建一个动画"按钮后，

1107
01:00:05,678 --> 01:00:08,513
需要为这个新动画输入名称前缀。

1108
01:00:08,513 --> 01:00:11,348
我们将它命名为"idle"。

1109
01:00:11,348 --> 01:00:14,183
操作完成后，

1110
01:00:14,183 --> 01:00:17,018
一个全新的动画就会被添加到左侧大纲中，

1111
01:00:17,018 --> 01:00:19,853
这里会显示组成动画的帧序列，

1112
01:00:19,853 --> 01:00:22,688
并且可以直接在编辑器中预览动画效果。

1113
01:00:23,148 --> 01:00:26,759
我们正在这个选项中创建动画。

1114
01:00:26,759 --> 01:00:30,370
点击"创建动画"后，

1115
01:00:30,370 --> 01:00:33,982
需要为新动画输入名称前缀。

1116
01:00:33,982 --> 01:00:37,593
这里我们命名为"idle"。

1117
01:00:37,593 --> 01:00:41,204
完成后，系统会添加一个全新的动画，

1118
01:00:41,204 --> 01:00:44,816
并显示在概要视图中。

1119
01:00:44,816 --> 01:00:48,427
这里会展示构成动画的帧序列，

1120
01:00:48,427 --> 01:00:52,039
我们可以在编辑器中预览动画效果。

1121
01:00:52,500 --> 01:00:55,379
在配置中你还可以进行其他操作。

1122
01:00:55,379 --> 01:00:58,259
创建好动画JSON文件后，

1123
01:00:58,259 --> 01:01:01,139
我们需要告知Phaser编辑器将其作为游戏资源加载。

1124
01:01:01,139 --> 01:01:04,019
选中动画JSON文件后，

1125
01:01:04,019 --> 01:01:06,898
选择"导入为动画"选项，

1126
01:01:06,898 --> 01:01:09,778
并将其添加到资源包中。

1127
01:01:09,778 --> 01:01:12,658
回到关卡场景界面，

1128
01:01:12,658 --> 01:01:15,538
要播放动画还需将游戏对象类型更新为支持动画播放的类型。

1129
01:01:16,047 --> 01:01:19,542
在Phaser中，默认情况下，

1130
01:01:19,542 --> 01:01:23,036
图像游戏对象仅用于显示单张图片或纹理。

1131
01:01:23,036 --> 01:01:26,530
而精灵游戏对象则用于播放动画效果。

1132
01:01:26,530 --> 01:01:30,024
因此我们需要为汉堡对象更新游戏对象类型。

1133
01:01:30,024 --> 01:01:33,518
进入汉堡预设文件后，在此处选择汉堡类型，

1134
01:01:33,518 --> 01:01:37,012
将变量类型从"arcade image"更改为"arcade sprite"。

1135
01:01:37,012 --> 01:01:40,506
完成修改后，

1136
01:01:40,506 --> 01:01:44,001
现在需要告知Phaser我们希望为这个精灵播放动画。

1137
01:01:44,326 --> 01:01:46,931
如果我们打开burgerprefab.js文件，

1138
01:01:46,931 --> 01:01:49,537
在构造函数中就可以添加额外代码。

1139
01:01:49,537 --> 01:01:52,143
这里我们需要告诉Phaser默认播放动画。

1140
01:01:52,143 --> 01:01:54,749
为此，

1141
01:01:54,749 --> 01:01:57,355
我们需要通过this来引用精灵游戏对象，

1142
01:01:57,355 --> 01:01:59,960
然后调用play方法。

1143
01:01:59,960 --> 01:02:02,566
接下来在play方法中，

1144
01:02:02,566 --> 01:02:05,172
我们需要提供动画配置或想要播放的动画名称/键值。

1145
01:02:05,172 --> 01:02:07,778
现在要设置动画键值，

1146
01:02:07,778 --> 01:02:10,384
如果我们打开animations.json文件，

1147
01:02:10,793 --> 01:02:14,526
这里显示的"idle burger"键名，

1148
01:02:14,526 --> 01:02:18,259
就是我们需要的值。

1149
01:02:18,259 --> 01:02:21,992
让我们复制这个闲置汉堡精灵表的键名。

1150
01:02:21,992 --> 01:02:25,725
回到预制件文件中，

1151
01:02:25,725 --> 01:02:29,458
在play方法里添加这个键名，然后保存。

1152
01:02:29,458 --> 01:02:33,191
现在回到关卡场景，切换到浏览器窗口，

1153
01:02:33,191 --> 01:02:36,923
保存并刷新后就能看到汉堡动画效果了，

1154
01:02:36,923 --> 01:02:40,657
而且游戏对象仍然可以拾取。

1155
01:02:41,101 --> 01:02:43,541
现在我们的模板中有两个资源包文件。

1156
01:02:43,541 --> 01:02:45,981
一个是这个asset pack JSON，

1157
01:02:45,981 --> 01:02:48,421
另一个是preload asset pack JSON。

1158
01:02:48,421 --> 01:02:50,861
在Phaser编辑器和Phaser项目中，

1159
01:02:50,861 --> 01:02:53,302
你可以根据需要创建任意多个资源包JSON文件。

1160
01:02:53,302 --> 01:02:55,742
这让你能够将不同资源打包，

1161
01:02:55,742 --> 01:02:58,182
然后通过这些配置来加载它们。

1162
01:02:58,182 --> 01:03:00,622
在当前的项目设置中，

1163
01:03:00,622 --> 01:03:03,063
我们的模板里有两个这样的文件。

1164
01:03:03,268 --> 01:03:06,095
在我们测试游戏时，

1165
01:03:06,095 --> 01:03:08,923
偶尔会看到这个预加载场景在加载资源时弹出。

1166
01:03:08,923 --> 01:03:11,750
在Phaser中，

1167
01:03:11,750 --> 01:03:14,577
你可以设置一个或多个场景，

1168
01:03:14,577 --> 01:03:17,405
在Phaser编辑器中同样可以实现。

1169
01:03:17,405 --> 01:03:20,233
我们项目的设置方式是：预加载场景相当于加载界面，

1170
01:03:20,233 --> 01:03:23,060
这里会显示进度条来加载游戏所需的所有资源。

1171
01:03:23,060 --> 01:03:25,887
当所有资源加载完成后，

1172
01:03:25,887 --> 01:03:28,715
就会切换到关卡场景，

1173
01:03:28,715 --> 01:03:31,543
这时玩家就可以开始游戏了。

1174
01:03:31,817 --> 01:03:34,839
在预加载场景中，若需使用任何资源，

1175
01:03:34,839 --> 01:03:37,861
请确保这些资源在预加载场景启动前已准备就绪。

1176
01:03:37,861 --> 01:03:40,884
否则，

1177
01:03:40,884 --> 01:03:43,906
当我们引用它们时会出现图像损坏的情况。

1178
01:03:43,906 --> 01:03:46,929
这就是为什么我们在main.js文件中设置了两组资源包。

1179
01:03:46,929 --> 01:03:49,951
目前我们有一个启动场景（boot scene），

1180
01:03:49,951 --> 01:03:52,974
这是一个自定义的Phaser场景，

1181
01:03:52,974 --> 01:03:55,996
它被添加在此处而非我们的常规场景列表中。

1182
01:03:55,996 --> 01:03:59,018
该场景的唯一作用就是引用我们的预加载资源包。

1183
01:03:59,445 --> 01:04:01,765
在我们的预加载资源包中，

1184
01:04:01,765 --> 01:04:04,086
只有预加载器使用的那张图片。

1185
01:04:04,086 --> 01:04:06,407
然后在预加载场景中，

1186
01:04:06,407 --> 01:04:08,728
我们会加载另一个资源包的JSON文件，

1187
01:04:08,728 --> 01:04:11,049
这里面包含了所有其他资源。

1188
01:04:11,049 --> 01:04:13,369
关于Phaser需要注意的另一点是：一旦加载了资源，

1189
01:04:13,369 --> 01:04:15,690
它们就会被存入缓存，

1190
01:04:15,690 --> 01:04:18,011
可以在任何场景中使用。

1191
01:04:18,011 --> 01:04:20,332
虽然我们在预加载场景中加载了食物素材，

1192
01:04:20,332 --> 01:04:22,653
但在关卡场景中仍能使用它，

1193
01:04:22,653 --> 01:04:24,974
因为Phaser会从缓存中调用，

1194
01:04:24,974 --> 01:04:27,295
我们只需加载一次即可。

1195
01:04:27,568 --> 01:04:30,535
因此，对于开发大型游戏来说，

1196
01:04:30,535 --> 01:04:33,503
这是一个很好的模式。

1197
01:04:33,503 --> 01:04:36,470
这样在加载所有内容时，

1198
01:04:36,470 --> 01:04:39,438
你可以向玩家展示一个友好的界面，

1199
01:04:39,438 --> 01:04:42,405
而不是一次性加载所有内容导致玩家只能看到黑屏（因为没有任何内容对玩家可见）。

1200
01:04:42,405 --> 01:04:45,373
我们为《饥饿恐龙》游戏要做的最后一件事是添加一个非常简单的标题界面。

1201
01:04:45,373 --> 01:04:48,341
在这个标题界面中，

1202
01:04:48,341 --> 01:04:51,308
我们只需显示包含游戏名称的文本，

1203
01:04:51,308 --> 01:04:54,276
当玩家点击后，就会跳转到我们的关卡场景。

1204
01:04:54,276 --> 01:04:57,244
之前我们在这里创建了这个测试场景，

1205
01:04:57,500 --> 01:04:59,989
让我们快速清理一下。我们右键点击它。

1206
01:05:00,009 --> 01:05:02,780
删除这个。

1207
01:05:02,780 --> 01:05:05,551
现在我们要创建一个全新的场景，

1208
01:05:05,551 --> 01:05:08,322
选择场景菜单，新建一个场景文件，

1209
01:05:08,322 --> 01:05:11,094
命名为"标题"。

1210
01:05:11,094 --> 01:05:13,865
创建新场景后，

1211
01:05:13,865 --> 01:05:16,636
只需添加一个简单的文本游戏对象。

1212
01:05:16,636 --> 01:05:19,408
在字符串选项下，

1213
01:05:19,408 --> 01:05:22,179
添加文本内容"饥饿的恐龙"。

1214
01:05:22,179 --> 01:05:24,950
将字体大小调整为64像素，

1215
01:05:24,950 --> 01:05:27,722
并在屏幕中央对齐显示。

1216
01:05:28,097 --> 01:05:30,730
当前游戏对象的原点位于左上角。

1217
01:05:30,730 --> 01:05:33,364
我们需要将其调整到中心位置。

1218
01:05:33,364 --> 01:05:35,998
让我们更新原点坐标，

1219
01:05:35,998 --> 01:05:38,631
设置为0.5和0.5。

1220
01:05:38,631 --> 01:05:41,265
现在要让标题文本在屏幕居中显示，

1221
01:05:41,265 --> 01:05:43,899
我们可以使用布局工具来实现。

1222
01:05:43,899 --> 01:05:46,532
右键点击游戏对象选择布局选项，

1223
01:05:46,532 --> 01:05:49,166
会出现多种布局方式可供选择。

1224
01:05:49,166 --> 01:05:51,800
其中有个很实用的功能是"对齐到边界"选项。

1225
01:05:52,072 --> 01:05:55,162
这样我们就能让游戏物体与游戏边界完美对齐。

1226
01:05:55,162 --> 01:05:58,253
比如选择左右边界时，

1227
01:05:58,253 --> 01:06:01,344
可以看到游戏物体的边缘会与边界对齐。

1228
01:06:01,344 --> 01:06:04,435
如果选择"布局-居中"，

1229
01:06:04,435 --> 01:06:07,525
游戏物体就会在游戏两侧边缘之间居中显示。

1230
01:06:07,525 --> 01:06:10,616
同理，选择"布局-对齐边界"后，

1231
01:06:10,616 --> 01:06:13,707
无论是顶部边界还是底部边界，

1232
01:06:13,707 --> 01:06:16,798
游戏物体的上下边缘都会与边界对齐。

1233
01:06:16,798 --> 01:06:19,889
而如果选择"边界中间"，

1234
01:06:20,333 --> 01:06:23,463
这样我们的游戏对象就能完美居中于画布元素中了。

1235
01:06:23,463 --> 01:06:26,593
通过布局工具，您还能实现更多功能。

1236
01:06:26,593 --> 01:06:29,724
比如您可以选择创建网格布局 - 当您需要将多个游戏对象按网格排列时，

1237
01:06:29,724 --> 01:06:32,854
系统已内置了相应工具来自动完成。

1238
01:06:32,854 --> 01:06:35,985
好，如果我们保存后返回浏览器，刷新页面，

1239
01:06:35,985 --> 01:06:39,115
依然会看到关卡场景。

1240
01:06:39,115 --> 01:06:42,246
请注意：每次向游戏添加全新场景时，

1241
01:06:42,246 --> 01:06:45,376
都需要更新Phaser配置，

1242
01:06:45,376 --> 01:06:48,507
以便Phaser能识别该场景。

1243
01:06:48,898 --> 01:06:52,155
现在我们需要打开main.js文件。

1244
01:06:52,155 --> 01:06:55,412
无论你在哪里定义Phaser游戏配置（这里就是main.js文件），

1245
01:06:55,412 --> 01:06:58,669
我们都要自动添加游戏中使用的场景。

1246
01:06:58,669 --> 01:07:01,925
现在我们要为标题场景新增一个配置。

1247
01:07:01,925 --> 01:07:05,182
让我们复制这里的逻辑，粘贴到这里。

1248
01:07:05,182 --> 01:07:08,440
将键名更新为title，

1249
01:07:08,440 --> 01:07:11,697
类名也更新为title。

1250
01:07:12,159 --> 01:07:15,317
要使用标题场景类，

1251
01:07:15,317 --> 01:07:18,476
我们需要将其导入代码中。

1252
01:07:18,476 --> 01:07:21,635
让我们输入：import title from scenes/title.js。

1253
01:07:21,635 --> 01:07:24,794
这样做是告诉Phaser游戏配置我们新增了一个场景，

1254
01:07:24,794 --> 01:07:27,952
它会创建该场景的实例。

1255
01:07:27,952 --> 01:07:31,111
但如果要真正启动这个场景，

1256
01:07:31,111 --> 01:07:34,270
我们需要在代码中进行引用。

1257
01:07:34,270 --> 01:07:37,429
例如，在启动场景准备就绪后，

1258
01:07:37,429 --> 01:07:40,588
我们调用scene.start来加载预载场景。

1259
01:07:41,117 --> 01:07:43,754
因此，要使用我们的标题场景类，

1260
01:07:43,754 --> 01:07:46,391
需要将其导入代码中。

1261
01:07:46,391 --> 01:07:49,029
让我们输入：从场景文件夹的title.js导入Title类。

1262
01:07:49,029 --> 01:07:51,666
这样做之后，

1263
01:07:51,666 --> 01:07:54,303
就是在告诉Phaser游戏配置我们新增了一个场景，

1264
01:07:54,303 --> 01:07:56,941
系统会创建该场景的实例。

1265
01:07:56,941 --> 01:07:59,578
但如果我们想真正启动这个场景，

1266
01:07:59,578 --> 01:08:02,215
还需要在代码中进行引用。

1267
01:08:02,215 --> 01:08:04,853
以启动场景为例，

1268
01:08:04,853 --> 01:08:07,490
当它加载完成并准备就绪后，

1269
01:08:07,490 --> 01:08:10,128
我们调用scene.start方法来启动预加载场景。

1270
01:08:11,067 --> 01:08:14,009
让我们打开title.js文件。

1271
01:08:14,009 --> 01:08:16,952
进入create方法后，

1272
01:08:16,952 --> 01:08:19,895
在编辑器创建代码下方，

1273
01:08:19,895 --> 01:08:22,837
添加事件监听器的逻辑代码。

1274
01:08:22,837 --> 01:08:25,780
我们将通过this来引用Phaser场景，

1275
01:08:25,780 --> 01:08:28,722
用input来引用输入插件，

1276
01:08:28,722 --> 01:08:31,665
然后调用once方法。

1277
01:08:31,665 --> 01:08:34,608
这个方法的作用是让我们能够为指定事件添加一次性监听器，

1278
01:08:34,608 --> 01:08:37,551
这样就能在场景中监听一次点击事件并执行相应操作。

1279
01:08:38,131 --> 01:08:40,551
而如果我们使用on方法，

1280
01:08:40,551 --> 01:08:42,971
每次触发该事件时都会调用回调函数。

1281
01:08:42,971 --> 01:08:45,391
由于我们只需要点击一次就跳转到关卡场景，

1282
01:08:45,391 --> 01:08:47,811
这里我们只需使用once方法即可。

1283
01:08:47,811 --> 01:08:50,231
现在我们需要指定事件类型。

1284
01:08:50,231 --> 01:08:52,651
输入phaser.input.events，

1285
01:08:52,651 --> 01:08:55,071
然后选择POINTER_DOWN事件。

1286
01:08:55,071 --> 01:08:57,491
当这个事件被触发时，

1287
01:08:57,491 --> 01:08:59,911
我们提供的回调函数就会被执行。

1288
01:08:59,911 --> 01:09:02,331
在回调函数中，

1289
01:09:02,331 --> 01:09:04,752
我们只需启动我们的关卡场景。

1290
01:09:05,145 --> 01:09:08,528
之前我们在预加载场景中所做的scene.start操作，

1291
01:09:08,528 --> 01:09:11,911
现在要在标题场景中重复同样的步骤，

1292
01:09:11,911 --> 01:09:15,294
只不过这次需要将其改为level场景。

1293
01:09:15,294 --> 01:09:18,677
完成修改后，回到浏览器刷新场景，

1294
01:09:18,677 --> 01:09:22,060
点击画面时就能启动关卡场景，

1295
01:09:22,060 --> 01:09:25,443
玩家依然可以收集物品。

1296
01:09:25,443 --> 01:09:28,826
最后我们要对游戏做的一个调整是更新浏览器中显示的游戏标题。

1297
01:09:28,826 --> 01:09:32,210
目前显示的是"我的游戏"。

1298
01:09:32,551 --> 01:09:36,470
在我们之前的预加载场景中，

1299
01:09:36,470 --> 01:09:40,390
通过scene.start实现的功能，

1300
01:09:40,390 --> 01:09:44,310
现在我们要在标题场景做同样操作，

1301
01:09:44,310 --> 01:09:48,230
但这次要切换至关卡场景。

1302
01:09:48,230 --> 01:09:52,150
完成修改后，回到浏览器刷新场景。

1303
01:09:52,150 --> 01:09:56,070
点击场景后，关卡现在能正常启动，

1304
01:09:56,070 --> 01:09:59,989
玩家仍可收集物品。

1305
01:10:00,009 --> 01:10:02,574
以及对象交互功能。

1306
01:10:02,574 --> 01:10:05,140
通过Phaser Editor，

1307
01:10:05,140 --> 01:10:07,706
您无需深入编码即可直观地快速创建和测试游戏，

1308
01:10:07,706 --> 01:10:10,272
当然您随时可以选择手动编码。

1309
01:10:10,272 --> 01:10:12,838
跟随本教程后，

1310
01:10:12,838 --> 01:10:15,404
您现在已掌握使用Phaser Editor v4构建Phaser 3游戏的基础知识。

1311
01:10:15,404 --> 01:10:17,970
请记住，游戏开发重在实践与学习，

1312
01:10:17,970 --> 01:10:20,536
大胆尝试新功能并拓展今天所学内容吧。

1313
01:10:20,536 --> 01:10:23,102
一如既往，

1314
01:10:23,102 --> 01:10:25,668
期待听到您的想法并欣赏您的作品，

1315
01:10:25,668 --> 01:10:28,234
欢迎在评论区留言提问。

1316
01:10:28,506 --> 01:10:31,280
如果你觉得这个教程有帮助，

1317
01:10:31,280 --> 01:10:34,056
别忘了点赞视频并订阅以获取更多游戏开发内容。

1318
01:10:34,056 --> 01:10:36,831
同时，查看描述中的链接，

1319
01:10:36,831 --> 01:10:39,606
可以找到Phaser Editor的文档和其他有用资源。

1320
01:10:39,606 --> 01:10:42,381
感谢观看，祝游戏开发愉快。下个教程见。

