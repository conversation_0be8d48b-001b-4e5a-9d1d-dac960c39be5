package com.translation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 百度语音识别服务
 */
@Service
public class BaiduASRService {
    private static final Logger logger = LoggerFactory.getLogger(BaiduASRService.class);
    
    @Value("${baidu.asr.api.key:}")
    private String apiKey;
    
    @Value("${baidu.asr.secret.key:}")
    private String secretKey;
    
    private final OkHttpClient client;
    private final ObjectMapper objectMapper;
    
    // 百度API URLs
    private static final String TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
    private static final String ASR_URL = "https://vop.baidu.com/server_api";  // 使用pro_api端点
    
    // 文件大小限制 - 百度ASR Base64编码后不能超过4MB，原始文件约3MB安全
    private static final long MAX_FILE_SIZE = 3 * 1024 * 1024; // 3MB
    
    private String accessToken;
    private long tokenExpireTime;
    
    public BaiduASRService() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)  // 增加连接超时
                .readTimeout(300, TimeUnit.SECONDS)    // 增加读取超时到5分钟
                .writeTimeout(300, TimeUnit.SECONDS)   // 增加写入超时
                .retryOnConnectionFailure(true)       // 启用连接失败重试
                // 强制使用HTTP/1.1以避免HTTP/2的流重置问题
                .protocols(java.util.Arrays.asList(Protocol.HTTP_1_1))
                // 禁用连接池以避免连接复用问题
                .connectionPool(new ConnectionPool(0, 1, TimeUnit.NANOSECONDS))
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 检查服务是否已配置
     */
    public boolean isServiceConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() && 
               secretKey != null && !secretKey.trim().isEmpty();
    }
    
    /**
     * 语音识别（带重试机制和大文件分片处理）
     */
    public String transcribe(File audioFile, String language) throws IOException {
        if (!isServiceConfigured()) {
            throw new IOException("百度ASR服务未配置：请在application.yml中设置 baidu.asr.api.key 和 baidu.asr.secret.key");
        }
        
        logger.info("开始百度语音识别: {} ({}MB)", audioFile.getName(), audioFile.length() / 1024 / 1024);
        
        // 检查文件大小，如果超过限制则进行分片处理
        if (audioFile.length() > MAX_FILE_SIZE) {
            logger.warn("文件大小{}MB超过{}MB限制，将进行分片处理", 
                       audioFile.length() / 1024 / 1024, MAX_FILE_SIZE / 1024 / 1024);
            return transcribeWithChunking(audioFile, language);
        }
        
        // 重试机制
        int maxRetries = 3;
        IOException lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info("语音识别尝试 {}/{}", attempt, maxRetries);
                return transcribeFile(audioFile, language);
            } catch (IOException e) {
                lastException = e;
                logger.warn("语音识别第{}次尝试失败: {}", attempt, e.getMessage());
                
                if (attempt < maxRetries) {
                    try {
                        // 指数退避：第一次重试等待2秒，第二次等待4秒
                        long waitTime = 2000L * attempt;
                        logger.info("等待{}秒后重试...", waitTime / 1000);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("重试被中断", ie);
                    }
                }
            }
        }
        
        throw new IOException("语音识别失败，已重试" + maxRetries + "次", lastException);
    }
    
    /**
     * 获取访问令牌
     */
    private void getAccessToken() throws IOException {
        if (accessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return; // Token still valid
        }
        
        RequestBody requestBody = new FormBody.Builder()
                .add("grant_type", "client_credentials")
                .add("client_id", apiKey)
                .add("client_secret", secretKey)
                .build();
        
        Request request = new Request.Builder()
                .url(TOKEN_URL)
                .post(requestBody)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("获取访问令牌失败: " + response.code());
            }
            
            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (jsonNode.has("error")) {
                throw new IOException("获取访问令牌失败: " + jsonNode.get("error_description").asText());
            }
            
            accessToken = jsonNode.get("access_token").asText();
            int expiresIn = jsonNode.get("expires_in").asInt();
            tokenExpireTime = System.currentTimeMillis() + (expiresIn - 300) * 1000L; // 提前5分钟过期
            
            logger.info("获取访问令牌成功，有效期: {} 秒", expiresIn);
        }
    }
    
    /**
     * 识别音频文件
     */
    private String transcribeFile(File audioFile, String language) throws IOException {
        // 获取访问令牌
        getAccessToken();
        
        // 读取音频文件并转换为Base64
        byte[] audioData = Files.readAllBytes(audioFile.toPath());
        String audioBase64 = Base64.getEncoder().encodeToString(audioData);
        
        logger.info("准备发送语音识别请求，文件大小: {} bytes, Base64大小: {} chars", 
                   audioData.length, audioBase64.length());
        
        // 智能选择语言模型
        int devPid = determineLanguageModel(audioFile, language);
        
        // 构建JSON请求参数 - 百度server_api标准格式
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("format", "wav");
        requestData.put("rate", 16000);
        requestData.put("channel", 1);
        requestData.put("cuid", "translation-service");
        requestData.put("token", accessToken);
        requestData.put("speech", audioBase64);
        requestData.put("len", audioData.length);
        requestData.put("dev_pid", devPid);  // 智能选择的语言模型
        
        String jsonBody = objectMapper.writeValueAsString(requestData);
        RequestBody requestBody = RequestBody.create(jsonBody, MediaType.parse("application/json"));
        
        Request request = new Request.Builder()
                .url(ASR_URL)
                .post(requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            logger.info("收到语音识别响应，状态码: {}", response.code());
            
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "无响应体";
                throw new IOException(String.format("语音识别请求失败: HTTP %d, 响应: %s", 
                                                   response.code(), errorBody));
            }
            
            String responseBody = response.body().string();
            logger.info("语音识别响应内容: {}", responseBody);
            
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (jsonNode.has("err_no") && jsonNode.get("err_no").asInt() != 0) {
                String errorMsg = jsonNode.has("err_msg") ? jsonNode.get("err_msg").asText() : "未知错误";
                int errorCode = jsonNode.get("err_no").asInt();
                throw new IOException(String.format("语音识别失败: [%d] %s", errorCode, errorMsg));
            }
            
            if (jsonNode.has("result") && jsonNode.get("result").isArray() && jsonNode.get("result").size() > 0) {
                String result = jsonNode.get("result").get(0).asText();
                logger.info("语音识别成功: {}", result);
                return result;
            } else {
                logger.warn("语音识别结果为空，响应: {}", responseBody);
                return "";
            }
        } catch (Exception e) {
            // 特别处理HTTP/2流重置错误
            if (e.getMessage() != null && e.getMessage().contains("stream was reset")) {
                throw new IOException("网络连接被重置，可能是由于文件过大或网络不稳定: " + e.getMessage(), e);
            }
            throw e;
        }
    }
    
    /**
     * 大文件分片处理语音识别
     */
    private String transcribeWithChunking(File audioFile, String language) throws IOException {
        logger.info("开始分片处理大音频文件: {}", audioFile.getName());
        
        try {
            // 使用FFmpeg将音频文件分割成小片段
            List<File> chunks = splitAudioFile(audioFile);
            StringBuilder fullTranscription = new StringBuilder();
            
            for (int i = 0; i < chunks.size(); i++) {
                File chunk = chunks.get(i);
                logger.info("处理音频片段 {}/{}: {} ({}KB)", 
                           i + 1, chunks.size(), chunk.getName(), chunk.length() / 1024);
                
                try {
                    String chunkResult = transcribeFile(chunk, language);
                    if (!chunkResult.trim().isEmpty()) {
                        if (fullTranscription.length() > 0) {
                            fullTranscription.append(" ");
                        }
                        fullTranscription.append(chunkResult.trim());
                    }
                } catch (IOException e) {
                    logger.warn("音频片段{}识别失败: {}", i + 1, e.getMessage());
                    // 继续处理其他片段，不因单个片段失败而终止
                } finally {
                    // 清理临时文件 - 暂时注释掉以保留文件用于调试
                    // if (chunk.exists()) {
                    //     chunk.delete();
                    // }
                    logger.info("保留音频片段文件用于调试: {}", chunk.getAbsolutePath());
                }
            }
            
            String result = fullTranscription.toString().trim();
            logger.info("分片处理完成，合并结果长度: {} 字符", result.length());
            return result;
            
        } catch (Exception e) {
            throw new IOException("分片处理失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 使用FFmpeg分割音频文件
     */
    private List<File> splitAudioFile(File audioFile) throws IOException {
        List<File> chunks = new ArrayList<>();
        String tempDir = System.getProperty("java.io.tmpdir");
        String baseName = audioFile.getName().replaceFirst("\\.[^.]+$", "");
        
        try {
            // 获取音频时长
            double duration = getAudioDuration(audioFile);
            logger.info("音频总时长: {} 秒", duration);
            
            // 计算分片数量（每片45秒，确保在百度ASR 60秒限制内有安全余量）
            int chunkDuration = 45; // 45秒每片，避免触发60秒限制
            int numChunks = (int) Math.ceil(duration / chunkDuration);
            
            for (int i = 0; i < numChunks; i++) {
                double startTime = i * chunkDuration;
                File chunkFile = new File(tempDir, baseName + "_chunk_" + i + ".wav");
                
                // 使用FFmpeg命令分割音频 - 优化参数以符合百度ASR要求
                ProcessBuilder pb = new ProcessBuilder(
                    "ffmpeg", "-i", audioFile.getAbsolutePath(),
                    "-ss", String.valueOf(startTime),
                    "-t", String.valueOf(chunkDuration),
                    "-acodec", "pcm_s16le",  // 16位PCM编码
                    "-ar", "16000",          // 16kHz采样率
                    "-ac", "1",              // 单声道
                    "-f", "wav",             // 强制WAV格式
                    "-avoid_negative_ts", "make_zero",  // 避免负时间戳
                    "-y",                    // 覆盖输出文件
                    chunkFile.getAbsolutePath()
                );
                
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                // 等待处理完成
                int exitCode = process.waitFor();
                if (exitCode == 0 && chunkFile.exists() && chunkFile.length() > 0) {
                    // 检查文件大小是否过小
                    if (chunkFile.length() < 1024) { // 跳过小于1KB的文件
                        logger.warn("跳过过小的音频片段: {} ({}字节)", chunkFile.getName(), chunkFile.length());
                        continue;
                    }
                    
                    // 检查Base64编码后的大小是否超过百度ASR限制
                    long base64Size = (long) Math.ceil(chunkFile.length() * 4.0 / 3.0); // Base64编码增加约33%
                    if (base64Size > 2.8 * 1024 * 1024) { // 2.8MB限制，给百度ASR留安全余量
                        logger.warn("跳过过大的音频片段: {} (原始:{}KB, Base64预估:{}KB)", 
                                   chunkFile.getName(), chunkFile.length() / 1024, base64Size / 1024);
                        continue;
                    }
                    
                    chunks.add(chunkFile);
                    logger.debug("创建音频片段: {} ({}KB)", chunkFile.getName(), chunkFile.length() / 1024);
                } else {
                    logger.warn("创建音频片段失败: {}, 退出码: {}", chunkFile.getName(), exitCode);
                }
            }
            
        } catch (Exception e) {
            // 清理已创建的片段文件 - 暂时注释掉以保留文件用于调试
            // for (File chunk : chunks) {
            //     if (chunk.exists()) {
            //         chunk.delete();
            //     }
            // }
            logger.warn("音频分割失败，但保留已创建的片段文件用于调试: {}", e.getMessage());
            throw new IOException("音频分割失败: " + e.getMessage(), e);
        }
        
        if (chunks.isEmpty()) {
            throw new IOException("未能创建任何音频片段");
        }
        
        return chunks;
    }
    
    /**
     * 获取音频文件时长
     */
    private double getAudioDuration(File audioFile) throws IOException {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", audioFile.getAbsolutePath()
            );
            
            Process process = pb.start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String durationStr = reader.readLine();
                if (durationStr != null && !durationStr.trim().isEmpty()) {
                    return Double.parseDouble(durationStr.trim());
                }
            }
            
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new IOException("FFprobe执行失败，退出码: " + exitCode);
            }
            
        } catch (Exception e) {
            throw new IOException("获取音频时长失败: " + e.getMessage(), e);
        }
        
        throw new IOException("无法获取音频时长");
    }
    
    /**
     * 智能选择语言模型
     */
    private int determineLanguageModel(File audioFile, String language) {
        String fileName = audioFile.getName().toLowerCase();
        
        // 根据文件名判断语言类型
        if (fileName.contains("english") || fileName.contains("test") || 
            fileName.contains("input") || fileName.contains("video_")) {
            logger.info("检测到英文音频文件，使用英文识别模型 (dev_pid: 1737)");
            return 1737; // 英文模型
        }
        
        // 根据传入的language参数判断
        if ("en".equals(language) || "english".equals(language)) {
            logger.info("指定英文语言，使用英文识别模型 (dev_pid: 1737)");
            return 1737; // 英文模型
        }
        
        // 默认使用中文模型
        logger.info("使用中文识别模型 (dev_pid: 1536)");
        return 1536; // 普通话模型
    }
    
    /**
     * 获取音频格式
     */
    private String getAudioFormat(File audioFile) {
        String fileName = audioFile.getName().toLowerCase();
        if (fileName.endsWith(".wav")) {
            return "wav";
        } else if (fileName.endsWith(".mp3")) {
            return "mp3";
        } else if (fileName.endsWith(".m4a")) {
            return "m4a";
        } else {
            return "wav"; // 默认格式
        }
    }
}