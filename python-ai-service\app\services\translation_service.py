"""
Translation service for video translation AI service
"""

from typing import Dict, Any
from openai import Async<PERSON><PERSON>A<PERSON>
from .base_service import BaseService
from app.models.schemas import TranslationRequest


class TranslationService(BaseService):
    """Translation service using OpenAI API"""
    
    def __init__(self):
        super().__init__()
        self.openai_client = None
        if self.settings.openai_api_key:
            # Create client with custom base_url if provided
            client_kwargs = {
                "api_key": self.settings.openai_api_key
            }
            if self.settings.openai_base_url:
                client_kwargs["base_url"] = self.settings.openai_base_url
            
            self.openai_client = AsyncOpenAI(**client_kwargs)
    
    async def translate_text_async(self, text: str, source_language: str = "en", 
                                  target_language: str = "zh-CN", service: str = "openai") -> str:
        """Create async task for translation"""
        task_id = await self.create_task("translation", {
            "text": text,
            "source_language": source_language,
            "target_language": target_language,
            "service": service
        })
        return task_id
    
    async def process_translation(self, task_id: str, request: TranslationRequest):
        """Process translation task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Translate text
            result = await self.translate_text(
                request.text,
                request.source_language,
                request.target_language,
                request.service
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "translated_text": result["translated_text"],
                "source_language": result["source_language"],
                "target_language": result["target_language"],
                "confidence": result["confidence"]
            })
            
        except Exception as e:
            self.logger.error(f"Translation failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def translate_text(self, text: str, source_language: str = "en", 
                           target_language: str = "zh-CN", service: str = "openai") -> Dict[str, Any]:
        """Translate text using specified service"""
        try:
            if service == "openai":
                return await self._translate_with_openai(text, source_language, target_language)
            else:
                raise ValueError(f"Unsupported translation service: {service}")
                
        except Exception as e:
            self.logger.error(f"Translation failed: {e}")
            raise
    
    async def _translate_with_openai(self, text: str, source_language: str, 
                                   target_language: str) -> Dict[str, Any]:
        """Translate text using OpenAI API"""
        try:
            # Language mapping
            lang_map = {
                "en": "English",
                "zh-CN": "Chinese (Simplified)",
                "zh-TW": "Chinese (Traditional)",
                "ja": "Japanese",
                "ko": "Korean",
                "es": "Spanish",
                "fr": "French",
                "de": "German",
                "ru": "Russian"
            }
            
            source_lang_name = lang_map.get(source_language, source_language)
            target_lang_name = lang_map.get(target_language, target_language)
            
            prompt = f"Translate the following text from {source_lang_name} to {target_lang_name}. Only return the translation, no explanations:\n\n{text}"
            
            if not self.openai_client:
                raise Exception("OpenAI API key not configured")
            
            response = await self.openai_client.chat.completions.create(
                model=self.settings.openai_model,
                messages=[
                    {"role": "system", "content": "You are a professional translator."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.3
            )
            
            translated_text = response.choices[0].message.content.strip()
            
            return {
                "translated_text": translated_text,
                "source_language": source_language,
                "target_language": target_language,
                "confidence": 0.9  # OpenAI doesn't provide confidence scores
            }
            
        except Exception as e:
            self.logger.error(f"OpenAI translation failed: {e}")
            raise
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process translation task"""
        pass 