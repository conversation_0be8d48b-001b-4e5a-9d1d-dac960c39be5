{"a. Download or Upload Video": "a. 下载或上传视频", "Delete and Reselect": "删除并重新选择", "Enter YouTube link:": "输入YouTube链接:", "Resolution": "分辨率", "Download Video": "下载视频", "Or upload video": "或上传视频", "Youtube Settings": "Youtube设置", "Cookies Path": "Cookies文件路径", "LLM Configuration": "LLM配置", "API_KEY": "API密钥", "BASE_URL": "BASE_URL", "MODEL": "模型", "Openai format, will add /v1/chat/completions automatically": "OpenAI格式,将自动添加/v1/chat/completions", "click to check API validity": "点击检查API有效性", "API Key is valid": "API密钥有效", "API Key is invalid": "API密钥无效", "Recog Lang": "识别语言", "Subtitles Settings": "字幕设置", "Target Lang": "目标语言", "Input any language in natural language, as long as llm can understand": "用自然语言输入任何语言,只要LLM能理解即可", "Vocal separation enhance": "人声分离增强", "Burn-in Subtitles": "烧录字幕", "Whether to burn subtitles into the video, will increase processing time": "是否将字幕烧录到视频中，会增加处理时间", "Video Resolution": "视频分辨率", "Recommended for videos with loud background noise, but will increase processing time": "推荐用于背景噪音较大的视频,但会增加处理时间", "Dubbing Settings": "配音设置", "TTS Method": "TTS方法", "SiliconFlow API Key": "SiliconFlow API密钥", "Mode Selection": "模式选择", "Preset": "预设", "Refer_stable": "稳定参考", "Refer_dynamic": "动态参考", "OpenAI Voice": "OpenAI语音", "Fish TTS Character": "Fish TTS角色", "Azure Voice": "Azure语音", "Please refer to Github homepage for GPT_SoVITS configuration": "请参考Github主页了解GPT_SoVITS配置", "SoVITS Character": "SoVITS角色", "Refer Mode": "参考模式", "Mode 1: Use provided reference audio only": "模式1:仅使用提供的参考音频", "Mode 2: Use first audio from video as reference": "模式2:使用视频中的第一段音频作为参考", "Mode 3: Use each audio from video as reference": "模式3:使用视频中的每段音频作为参考", "Configure reference audio mode for GPT-SoVITS": "配置GPT-SoVITS的参考音频模式", "Edge TTS Voice": "Edge TTS语音", "=====NOTE=====": "以下是st.py中的内容", "b. Translate and Generate Subtitles": "b. 翻译并生成字幕", "This stage includes the following steps:": "此阶段包含以下步骤:", "WhisperX word-level transcription": "WhisperX词级转录", "Sentence segmentation using NLP and LLM": "使用NLP和LLM进行句子分段", "Summarization and multi-step translation": "摘要和多步翻译", "Cutting and aligning long subtitles": "切割和对齐长字幕", "Generating timeline and subtitles": "生成时间轴和字幕", "Merging subtitles into the video": "将字幕合并到视频中", "Start Processing Subtitles": "开始处理字幕", "Download All Srt Files": "下载所有Srt文件", "Archive to 'history'": "归档到'history'", "Using Whisper for transcription...": "正在使用Whisper进行转录...", "Splitting long sentences...": "正在分割长句...", "Summarizing and translating...": "正在总结和翻译...", "Processing and aligning subtitles...": "正在处理和对齐字幕...", "Merging subtitles to video...": "正在将字幕合并到视频...", "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...": "⚠️ 翻译前暂停。请前往`output/log/terminology.json`编辑术语。然后按回车键继续...", "Subtitle processing complete! 🎉": "字幕处理完成! 🎉", "c. Dubbing": "c. 配音", "Generate audio tasks and chunks": "生成音频任务和分块", "Extract reference audio": "提取参考音频", "Generate and merge audio files": "生成和合并音频文件", "Merge final audio into video": "将最终音频合并到视频中", "Start Audio Processing": "开始音频处理", "Audio processing is complete! You can check the audio files in the `output` folder.": "音频处理完成！您可以在`output`文件夹中查看音频文件。", "Delete dubbing files": "删除配音文件", "Generate audio tasks": "生成音频任务", "Extract refer audio": "提取参考音频", "Generate all audio": "生成所有音频", "Merge full audio": "合并完整音频", "Merge dubbing to the video": "将配音合并到视频中", "Audio processing complete! 🎇": "音频处理完成! 🎇", "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!": "欢迎来到VideoLingo。如果遇到任何问题，随时可以通过我们的免费问答助手 <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a> 获取即时解答！还可以免费试用我们的SaaS网站 <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a>  <br /><a href=\"https://nuowa.net/1697\" target=\"_blank\" style=\"color:red;\">整合包使用说明>></a>", "WhisperX Runtime": "WhisperX 运行环境", "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key": "本地运行需要>8GB显存GPU，云端运行需要302ai API密钥，elevenlabs运行需要ElevenLabs API密钥", "WhisperX 302ai API": "WhisperX 302ai API密钥", "=====NOTE2=====": "以下是install.py中的内容", "🚀 Starting Installation": "🚀 开始安装", "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)": "是否需要自动配置PyPI镜像？（如果访问pypi.org困难，建议使用）", "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...": "🎮 检测到NVIDIA GPU，正在安装CUDA版本的PyTorch...", "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 检测到MacOS，正在安装CPU版本的PyTorch... 注意：在whisperX转录过程中可能会较慢。", "💻 No NVIDIA GPU detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "💻 未检测到NVIDIA GPU，正在安装CPU版本的PyTorch... 注意：在whisperX转录过程中可能会较慢。", "❌ Failed to install requirements:": "❌ 安装依赖失败：", "✅ FFmpeg is already installed": "✅ FFmpeg已安装", "❌ FFmpeg not found\n\n": "❌ 未找到FFmpeg\n\n", "🛠️ Install using:": "🛠️ 使用以下命令安装：", "💡 Note:": "💡 注意：", "🔄 After installing FFmpeg, please run this installer again:": "🔄 安装FFmpeg后，请重新运行此安装程序：", "Install Chocolatey first (https://chocolatey.org/)": "请先安装Chocolatey (https://chocolatey.org/)", "Install Homebrew first (https://brew.sh/)": "请先安装Homebrew (https://brew.sh/)", "Use your distribution's package manager": "使用您的发行版包管理器", "FFmpeg is required. Please install it and run the installer again.": "需要安装FFmpeg。请安装后重新运行安装程序。", "Installation completed": "安装完成", "Now I will run this command to start the application:": "现在我将运行以下命令启动应用：", "Note: First startup may take up to 1 minute": "注意：首次启动可能需要最多1分钟", "If the application fails to start:": "如果应用启动失败：", "Check your network connection": "检查网络连接", "Re-run the installer: [bold]python install.py[/bold]": "重新运行安装程序：[bold]python install.py[/bold]", "Installing requirements using `pip install -r requirements.txt`": "正在使用 `pip install -r requirements.txt` 安装依赖", "Detected NVIDIA GPU(s)": "检测到NVIDIA GPU", "No NVIDIA GPU detected": "未检测到NVIDIA GPU", "No NVIDIA GPU detected or NVIDIA drivers not properly installed": "未检测到NVIDIA GPU或NVIDIA驱动未正确安装", "LLM JSON Format Support": "LLM JSON格式支持", "Enable if your LLM supports JSON mode output": "如果选用的LLM支持JSON模式输出，请启用"}