#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频问题分析工具
分析声音快慢不一、大小不一的根本原因
"""

import os
import re
import numpy as np
from moviepy import VideoFileClip
from pydub import AudioSegment
import matplotlib.pyplot as plt
from collections import defaultdict

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                if ' --> ' in time_line:
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
            except ValueError:
                continue
    
    return subtitles

def analyze_audio_files(audio_dir: str, max_samples: int = 50):
    """分析音频文件的特征"""
    print("🔍 分析音频文件特征...")
    
    audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
    audio_files.sort()
    
    # 采样分析（避免处理所有文件）
    sample_files = audio_files[:max_samples] if len(audio_files) > max_samples else audio_files
    
    audio_stats = []
    
    for i, audio_file in enumerate(sample_files):
        audio_path = os.path.join(audio_dir, audio_file)
        
        try:
            # 加载音频
            try:
                audio = AudioSegment.from_wav(audio_path)
            except:
                audio = AudioSegment.from_mp3(audio_path)
            
            # 提取特征
            duration = len(audio) / 1000.0  # 秒
            volume_db = audio.dBFS
            max_volume = audio.max_dBFS
            
            # 转换为numpy数组进行更详细分析
            samples = np.array(audio.get_array_of_samples())
            if audio.channels == 2:
                samples = samples.reshape((-1, 2))
                samples = samples.mean(axis=1)
            
            # 计算音频特征
            rms = np.sqrt(np.mean(samples**2))
            peak = np.max(np.abs(samples))
            dynamic_range = np.max(samples) - np.min(samples)
            
            audio_stats.append({
                'file': audio_file,
                'duration': duration,
                'volume_db': volume_db,
                'max_volume': max_volume,
                'rms': rms,
                'peak': peak,
                'dynamic_range': dynamic_range
            })
            
            if (i + 1) % 10 == 0:
                print(f"  分析进度: {i+1}/{len(sample_files)}")
                
        except Exception as e:
            print(f"❌ 分析失败 {audio_file}: {str(e)}")
    
    return audio_stats

def analyze_subtitle_audio_mapping(subtitles, audio_dir):
    """分析字幕与音频的映射关系"""
    print("🔍 分析字幕与音频映射关系...")
    
    audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
    audio_files.sort()
    
    mapping_issues = []
    
    # 分析前100个样本
    sample_size = min(100, len(subtitles), len(audio_files))
    
    for i in range(sample_size):
        index, start_time, end_time, text = subtitles[i]
        audio_file = audio_files[i]
        audio_path = os.path.join(audio_dir, audio_file)
        
        try:
            # 加载音频
            try:
                audio = AudioSegment.from_wav(audio_path)
            except:
                audio = AudioSegment.from_mp3(audio_path)
            
            audio_duration = len(audio) / 1000.0
            subtitle_duration = end_time - start_time
            
            # 计算时长差异
            duration_ratio = audio_duration / subtitle_duration if subtitle_duration > 0 else float('inf')
            
            issue = {
                'index': i,
                'subtitle_index': index,
                'audio_file': audio_file,
                'subtitle_duration': subtitle_duration,
                'audio_duration': audio_duration,
                'duration_ratio': duration_ratio,
                'text_length': len(text),
                'text_preview': text[:50] + '...' if len(text) > 50 else text
            }
            
            mapping_issues.append(issue)
            
        except Exception as e:
            print(f"❌ 映射分析失败 {i}: {str(e)}")
    
    return mapping_issues

def analyze_video_audio(video_path: str, sample_duration: int = 300):
    """分析视频中的音频质量"""
    print("🔍 分析视频音频质量...")
    
    try:
        video = VideoFileClip(video_path)
        
        if video.audio is None:
            print("❌ 视频没有音频")
            return None
        
        # 分析前5分钟的音频
        test_duration = min(sample_duration, video.duration)
        audio_clip = video.audio.subclipped(0, test_duration)
        
        # 转换为numpy数组
        audio_array = audio_clip.to_soundarray()
        
        # 分析音频特征
        if len(audio_array.shape) == 2:
            # 立体声转单声道
            audio_array = audio_array.mean(axis=1)
        
        # 计算统计信息
        segments = []
        segment_length = int(video.fps * 10)  # 10秒片段
        
        for i in range(0, len(audio_array), segment_length):
            segment = audio_array[i:i+segment_length]
            if len(segment) > 0:
                rms = np.sqrt(np.mean(segment**2))
                peak = np.max(np.abs(segment))
                segments.append({
                    'time': i / video.fps,
                    'rms': rms,
                    'peak': peak
                })
        
        video.close()
        audio_clip.close()
        
        return segments
        
    except Exception as e:
        print(f"❌ 视频音频分析失败: {str(e)}")
        return None

def generate_analysis_report(audio_stats, mapping_issues, video_segments):
    """生成分析报告"""
    print("\n" + "="*60)
    print("🔍 音频问题分析报告")
    print("="*60)
    
    # 1. 音频文件特征分析
    print("\n📊 1. 音频文件特征分析")
    print("-" * 30)
    
    if audio_stats:
        durations = [stat['duration'] for stat in audio_stats]
        volumes = [stat['volume_db'] for stat in audio_stats if stat['volume_db'] != -float('inf')]
        
        print(f"📈 音频时长统计:")
        print(f"   平均时长: {np.mean(durations):.2f}秒")
        print(f"   最短时长: {np.min(durations):.2f}秒")
        print(f"   最长时长: {np.max(durations):.2f}秒")
        print(f"   时长标准差: {np.std(durations):.2f}秒")
        
        if volumes:
            print(f"\n🔊 音量统计:")
            print(f"   平均音量: {np.mean(volumes):.2f}dB")
            print(f"   最小音量: {np.min(volumes):.2f}dB")
            print(f"   最大音量: {np.max(volumes):.2f}dB")
            print(f"   音量标准差: {np.std(volumes):.2f}dB")
            
            # 音量差异分析
            volume_range = np.max(volumes) - np.min(volumes)
            print(f"   音量范围: {volume_range:.2f}dB")
            
            if volume_range > 10:
                print("   ⚠️  音量差异过大（>10dB）")
            elif volume_range > 5:
                print("   ⚠️  音量差异较大（>5dB）")
            else:
                print("   ✅ 音量差异可接受")
    
    # 2. 字幕音频映射分析
    print("\n📋 2. 字幕音频映射分析")
    print("-" * 30)
    
    if mapping_issues:
        duration_ratios = [issue['duration_ratio'] for issue in mapping_issues if issue['duration_ratio'] != float('inf')]
        
        print(f"📊 时长比例统计 (音频时长/字幕时长):")
        print(f"   平均比例: {np.mean(duration_ratios):.2f}")
        print(f"   最小比例: {np.min(duration_ratios):.2f}")
        print(f"   最大比例: {np.max(duration_ratios):.2f}")
        print(f"   比例标准差: {np.std(duration_ratios):.2f}")
        
        # 分析问题案例
        problematic_cases = [issue for issue in mapping_issues if issue['duration_ratio'] > 2.0 or issue['duration_ratio'] < 0.5]
        
        print(f"\n⚠️  问题案例数量: {len(problematic_cases)}/{len(mapping_issues)}")
        
        if problematic_cases:
            print("   前5个问题案例:")
            for i, case in enumerate(problematic_cases[:5]):
                print(f"   {i+1}. 字幕{case['subtitle_index']}: {case['subtitle_duration']:.2f}s -> {case['audio_file']}: {case['audio_duration']:.2f}s (比例: {case['duration_ratio']:.2f})")
                print(f"      文本: {case['text_preview']}")
    
    # 3. 视频音频质量分析
    print("\n🎬 3. 视频音频质量分析")
    print("-" * 30)
    
    if video_segments:
        rms_values = [seg['rms'] for seg in video_segments]
        peak_values = [seg['peak'] for seg in video_segments]
        
        print(f"📊 音频质量统计:")
        print(f"   RMS平均值: {np.mean(rms_values):.6f}")
        print(f"   RMS标准差: {np.std(rms_values):.6f}")
        print(f"   峰值平均值: {np.mean(peak_values):.6f}")
        print(f"   峰值标准差: {np.std(peak_values):.6f}")
        
        # 分析音量变化
        rms_variation = np.std(rms_values) / np.mean(rms_values) if np.mean(rms_values) > 0 else 0
        print(f"   音量变化系数: {rms_variation:.2f}")
        
        if rms_variation > 0.5:
            print("   ⚠️  音量变化过大")
        elif rms_variation > 0.3:
            print("   ⚠️  音量变化较大")
        else:
            print("   ✅ 音量变化可接受")
    
    # 4. 问题诊断和建议
    print("\n🔧 4. 问题诊断和建议")
    print("-" * 30)
    
    issues_found = []
    
    # 检查音频时长差异
    if audio_stats:
        duration_std = np.std([stat['duration'] for stat in audio_stats])
        if duration_std > 1.0:
            issues_found.append("音频片段时长差异过大")
    
    # 检查音量差异
    if audio_stats:
        volumes = [stat['volume_db'] for stat in audio_stats if stat['volume_db'] != -float('inf')]
        if volumes:
            volume_range = np.max(volumes) - np.min(volumes)
            if volume_range > 10:
                issues_found.append("音频音量差异过大")
    
    # 检查映射问题
    if mapping_issues:
        problematic_ratio = len([issue for issue in mapping_issues if issue['duration_ratio'] > 2.0 or issue['duration_ratio'] < 0.5]) / len(mapping_issues)
        if problematic_ratio > 0.3:
            issues_found.append("字幕与音频时长不匹配")
    
    if issues_found:
        print("❌ 发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
        
        print("\n💡 建议解决方案:")
        print("   1. 重新生成TTS音频，确保时长匹配")
        print("   2. 使用更严格的音量标准化")
        print("   3. 检查字幕分割是否正确")
        print("   4. 考虑使用更稳定的TTS服务")
    else:
        print("✅ 未发现明显问题")

def main():
    """主函数"""
    print("🔍 音频问题分析工具")
    print("分析声音快慢不一、大小不一的根本原因")
    
    # 文件路径
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese_split.srt"
    audio_dir = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/audio"
    video_path = "chinese_video_final.mp4"  # 使用已存在的视频文件
    
    # 检查文件是否存在
    if not os.path.exists(srt_path):
        print(f"❌ 字幕文件不存在: {srt_path}")
        return
    
    if not os.path.exists(audio_dir):
        print(f"❌ 音频目录不存在: {audio_dir}")
        return
    
    # 视频文件是可选的，主要分析音频文件
    video_exists = os.path.exists(video_path)
    if not video_exists:
        print(f"⚠️  视频文件不存在: {video_path}，跳过视频分析")
        video_path = None
    
    # 读取字幕
    subtitles = read_srt_file(srt_path)
    print(f"📝 读取到 {len(subtitles)} 条字幕")
    
    # 分析音频文件
    audio_stats = analyze_audio_files(audio_dir)
    
    # 分析字幕音频映射
    mapping_issues = analyze_subtitle_audio_mapping(subtitles, audio_dir)
    
    # 分析视频音频质量
    video_segments = analyze_video_audio(video_path) if video_path else None
    
    # 生成分析报告
    generate_analysis_report(audio_stats, mapping_issues, video_segments)

if __name__ == "__main__":
    main() 