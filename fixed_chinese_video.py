#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版中文配音视频合成器
解决音频格式问题和MoviePy API问题
"""

import os
import re
from moviepy import VideoFileClip, AudioFileClip
from pydub import AudioSegment
import shutil

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def fix_audio_format(audio_dir: str, fixed_audio_dir: str):
    """修复音频格式问题 - 将MP3文件转换为真正的WAV格式"""
    
    print("🔧 修复音频格式...")
    os.makedirs(fixed_audio_dir, exist_ok=True)
    
    audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
    
    for audio_file in audio_files:
        original_path = os.path.join(audio_dir, audio_file)
        fixed_path = os.path.join(fixed_audio_dir, audio_file)
        
        try:
            # 尝试作为MP3加载（因为Edge TTS实际生成的是MP3）
            audio = AudioSegment.from_mp3(original_path)
            # 导出为真正的WAV格式
            audio.export(fixed_path, format="wav")
            print(f"✅ 修复音频格式: {audio_file}")
        except Exception as e:
            print(f"❌ 修复音频格式失败 {audio_file}: {str(e)}")
            # 如果失败，尝试直接复制
            try:
                shutil.copy2(original_path, fixed_path)
                print(f"⚠️  直接复制: {audio_file}")
            except Exception as e2:
                print(f"❌ 复制失败 {audio_file}: {str(e2)}")

def create_fixed_chinese_video():
    """创建修复版的中文配音视频"""
    
    # 文件路径
    video_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/input/test_30s.mp4"
    srt_path = "output_chinese_split.srt"
    audio_dir = "out/audio"
    fixed_audio_dir = "out/audio_fixed"
    output_path = "chinese_video_fixed.mp4"
    
    print("🎬 开始创建修复版中文配音视频...")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    try:
        # 修复音频格式
        fix_audio_format(audio_dir, fixed_audio_dir)
        
        # 读取字幕文件
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 分割字幕块
        subtitle_blocks = re.split(r'\n\s*\n', content)
        subtitles = []
        
        for block in subtitle_blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 解析时间
                start_time_str, end_time_str = time_line.split(' --> ')
                start_time = parse_srt_time(start_time_str)
                end_time = parse_srt_time(end_time_str)
                
                subtitles.append((index, start_time, end_time, text.strip()))
        
        print(f"📝 读取到 {len(subtitles)} 条字幕")
        
        # 加载原视频
        print("📹 加载原视频...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        print(f"📊 原视频时长: {original_duration:.2f}s")
        
        # 创建静音背景音轨
        print("🎵 创建合成音频...")
        composite_audio = AudioSegment.silent(duration=int(original_duration * 1000))
        
        # 处理每个字幕的音频
        successful_audio_count = 0
        for i, (index, start_time, end_time, text) in enumerate(subtitles):
            audio_file = os.path.join(fixed_audio_dir, f"subtitle_{index:03d}.wav")
            
            if os.path.exists(audio_file):
                try:
                    # 加载修复后的音频
                    tts_audio = AudioSegment.from_wav(audio_file)
                    tts_duration = len(tts_audio) / 1000.0
                    
                    # 计算字幕时间段长度
                    subtitle_duration = end_time - start_time
                    
                    print(f"🔄 [{i+1}/{len(subtitles)}] 处理: {text[:30]}... (时长: {tts_duration:.2f}s)")
                    
                    # 如果TTS音频比字幕时间段长，需要加速
                    if tts_duration > subtitle_duration:
                        speed_factor = tts_duration / subtitle_duration
                        tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                        print(f"  🔄 音频加速 {speed_factor:.2f}x")
                    
                    # 将TTS音频插入到指定时间位置
                    start_ms = int(start_time * 1000)
                    
                    # 确保不超出总时长
                    if start_ms + len(tts_audio) > len(composite_audio):
                        tts_audio = tts_audio[:len(composite_audio) - start_ms]
                    
                    # 叠加音频
                    composite_audio = composite_audio.overlay(tts_audio, position=start_ms)
                    successful_audio_count += 1
                    
                except Exception as e:
                    print(f"❌ 处理音频失败 {audio_file}: {str(e)}")
                    continue
            else:
                print(f"⚠️  音频文件不存在: {audio_file}")
        
        print(f"✅ 成功处理 {successful_audio_count}/{len(subtitles)} 个音频文件")
        
        # 将AudioSegment转换为临时文件
        temp_audio_path = "temp_fixed_audio.wav"
        print("💾 导出合成音频...")
        composite_audio.export(temp_audio_path, format="wav")
        
        # 加载合成音频
        print("🎧 加载合成音频到MoviePy...")
        new_audio = AudioFileClip(temp_audio_path)
        
        # 设置视频的新音轨（修复API调用）
        print("🎬 合成最终视频...")
        final_video = video.with_audio(new_audio)  # 使用with_audio而不是set_audio
        
        # 输出视频
        print(f"💾 保存视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac'
        )
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        
        # 释放资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"🎉 中文配音视频创建成功！")
        print(f"📁 输出文件: {output_path}")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            print(f"📊 文件大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建视频失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_fixed_chinese_video()
    if success:
        print("✅ 任务完成！")
    else:
        print("❌ 任务失败！") 