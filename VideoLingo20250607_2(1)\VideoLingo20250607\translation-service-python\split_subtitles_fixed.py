#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕分割工具 - 修复版本
将长字幕按句子分割成更短、更易读的片段
"""

import re
import os

def parse_srt_time(time_str):
    """解析SRT时间格式到秒数"""
    try:
        time_parts = time_str.replace(',', '.').split(':')
        hours = int(time_parts[0])
        minutes = int(time_parts[1])
        seconds = float(time_parts[2])
        return hours * 3600 + minutes * 60 + seconds
    except Exception as e:
        print(f"❌ 时间解析错误: {time_str}, 错误: {e}")
        return 0

def seconds_to_srt_time(seconds):
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def split_chinese_text(text, max_chars=20):
    """专门为中文字幕设计的分割函数"""
    if len(text) <= max_chars:
        return [text]
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    segments = []
    
    # 首先按句号、感叹号、问号分割
    sentences = re.split(r'([。！？])', text)
    
    current_segment = ""
    
    i = 0
    while i < len(sentences):
        if i + 1 < len(sentences) and sentences[i+1] in ['。', '！', '？']:
            # 当前是句子，下一个是标点
            sentence = sentences[i] + sentences[i+1]
            i += 2
        else:
            sentence = sentences[i]
            i += 1
        
        sentence = sentence.strip()
        if not sentence:
            continue
        
        # 检查是否能加入当前段落
        if len(current_segment + sentence) <= max_chars:
            current_segment += sentence
        else:
            # 保存当前段落
            if current_segment:
                segments.append(current_segment.strip())
            
            # 如果单个句子太长，按逗号或空格分割
            if len(sentence) > max_chars:
                # 按逗号分割
                parts = re.split(r'([，,])', sentence)
                temp_segment = ""
                
                j = 0
                while j < len(parts):
                    if j + 1 < len(parts) and parts[j+1] in ['，', ',']:
                        part = parts[j] + parts[j+1]
                        j += 2
                    else:
                        part = parts[j]
                        j += 1
                    
                    if len(temp_segment + part) <= max_chars:
                        temp_segment += part
                    else:
                        if temp_segment:
                            segments.append(temp_segment.strip())
                        temp_segment = part
                
                current_segment = temp_segment
            else:
                current_segment = sentence
    
    # 添加最后一个段落
    if current_segment:
        segments.append(current_segment.strip())
    
    return segments

def split_subtitles(input_srt, output_srt, max_chars=20):
    """分割字幕文件"""
    print(f"🔄 分割字幕文件: {input_srt}")
    print(f"📝 最大字符数: {max_chars}")
    
    try:
        with open(input_srt, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        print(f"📄 读取文件成功，内容长度: {len(content)} 字符")
        
        # 解析原始字幕
        blocks = content.split('\n\n')
        original_subs = []
        
        print(f"📊 检测到 {len(blocks)} 个字幕块")
        
        for i, block in enumerate(blocks):
            if not block.strip():
                continue
                
            lines = block.strip().split('\n')
            if len(lines) < 3:
                print(f"⚠️ 跳过格式错误的字幕块 {i+1}: {block[:50]}...")
                continue
            
            try:
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 解析时间
                if ' --> ' not in time_line:
                    print(f"⚠️ 时间格式错误: {time_line}")
                    continue
                    
                start_str, end_str = time_line.split(' --> ')
                start_time = parse_srt_time(start_str)
                end_time = parse_srt_time(end_str)
                
                if end_time <= start_time:
                    print(f"⚠️ 时间顺序错误: {start_str} -> {end_str}")
                    continue
                
                original_subs.append({
                    'index': index,
                    'start': start_time,
                    'end': end_time,
                    'text': text,
                    'duration': end_time - start_time
                })
                
            except Exception as e:
                print(f"⚠️ 解析字幕块失败 {i+1}: {e}")
                continue
        
        print(f"✅ 成功解析 {len(original_subs)} 条字幕")
        
        # 分割字幕
        new_subs = []
        new_index = 1
        split_count = 0
        
        for sub in original_subs:
            # 分割文本
            text_segments = split_chinese_text(sub['text'], max_chars)
            
            if len(text_segments) <= 1:
                # 不需要分割
                new_subs.append({
                    'index': new_index,
                    'start': sub['start'],
                    'end': sub['end'],
                    'text': sub['text']
                })
                new_index += 1
            else:
                # 需要分割，按时间平均分配
                split_count += 1
                segment_duration = sub['duration'] / len(text_segments)
                
                print(f"分割字幕 {sub['index']}: {len(text_segments)} 段")
                print(f"  原文: {sub['text'][:50]}...")
                
                for i, segment_text in enumerate(text_segments):
                    segment_start = sub['start'] + i * segment_duration
                    segment_end = sub['start'] + (i + 1) * segment_duration
                    
                    new_subs.append({
                        'index': new_index,
                        'start': segment_start,
                        'end': segment_end,
                        'text': segment_text
                    })
                    print(f"  分段 {i+1}: {segment_text}")
                    new_index += 1
        
        # 写入新的SRT文件
        os.makedirs(os.path.dirname(output_srt) if os.path.dirname(output_srt) else '.', exist_ok=True)
        
        with open(output_srt, 'w', encoding='utf-8') as f:
            for sub in new_subs:
                f.write(f"{sub['index']}\n")
                f.write(f"{seconds_to_srt_time(sub['start'])} --> {seconds_to_srt_time(sub['end'])}\n")
                f.write(f"{sub['text']}\n\n")
        
        print(f"✅ 字幕分割完成!")
        print(f"📊 原始字幕: {len(original_subs)} 段")
        print(f"📊 分割后: {len(new_subs)} 段")
        print(f"📊 分割了: {split_count} 段长字幕")
        print(f"💾 输出文件: {output_srt}")
        
        return len(new_subs)
        
    except Exception as e:
        print(f"❌ 字幕分割失败: {e}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    print("🚀 开始字幕分割优化...")
    
    # 分割中文字幕
    chinese_input = "out/output_chinese.srt"
    chinese_output = "out/output_chinese_split.srt"
    
    print(f"📂 输入文件: {chinese_input}")
    print(f"📂 输出文件: {chinese_output}")
    
    if os.path.exists(chinese_input):
        result = split_subtitles(chinese_input, chinese_output, max_chars=20)
        if result > 0:
            print(f"🎉 字幕优化完成！生成了 {result} 段优化后的字幕")
        else:
            print("❌ 字幕分割失败")
    else:
        print(f"❌ 文件不存在: {chinese_input}")
        print("📁 当前目录内容:")
        for item in os.listdir('.'):
            print(f"  {item}")

if __name__ == "__main__":
    main() 