2025-07-19 10:16:38 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:16:38 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 164f34ef-97c2-46a4-8098-ad8b5428e144: 'Whisper' object has no attribute 'name'
2025-07-19 10:17:11 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:17:11 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task ca0723b8-2755-43b8-8efa-858d909f1318: 'Whisper' object has no attribute 'name'
2025-07-19 10:19:24 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:24:21 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:33:34 | ERROR    | app.services.video_service:compose_video:200 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:54:13 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:13 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:13 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:14 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:14 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:14 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:15 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:15 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:29 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:29 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:29 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:44 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:44 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:44 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:56 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:56 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:56 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:25 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 11:09:06 | ERROR    | app.services.video_service:compose_video:200 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 11:59:52 | ERROR    | app.services.video_service:compose_video:388 - Video composition failed: cannot access local variable 'os' where it is not associated with a value
2025-07-19 11:59:52 | ERROR    | app.services.task_service:process_full_pipeline:240 - Full pipeline failed for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993: cannot access local variable 'os' where it is not associated with a value
2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:226 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:273 - FFmpeg命令失败，返回码: 4294967294
2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:274 - FFmpeg错误输出: ffmpeg version 2025-07-01-git-11d1b71c31-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-liboapv --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-openal --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
  libavutil      60.  4.101 / 60.  4.101
  libavcodec     62.  4.103 / 62.  4.103
  libavformat    62.  1.101 / 62.  1.101
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
[in#0 @ 0000011defa6f940] Error opening input: No such file or directory
Error opening input file storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4.
Error opening input files: No such file or directory

2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:278 - FFmpeg备选方案也失败: Command '['ffmpeg', '-y', '-i', 'storage\\uploads\\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4', '-i', 'storage\\outputs\\tts_3ccce711.wav', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest', 'storage\\outputs\\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4']' returned non-zero exit status 4294967294.
2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:226 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:273 - FFmpeg命令失败，返回码: 4294967294
2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:274 - FFmpeg错误输出: ffmpeg version 2025-07-01-git-11d1b71c31-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-liboapv --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-openal --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
  libavutil      60.  4.101 / 60.  4.101
  libavcodec     62.  4.103 / 62.  4.103
  libavformat    62.  1.101 / 62.  1.101
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
[in#0 @ 00000231d67ff940] Error opening input: No such file or directory
Error opening input file storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4.
Error opening input files: No such file or directory

2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:278 - FFmpeg备选方案也失败: Command '['ffmpeg', '-y', '-i', 'storage\\uploads\\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4', '-i', 'storage\\outputs\\tts_8ebcf7c4.wav', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest', 'storage\\outputs\\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4']' returned non-zero exit status 4294967294.
2025-07-19 12:19:27 | ERROR    | app.services.video_service:compose_video:232 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
