package com.translation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * 百度翻译服务
 * 免费额度：每月200万字符
 */
@Service
public class BaiduTranslationService {
    private static final Logger logger = LoggerFactory.getLogger(BaiduTranslationService.class);
    
    @Value("${baidu.translate.app.id:}")
    private String appId;
    
    @Value("${baidu.translate.secret.key:}")
    private String secretKey;
    
    private final OkHttpClient client;
    private final ObjectMapper objectMapper;
    
    private static final String TRANSLATE_URL = "https://fanyi-api.baidu.com/api/trans/vip/translate";
    
    public BaiduTranslationService() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 检查服务是否已配置
     */
    public boolean isConfigured() {
        return appId != null && !appId.trim().isEmpty() && 
               !"your-baidu-translate-app-id".equals(appId) &&
               secretKey != null && !secretKey.trim().isEmpty() &&
               !"your-baidu-translate-secret-key".equals(secretKey);
    }
    
    /**
     * 翻译文本
     */
    public String translateText(String text, String sourceLanguage, String targetLanguage) throws IOException {
        if (!isConfigured()) {
            throw new IOException("百度翻译服务未配置：请设置 BAIDU_TRANSLATE_APP_ID 和 BAIDU_TRANSLATE_SECRET_KEY 环境变量");
        }
        
        logger.info("开始百度翻译，源语言: {}, 目标语言: {}", sourceLanguage, targetLanguage);
        
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 预处理：修正常见的语音识别错误
        text = preprocessText(text, sourceLanguage);
        
        // 生成随机数
        String salt = String.valueOf(System.currentTimeMillis());
        
        // 生成签名
        String sign = generateSign(text, salt);
        
        // 转换语言代码
        String from = convertLanguageCode(sourceLanguage);
        String to = convertLanguageCode(targetLanguage);
        
        RequestBody requestBody = new FormBody.Builder()
                .add("q", text)
                .add("from", from)
                .add("to", to)
                .add("appid", appId)
                .add("salt", salt)
                .add("sign", sign)
                .build();
        
        Request request = new Request.Builder()
                .url(TRANSLATE_URL)
                .post(requestBody)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("百度翻译调用失败: " + response.code());
            }
            
            String responseBody = response.body().string();
            logger.debug("百度翻译API响应: {}", responseBody);
            
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (jsonNode.has("error_code")) {
                String errorMsg = jsonNode.has("error_msg") ? jsonNode.get("error_msg").asText() : "未知错误";
                logger.error("百度翻译API错误: error_code={}, error_msg={}", 
                    jsonNode.get("error_code").asText(), errorMsg);
                throw new IOException("百度翻译失败: " + errorMsg);
            }
            
            if (jsonNode.has("trans_result") && jsonNode.get("trans_result").isArray()) {
                StringBuilder translatedText = new StringBuilder();
                for (JsonNode resultNode : jsonNode.get("trans_result")) {
                    translatedText.append(resultNode.get("dst").asText()).append(" ");
                }
                
                String result = translatedText.toString().trim();
                
                // 后处理：优化翻译结果
                result = postprocessTranslation(result, sourceLanguage, targetLanguage);
                
                logger.info("百度翻译完成，原文长度: {}, 译文长度: {}", text.length(), result.length());
                return result;
            }
            
            throw new IOException("百度翻译返回格式错误");
        }
    }
    
    /**
     * 生成签名
     */
    private String generateSign(String query, String salt) {
        String signStr = appId + query + salt + secretKey;
        return md5(signStr);
    }
    
    /**
     * MD5加密
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }
    
    /**
     * 转换语言代码为百度格式
     */
    private String convertLanguageCode(String languageCode) {
        if (languageCode == null || languageCode.trim().isEmpty()) {
            return "auto"; // 自动检测
        }
        
        switch (languageCode.toLowerCase()) {
            case "en":
            case "en-us":
                return "en";
            case "zh":
            case "zh-cn":
                return "zh";
            case "zh-tw":
                return "cht";
            case "ja":
                return "jp";
            case "ko":
                return "kor";
            case "fr":
                return "fra";
            case "de":
                return "de";
            case "es":
                return "spa";
            case "ru":
                return "ru";
            default:
                return "auto"; // 自动检测
        }
    }
    
    /**
     * 预处理文本：修正常见的语音识别错误
     */
    private String preprocessText(String text, String sourceLanguage) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }
        
        // 修正英文游戏开发相关的专业术语
        if ("en".equals(sourceLanguage) || "english".equals(sourceLanguage)) {
            text = text.replaceAll("\\bfazer\\b", "Phaser");
            text = text.replaceAll("\\bfaze\\b", "Phaser");
            text = text.replaceAll("\\bphase are\\b", "Phaser");
            text = text.replaceAll("\\bneo five\\b", "HTML5");
            text = text.replaceAll("\\beach neo five\\b", "HTML5");
            text = text.replaceAll("\\blease\\b", "create");
            text = text.replaceAll("\\bphase or\\b", "Phaser");
        }
        
        return text;
    }
    
    /**
     * 后处理翻译结果：优化翻译质量
     */
    private String postprocessTranslation(String translation, String sourceLanguage, String targetLanguage) {
        if (translation == null || translation.trim().isEmpty()) {
            return translation;
        }
        
        // 英文到中文的翻译优化
        if ("en".equals(sourceLanguage) && "zh".equals(targetLanguage)) {
            // 修正专业术语翻译
            translation = translation.replaceAll("fazer编辑器", "Phaser编辑器");
            translation = translation.replaceAll("fazer", "Phaser");
            translation = translation.replaceAll("三个游戏阶段", "Phaser 3游戏");
            translation = translation.replaceAll("第四个游戏阶段", "Phaser 4");
            translation = translation.replaceAll("neo-5", "HTML5");
            translation = translation.replaceAll("neo五", "HTML5");
            
            // 修正表达方式
            translation = translation.replaceAll("你处于正确的位置", "你来对地方了");
            translation = translation.replaceAll("如何租赁", "如何创建");
            translation = translation.replaceAll("租赁一个全新的项目", "创建一个全新的项目");
            translation = translation.replaceAll("构建每个", "构建");
            
            // 修正语法和表达
            translation = translation.replaceAll("，那么", "。");
            translation = translation.replaceAll("而不是迷失在代码中", "而不用陷入复杂的代码编写");
            translation = translation.replaceAll("这就是我们今天要介绍的内容", "今天我们将介绍以下内容");
        }
        
        return translation;
    }
    
    /**
     * 检测语言
     */
    public String detectLanguage(String text) {
        try {
            // 简单的语言检测逻辑
            if (text.matches(".*[\\u4e00-\\u9fa5].*")) {
                return "zh-CN"; // 包含中文字符
            } else if (text.matches(".*[\\u3040-\\u309f\\u30a0-\\u30ff].*")) {
                return "ja"; // 包含日文字符
            } else if (text.matches(".*[\\uac00-\\ud7af].*")) {
                return "ko"; // 包含韩文字符
            } else {
                return "en"; // 默认英文
            }
        } catch (Exception e) {
            logger.warn("语言检测失败，使用默认语言", e);
            return "en";
        }
    }
}