#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕添加器
使用ffmpeg为视频添加字幕，比MoviePy更稳定
"""

import os
import subprocess
import argparse

def add_subtitles_with_ffmpeg(video_path: str, subtitle_path: str, output_path: str, 
                              subtitle_style: str = None):
    """使用ffmpeg为视频添加字幕"""
    
    # 默认字幕样式 - 白色字体，黑色边框，底部居中
    if subtitle_style is None:
        subtitle_style = "FontName=Arial,FontSize=24,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2,Alignment=2"
    
    try:
        print(f"🎬 使用ffmpeg添加字幕...")
        print(f"📹 输入视频: {video_path}")
        print(f"📝 字幕文件: {subtitle_path}")
        print(f"💾 输出视频: {output_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # ffmpeg命令：添加字幕
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-vf', f"subtitles={subtitle_path}:force_style='{subtitle_style}'",
            '-c:a', 'copy',  # 音频直接复制，不重新编码
            '-y',  # 覆盖输出文件
            output_path
        ]
        
        print("🔄 正在处理...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 字幕添加成功: {output_path}")
            return True
        else:
            print(f"❌ ffmpeg错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 字幕添加失败: {str(e)}")
        return False

def add_dual_subtitles(video_path: str, chinese_srt: str, english_srt: str, output_path: str):
    """添加中英文双字幕"""
    
    try:
        print(f"🎬 添加中英文双字幕...")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 创建复杂的filter来同时显示两个字幕
        # 中文字幕在底部，英文字幕在上面一点
        filter_complex = f"[0:v]subtitles={chinese_srt}:force_style='FontSize=28,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2,Alignment=2,MarginV=30'[v1]; [v1]subtitles={english_srt}:force_style='FontSize=24,PrimaryColour=&Hffff00,OutlineColour=&H000000,Outline=2,Alignment=2,MarginV=80'[v2]"
        
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-filter_complex', filter_complex,
            '-map', '[v2]',
            '-map', '0:a',
            '-c:a', 'copy',
            '-y',
            output_path
        ]
        
        print("🔄 正在处理双字幕...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 双字幕添加成功: {output_path}")
            return True
        else:
            print(f"❌ ffmpeg错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 双字幕添加失败: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="字幕添加器 - 使用ffmpeg为视频添加字幕")
    parser.add_argument("--video", default="out/video_chinese_dubbed.mp4", help="输入视频文件")
    parser.add_argument("--chinese_srt", default="VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt", help="中文字幕文件")
    parser.add_argument("--english_srt", default="VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output.srt", help="英文字幕文件")
    parser.add_argument("--output", default="out/final_video_with_subtitles.mp4", help="输出视频文件")
    parser.add_argument("--mode", choices=["chinese", "english", "dual"], default="dual", help="字幕模式")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return
    
    print("🚀 开始添加字幕...")
    print(f"📹 输入视频: {args.video}")
    print(f"📝 字幕模式: {args.mode}")
    print(f"💾 输出视频: {args.output}")
    
    success = False
    
    if args.mode == "chinese":
        if os.path.exists(args.chinese_srt):
            success = add_subtitles_with_ffmpeg(
                args.video, 
                args.chinese_srt, 
                args.output,
                "FontSize=28,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2,Alignment=2"
            )
        else:
            print(f"❌ 中文字幕文件不存在: {args.chinese_srt}")
    
    elif args.mode == "english":
        if os.path.exists(args.english_srt):
            success = add_subtitles_with_ffmpeg(
                args.video, 
                args.english_srt, 
                args.output,
                "FontSize=24,PrimaryColour=&Hffff00,OutlineColour=&H000000,Outline=2,Alignment=2"
            )
        else:
            print(f"❌ 英文字幕文件不存在: {args.english_srt}")
    
    elif args.mode == "dual":
        if os.path.exists(args.chinese_srt) and os.path.exists(args.english_srt):
            success = add_dual_subtitles(args.video, args.chinese_srt, args.english_srt, args.output)
        else:
            print(f"❌ 字幕文件缺失:")
            if not os.path.exists(args.chinese_srt):
                print(f"   中文字幕: {args.chinese_srt}")
            if not os.path.exists(args.english_srt):
                print(f"   英文字幕: {args.english_srt}")
    
    if success:
        print(f"\n🎉 字幕添加完成！")
        print(f"📁 输出文件: {args.output}")
        print(f"🎬 现在你可以播放查看带字幕的中文配音视频了！")
        
        # 显示文件信息
        if os.path.exists(args.output):
            file_size = os.path.getsize(args.output) / (1024*1024)  # MB
            print(f"📊 文件大小: {file_size:.1f} MB")
    else:
        print("❌ 字幕添加失败")

if __name__ == "__main__":
    main() 