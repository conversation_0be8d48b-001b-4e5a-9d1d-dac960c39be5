#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的字幕添加器
解决字幕显示时间和位置问题
"""

import os
import subprocess
import argparse
import re

def parse_srt_file(srt_path):
    """解析SRT文件，返回字幕数据"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 按空行分割字幕块
    blocks = content.split('\n\n')
    
    for block in blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            # 序号
            index = lines[0]
            # 时间轴
            time_line = lines[1]
            # 字幕文本
            text = '\n'.join(lines[2:])
            
            # 解析时间
            time_match = re.match(r'(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})', time_line)
            if time_match:
                start_h, start_m, start_s, start_ms = map(int, time_match.groups()[:4])
                end_h, end_m, end_s, end_ms = map(int, time_match.groups()[4:])
                
                start_time = start_h * 3600 + start_m * 60 + start_s + start_ms / 1000
                end_time = end_h * 3600 + end_m * 60 + end_s + end_ms / 1000
                
                subtitles.append({
                    'index': int(index),
                    'start': start_time,
                    'end': end_time,
                    'text': text
                })
    
    return subtitles

def create_ass_subtitle(subtitles, output_path, style_name="Chinese", 
                       font_size=28, primary_color="&Hffffff", outline_color="&H000000",
                       margin_v=30):
    """创建ASS格式字幕文件，提供更精确的控制"""
    
    ass_content = f"""[Script Info]
Title: Generated Subtitles
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: {style_name},Arial,{font_size},{primary_color},&Hffffff,{outline_color},&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,{margin_v},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
    
    for sub in subtitles:
        # 转换时间格式为ASS格式 (h:mm:ss.cc)
        start_time = format_ass_time(sub['start'])
        end_time = format_ass_time(sub['end'])
        
        # 清理文本，避免特殊字符问题
        text = sub['text'].replace('\n', '\\N').replace('{', '{{').replace('}', '}}')
        
        ass_content += f"Dialogue: 0,{start_time},{end_time},{style_name},,0,0,0,,{text}\n"
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(ass_content)
    
    print(f"✅ 创建ASS字幕文件: {output_path}")
    return True

def format_ass_time(seconds):
    """将秒数转换为ASS时间格式 h:mm:ss.cc"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    centiseconds = int((seconds % 1) * 100)
    
    return f"{hours}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"

def add_subtitles_with_ass(video_path: str, subtitle_path: str, output_path: str):
    """使用ASS字幕格式添加字幕到视频"""
    
    try:
        print(f"🎬 使用ASS格式添加字幕...")
        print(f"📹 输入视频: {video_path}")
        print(f"📝 字幕文件: {subtitle_path}")
        print(f"💾 输出视频: {output_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 使用ass filter添加字幕
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-vf', f"ass={subtitle_path}",
            '-c:a', 'copy',  # 音频直接复制
            '-y',  # 覆盖输出文件
            output_path
        ]
        
        print("🔄 正在处理...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✅ 字幕添加成功: {output_path}")
            return True
        else:
            print(f"❌ ffmpeg错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 字幕添加失败: {str(e)}")
        return False

def add_dual_subtitles_with_ass(video_path: str, chinese_srt: str, english_srt: str, output_path: str):
    """使用ASS格式添加双字幕"""
    
    try:
        print(f"🎬 添加中英文双字幕 (ASS格式)...")
        
        # 解析字幕文件
        chinese_subs = parse_srt_file(chinese_srt)
        english_subs = parse_srt_file(english_srt)
        
        print(f"📝 中文字幕: {len(chinese_subs)} 条")
        print(f"📝 英文字幕: {len(english_subs)} 条")
        
        # 创建临时ASS文件
        temp_chinese_ass = "temp_chinese.ass"
        temp_english_ass = "temp_english.ass"
        
        # 中文字幕 - 白色，底部
        create_ass_subtitle(
            chinese_subs, 
            temp_chinese_ass, 
            "Chinese",
            font_size=16,  # 减小字体
            primary_color="&Hffffff",  # 白色
            margin_v=15  # 减小底部边距
        )
        
        # 英文字幕 - 黄色，稍微靠上
        create_ass_subtitle(
            english_subs, 
            temp_english_ass, 
            "English",
            font_size=14,  # 减小字体
            primary_color="&H00ffff",  # 黄色
            margin_v=40  # 减小底部边距（字幕更靠上）
        )
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 使用filter_complex同时应用两个ASS字幕
        filter_complex = f"[0:v]ass={temp_chinese_ass}[v1];[v1]ass={temp_english_ass}[v2]"
        
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-filter_complex', filter_complex,
            '-map', '[v2]',
            '-map', '0:a',
            '-c:a', 'copy',
            '-y',
            output_path
        ]
        
        print("🔄 正在处理双字幕...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        # 清理临时文件
        for temp_file in [temp_chinese_ass, temp_english_ass]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        if result.returncode == 0:
            print(f"✅ 双字幕添加成功: {output_path}")
            return True
        else:
            print(f"❌ ffmpeg错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 双字幕添加失败: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="改进的字幕添加器 - 解决字幕显示问题")
    parser.add_argument("--video", default="out/video_chinese_dubbed.mp4", help="输入视频文件")
    parser.add_argument("--chinese_srt", default="VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt", help="中文字幕文件")
    parser.add_argument("--english_srt", default="VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output.srt", help="英文字幕文件")
    parser.add_argument("--output", default="out/fixed_video_with_subtitles.mp4", help="输出视频文件")
    parser.add_argument("--mode", choices=["chinese", "english", "dual"], default="dual", help="字幕模式")
    parser.add_argument("--test", action="store_true", help="测试模式：只处理前10秒")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return
    
    print("🚀 开始添加字幕 (改进版)...")
    print(f"📹 输入视频: {args.video}")
    print(f"📝 字幕模式: {args.mode}")
    print(f"💾 输出视频: {args.output}")
    
    if args.test:
        print("⚠️ 测试模式：只处理前10秒")
    
    success = False
    
    if args.mode == "chinese":
        if os.path.exists(args.chinese_srt):
            chinese_subs = parse_srt_file(args.chinese_srt)
            temp_ass = "temp_chinese.ass"
            create_ass_subtitle(chinese_subs, temp_ass, "Chinese", 
                              font_size=12,  # 更小的字体
                              primary_color="&Hffffff",  # 白色
                              margin_v=10)  # 更小的底部边距
            success = add_subtitles_with_ass(args.video, temp_ass, args.output)
            if os.path.exists(temp_ass):
                os.remove(temp_ass)
        else:
            print(f"❌ 中文字幕文件不存在: {args.chinese_srt}")
    
    elif args.mode == "english":
        if os.path.exists(args.english_srt):
            english_subs = parse_srt_file(args.english_srt)
            temp_ass = "temp_english.ass"
            create_ass_subtitle(english_subs, temp_ass, "English", 
                              primary_color="&H00ffff", margin_v=80)
            success = add_subtitles_with_ass(args.video, temp_ass, args.output)
            if os.path.exists(temp_ass):
                os.remove(temp_ass)
        else:
            print(f"❌ 英文字幕文件不存在: {args.english_srt}")
    
    elif args.mode == "dual":
        if os.path.exists(args.chinese_srt) and os.path.exists(args.english_srt):
            success = add_dual_subtitles_with_ass(args.video, args.chinese_srt, args.english_srt, args.output)
        else:
            print(f"❌ 字幕文件缺失:")
            if not os.path.exists(args.chinese_srt):
                print(f"   中文字幕: {args.chinese_srt}")
            if not os.path.exists(args.english_srt):
                print(f"   英文字幕: {args.english_srt}")
    
    if success:
        print(f"\n🎉 字幕添加完成！")
        print(f"📁 输出文件: {args.output}")
        print(f"🎬 现在字幕应该正确按时间显示了！")
        
        # 显示文件信息
        if os.path.exists(args.output):
            file_size = os.path.getsize(args.output) / (1024*1024)  # MB
            print(f"📊 文件大小: {file_size:.1f} MB")
    else:
        print("❌ 字幕添加失败")

if __name__ == "__main__":
    main() 