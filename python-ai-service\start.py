#!/usr/bin/env python3
"""
启动脚本 - Video Translation AI Service
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_env_file():
    """创建.env文件（如果不存在）"""
    env_file = project_root / ".env"
    env_example = project_root / "env.example"
    
    if not env_file.exists() and env_example.exists():
        print("创建.env文件...")
        with open(env_example, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ .env文件已创建，请编辑配置后重新启动")
        return False
    
    return True

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import whisper
        import edge_tts
        import moviepy
        import librosa
        import openai
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动Video Translation AI Service")
    parser.add_argument("--host", default="0.0.0.0", help="服务器地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开发模式（自动重载）")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    parser.add_argument("--log-level", default="info", help="日志级别")
    
    args = parser.parse_args()
    
    print("🚀 启动Video Translation AI Service...")
    print(f"📁 项目目录: {project_root}")
    
    # 检查环境文件
    if not create_env_file():
        return
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 创建必要的目录
    storage_dirs = ["storage", "storage/temp", "storage/uploads", "storage/outputs", "logs"]
    for dir_name in storage_dirs:
        dir_path = project_root / dir_name
        dir_path.mkdir(parents=True, exist_ok=True)
    
    print("📂 存储目录已创建")
    
    # 启动服务
    try:
        import uvicorn
        from main import app
        
        print(f"🌐 服务将在 http://{args.host}:{args.port} 启动")
        print(f"📖 API文档: http://{args.host}:{args.port}/docs")
        print("按 Ctrl+C 停止服务")
        
        uvicorn.run(
            "main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers if not args.reload else 1,
            log_level=args.log_level,
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 