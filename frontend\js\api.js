/**
 * API客户端 - 处理与视频翻译AI服务的通信
 */

// API配置
const API_CONFIG = {
    baseUrl: 'http://localhost:8000',
    timeout: 30000, // 30秒超时
    retryAttempts: 3,
    retryDelay: 1000 // 1秒重试延迟
};

/**
 * API客户端类
 */
class APIClient {
    constructor(config = API_CONFIG) {
        this.baseUrl = config.baseUrl;
        this.timeout = config.timeout;
        this.retryAttempts = config.retryAttempts;
        this.retryDelay = config.retryDelay;
    }

    /**
     * 创建AbortController用于超时控制
     */
    createAbortController() {
        const controller = new AbortController();
        setTimeout(() => controller.abort(), this.timeout);
        return controller;
    }

    /**
     * 通用HTTP请求方法
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const controller = this.createAbortController();
        
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            signal: controller.signal,
            ...options
        };

        let lastError;
        for (let attempt = 0; attempt < this.retryAttempts; attempt++) {
            try {
                console.log(`尝试请求 (${attempt + 1}/${this.retryAttempts}): ${url}`);
                
                const response = await fetch(url, defaultOptions);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`HTTP ${response.status}: ${errorData.message || response.statusText}`);
                }
                
                const data = await response.json();
                console.log('请求成功:', data);
                return data;
                
            } catch (error) {
                lastError = error;
                console.warn(`请求失败 (尝试 ${attempt + 1}):`, error.message);
                
                if (error.name === 'AbortError') {
                    throw new Error('请求超时');
                }
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < this.retryAttempts - 1) {
                    await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                }
            }
        }
        
        throw lastError;
    }

    /**
     * GET请求
     */
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    /**
     * POST请求
     */
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * 上传文件
     */
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        // 添加其他数据
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        const controller = this.createAbortController();
        
        return this.request(endpoint, {
            method: 'POST',
            body: formData,
            headers: {}, // 让浏览器自动设置Content-Type
            signal: controller.signal
        });
    }

    // =============================================================
    // 具体的API方法
    // =============================================================

    /**
     * 健康检查
     */
    async healthCheck() {
        return this.get('/health');
    }

    /**
     * 完整翻译流程
     */
    async fullPipeline(data) {
        return this.post('/api/v1/pipeline/full', data);
    }

    /**
     * 音频提取
     */
    async extractAudio(data) {
        return this.post('/api/v1/audio/extract', data);
    }

    /**
     * 语音识别
     */
    async speechRecognition(data) {
        return this.post('/api/v1/speech/recognize', data);
    }

    /**
     * 文本翻译
     */
    async translateText(data) {
        return this.post('/api/v1/translation/translate', data);
    }

    /**
     * 字幕生成
     */
    async generateSubtitles(data) {
        return this.post('/api/v1/subtitle/generate', data);
    }

    /**
     * 语音合成 (TTS)
     */
    async textToSpeech(data) {
        return this.post('/api/v1/tts/generate', data);
    }

    /**
     * 获取可用的TTS语音列表
     */
    async getAvailableVoices() {
        return this.get('/api/v1/tts/voices');
    }

    /**
     * 视频合成
     */
    async composeVideo(data) {
        return this.post('/api/v1/video/compose', data);
    }

    /**
     * 获取任务状态
     */
    async getTaskStatus(taskId) {
        return this.get(`/api/v1/tasks/${taskId}`);
    }

    /**
     * 获取任务列表
     */
    async getTaskList(page = 1, perPage = 10) {
        return this.get(`/api/v1/tasks?page=${page}&per_page=${perPage}`);
    }

    /**
     * 取消任务
     */
    async cancelTask(taskId) {
        return this.post(`/api/v1/tasks/${taskId}/cancel`, {});
    }
}

/**
 * 文件上传管理器
 */
class FileUploadManager {
    constructor(apiClient) {
        this.apiClient = apiClient;
        this.uploadedFiles = new Map(); // 存储已上传的文件信息
    }

    /**
     * 上传文件到服务器
     */
    async uploadFile(file, onProgress = null) {
        try {
            console.log('开始上传文件:', file.name);
            const formData = new FormData();
            formData.append('file', file);

            const xhr = new XMLHttpRequest();
            
            return new Promise((resolve, reject) => {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable && onProgress) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });

                xhr.addEventListener('load', () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            console.log('文件上传成功:', response);
                            this.uploadedFiles.set(file.name, response);
                            resolve(response);
                        } catch (parseError) {
                            reject(new Error(`响应解析失败: ${parseError.message}`));
                        }
                    } else {
                        reject(new Error(`上传失败: HTTP ${xhr.status} ${xhr.statusText}`));
                    }
                });

                xhr.addEventListener('error', () => {
                    reject(new Error('上传过程中发生网络错误'));
                });

                xhr.addEventListener('timeout', () => {
                    reject(new Error('上传超时'));
                });

                xhr.open('POST', `${this.apiClient.baseUrl}/api/v1/upload`);
                xhr.timeout = 300000; // 5分钟超时
                xhr.send(formData);
            });
        } catch (error) {
            console.error('文件上传失败:', error);
            throw error;
        }
    }

    /**
     * 获取已上传文件的路径
     */
    getUploadedFilePath(fileName) {
        const fileInfo = this.uploadedFiles.get(fileName);
        return fileInfo ? fileInfo.file_path : null;
    }

    /**
     * 清理上传记录
     */
    clearUploadedFiles() {
        this.uploadedFiles.clear();
    }
}

/**
 * 任务轮询管理器
 */
class TaskPollingManager {
    constructor(apiClient) {
        this.apiClient = apiClient;
        this.pollingIntervals = new Map(); // 存储轮询间隔ID
        this.taskCallbacks = new Map(); // 存储任务回调函数
    }

    /**
     * 开始轮询任务状态
     */
    startPolling(taskId, callback, interval = 2000) {
        // 停止现有的轮询（如果存在）
        this.stopPolling(taskId);

        // 存储回调函数
        this.taskCallbacks.set(taskId, callback);

        // 开始轮询
        const intervalId = setInterval(async () => {
            try {
                const taskInfo = await this.apiClient.getTaskStatus(taskId);
                callback(taskInfo);

                // 如果任务完成或失败，停止轮询
                if (['completed', 'failed', 'cancelled'].includes(taskInfo.status)) {
                    this.stopPolling(taskId);
                }
            } catch (error) {
                console.error('获取任务状态失败:', error);
                callback({ error: error.message });
            }
        }, interval);

        this.pollingIntervals.set(taskId, intervalId);
    }

    /**
     * 停止轮询任务状态
     */
    stopPolling(taskId) {
        const intervalId = this.pollingIntervals.get(taskId);
        if (intervalId) {
            clearInterval(intervalId);
            this.pollingIntervals.delete(taskId);
            this.taskCallbacks.delete(taskId);
        }
    }

    /**
     * 停止所有轮询
     */
    stopAllPolling() {
        this.pollingIntervals.forEach((intervalId) => {
            clearInterval(intervalId);
        });
        this.pollingIntervals.clear();
        this.taskCallbacks.clear();
    }
}

/**
 * 创建全局API客户端实例
 */
const apiClient = new APIClient();
const fileUploadManager = new FileUploadManager(apiClient);
const taskPollingManager = new TaskPollingManager(apiClient);

// 导出API客户端（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        APIClient,
        FileUploadManager,
        TaskPollingManager,
        apiClient,
        fileUploadManager,
        taskPollingManager
    };
} 