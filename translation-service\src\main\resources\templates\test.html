<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多媒体翻译服务测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: #f8f9fa; 
        }
        .test-card {
            margin-bottom: 20px;
            transition: transform 0.2s;
        }
        .test-card:hover {
            transform: translateY(-2px);
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .btn-test {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- 导航栏 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-flask me-2"></i>
                            多媒体翻译服务测试中心
                        </h2>
                        <p class="card-text mt-2 mb-0">测试各项服务功能和API接口</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧测试面板 -->
            <div class="col-lg-8">
                <!-- 服务健康检查 -->
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-heartbeat me-2"></i>服务健康检查</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button onclick="testHealth()" class="btn btn-success btn-test">
                                    <i class="fas fa-check-circle me-1"></i>健康检查
                                </button>
                                <button onclick="testTranslationServices()" class="btn btn-info btn-test">
                                    <i class="fas fa-language me-1"></i>翻译服务
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button onclick="testTTSServices()" class="btn btn-warning btn-test">
                                    <i class="fas fa-volume-up me-1"></i>TTS服务
                                </button>
                                <button onclick="testAudioServices()" class="btn btn-secondary btn-test">
                                    <i class="fas fa-music me-1"></i>音频服务
                                </button>
                            </div>
                        </div>
                        <div id="healthResult"></div>
                    </div>
                </div>

                <!-- 翻译功能测试 -->
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-language me-2"></i>翻译功能测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group mb-3">
                                    <input type="text" id="testText" class="form-control" placeholder="输入测试文本" value="Hello, how are you?">
                                    <select id="testTargetLang" class="form-select" style="max-width: 150px;">
                                        <option value="zh">中文</option>
                                        <option value="en">英语</option>
                                        <option value="ja">日语</option>
                                        <option value="ko">韩语</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button onclick="testTranslation()" class="btn btn-primary">
                                    <i class="fas fa-play me-1"></i>测试翻译
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <button onclick="testTranslationProvider('baidu')" class="btn btn-outline-primary btn-sm btn-test">
                                    百度翻译
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button onclick="testTranslationProvider('google')" class="btn btn-outline-success btn-sm btn-test">
                                    Google翻译
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button onclick="testTranslationProvider('deepl')" class="btn btn-outline-info btn-sm btn-test">
                                    DeepL翻译
                                </button>
                            </div>
                        </div>
                        <div id="translationResult"></div>
                    </div>
                </div>

                <!-- TTS功能测试 -->
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-volume-up me-2"></i>语音合成测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group mb-3">
                                    <input type="text" id="ttsTestText" class="form-control" placeholder="输入要合成的文本" value="你好，这是语音合成测试。">
                                    <select id="ttsTestProvider" class="form-select" style="max-width: 150px;">
                                        <option value="edge">Edge TTS</option>
                                        <option value="baidu">百度TTS</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button onclick="testTTS()" class="btn btn-success">
                                    <i class="fas fa-play me-1"></i>测试TTS
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <button onclick="testTTSProviders()" class="btn btn-outline-warning btn-sm btn-test">
                                    <i class="fas fa-list me-1"></i>获取提供商
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button onclick="testTTSVoices()" class="btn btn-outline-info btn-sm btn-test">
                                    <i class="fas fa-microphone me-1"></i>获取语音列表
                                </button>
                            </div>
                        </div>
                        <div id="ttsResult"></div>
                    </div>
                </div>

                <!-- 视频翻译配音工作流 -->
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-video me-2"></i>视频翻译配音工作流</h5>
                        <small class="text-muted">完整的视频翻译配音流程：上传→识别→翻译→编辑→配音→合成</small>
                    </div>
                    <div class="card-body">
                        <!-- 步骤1: 上传视频 -->
                        <div class="workflow-step mb-4">
                            <h6><span class="badge bg-primary me-2">1</span>上传视频文件</h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <input type="file" id="workflowVideoFile" class="form-control" accept="video/*">
                                    <small class="text-muted">支持 MP4, AVI, MOV 等格式</small>
                                </div>
                                <div class="col-md-4">
                                    <button onclick="uploadWorkflowVideo()" class="btn btn-primary" id="uploadBtn">
                                        <i class="fas fa-upload me-1"></i>上传并分析
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤2: 语音识别设置 -->
                        <div class="workflow-step mb-4" id="asrStep" style="display:none;">
                            <h6><span class="badge bg-info me-2">2</span>语音识别设置</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">源语言:</label>
                                    <select id="sourceLanguage" class="form-select">
                                        <option value="zh">中文</option>
                                        <option value="en">英语</option>
                                        <option value="ja">日语</option>
                                        <option value="ko">韩语</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">识别服务:</label>
                                    <select id="asrService" class="form-select">
                                        <option value="baidu">百度语音识别</option>
                                        <option value="alibaba">阿里云智能语音</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">操作:</label><br>
                                    <button onclick="startASR()" class="btn btn-info" id="asrBtn">
                                        <i class="fas fa-microphone me-1"></i>开始识别
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤3: 翻译设置 -->
                        <div class="workflow-step mb-4" id="translateStep" style="display:none;">
                            <h6><span class="badge bg-success me-2">3</span>翻译设置</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">目标语言:</label>
                                    <select id="targetLanguage" class="form-select">
                                        <option value="zh">中文</option>
                                        <option value="en">英语</option>
                                        <option value="ja">日语</option>
                                        <option value="ko">韩语</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">翻译服务:</label>
                                    <select id="translateService" class="form-select">
                                        <option value="baidu">百度翻译</option>
                                        <option value="google">Google翻译</option>
                                        <option value="deepl">DeepL翻译</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">操作:</label><br>
                                    <button onclick="startTranslation()" class="btn btn-success" id="translateBtn">
                                        <i class="fas fa-language me-1"></i>开始翻译
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤4: 编辑翻译结果 -->
                        <div class="workflow-step mb-4" id="editStep" style="display:none;">
                            <h6><span class="badge bg-warning me-2">4</span>编辑翻译结果</h6>
                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label">翻译文本 (可编辑):</label>
                                    <textarea id="translatedText" class="form-control" rows="6" placeholder="翻译结果将显示在这里，您可以进行编辑..."></textarea>
                                    <small class="text-muted">您可以修改翻译结果以获得更好的配音效果</small>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <button onclick="confirmEdit()" class="btn btn-warning">
                                        <i class="fas fa-check me-1"></i>确认编辑
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button onclick="resetTranslation()" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo me-1"></i>重置翻译
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤5: 配音设置 -->
                        <div class="workflow-step mb-4" id="ttsStep" style="display:none;">
                            <h6><span class="badge bg-purple me-2">5</span>配音设置</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">TTS服务:</label>
                                    <select id="workflowTTSProvider" class="form-select">
                                        <option value="edge">Edge TTS</option>
                                        <option value="baidu">百度TTS</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">语音类型:</label>
                                    <select id="voiceType" class="form-select">
                                        <option value="zh-CN-XiaoxiaoNeural">小晓 (女声)</option>
                                        <option value="zh-CN-YunxiNeural">云希 (男声)</option>
                                        <option value="zh-CN-YunyangNeural">云扬 (男声)</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">操作:</label><br>
                                    <button onclick="generateDubbing()" class="btn btn-purple" id="dubbingBtn" style="background-color: #6f42c1; border-color: #6f42c1; color: white;">
                                        <i class="fas fa-volume-up me-1"></i>生成配音
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤6: 合成视频 -->
                        <div class="workflow-step mb-4" id="mergeStep" style="display:none;">
                            <h6><span class="badge bg-danger me-2">6</span>合成最终视频</h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="mb-2">准备将新的配音合成到原视频中...</p>
                                    <div class="progress mb-2" id="mergeProgress" style="display:none;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <button onclick="mergeVideoAudio()" class="btn btn-danger" id="mergeBtn">
                                        <i class="fas fa-magic me-1"></i>合成视频
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 工作流程状态 -->
                        <div class="workflow-status mt-4">
                            <div class="alert alert-info" id="workflowStatus">
                                <i class="fas fa-info-circle me-2"></i>请上传视频文件开始翻译配音流程
                            </div>
                        </div>

                        <!-- 结果展示 -->
                        <div id="workflowResult"></div>
                    </div>
                </div>

                <!-- 音频处理测试 -->
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-music me-2"></i>音频处理测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">测试视频文件:</label>
                                    <input type="file" id="testVideoFile" class="form-control" accept="video/*">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">测试音频文件:</label>
                                    <input type="file" id="testAudioFile" class="form-control" accept="audio/*">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">操作:</label>
                                    <div>
                                        <button onclick="testAudioExtract()" class="btn btn-outline-primary btn-sm btn-test">
                                            <i class="fas fa-extract me-1"></i>提取音频
                                        </button>
                                        <button onclick="testAudioDuration()" class="btn btn-outline-secondary btn-sm btn-test">
                                            <i class="fas fa-clock me-1"></i>获取时长
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="audioResult"></div>
                    </div>
                </div>

                <!-- 百度API测试 -->
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-key me-2"></i>百度API测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <button onclick="testBaiduApiStatus()" class="btn btn-outline-info btn-test">
                                    <i class="fas fa-info-circle me-1"></i>配置状态
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button onclick="testBaiduApiToken()" class="btn btn-outline-primary btn-test">
                                    <i class="fas fa-key me-1"></i>获取令牌
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button onclick="testBaiduApiConnection()" class="btn btn-outline-success btn-test">
                                    <i class="fas fa-link me-1"></i>连接测试
                                </button>
                            </div>
                        </div>
                        <div id="baiduApiResult"></div>
                    </div>
                </div>

                <!-- 批量测试 -->
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-tasks me-2"></i>批量测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button onclick="runAllTests()" class="btn btn-danger">
                                    <i class="fas fa-rocket me-1"></i>运行所有测试
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button onclick="clearAllResults()" class="btn btn-outline-secondary">
                                    <i class="fas fa-trash me-1"></i>清除所有结果
                                </button>
                            </div>
                        </div>
                        <div id="batchResult"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧日志面板 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-terminal me-2"></i>测试日志</h5>
                        <button onclick="clearLogs()" class="btn btn-sm btn-outline-secondary float-end">
                            <i class="fas fa-eraser me-1"></i>清除
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div id="testLogs" class="log-container">
                            === 测试日志 ===\n等待测试开始...
                        </div>
                    </div>
                </div>

                <!-- 测试统计 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>测试统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="text-success">
                                    <h4 id="successCount">0</h4>
                                    <small>成功</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h4 id="failCount">0</h4>
                                    <small>失败</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-info">
                                    <h4 id="totalCount">0</h4>
                                    <small>总计</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testStats = { success: 0, fail: 0, total: 0 };

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLogs');
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            logElement.textContent += `\n[${timestamp}] ${prefix} ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('testLogs').textContent = '=== 测试日志 ===\n等待测试开始...';
        }

        function updateStats() {
            document.getElementById('successCount').textContent = testStats.success;
            document.getElementById('failCount').textContent = testStats.fail;
            document.getElementById('totalCount').textContent = testStats.total;
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // 健康检查
        async function testHealth() {
            log('开始健康检查测试');
            testStats.total++;
            
            try {
                const response = await fetch('/api/translation/health');
                const data = await response.json();
                
                if (data.status === 'UP') {
                    testStats.success++;
                    log('健康检查通过', 'success');
                    showResult('healthResult', `✅ 服务状态: ${data.status}<br/>时间: ${data.timestamp}`, 'success');
                } else {
                    testStats.fail++;
                    log('健康检查失败', 'error');
                    showResult('healthResult', `❌ 服务状态异常: ${data.status}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`健康检查错误: ${error.message}`, 'error');
                showResult('healthResult', `❌ 健康检查失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试翻译服务
        async function testTranslationServices() {
            log('测试翻译服务可用性');
            testStats.total++;
            
            try {
                const response = await fetch('/api/translation/status');
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('翻译服务检查通过', 'success');
                    showResult('healthResult', `✅ 可用翻译服务: ${data.availableProviders.join(', ')}`, 'success');
                } else {
                    testStats.fail++;
                    log('翻译服务检查失败', 'error');
                    showResult('healthResult', `❌ 翻译服务检查失败`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`翻译服务检查错误: ${error.message}`, 'error');
                showResult('healthResult', `❌ 翻译服务检查失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试TTS服务
        async function testTTSServices() {
            log('测试TTS服务可用性');
            testStats.total++;
            
            try {
                const response = await fetch('/api/translation/tts/providers');
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('TTS服务检查通过', 'success');
                    showResult('healthResult', `✅ 可用TTS服务: ${data.providers.join(', ')}`, 'success');
                } else {
                    testStats.fail++;
                    log('TTS服务检查失败', 'error');
                    showResult('healthResult', `❌ TTS服务检查失败`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`TTS服务检查错误: ${error.message}`, 'error');
                showResult('healthResult', `❌ TTS服务检查失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试音频服务
        async function testAudioServices() {
            log('测试音频服务可用性');
            testStats.total++;
            
            // 这里可以添加音频服务的具体测试
            testStats.success++;
            log('音频服务检查通过', 'success');
            showResult('healthResult', `✅ 音频服务可用`, 'success');
            updateStats();
        }

        // 测试翻译功能
        async function testTranslation() {
            const text = document.getElementById('testText').value;
            const targetLang = document.getElementById('testTargetLang').value;
            
            if (!text.trim()) {
                alert('请输入测试文本');
                return;
            }
            
            log(`测试翻译: "${text}" -> ${targetLang}`);
            testStats.total++;
            
            try {
                const response = await fetch('/api/translation/translate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        text: text,
                        targetLanguage: targetLang
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('翻译测试成功', 'success');
                    showResult('translationResult', `✅ 翻译结果: ${data.translatedText}<br/>检测语言: ${data.detectedLanguage}`, 'success');
                } else {
                    testStats.fail++;
                    log('翻译测试失败', 'error');
                    showResult('translationResult', `❌ 翻译失败: ${data.error}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`翻译测试错误: ${error.message}`, 'error');
                showResult('translationResult', `❌ 翻译测试失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试指定翻译提供商
        async function testTranslationProvider(provider) {
            const text = document.getElementById('testText').value || 'Hello World';
            const targetLang = document.getElementById('testTargetLang').value;
            
            log(`测试${provider}翻译服务`);
            testStats.total++;
            
            try {
                const response = await fetch('/api/translation/translate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        text: text,
                        targetLanguage: targetLang,
                        provider: provider
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log(`${provider}翻译测试成功`, 'success');
                    showResult('translationResult', `✅ ${provider}翻译结果: ${data.translatedText}`, 'success');
                } else {
                    testStats.fail++;
                    log(`${provider}翻译测试失败`, 'error');
                    showResult('translationResult', `❌ ${provider}翻译失败: ${data.error}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`${provider}翻译测试错误: ${error.message}`, 'error');
                showResult('translationResult', `❌ ${provider}翻译测试失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试TTS
        async function testTTS() {
            const text = document.getElementById('ttsTestText').value;
            const provider = document.getElementById('ttsTestProvider').value;
            
            if (!text.trim()) {
                alert('请输入测试文本');
                return;
            }
            
            log(`测试${provider} TTS: "${text}"`);
            testStats.total++;
            
            try {
                const response = await fetch('/api/translation/tts/synthesize', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        text: text,
                        provider: provider
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('TTS测试成功', 'success');
                    showResult('ttsResult', `✅ TTS生成成功<br/><audio controls><source src="${data.audioUrl}" type="audio/mpeg"></audio>`, 'success');
                } else {
                    testStats.fail++;
                    log('TTS测试失败', 'error');
                    showResult('ttsResult', `❌ TTS生成失败: ${data.message}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`TTS测试错误: ${error.message}`, 'error');
                showResult('ttsResult', `❌ TTS测试失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试TTS提供商
        async function testTTSProviders() {
            log('获取TTS提供商列表');
            testStats.total++;
            
            try {
                const response = await fetch('/api/tts/providers');
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('获取TTS提供商成功', 'success');
                    showResult('ttsResult', `✅ TTS提供商: ${data.providers.join(', ')}`, 'success');
                } else {
                    testStats.fail++;
                    log('获取TTS提供商失败', 'error');
                    showResult('ttsResult', `❌ 获取TTS提供商失败`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`获取TTS提供商错误: ${error.message}`, 'error');
                showResult('ttsResult', `❌ 获取TTS提供商失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试TTS语音列表
        async function testTTSVoices() {
            const provider = document.getElementById('ttsTestProvider').value;
            
            log(`获取${provider}语音列表`);
            testStats.total++;
            
            try {
                const response = await fetch(`/api/translation/tts/voices?provider=${provider}`);
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('获取语音列表成功', 'success');
                    const voiceNames = data.voices.map(v => v.name || v.id).slice(0, 5);
                    showResult('ttsResult', `✅ ${provider}语音列表 (前5个): ${voiceNames.join(', ')}`, 'success');
                } else {
                    testStats.fail++;
                    log('获取语音列表失败', 'error');
                    showResult('ttsResult', `❌ 获取语音列表失败`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`获取语音列表错误: ${error.message}`, 'error');
                showResult('ttsResult', `❌ 获取语音列表失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试音频提取
        async function testAudioExtract() {
            const videoFile = document.getElementById('testVideoFile').files[0];
            
            if (!videoFile) {
                alert('请选择视频文件');
                return;
            }
            
            log(`测试音频提取: ${videoFile.name}`);
            testStats.total++;
            
            try {
                const formData = new FormData();
                formData.append('videoFile', videoFile);
                
                const response = await fetch('/api/translation/audio/extract', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('音频提取成功', 'success');
                    showResult('audioResult', `✅ 音频提取成功<br/><audio controls><source src="${data.audioUrl}" type="audio/wav"></audio>`, 'success');
                } else {
                    testStats.fail++;
                    log('音频提取失败', 'error');
                    showResult('audioResult', `❌ 音频提取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`音频提取错误: ${error.message}`, 'error');
                showResult('audioResult', `❌ 音频提取失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 测试音频时长
        async function testAudioDuration() {
            const audioFile = document.getElementById('testAudioFile').files[0];
            
            if (!audioFile) {
                alert('请选择音频文件');
                return;
            }
            
            log(`测试音频时长: ${audioFile.name}`);
            testStats.total++;
            
            try {
                // 上传文件到临时位置
                const formData = new FormData();
                formData.append('audioFile', audioFile);
                
                const uploadResponse = await fetch('/api/translation/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const uploadData = await uploadResponse.json();
                
                if (!uploadData.success) {
                    throw new Error(uploadData.message || '文件上传失败');
                }
                
                // 使用GET请求获取音频时长
                const response = await fetch(`/api/translation/audio/duration?audioPath=${encodeURIComponent(uploadData.filePath)}`, {
                    method: 'GET'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('获取音频时长成功', 'success');
                    showResult('audioResult', `✅ 音频时长: ${data.duration}秒`, 'success');
                } else {
                    testStats.fail++;
                    log('获取音频时长失败', 'error');
                    showResult('audioResult', `❌ 获取音频时长失败: ${data.message}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`获取音频时长错误: ${error.message}`, 'error');
                showResult('audioResult', `❌ 获取音频时长失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 运行所有测试
        async function runAllTests() {
            log('开始运行所有测试', 'info');
            showResult('batchResult', '🚀 正在运行所有测试...', 'info');
            
            // 重置统计
            testStats = { success: 0, fail: 0, total: 0 };
            updateStats();
            
            // 依次运行测试
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTranslationServices();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTTSServices();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTranslation();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTTSProviders();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            log('所有测试完成', 'success');
            showResult('batchResult', `✅ 批量测试完成<br/>成功: ${testStats.success}, 失败: ${testStats.fail}, 总计: ${testStats.total}`, 'success');
        }

        // 百度API状态检查
        async function testBaiduApiStatus() {
            log('检查百度API配置状态');
            testStats.total++;
            
            try {
                const response = await fetch('/api/baidu/status');
                const data = await response.json();
                
                if (data.configured) {
                    testStats.success++;
                    log('百度API已配置', 'success');
                    showResult('baiduApiResult', `✅ ${data.message}`, 'success');
                } else {
                    testStats.fail++;
                    log('百度API未配置', 'error');
                    const envVars = data.required_env ? data.required_env.join(', ') : '未知';
                    showResult('baiduApiResult', `❌ ${data.message}<br/>需要环境变量: ${envVars}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`百度API状态检查错误: ${error.message}`, 'error');
                showResult('baiduApiResult', `❌ 百度API状态检查失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 百度API令牌获取测试
        async function testBaiduApiToken() {
            log('测试百度API令牌获取');
            testStats.total++;
            
            try {
                const response = await fetch('/api/baidu/token');
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('百度API令牌获取成功', 'success');
                    const tokenPreview = data.access_token ? data.access_token.substring(0, 20) + '...' : '未知';
                    showResult('baiduApiResult', `✅ ${data.message}<br/>令牌预览: ${tokenPreview}`, 'success');
                } else {
                    testStats.fail++;
                    log('百度API令牌获取失败', 'error');
                    showResult('baiduApiResult', `❌ 令牌获取失败: ${data.error}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`百度API令牌获取错误: ${error.message}`, 'error');
                showResult('baiduApiResult', `❌ 百度API令牌获取失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 百度API连接测试
        async function testBaiduApiConnection() {
            log('测试百度API连接');
            testStats.total++;
            
            try {
                const response = await fetch('/api/baidu/test', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    testStats.success++;
                    log('百度API连接测试成功', 'success');
                    showResult('baiduApiResult', `✅ ${data.message}`, 'success');
                } else {
                    testStats.fail++;
                    log('百度API连接测试失败', 'error');
                    showResult('baiduApiResult', `❌ 连接测试失败: ${data.error}`, 'error');
                }
            } catch (error) {
                testStats.fail++;
                log(`百度API连接测试错误: ${error.message}`, 'error');
                showResult('baiduApiResult', `❌ 百度API连接测试失败: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // 清除所有结果
        function clearAllResults() {
            const resultElements = ['healthResult', 'translationResult', 'ttsResult', 'audioResult', 'baiduApiResult', 'batchResult'];
            resultElements.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            testStats = { success: 0, fail: 0, total: 0 };
            updateStats();
            clearLogs();
            log('已清除所有测试结果', 'info');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成', 'success');
            updateStats();
        });
    </script>
</body>
</html>