#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试音频合成过程
检查音频片段是否正确合成到最终音轨中
"""

import os
import re
from pydub import AudioSegment

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                # 尝试解析第一行为数字索引
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 检查时间行格式
                if ' --> ' in time_line:
                    # 解析时间
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
            except ValueError:
                continue
    
    return subtitles

def debug_audio_composition():
    """调试音频合成过程"""
    print("🔍 调试音频合成过程")
    
    # 文件路径
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt"
    audio_dir = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/audio"
    total_duration = 4242.68
    
    # 读取字幕文件
    subtitles = read_srt_file(srt_path)
    print(f"📝 读取到 {len(subtitles)} 条字幕")
    
    # 创建一个短时间的测试合成（前1分钟）
    test_duration = 60  # 1分钟测试
    print(f"🧪 创建{test_duration}秒的测试合成...")
    
    # 创建静音背景
    composite_audio = AudioSegment.silent(duration=int(test_duration * 1000))
    print(f"📊 创建了{test_duration}秒的静音背景")
    
    # 处理前1分钟内的字幕
    processed_count = 0
    for i, (index, start_time_sub, end_time_sub, text) in enumerate(subtitles):
        # 只处理前1分钟的字幕
        if start_time_sub >= test_duration:
            break
            
        # 查找对应的音频文件
        audio_file = os.path.join(audio_dir, f"subtitle_{index}.wav")
        
        if os.path.exists(audio_file):
            try:
                # 加载TTS音频（MP3格式但扩展名为wav）
                try:
                    tts_audio = AudioSegment.from_wav(audio_file)
                except Exception:
                    tts_audio = AudioSegment.from_mp3(audio_file)
                
                tts_duration = len(tts_audio) / 1000.0
                subtitle_duration = end_time_sub - start_time_sub
                
                print(f"🎵 [{i+1}] 字幕{index}: {start_time_sub:.2f}-{end_time_sub:.2f}s (时长{subtitle_duration:.2f}s), 音频{tts_duration:.2f}s")
                
                # 调整音频时长
                if tts_duration > subtitle_duration:
                    speed_factor = tts_duration / subtitle_duration
                    if speed_factor > 2.5:
                        speed_factor = 2.5
                    tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                    print(f"    加速{speed_factor:.2f}倍")
                
                # 将TTS音频插入到指定时间位置
                start_ms = int(start_time_sub * 1000)
                
                # 确保在测试时间范围内
                if start_ms < test_duration * 1000:
                    # 确保不超出测试时长
                    if start_ms + len(tts_audio) > len(composite_audio):
                        tts_audio = tts_audio[:len(composite_audio) - start_ms]
                    
                    # 叠加音频
                    print(f"    叠加到位置{start_ms}ms")
                    composite_audio = composite_audio.overlay(tts_audio, position=start_ms)
                    processed_count += 1
                
            except Exception as e:
                print(f"❌ 处理音频失败 {audio_file}: {str(e)}")
                continue
        else:
            print(f"⚠️  音频文件不存在: subtitle_{index}.wav")
    
    print(f"✅ 处理了 {processed_count} 个音频片段")
    
    # 分析合成结果
    print("🔍 分析合成结果...")
    
    # 检查音频数据
    audio_samples = composite_audio.get_array_of_samples()
    import numpy as np
    audio_array = np.array(audio_samples)
    
    max_amplitude = np.max(np.abs(audio_array))
    non_zero_samples = np.count_nonzero(audio_array)
    total_samples = len(audio_array)
    
    print(f"📊 最大振幅: {max_amplitude}")
    print(f"📊 非零样本: {non_zero_samples}/{total_samples} ({non_zero_samples/total_samples*100:.2f}%)")
    
    # 导出测试音频
    test_output = "debug_audio_test.wav"
    composite_audio.export(test_output, format="wav")
    print(f"💾 测试音频已保存: {test_output}")
    
    # 导出完整合成音频的一小段进行对比
    if os.path.exists("temp_full_video_audio_complete.wav"):
        print("🔍 分析完整合成音频...")
        full_audio = AudioSegment.from_wav("temp_full_video_audio_complete.wav")
        
        # 提取前1分钟
        full_test = full_audio[:test_duration * 1000]
        full_samples = full_test.get_array_of_samples()
        full_array = np.array(full_samples)
        
        full_max = np.max(np.abs(full_array))
        full_non_zero = np.count_nonzero(full_array)
        full_total = len(full_array)
        
        print(f"📊 完整音频最大振幅: {full_max}")
        print(f"📊 完整音频非零样本: {full_non_zero}/{full_total} ({full_non_zero/full_total*100:.2f}%)")
        
        # 导出对比
        full_test.export("debug_full_audio_test.wav", format="wav")
        print(f"💾 完整音频测试段已保存: debug_full_audio_test.wav")

def main():
    """主函数"""
    print("🎯 音频合成调试器")
    debug_audio_composition()

if __name__ == "__main__":
    main() 