#!/usr/bin/env python3
"""
测试FFmpeg路径问题修复
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def test_path_resolution():
    """测试路径解析"""
    try:
        print("🔍 测试路径解析...")
        
        # 测试相对路径转绝对路径
        test_paths = [
            "storage/uploads/test.mp4",
            "storage\\uploads\\test.mp4",
            "./storage/outputs/test.wav",
            "python-ai-service/storage/uploads/test.mp4"
        ]
        
        for path in test_paths:
            abs_path = os.path.abspath(path)
            print(f"  相对路径: {path}")
            print(f"  绝对路径: {abs_path}")
            print(f"  存在: {os.path.exists(abs_path)}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 路径解析测试失败: {e}")
        return False

async def test_file_existence():
    """测试文件存在性检查"""
    try:
        print("📁 测试文件存在性检查...")
        
        # 检查关键目录
        directories = [
            "python-ai-service/storage",
            "python-ai-service/storage/uploads",
            "python-ai-service/storage/outputs"
        ]
        
        for dir_path in directories:
            abs_dir = os.path.abspath(dir_path)
            exists = os.path.exists(abs_dir)
            print(f"  目录: {abs_dir}")
            print(f"  存在: {exists}")
            
            if exists:
                # 列出文件
                try:
                    files = list(Path(abs_dir).glob("*"))
                    print(f"  文件数量: {len(files)}")
                    for file in files[:3]:  # 只显示前3个
                        print(f"    - {file.name}")
                    if len(files) > 3:
                        print(f"    ... 还有 {len(files) - 3} 个文件")
                except Exception as e:
                    print(f"  列出文件失败: {e}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 文件存在性检查失败: {e}")
        return False

async def test_ffmpeg_command_construction():
    """测试FFmpeg命令构建"""
    try:
        print("🎬 测试FFmpeg命令构建...")
        
        # 模拟文件路径
        video_path = "storage/uploads/test.mp4"
        audio_path = "storage/outputs/test.wav"
        output_path = "storage/outputs/test_output.mp4"
        
        # 转换为绝对路径
        abs_video_path = os.path.abspath(video_path)
        abs_audio_path = os.path.abspath(audio_path)
        abs_output_path = os.path.abspath(output_path)
        
        print(f"输入视频: {abs_video_path}")
        print(f"输入音频: {abs_audio_path}")
        print(f"输出文件: {abs_output_path}")
        
        # 构建FFmpeg命令
        ffmpeg_cmd = [
            'ffmpeg', '-y',
            '-i', abs_video_path,
            '-i', abs_audio_path,
            '-c:v', 'copy',
            '-c:a', 'aac',
            '-map', '0:v:0',
            '-map', '1:a:0',
            '-shortest',
            abs_output_path
        ]
        
        print(f"\nFFmpeg命令:")
        print(f"  {' '.join(ffmpeg_cmd)}")
        
        # 检查路径中是否有问题字符
        for i, arg in enumerate(ffmpeg_cmd):
            if '\\' in arg and not arg.startswith('-'):
                print(f"  参数 {i}: {arg} (包含反斜杠)")
            elif '/' in arg and not arg.startswith('-'):
                print(f"  参数 {i}: {arg} (包含正斜杠)")
        
        return True
        
    except Exception as e:
        print(f"❌ FFmpeg命令构建测试失败: {e}")
        return False

async def test_video_service_paths():
    """测试VideoService中的路径处理"""
    try:
        print("🎥 测试VideoService路径处理...")
        
        from app.services.video_service import VideoService
        
        # 初始化服务
        video_service = VideoService()
        print("✅ VideoService初始化成功")
        
        # 检查输出目录
        output_dir = video_service.output_dir
        abs_output_dir = os.path.abspath(str(output_dir))
        print(f"输出目录: {abs_output_dir}")
        print(f"输出目录存在: {os.path.exists(abs_output_dir)}")
        
        # 检查临时目录
        temp_dir = video_service.temp_dir
        abs_temp_dir = os.path.abspath(str(temp_dir))
        print(f"临时目录: {abs_temp_dir}")
        print(f"临时目录存在: {os.path.exists(abs_temp_dir)}")
        
        return True
        
    except Exception as e:
        print(f"❌ VideoService路径测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_working_directory():
    """测试工作目录"""
    try:
        print("📂 测试工作目录...")
        
        cwd = os.getcwd()
        print(f"当前工作目录: {cwd}")
        
        # 检查是否在正确的项目目录中
        expected_files = [
            "python-ai-service",
            "frontend",
            "test_ffmpeg_path_fix.py"
        ]
        
        for file in expected_files:
            exists = os.path.exists(file)
            print(f"  {file}: {'✅' if exists else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作目录测试失败: {e}")
        return False

async def main():
    print("🚀 开始FFmpeg路径问题修复测试...")
    
    # 1. 测试工作目录
    wd_ok = await test_working_directory()
    
    # 2. 测试路径解析
    path_ok = await test_path_resolution()
    
    # 3. 测试文件存在性
    file_ok = await test_file_existence()
    
    # 4. 测试FFmpeg命令构建
    cmd_ok = await test_ffmpeg_command_construction()
    
    # 5. 测试VideoService路径处理
    service_ok = await test_video_service_paths()
    
    print(f"\n📊 测试结果总结:")
    print(f"  工作目录: {'✅ 正常' if wd_ok else '❌ 异常'}")
    print(f"  路径解析: {'✅ 正常' if path_ok else '❌ 异常'}")
    print(f"  文件检查: {'✅ 正常' if file_ok else '❌ 异常'}")
    print(f"  命令构建: {'✅ 正常' if cmd_ok else '❌ 异常'}")
    print(f"  服务路径: {'✅ 正常' if service_ok else '❌ 异常'}")
    
    if all([wd_ok, path_ok, file_ok, cmd_ok, service_ok]):
        print("\n🎉 FFmpeg路径修复测试成功!")
        print("现在FFmpeg应该能够正确找到输入文件了")
        print("\n建议:")
        print("1. 重启AI服务")
        print("2. 重新运行完整翻译流程")
        print("3. 检查FFmpeg是否能正确处理文件")
    else:
        print("\n⚠️ 路径处理仍有问题")
        if not wd_ok:
            print("- 工作目录不正确")
        if not path_ok:
            print("- 路径解析有问题")
        if not file_ok:
            print("- 文件检查有问题")
        if not cmd_ok:
            print("- 命令构建有问题")
        if not service_ok:
            print("- 服务路径配置有问题")

if __name__ == "__main__":
    asyncio.run(main())
