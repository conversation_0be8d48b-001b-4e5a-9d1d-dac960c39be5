# 🔧 视频翻译问题修复指南

## 🚨 **问题诊断**

您的视频翻译遇到了以下问题：

### ❌ **TTS语音失败**
- **原因**：使用了无效的语音选项 `"local"`
- **结果**：没有生成中文语音，只有fallback文本
- **文件**：`tts/chinese_fallback.txt` 而不是 `tts/chinese.wav`

### ❌ **字幕未显示**
- **原因**：`burn_subtitles: false`，字幕没有烧录到视频中
- **结果**：虽然生成了字幕文件，但视频中看不到
- **文件**：字幕存在于 `subtitles/translated.srt`，但未嵌入视频

### ✅ **工作正常的部分**
- 语音识别：成功提取英文文本
- 翻译：成功翻译成中文
- 字幕生成：成功生成中文字幕文件

## 🎯 **立即修复方案**

### 方案1：使用修复脚本（推荐）

```bash
# 1. 查看需要修复的任务
python fix_video_translation.py

# 2. 修复指定任务
python fix_video_translation.py 20250719_1541_8bfd5a73-d2c7-43af-8048-c09bbae258b0_test_30s
```

### 方案2：重新运行翻译（正确参数）

使用正确的API参数重新翻译：

```json
{
    "video_file_path": "your_video.mp4",
    "source_language": "en",
    "target_language": "zh-CN",
    "include_subtitles": true,
    "burn_subtitles": true,
    "tts_voice": "zh-CN-XiaoxiaoNeural"
}
```

### 方案3：手动修复

如果您想手动修复，可以：

1. **重新生成TTS**：
   ```bash
   # 使用正确的语音重新生成TTS
   # 将 translated.txt 的内容用正确的TTS语音合成
   ```

2. **重新合成视频**：
   ```bash
   # 使用FFmpeg手动合成
   ffmpeg -i original.mp4 -i chinese.wav -vf "subtitles=translated.srt" -c:a aac -c:v libx264 final_video_fixed.mp4
   ```

## 🔧 **正确的TTS语音选项**

### 推荐的中文语音：
- `"zh-CN-XiaoxiaoNeural"` - 女声，清晰自然（推荐）
- `"zh-CN-YunyangNeural"` - 男声，专业
- `"zh-CN-YunxiNeural"` - 男声，年轻
- `"zh-CN-XiaoyiNeural"` - 女声，温和

### ❌ 无效的语音选项：
- `"local"` - 不存在
- `"chinese"` - 不存在
- `"zh"` - 不完整

## 🎬 **字幕烧录选项**

### 软字幕 vs 硬字幕

| 特性 | 软字幕 (`burn_subtitles: false`) | 硬字幕 (`burn_subtitles: true`) |
|------|--------------------------------|--------------------------------|
| **显示** | 需要播放器支持 | ✅ 始终显示 |
| **编辑** | 可以编辑SRT文件 | 不可编辑 |
| **兼容性** | 依赖播放器 | ✅ 通用兼容 |
| **文件大小** | 小 | 稍大 |
| **社交媒体** | 可能不显示 | ✅ 始终显示 |

**建议**：使用 `burn_subtitles: true` 确保字幕始终显示。

## 🚀 **修复步骤详解**

### Step 1: 运行修复脚本
```bash
python fix_video_translation.py
```

这会：
- 🔍 自动检测失败的任务
- 📋 显示需要修复的任务列表

### Step 2: 修复指定任务
```bash
python fix_video_translation.py [任务名]
```

这会：
- 🎵 使用正确的TTS语音重新生成中文语音
- 🎬 重新合成视频，烧录字幕
- 📋 更新任务元数据
- 💾 生成 `final_video_fixed.mp4`

### Step 3: 验证结果
修复后的视频应该包含：
- ✅ 中文TTS语音
- ✅ 烧录的中文字幕
- ✅ 与原视频相同的画质

## 📍 **修复后的文件位置**

```
任务目录/
├── tts/
│   ├── chinese.wav              # ✅ 新生成的中文语音
│   └── chinese_fallback.txt     # 旧的失败文件
├── output/
│   ├── final_video.mp4          # 原始的失败视频
│   └── final_video_fixed.mp4    # 🎯 修复后的视频
└── metadata.json                # 更新的元数据
```

## 🎉 **预期结果**

修复成功后，您应该看到：

```
🎉 修复完成!
📍 修复后的视频位置: .../final_video_fixed.mp4
🎬 现在视频应该包含:
   ✅ 中文TTS语音
   ✅ 烧录的中文字幕
```

## 🔍 **故障排除**

### 如果TTS仍然失败：
1. 检查网络连接
2. 确认Edge-TTS服务可用
3. 尝试不同的语音选项

### 如果字幕不显示：
1. 确认 `burn_subtitles: true`
2. 检查字幕文件格式
3. 验证FFmpeg支持subtitles滤镜

### 如果视频合成失败：
1. 检查FFmpeg安装
2. 确认输入文件存在
3. 查看详细错误日志

## 📞 **获取帮助**

如果修复脚本无法解决问题：

1. **查看详细日志**：
   ```bash
   python fix_video_translation.py [任务名] 2>&1 | tee fix.log
   ```

2. **检查任务状态**：
   ```bash
   python find_latest_video.py [任务名]
   ```

3. **手动验证文件**：
   - 检查翻译文本是否正确
   - 验证字幕文件格式
   - 确认TTS语音选项

---

通过这个修复方案，您的视频翻译应该能够正常工作，包含中文语音和字幕！🎬✨
