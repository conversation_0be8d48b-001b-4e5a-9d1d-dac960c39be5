#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复视频音频音量
增强完整版视频的音量，使其更易听清
"""

import os
from moviepy import VideoFileClip, AudioFileClip
from pydub import AudioSegment

def fix_audio_volume(input_path, output_path, volume_db=10):
    """修复视频音频音量"""
    print(f"🎵 修复音频音量: {input_path}")
    print(f"📈 音量增强: +{volume_db}dB")
    
    if not os.path.exists(input_path):
        print(f"❌ 输入文件不存在: {input_path}")
        return False
    
    try:
        # 加载视频
        print("📹 加载原视频...")
        video = VideoFileClip(input_path)
        
        if video.audio is None:
            print("❌ 视频没有音频轨道")
            video.close()
            return False
        
        print(f"📊 原视频时长: {video.duration:.2f}秒")
        
        # 导出原音频到临时文件
        temp_audio_path = "temp_original_audio.wav"
        print("🎵 导出原音频...")
        video.audio.write_audiofile(temp_audio_path)
        
        # 使用pydub增强音量
        print("🔊 使用pydub增强音频音量...")
        audio_segment = AudioSegment.from_wav(temp_audio_path)
        enhanced_audio_segment = audio_segment + volume_db  # 增加dB
        
        # 导出增强后的音频
        enhanced_audio_path = "temp_enhanced_audio.wav"
        enhanced_audio_segment.export(enhanced_audio_path, format="wav")
        
        # 加载增强后的音频
        print("🎧 加载增强后的音频...")
        enhanced_audio = AudioFileClip(enhanced_audio_path)
        
        # 应用增强的音频到视频
        print("🎬 应用增强音频...")
        final_video = video.with_audio(enhanced_audio)
        
        # 输出视频
        print(f"💾 保存增强版视频: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            bitrate='2000k',
            audio_bitrate='128k'
        )
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        if os.path.exists(enhanced_audio_path):
            os.remove(enhanced_audio_path)
        
        # 清理资源
        video.close()
        enhanced_audio.close()
        final_video.close()
        
        print(f"✅ 音频音量修复完成！")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"📊 输出文件大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 视频音频音量修复器")
    
    # 修复完整版视频的音量
    input_file = "chinese_video_full_complete.mp4"
    output_file = "chinese_video_full_enhanced.mp4"
    
    success = fix_audio_volume(input_file, output_file, volume_db=12)
    
    if success:
        print("✅ 任务完成！")
        print(f"📁 增强版视频: {output_file}")
        print("🎧 请测试新视频的音频是否正常")
    else:
        print("❌ 任务失败！")

if __name__ == "__main__":
    main() 