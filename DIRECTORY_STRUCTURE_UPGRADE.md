# 📁 目录结构升级方案

## 🎯 **新的目录结构**

### 当前结构（问题）
```
python-ai-service/storage/
├── uploads/
│   ├── file1.mp4
│   ├── file2.mp4
│   └── ...
└── outputs/
    ├── file1_audio.wav
    ├── file1_translated.mp4
    ├── file2_audio.wav
    ├── tts_xxxxx.wav
    ├── tts_yyyyy.wav
    └── ... (所有文件混在一起)
```

### 新结构（清晰）
```
python-ai-service/storage/outputs/
├── 20250719_1230_test_video/
│   ├── source/              # 原始上传文件
│   │   └── test_video.mp4
│   ├── audio/               # 提取的音频
│   │   ├── original.wav     # 原始音频
│   │   └── segments/        # 音频分段（如果需要）
│   ├── transcription/       # 语音识别结果
│   │   ├── original.txt     # 原始文本
│   │   └── segments.json    # 分段文本（带时间戳）
│   ├── translation/         # 翻译结果
│   │   ├── translated.txt   # 翻译后文本
│   │   └── metadata.json    # 翻译元数据
│   ├── tts/                # TTS生成的音频
│   │   ├── chinese.wav     # 中文语音
│   │   └── fallback.txt    # 回退文本（如果TTS失败）
│   ├── subtitles/          # 字幕文件
│   │   ├── original.srt    # 原始字幕
│   │   └── translated.srt  # 翻译字幕
│   ├── output/             # 最终输出
│   │   └── final_video.mp4 # 最终翻译视频
│   ├── logs/               # 任务专用日志
│   │   └── task.log
│   ├── temp/               # 临时文件
│   └── metadata.json       # 任务元数据
└── 20250719_1245_another_video/
    └── ... (相同结构)
```

## 🎉 **优势**

### 1. **组织清晰**
- 每个任务有独立目录
- 文件类型分类存储
- 时间戳便于排序和查找

### 2. **易于管理**
- 可以单独删除某个任务的所有文件
- 便于备份和恢复
- 支持批量操作

### 3. **调试友好**
- 每个任务有专用日志
- 中间文件都保留
- 便于问题追踪

### 4. **扩展性好**
- 容易添加新的文件类型
- 支持复杂的处理流程
- 便于并行处理

## 🛠️ **实现方案**

### 1. **DirectoryService使用示例**

```python
from app.services.directory_service import DirectoryService

# 初始化服务
dir_service = DirectoryService()

# 创建任务目录
paths = dir_service.create_task_directory("test_video.mp4", "task_123")

# 获取路径
source_dir = paths['source']      # 存放原始文件
audio_dir = paths['audio']        # 存放音频文件
tts_dir = paths['tts']           # 存放TTS文件
output_dir = paths['output']      # 存放最终输出

# 复制文件到指定目录
dir_service.copy_file_to_task_dir(
    source_path="uploads/video.mp4",
    task_name=paths['task_name'],
    target_subdir="source",
    new_filename="original.mp4"
)

# 获取文件路径
tts_file = dir_service.get_file_path(
    task_name=paths['task_name'],
    subdir="tts",
    filename="chinese.wav"
)

# 更新任务状态
dir_service.update_task_metadata(
    task_name=paths['task_name'],
    updates={
        'status': 'completed',
        'duration': 30.5,
        'language_from': 'en',
        'language_to': 'zh'
    }
)
```

### 2. **集成到现有服务**

需要修改的服务：
- `TaskService` - 使用新的目录结构
- `VideoService` - 更新文件路径
- `AudioService` - 更新文件路径
- `TTSService` - 更新文件路径
- `TranslationService` - 保存翻译结果

## 📋 **迁移步骤**

### 1. **准备阶段**
```bash
# 备份现有数据
cp -r python-ai-service/storage python-ai-service/storage_backup

# 创建新的目录结构测试
mkdir -p python-ai-service/storage/outputs_new
```

### 2. **代码修改**
1. 集成 `DirectoryService`
2. 修改各个服务使用新路径
3. 更新API返回的文件路径
4. 修改前端文件访问路径

### 3. **测试阶段**
1. 使用新结构处理测试文件
2. 验证所有功能正常
3. 检查文件路径正确性

### 4. **正式迁移**
1. 停止服务
2. 迁移现有文件（可选）
3. 启用新的目录结构
4. 重启服务

## 🔧 **配置建议**

### 1. **环境变量**
```bash
# 存储根目录
STORAGE_ROOT=python-ai-service/storage

# 是否启用新目录结构
USE_STRUCTURED_DIRECTORIES=true

# 自动清理临时文件
AUTO_CLEANUP_TEMP=true

# 保留中间文件天数
KEEP_INTERMEDIATE_FILES_DAYS=7
```

### 2. **清理策略**
```python
# 自动清理配置
CLEANUP_POLICY = {
    'temp_files': 'immediate',      # 立即清理临时文件
    'intermediate_files': '7_days', # 7天后清理中间文件
    'output_files': 'never',        # 永不清理输出文件
    'failed_tasks': '1_day'         # 1天后清理失败任务
}
```

## 📊 **元数据示例**

```json
{
  "task_id": "21d3f806-279a-43fc-afac-770e2f3524a9",
  "original_filename": "test_video.mp4",
  "created_at": "2025-07-19T12:30:00",
  "updated_at": "2025-07-19T12:35:00",
  "task_name": "20250719_1230_test_video",
  "status": "completed",
  "duration": 30.5,
  "language_from": "en",
  "language_to": "zh",
  "processing_time": 120.5,
  "file_sizes": {
    "source": 15728640,
    "audio": 2341542,
    "tts": 2341542,
    "output": 15934182
  },
  "paths": {
    "task_dir": "/path/to/20250719_1230_test_video",
    "source": "/path/to/20250719_1230_test_video/source",
    "audio": "/path/to/20250719_1230_test_video/audio",
    "tts": "/path/to/20250719_1230_test_video/tts",
    "output": "/path/to/20250719_1230_test_video/output"
  }
}
```

## 🎯 **下一步**

1. **审查目录结构设计**
2. **确认需要的修改范围**
3. **制定详细的迁移计划**
4. **开始集成DirectoryService**

这个新的目录结构将大大改善文件管理的混乱状况，让系统更加专业和易于维护！
