{"a. Download or Upload Video": "a. 下載或上傳影片", "Delete and Reselect": "刪除並重新選擇", "Enter YouTube link:": "輸入YouTube連結:", "Resolution": "解析度", "Download Video": "下載影片", "Or upload video": "或上傳影片", "Youtube Settings": "Youtube設定", "Cookies Path": "Cookies文件路徑", "LLM Configuration": "LLM設定", "API_KEY": "API金鑰", "BASE_URL": "BASE_URL", "MODEL": "模型", "Openai format, will add /v1/chat/completions automatically": "OpenAI格式,將自動添加/v1/chat/completions", "click to check API validity": "點擊檢查API有效性", "API Key is valid": "API金鑰有效", "API Key is invalid": "API金鑰無效", "Recog Lang": "識別語言", "Subtitles Settings": "字幕設定", "Target Lang": "目標語言", "Input any language in natural language, as long as llm can understand": "用自然語言輸入任何語言,只要LLM能理解即可", "Vocal separation enhance": "人聲分離增強", "Burn-in Subtitles": "燒錄字幕", "Whether to burn subtitles into the video, will increase processing time": "是否將字幕燒錄到影片中，會增加處理時間", "Video Resolution": "影片解析度", "Recommended for videos with loud background noise, but will increase processing time": "建議用於背景噪音較大的影片,但會增加處理時間", "Dubbing Settings": "配音設定", "TTS Method": "TTS方法", "SiliconFlow API Key": "SiliconFlow API金鑰", "Mode Selection": "模式選擇", "Preset": "預設", "Refer_stable": "穩定參考", "Refer_dynamic": "動態參考", "OpenAI Voice": "OpenAI語音", "Fish TTS Character": "Fish TTS角色", "Azure Voice": "Azure語音", "Please refer to Github homepage for GPT_SoVITS configuration": "請參考Github主頁了解GPT_SoVITS設定", "SoVITS Character": "SoVITS角色", "Refer Mode": "參考模式", "Mode 1: Use provided reference audio only": "模式1:僅使用提供的參考音頻", "Mode 2: Use first audio from video as reference": "模式2:使用影片中的第一段音頻作為參考", "Mode 3: Use each audio from video as reference": "模式3:使用影片中的每段音頻作為參考", "Configure reference audio mode for GPT-SoVITS": "配置GPT-SoVITS的參考音頻模式", "Edge TTS Voice": "Edge TTS語音", "=====NOTE=====": "以下是st.py中的內容", "b. Translate and Generate Subtitles": "b. 翻譯並生成字幕", "This stage includes the following steps:": "此階段包含以下步驟:", "WhisperX word-level transcription": "WhisperX詞級轉錄", "Sentence segmentation using NLP and LLM": "使用NLP和LLM進行句子分段", "Summarization and multi-step translation": "摘要和多步翻譯", "Cutting and aligning long subtitles": "切割和對齊長字幕", "Generating timeline and subtitles": "生成時間軸和字幕", "Merging subtitles into the video": "將字幕合併到影片中", "Start Processing Subtitles": "開始處理字幕", "Download All Srt Files": "下載所有Srt檔案", "Archive to 'history'": "歸檔到'history'", "Using Whisper for transcription...": "正在使用Whisper進行轉錄...", "Splitting long sentences...": "正在分割長句...", "Summarizing and translating...": "正在總結和翻譯...", "Processing and aligning subtitles...": "正在處理和對齊字幕...", "Merging subtitles to video...": "正在將字幕合併到影片中...", "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...": "⚠️ 翻譯前暫停。請前往`output/log/terminology.json`編輯術語。然後按Enter鍵繼續...", "Subtitle processing complete! 🎉": "字幕處理完成! 🎉", "c. Dubbing": "c. 配音", "Generate audio tasks and chunks": "生成音頻任務和分塊", "Extract reference audio": "提取參考音頻", "Generate and merge audio files": "生成和合併音頻檔案", "Merge final audio into video": "將最終音頻合併到影片中", "Start Audio Processing": "開始音頻處理", "Audio processing is complete! You can check the audio files in the `output` folder.": "音頻處理完成！您可以在`output`資料夾中查看音頻檔案。", "Delete dubbing files": "刪除配音檔案", "Generate audio tasks": "生成音頻任務", "Extract refer audio": "提取參考音頻", "Generate all audio": "生成所有音頻", "Merge full audio": "合併完整音頻", "Merge dubbing to the video": "將配音合併到影片中", "Audio processing complete! 🎇": "音頻處理完成! 🎇", "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!": "歡迎來到VideoLingo。如果遇到任何問題，隨時可以透過我們的免費問答助手 <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a> 獲取即時解答！還可以免費試用我們的SaaS網站 <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a>！", "WhisperX Runtime": "WhisperX 運行環境", "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key": "本地運行需要>8GB顯存GPU，雲端運行需要302ai API金鑰，elevenlabs運行需要ElevenLabs API金鑰", "WhisperX 302ai API": "WhisperX 302ai API金鑰", "=====NOTE2=====": "以下是install.py中的內容", "🚀 Starting Installation": "🚀 開始安裝", "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)": "是否需要自動配置PyPI鏡像？（如果訪問pypi.org困難，建議使用）", "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...": "🎮 檢測到NVIDIA GPU，正在安裝CUDA版本的PyTorch...", "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 檢測到MacOS，正在安裝CPU版本的PyTorch... 注意：在whisperX轉錄過程中可能會較慢。", "💻 No NVIDIA GPU detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "💻 未檢測到NVIDIA GPU，正在安裝CPU版本的PyTorch... 注意：在whisperX轉錄過程中可能會較慢。", "❌ Failed to install requirements:": "❌ 安裝依賴失敗：", "✅ FFmpeg is already installed": "✅ FFmpeg已安裝", "❌ FFmpeg not found\n\n": "❌ 未找到FFmpeg\n\n", "🛠️ Install using:": "🛠️ 使用以下命令安裝：", "💡 Note:": "💡 注意：", "🔄 After installing FFmpeg, please run this installer again:": "🔄 安裝FFmpeg後，請重新運行此安裝程序：", "Install Chocolatey first (https://chocolatey.org/)": "請先安裝Chocolatey (https://chocolatey.org/)", "Install Homebrew first (https://brew.sh/)": "請先安裝Homebrew (https://brew.sh/)", "Use your distribution's package manager": "使用您的發行版套件管理器", "FFmpeg is required. Please install it and run the installer again.": "需要安裝FFmpeg。請安裝後重新運行安裝程序。", "Installation completed": "安裝完成", "Now I will run this command to start the application:": "現在我將運行以下命令啟動應用：", "Note: First startup may take up to 1 minute": "注意：首次啟動可能需要最多1分鐘", "If the application fails to start:": "如果應用啟動失敗：", "Check your network connection": "檢查網絡連接", "Re-run the installer: [bold]python install.py[/bold]": "重新運行安裝程序：[bold]python install.py[/bold]", "Installing requirements using `pip install -r requirements.txt`": "正在使用 `pip install -r requirements.txt` 安裝依賴", "Detected NVIDIA GPU(s)": "檢測到NVIDIA GPU", "No NVIDIA GPU detected": "未檢測到NVIDIA GPU", "No NVIDIA GPU detected or NVIDIA drivers not properly installed": "未檢測到NVIDIA GPU或NVIDIA驅動未正確安裝", "LLM JSON Format Support": "LLM JSON格式支持", "Enable if your LLM supports JSON mode output": "如果選用的LLM支持JSON模式輸出，請啟用"}