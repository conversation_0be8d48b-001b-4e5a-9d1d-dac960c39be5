#!/usr/bin/env python3
"""
测试新的目录结构
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def test_directory_service():
    """测试DirectoryService"""
    try:
        from app.services.directory_service import DirectoryService
        
        print("🗂️ 测试DirectoryService...")
        
        # 初始化服务
        dir_service = DirectoryService()
        print("✅ DirectoryService初始化成功")
        
        # 创建测试任务目录
        task_paths = dir_service.create_task_directory("test_video.mp4", "test_task_123")
        
        print(f"📁 任务目录创建成功:")
        print(f"  任务名称: {task_paths['task_name']}")
        print(f"  主目录: {task_paths['task_dir']}")
        
        # 显示所有子目录
        subdirs = ['source', 'audio', 'transcription', 'translation', 'tts', 'subtitles', 'output', 'logs', 'temp']
        for subdir in subdirs:
            if subdir in task_paths:
                print(f"  {subdir}: {task_paths[subdir]}")
        
        # 测试文件操作
        print(f"\n📄 测试文件操作...")
        
        # 创建测试文件
        test_file = Path("test_source.txt")
        with open(test_file, 'w') as f:
            f.write("这是一个测试文件")
        
        # 复制文件到任务目录
        copied_file = dir_service.copy_file_to_task_dir(
            source_path=str(test_file),
            task_name=task_paths['task_name'],
            target_subdir='source',
            new_filename='original.txt'
        )
        
        print(f"✅ 文件复制成功: {copied_file}")
        
        # 获取文件路径
        file_path = dir_service.get_file_path(
            task_name=task_paths['task_name'],
            subdir='audio',
            filename='test.wav'
        )
        
        print(f"✅ 文件路径获取: {file_path}")
        
        # 更新元数据
        success = dir_service.update_task_metadata(
            task_name=task_paths['task_name'],
            updates={
                'status': 'testing',
                'test_field': 'test_value'
            }
        )
        
        print(f"✅ 元数据更新: {'成功' if success else '失败'}")
        
        # 清理测试文件
        test_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ DirectoryService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_service_integration():
    """测试服务集成"""
    try:
        print("\n🔧 测试服务集成...")
        
        from app.services.task_service import TaskService
        
        # 初始化TaskService
        task_service = TaskService()
        print("✅ TaskService初始化成功")
        
        # 检查DirectoryService是否正确集成
        if hasattr(task_service, 'directory_service'):
            print("✅ DirectoryService已集成到TaskService")
        else:
            print("❌ DirectoryService未集成到TaskService")
            return False
        
        # 测试目录创建
        test_paths = task_service.directory_service.create_task_directory("integration_test.mp4", "integration_123")
        print(f"✅ 集成测试目录创建: {test_paths['task_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_new_methods():
    """测试新添加的方法"""
    try:
        print("\n🆕 测试新添加的方法...")
        
        # 测试AudioService新方法
        try:
            from app.services.audio_service import AudioService
            audio_service = AudioService()
            
            if hasattr(audio_service, 'extract_audio_to_path'):
                print("✅ AudioService.extract_audio_to_path 方法存在")
            else:
                print("❌ AudioService.extract_audio_to_path 方法不存在")
        except Exception as e:
            print(f"❌ AudioService测试失败: {e}")
        
        # 测试SpeechService新方法
        try:
            from app.services.speech_service import SpeechService
            speech_service = SpeechService()
            
            if hasattr(speech_service, 'recognize_speech_to_path'):
                print("✅ SpeechService.recognize_speech_to_path 方法存在")
            else:
                print("❌ SpeechService.recognize_speech_to_path 方法不存在")
        except Exception as e:
            print(f"❌ SpeechService测试失败: {e}")
        
        # 测试TranslationService新方法
        try:
            from app.services.translation_service import TranslationService
            translation_service = TranslationService()
            
            if hasattr(translation_service, 'translate_text_to_path'):
                print("✅ TranslationService.translate_text_to_path 方法存在")
            else:
                print("❌ TranslationService.translate_text_to_path 方法不存在")
        except Exception as e:
            print(f"❌ TranslationService测试失败: {e}")
        
        # 测试TTSService新方法
        try:
            from app.services.tts_service import TTSService
            tts_service = TTSService()
            
            if hasattr(tts_service, 'synthesize_speech_to_path'):
                print("✅ TTSService.synthesize_speech_to_path 方法存在")
            else:
                print("❌ TTSService.synthesize_speech_to_path 方法不存在")
        except Exception as e:
            print(f"❌ TTSService测试失败: {e}")
        
        # 测试VideoService新方法
        try:
            from app.services.video_service import VideoService
            video_service = VideoService()
            
            if hasattr(video_service, 'compose_video_to_path'):
                print("✅ VideoService.compose_video_to_path 方法存在")
            else:
                print("❌ VideoService.compose_video_to_path 方法不存在")
        except Exception as e:
            print(f"❌ VideoService测试失败: {e}")
        
        # 测试SubtitleService新方法
        try:
            from app.services.subtitle_service import SubtitleService
            subtitle_service = SubtitleService()
            
            if hasattr(subtitle_service, 'generate_subtitles_to_path'):
                print("✅ SubtitleService.generate_subtitles_to_path 方法存在")
            else:
                print("❌ SubtitleService.generate_subtitles_to_path 方法不存在")
        except Exception as e:
            print(f"❌ SubtitleService测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 新方法测试失败: {e}")
        return False

async def main():
    print("🚀 开始目录结构重构测试...")
    
    # 1. 测试DirectoryService
    dir_ok = await test_directory_service()
    
    # 2. 测试服务集成
    integration_ok = await test_service_integration()
    
    # 3. 测试新方法
    methods_ok = await test_new_methods()
    
    print(f"\n📊 测试结果总结:")
    print(f"  DirectoryService: {'✅ 通过' if dir_ok else '❌ 失败'}")
    print(f"  服务集成: {'✅ 通过' if integration_ok else '❌ 失败'}")
    print(f"  新方法: {'✅ 通过' if methods_ok else '❌ 失败'}")
    
    if all([dir_ok, integration_ok, methods_ok]):
        print("\n🎉 目录结构重构测试成功!")
        print("新的目录结构已经准备就绪，可以开始使用了")
        print("\n下一步:")
        print("1. 重启AI服务")
        print("2. 运行完整翻译流程")
        print("3. 检查生成的结构化目录")
    else:
        print("\n⚠️ 目录结构重构仍有问题")
        if not dir_ok:
            print("- DirectoryService有问题")
        if not integration_ok:
            print("- 服务集成有问题")
        if not methods_ok:
            print("- 新方法有问题")

if __name__ == "__main__":
    asyncio.run(main())
