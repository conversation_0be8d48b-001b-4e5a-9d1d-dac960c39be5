# 🔧 FFmpeg路径错误修复

## 🚨 错误描述

```
2025-07-19 12:12:29 | ERROR | FFmpeg命令失败，返回码: 4294967294
2025-07-19 12:12:29 | ERROR | FFmpeg错误输出: 
[in#0 @ 00000231d67ff940] Error opening input: No such file or directory
Error opening input file storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4.
Error opening input files: No such file or directory
```

## 🔍 问题根源

这个错误是由于**文件路径问题**导致的：

### 1. **相对路径问题**
```bash
# FFmpeg尝试访问相对路径
storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4

# 但当前工作目录可能不是预期的目录
# 导致FFmpeg找不到文件
```

### 2. **工作目录不匹配**
```python
# 问题代码：在错误的工作目录中执行FFmpeg
subprocess.run(
    ffmpeg_cmd,
    cwd=str(self.temp_dir)  # ❌ 临时目录中没有输入文件
)
```

### 3. **路径分隔符问题**
- Windows使用反斜杠 `\`
- 但某些情况下需要正斜杠 `/`
- 相对路径在不同工作目录中指向不同位置

## ✅ 修复方案

### 1. **使用绝对路径**
```python
# ✅ 修复后：转换为绝对路径
abs_video_path = os.path.abspath(video_path)
abs_audio_path = os.path.abspath(audio_path)
abs_output_path = os.path.abspath(str(output_path))

# FFmpeg命令使用绝对路径
ffmpeg_cmd = [
    'ffmpeg', '-y',
    '-i', abs_video_path,  # ✅ 绝对路径
    '-i', abs_audio_path,  # ✅ 绝对路径
    '-c:v', 'copy',
    '-c:a', 'aac',
    '-map', '0:v:0',
    '-map', '1:a:0',
    '-shortest',
    abs_output_path        # ✅ 绝对路径
]
```

### 2. **文件存在性验证**
```python
# ✅ 修复后：验证输入文件存在
if not os.path.exists(abs_video_path):
    raise FileNotFoundError(f"视频文件不存在: {abs_video_path}")

if abs_audio_path and not os.path.exists(abs_audio_path):
    self.logger.warning(f"音频文件不存在，将只复制视频: {abs_audio_path}")
    abs_audio_path = None
```

### 3. **正确的工作目录**
```python
# ✅ 修复后：在项目根目录中执行
subprocess.run(
    ffmpeg_cmd,
    capture_output=True,
    text=True,
    timeout=300,
    cwd=os.getcwd()  # ✅ 当前工作目录
)
```

### 4. **统一路径处理**
```python
# ✅ 在函数开始就转换所有路径
abs_video_path = os.path.abspath(video_path)
abs_audio_path = os.path.abspath(audio_path)
abs_output_path = os.path.abspath(str(output_path))

# 所有后续操作都使用绝对路径
video = mp.VideoFileClip(abs_video_path)
new_audio = mp.AudioFileClip(abs_audio_path)
final_video.write_videofile(abs_output_path)
```

## 🎯 修复效果

### 修复前
```
ERROR: Error opening input file storage\uploads\test.mp4
ERROR: No such file or directory
ERROR: FFmpeg命令失败，返回码: 4294967294
```

### 修复后
```
INFO: 输入视频: F:\project\ai\video\python-ai-service\storage\uploads\test.mp4
INFO: 输入音频: F:\project\ai\video\python-ai-service\storage\outputs\audio.wav
INFO: 输出文件: F:\project\ai\video\python-ai-service\storage\outputs\output.mp4
INFO: 执行FFmpeg命令: ffmpeg -y -i F:\...\test.mp4 -i F:\...\audio.wav ...
INFO: FFmpeg命令执行成功: output.mp4
```

## 🔧 技术细节

### 绝对路径的优势
1. **位置无关**：不依赖当前工作目录
2. **明确性**：清楚指向具体文件
3. **跨平台**：`os.path.abspath()`处理平台差异
4. **调试友好**：日志中显示完整路径

### 文件验证机制
1. **提前检查**：在执行FFmpeg前验证文件存在
2. **智能降级**：音频文件不存在时只复制视频
3. **详细日志**：记录所有路径信息
4. **错误定位**：明确指出哪个文件缺失

## 🚀 测试方法

### 1. 运行测试脚本
```bash
python test_ffmpeg_path_fix.py
```

### 2. 检查测试结果
- ✅ 工作目录正常
- ✅ 路径解析正常
- ✅ 文件检查正常
- ✅ 命令构建正常
- ✅ 服务路径正常

### 3. 完整流程测试
1. 重启AI服务
2. 运行完整翻译流程
3. 观察FFmpeg日志
4. 检查生成的视频

## 📊 预期日志输出

### 成功情况
```
INFO: Composing video: F:\...\input.mp4 + F:\...\audio.wav
INFO: 输出路径: F:\...\output.mp4
INFO: 使用音频替换模式
INFO: 执行FFmpeg命令: ffmpeg -y -i F:\...\input.mp4 -i F:\...\audio.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest F:\...\output.mp4
INFO: 输入视频: F:\...\input.mp4
INFO: 输入音频: F:\...\audio.wav
INFO: 输出文件: F:\...\output.mp4
INFO: Video composition completed: F:\...\output.mp4
```

### 文件缺失情况
```
ERROR: 视频文件不存在: F:\...\nonexistent.mp4
```

### 音频缺失情况
```
WARNING: 音频文件不存在，将只复制视频: F:\...\nonexistent.wav
INFO: 使用视频复制模式
INFO: 执行FFmpeg命令: ffmpeg -y -i F:\...\input.mp4 -c copy F:\...\output.mp4
```

## 🎉 优势

1. **高可靠性**：绝对路径消除位置依赖
2. **清晰调试**：完整路径便于问题定位
3. **智能处理**：自动处理文件缺失情况
4. **跨平台兼容**：正确处理路径分隔符

## 🔍 相关修复

这个修复解决了以下相关问题：
- FFmpeg找不到输入文件
- 相对路径在不同工作目录中失效
- 路径分隔符不一致
- 文件存在性检查不准确

---

通过这些修复，FFmpeg应该能够正确找到并处理所有输入文件，成功生成翻译视频！
