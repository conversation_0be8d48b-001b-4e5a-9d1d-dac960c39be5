"""
本地TTS服务
使用pyttsx3提供离线文字转语音功能
"""

import os
import tempfile
import logging
from typing import Optional, Dict, List, Any
import pyttsx3
from .base_service import BaseService

logger = logging.getLogger(__name__)


class LocalTTSService(BaseService):
    """本地TTS服务类"""
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.voices = []
        self.current_voice = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化TTS引擎"""
        try:
            logger.info("初始化本地TTS引擎...")
            self.engine = pyttsx3.init()
            
            # 获取可用语音
            self.voices = self.engine.getProperty('voices')
            logger.info(f"找到 {len(self.voices)} 个可用语音")
            
            # 设置默认属性
            self.engine.setProperty('rate', 150)  # 语速
            self.engine.setProperty('volume', 1.0)  # 音量
            
            # 优先选择中文语音
            self._select_default_voice()
            
            logger.info("本地TTS引擎初始化成功")
            
        except Exception as e:
            logger.error(f"TTS引擎初始化失败: {e}")
            raise
    
    def _select_default_voice(self):
        """选择默认语音（优先中文）"""
        try:
            # 优先选择中文语音
            for voice in self.voices:
                if 'zh' in voice.id.lower() or 'chinese' in voice.name.lower():
                    self.current_voice = voice
                    self.engine.setProperty('voice', voice.id)
                    logger.info(f"选择中文语音: {voice.name}")
                    return
            
            # 如果没有中文语音，选择女声
            for voice in self.voices:
                if hasattr(voice, 'gender') and voice.gender == 'Female':
                    self.current_voice = voice
                    self.engine.setProperty('voice', voice.id)
                    logger.info(f"选择女声语音: {voice.name}")
                    return
            
            # 使用第一个可用语音
            if self.voices:
                self.current_voice = self.voices[0]
                self.engine.setProperty('voice', self.voices[0].id)
                logger.info(f"使用默认语音: {self.voices[0].name}")
            
        except Exception as e:
            logger.error(f"选择默认语音失败: {e}")
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """获取可用语音列表"""
        try:
            voice_list = []
            for i, voice in enumerate(self.voices):
                voice_info = {
                    'id': voice.id,
                    'name': voice.name,
                    'short_name': f"local-voice-{i+1}",
                    'language': getattr(voice, 'languages', ['unknown'])[0] if hasattr(voice, 'languages') else 'unknown',
                    'gender': getattr(voice, 'gender', 'Unknown'),
                    'age': getattr(voice, 'age', 'Unknown'),
                    'is_chinese': 'zh' in voice.id.lower() or 'chinese' in voice.name.lower()
                }
                voice_list.append(voice_info)
            
            logger.info(f"返回 {len(voice_list)} 个语音选项")
            return voice_list
            
        except Exception as e:
            logger.error(f"获取语音列表失败: {e}")
            return []
    
    def set_voice(self, voice_id: str) -> bool:
        """设置语音"""
        try:
            # 查找语音
            for voice in self.voices:
                if voice.id == voice_id or voice.name == voice_id:
                    self.current_voice = voice
                    self.engine.setProperty('voice', voice.id)
                    logger.info(f"切换到语音: {voice.name}")
                    return True
            
            logger.warning(f"未找到语音: {voice_id}")
            return False
            
        except Exception as e:
            logger.error(f"设置语音失败: {e}")
            return False
    
    def set_rate(self, rate: int):
        """设置语速 (50-300)"""
        try:
            rate = max(50, min(300, rate))  # 限制范围
            self.engine.setProperty('rate', rate)
            logger.info(f"语速设置为: {rate}")
        except Exception as e:
            logger.error(f"设置语速失败: {e}")
    
    def set_volume(self, volume: float):
        """设置音量 (0.0-1.0)"""
        try:
            volume = max(0.0, min(1.0, volume))  # 限制范围
            self.engine.setProperty('volume', volume)
            logger.info(f"音量设置为: {volume}")
        except Exception as e:
            logger.error(f"设置音量失败: {e}")
    
    def generate_speech(
        self,
        text: str,
        output_file: Optional[str] = None,
        voice: Optional[str] = None,
        rate: Optional[int] = None,
        volume: Optional[float] = None
    ) -> str:
        """
        生成语音文件
        
        Args:
            text: 要转换的文本
            output_file: 输出文件路径，如果为None则创建临时文件
            voice: 语音ID或名称
            rate: 语速
            volume: 音量
            
        Returns:
            生成的音频文件路径
        """
        try:
            logger.info(f"开始生成语音，文本长度: {len(text)}")
            
            # 设置语音参数
            if voice:
                self.set_voice(voice)
            if rate is not None:
                self.set_rate(rate)
            if volume is not None:
                self.set_volume(volume)
            
            # 创建输出文件
            if output_file is None:
                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
                    output_file = tmp_file.name
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 生成语音
            self.engine.save_to_file(text, output_file)
            self.engine.runAndWait()
            
            # 检查文件是否生成成功
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                file_size = os.path.getsize(output_file)
                logger.info(f"语音生成成功: {output_file}, 大小: {file_size} 字节")
                return output_file
            else:
                raise Exception("语音文件生成失败或文件为空")
            
        except Exception as e:
            logger.error(f"语音生成失败: {e}")
            # 清理可能的空文件
            if output_file and os.path.exists(output_file):
                try:
                    os.unlink(output_file)
                except:
                    pass
            raise
    
    def test_tts(self) -> Dict[str, Any]:
        """测试TTS功能"""
        try:
            test_text = "你好，这是本地TTS服务测试。"
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
                output_file = tmp_file.name
            
            # 生成语音
            result_file = self.generate_speech(test_text, output_file)
            
            # 获取文件信息
            file_size = os.path.getsize(result_file)
            
            # 清理文件
            os.unlink(result_file)
            
            return {
                'status': 'success',
                'message': '本地TTS测试成功',
                'test_text': test_text,
                'voice': self.current_voice.name if self.current_voice else 'Unknown',
                'file_size': file_size
            }
            
        except Exception as e:
            logger.error(f"TTS测试失败: {e}")
            return {
                'status': 'error',
                'message': f'TTS测试失败: {str(e)}'
            }


    async def process_task(self, task_id: str, *args, **kwargs):
        """Process TTS task (implementation required by BaseService)"""
        pass


# 创建全局实例
local_tts_service = LocalTTSService() 