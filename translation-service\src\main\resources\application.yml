# 翻译服务配置
spring:
  application:
    name: translation-service
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      file-size-threshold: 10MB
      location: ./temp/uploads

server:
  port: 8080
  tomcat:
    max-http-form-post-size: 500MB
    max-swallow-size: 500MB
    connection-timeout: 300000
  servlet:
    context-path: /
  max-http-request-header-size: 8KB

# 日志配置
logging:
  level:
    com.translation: INFO
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/translation-service.log

# HTTP客户端配置
http:
  client:
    timeout: 30000
    connection-timeout: 10000
    read-timeout: 30000

# 百度翻译API配置
baidu:
  # 百度API通用配置（用于获取访问令牌）
  api:
    client:
      id: ${BAIDU_API_CLIENT_ID:B5diUAEhqJbIyrRVnsv0MCOf}
      secret: ${BAIDU_API_CLIENT_SECRET:UJv0W3SkRtNYbtte1wSwSWkSHIYFjVg6}
  translate:
    app:
      id: 20250621002386804
    secret:
      key: 17p7Db8FUkRy7Rvk6BID
  # 百度TTS API配置
  tts:
    api:
      key: ${BAIDU_TTS_API_KEY:your_baidu_tts_api_key}
    secret:
      key: ${BAIDU_TTS_SECRET_KEY:your_baidu_tts_secret_key}
    cuid: translation-service
    format: mp3
    rate: 16000
    max:
      text:
        length: 1024
  # 百度ASR API配置
  asr:
    api:
      key: B5diUAEhqJbIyrRVnsv0MCOf
    secret:
      key: UJv0W3SkRtNYbtte1wSwSWkSHIYFjVg6
    cuid: translation-service
    format: wav
    rate: 16000
    channel: 1
    # 网络超时配置（秒）
    timeout:
      connect: 60
      read: 300
      write: 300
    # 重试配置
    retry:
      max-attempts: 3
      delay: 2000
    # 文件大小限制（字节）
    max-file-size: 8388608  # 8MB
    # 语音识别优化配置
    recognition:
      # 语音识别模型配置
      models:
        chinese: 1536      # 普通话(支持简单英文)
        english: 1737      # 英语
        mixed: 1536        # 混合语言（使用普通话模型）
        cantonese: 1637    # 粤语
        sichuan: 1837      # 四川话
      # 后处理配置
      post-processing:
        enabled: true
        fix-concatenation: true  # 修复英文单词粘连
        fix-common-errors: true  # 修复常见识别错误
        normalize-punctuation: true  # 标准化标点符号

# Google翻译API配置
google:
  translate:
    api:
      key: ${GOOGLE_TRANSLATE_API_KEY:your_google_api_key}

# DeepL翻译API配置
deepL:
  api:
    key: ${DEEPL_API_KEY:your_deepl_api_key}

# 翻译服务配置
translation:
  retry:
    max-attempts: 3
    delay: 1000
  cache:
    enabled: true
    ttl: 3600

# TTS配置
tts:
  default:
    provider: edge
  output:
    dir: ./output/audio
  temp:
    dir: ./temp/tts
  edge:
    script:
      path: src/main/resources/scripts/edge_tts_client.py
    max:
      text:
        length: 2000

# 音频处理配置
audio:
  temp:
    dir: ./temp/audio
  output:
    dir: ./output/audio
  ffmpeg:
    timeout: 1800  # 30分钟超时，处理长音频
  max:
    duration: 7200  # 2小时最大时长
    size: **********  # 2GB最大文件大小

# 视频处理配置
video:
  output:
    dir: ./output/video
  temp:
    dir: ./temp/video
  ffmpeg:
    timeout: 1800  # 30分钟超时，处理长视频
  max:
    size: **********  # 2GB最大文件大小
    duration: 7200  # 2小时最大时长

# 任务配置
task:
  executor:
    core-pool-size: 2
    max-pool-size: 10
    queue-capacity: 100
    thread-name-prefix: translation-task-