package com.translation.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 翻译服务管理器
 * 统一管理多个翻译服务提供商
 */
@Service
public class TranslationManager {
    private static final Logger logger = LoggerFactory.getLogger(TranslationManager.class);
    
    @Autowired
    private BaiduTranslationService baiduTranslationService;
    
    @Autowired
    private GoogleTranslationService googleTranslationService;
    
    @Autowired
    private DeepLTranslationService deepLTranslationService;
    
    /**
     * 翻译文本（自动选择服务）
     * @param text 要翻译的文本
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 翻译结果
     */
    public String translateText(String text, String sourceLanguage, String targetLanguage) throws IOException {
        return translateTextAuto(text, sourceLanguage, targetLanguage);
    }
    
    /**
     * 翻译文本
     * @param text 要翻译的文本
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @param provider 翻译服务提供商 (baidu, google, deepl)
     * @return 翻译结果
     */
    public String translateText(String text, String sourceLanguage, String targetLanguage, String provider) throws IOException {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        logger.info("开始翻译，提供商: {}, 源语言: {}, 目标语言: {}, 文本长度: {}", 
                provider, sourceLanguage, targetLanguage, text.length());
        
        switch (provider.toLowerCase()) {
            case "baidu":
                return baiduTranslationService.translateText(text, sourceLanguage, targetLanguage);
            case "google":
                return googleTranslationService.translateText(text, sourceLanguage, targetLanguage);
            case "deepl":
                return deepLTranslationService.translateText(text, sourceLanguage, targetLanguage);
            default:
                throw new IOException("不支持的翻译服务提供商: " + provider);
        }
    }
    
    /**
     * 自动选择可用的翻译服务
     */
    public String translateTextAuto(String text, String sourceLanguage, String targetLanguage) throws IOException {
        List<String> providers = getAvailableProviders();
        
        if (providers.isEmpty()) {
            throw new IOException("没有可用的翻译服务，请检查API配置");
        }
        
        IOException lastException = null;
        
        for (String provider : providers) {
            try {
                logger.info("尝试使用 {} 翻译服务", provider);
                return translateText(text, sourceLanguage, targetLanguage, provider);
            } catch (IOException e) {
                logger.warn("翻译服务 {} 失败: {}", provider, e.getMessage());
                lastException = e;
            }
        }
        
        throw new IOException("所有翻译服务都失败了", lastException);
    }
    
    /**
     * 获取可用的翻译服务列表
     */
    public List<String> getAvailableProviders() {
        List<String> providers = new ArrayList<>();
        
        if (baiduTranslationService.isConfigured()) {
            providers.add("baidu");
        }
        if (googleTranslationService.isConfigured()) {
            providers.add("google");
        }
        if (deepLTranslationService.isConfigured()) {
            providers.add("deepl");
        }
        
        return providers;
    }
    
    /**
     * 获取翻译服务状态
     */
    public Map<String, Object> getServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        
        Map<String, Object> baiduStatus = new HashMap<>();
        baiduStatus.put("configured", baiduTranslationService.isConfigured());
        baiduStatus.put("name", "百度翻译");
        baiduStatus.put("description", "免费额度：每月200万字符");
        status.put("baidu", baiduStatus);
        
        Map<String, Object> googleStatus = new HashMap<>();
        googleStatus.put("configured", googleTranslationService.isConfigured());
        googleStatus.put("name", "Google翻译");
        googleStatus.put("description", "高质量翻译，按使用量计费");
        status.put("google", googleStatus);
        
        Map<String, Object> deeplStatus = new HashMap<>();
        deeplStatus.put("configured", deepLTranslationService.isConfigured());
        deeplStatus.put("name", "DeepL翻译");
        deeplStatus.put("description", "最高质量翻译，免费版每月50万字符");
        status.put("deepl", deeplStatus);
        
        return status;
    }
    
    /**
     * 检测文本语言
     */
    public String detectLanguage(String text) {
        try {
            if (googleTranslationService.isConfigured()) {
                return googleTranslationService.detectLanguage(text);
            }
        } catch (Exception e) {
            logger.warn("Google语言检测失败: {}", e.getMessage());
        }
        
        try {
            if (baiduTranslationService.isConfigured()) {
                return baiduTranslationService.detectLanguage(text);
            }
        } catch (Exception e) {
            logger.warn("百度语言检测失败: {}", e.getMessage());
        }
        
        // 简单的语言检测逻辑作为后备
        if (text.matches(".*[\\u4e00-\\u9fa5].*")) {
            return "zh-CN";
        } else if (text.matches(".*[\\u3040-\\u309f\\u30a0-\\u30ff].*")) {
            return "ja";
        } else if (text.matches(".*[\\uac00-\\ud7af].*")) {
            return "ko";
        } else {
            return "en";
        }
    }
    
    /**
     * 批量翻译
     */
    public List<String> translateBatch(List<String> texts, String sourceLanguage, String targetLanguage, String provider) throws IOException {
        List<String> results = new ArrayList<>();
        
        for (String text : texts) {
            String result = translateText(text, sourceLanguage, targetLanguage, provider);
            results.add(result);
        }
        
        return results;
    }
}