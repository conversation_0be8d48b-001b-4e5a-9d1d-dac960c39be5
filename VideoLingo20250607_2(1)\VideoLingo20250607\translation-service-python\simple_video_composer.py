#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版视频合成器 - 仅替换音频
"""

import os
import sys
from moviepy.editor import VideoFileClip, AudioFileClip

def simple_compose_video(video_path, audio_path, output_path):
    """简单的视频音频合成"""
    try:
        print("🎬 开始加载视频和音频...")
        
        # 加载原视频
        video = VideoFileClip(video_path)
        print(f"📹 视频时长: {video.duration:.2f}s")
        
        # 加载新音频
        new_audio = AudioFileClip(audio_path)
        print(f"🎵 音频时长: {new_audio.duration:.2f}s")
        
        # 如果音频比视频短，循环音频
        if new_audio.duration < video.duration:
            print("🔄 音频较短，正在循环...")
            loops_needed = int(video.duration / new_audio.duration) + 1
            new_audio = new_audio.loop(loops_needed).subclip(0, video.duration)
        elif new_audio.duration > video.duration:
            print("✂️ 音频较长，正在截取...")
            new_audio = new_audio.subclip(0, video.duration)
        
        # 替换音频
        final_video = video.set_audio(new_audio)
        
        # 导出视频
        print("💾 正在导出视频...")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # 清理资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"✅ 视频合成完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 视频合成失败: {str(e)}")
        return False

def main():
    video_path = "input.mp4"
    audio_path = "out/temp_audio.wav"  # 使用原始提取的音频作为测试
    output_path = "out/test_simple_video.mp4"
    
    print("🚀 简化版视频合成测试")
    print(f"📹 输入视频: {video_path}")
    print(f"🎵 输入音频: {audio_path}")
    print(f"💾 输出视频: {output_path}")
    
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return
        
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return
    
    success = simple_compose_video(video_path, audio_path, output_path)
    
    if success:
        print("🎉 测试成功!")
    else:
        print("❌ 测试失败!")

if __name__ == "__main__":
    main() 