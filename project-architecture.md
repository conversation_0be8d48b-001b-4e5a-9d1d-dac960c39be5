# 视频翻译系统架构设计文档

## 📋 项目概述

### 项目名称
Video Translation System - 基于AI的视频翻译配音系统

### 技术栈
- **前端**: Vue.js 3 + TypeScript + Element Plus
- **后端主服务**: Spring Boot 3.x + Java 17
- **AI处理服务**: Python 3.10 + FastAPI
- **数据库**: MySQL 8.0 + Redis 7.0
- **消息队列**: RabbitMQ
- **文件存储**: MinIO / 阿里云OSS
- **容器化**: Docker + Docker Compose

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 (Vue.js)                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   文件上传      │  │   任务管理      │  │   进度监控      ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   结果预览      │  │   参数配置      │  │   用户管理      ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/WebSocket
┌─────────────────────▼───────────────────────────────────────┐
│              Spring Boot 主服务                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   用户管理      │  │   任务管理      │  │   文件管理      ││
│  │   UserService   │  │   TaskService   │  │   FileService   ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   进度跟踪      │  │   结果管理      │  │   系统监控      ││
│  │ ProgressService │  │ ResultService   │  │ MonitorService  ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   消息队列      │  │   缓存管理      │  │   配置管理      ││
│  │   MQService     │  │   CacheService  │  │  ConfigService  ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP API 调用 + 消息队列
┌─────────────────────▼───────────────────────────────────────┐
│              Python AI 处理服务                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   音频提取      │  │   语音识别      │  │   文本翻译      ││
│  │ AudioExtractor  │  │ SpeechRecognizer│  │   Translator    ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   TTS生成       │  │   音频优化      │  │   视频合成      ││
│  │  TTSGenerator   │  │ AudioOptimizer  │  │ VideoComposer   ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
video-translation-system/
├── frontend/                           # Vue.js 前端项目
│   ├── src/
│   │   ├── components/                # 组件
│   │   ├── views/                     # 页面
│   │   ├── api/                       # API调用
│   │   ├── store/                     # 状态管理
│   │   └── utils/                     # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── backend/                           # Spring Boot 后端项目
│   ├── src/main/java/com/videotrans/
│   │   ├── controller/                # 控制器
│   │   ├── service/                   # 业务服务
│   │   ├── entity/                    # 实体类
│   │   ├── repository/                # 数据访问
│   │   ├── config/                    # 配置类
│   │   ├── dto/                       # 数据传输对象
│   │   └── common/                    # 通用类
│   ├── src/main/resources/
│   │   ├── application.yml            # 配置文件
│   │   └── mapper/                    # MyBatis映射
│   └── pom.xml
├── python-ai-service/                 # Python AI 处理服务
│   ├── app/
│   │   ├── main.py                    # FastAPI主入口
│   │   ├── routers/                   # API路由
│   │   ├── services/                  # 业务逻辑
│   │   ├── models/                    # 数据模型
│   │   └── utils/                     # 工具函数
│   ├── requirements.txt
│   └── Dockerfile
├── docker-compose.yml                 # Docker编排文件
├── docs/                              # 文档
│   ├── api.md                         # API文档
│   ├── deployment.md                  # 部署文档
│   └── development.md                 # 开发文档
└── README.md
```

## 🔄 数据流程与通信方式

### 1. 用户上传视频
```
用户 → Vue前端 → Spring Boot → 文件存储 → 数据库记录
通信方式: HTTP REST API
```

### 2. 视频处理流程
```
Spring Boot → 创建任务 → 消息队列 → Python AI服务
    ↓
Python AI服务 → 音频提取 → 语音识别 → 翻译 → TTS → 音频优化 → 视频合成
    ↓
每个步骤完成 → HTTP回调Spring Boot → 更新进度 → WebSocket推送前端
```

### 3. 结果返回
```
Python AI服务 → 处理完成 → HTTP回调Spring Boot → 更新状态 → 通知前端
```

## 🔗 服务间通信方式设计

### HTTP REST API (同步调用)
**使用场景**: 快速查询、状态检查、配置获取
```java
// Java调用Python服务示例
@Service
public class PythonAIClient {
    @Autowired
    private RestTemplate restTemplate;
    
    // 健康检查
    public boolean checkHealth() {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(
                "http://python-ai-service:8000/health", String.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            return false;
        }
    }
    
    // 获取任务状态
    public TaskStatus getTaskStatus(Long taskId) {
        String url = "http://python-ai-service:8000/ai/tasks/" + taskId + "/status";
        return restTemplate.getForObject(url, TaskStatus.class);
    }
}
```

### 消息队列 (异步处理)
**使用场景**: 长时间处理任务、批量操作
```java
// Java发送处理消息
@Service
public class VideoProcessingService {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    public void processVideo(Long taskId, String videoPath, ProcessConfig config) {
        ProcessMessage message = ProcessMessage.builder()
            .taskId(taskId)
            .videoPath(videoPath)
            .config(config)
            .timestamp(System.currentTimeMillis())
            .build();
            
        rabbitTemplate.convertAndSend("video.processing.queue", message);
        log.info("视频处理任务已发送到队列: taskId={}", taskId);
    }
}
```

### HTTP回调 (异步结果通知)
**使用场景**: 处理进度更新、结果通知
```python
# Python回调Java服务
import requests

class SpringBootCallback:
    def __init__(self, base_url="http://backend:8080"):
        self.base_url = base_url
    
    def update_progress(self, task_id: int, progress: int, step: str):
        url = f"{self.base_url}/api/tasks/{task_id}/progress"
        data = {
            "progress": progress,
            "currentStep": step,
            "timestamp": datetime.now().isoformat()
        }
        requests.post(url, json=data)
    
    def notify_completion(self, task_id: int, result_path: str):
        url = f"{self.base_url}/api/tasks/{task_id}/complete"
        data = {
            "status": "COMPLETED",
            "resultPath": result_path,
            "timestamp": datetime.now().isoformat()
        }
        requests.post(url, json=data)
```

### WebSocket (实时通信)
**使用场景**: 前端实时进度显示
```java
// Java WebSocket推送
@Controller
public class ProgressWebSocketController {
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @EventListener
    public void handleProgressUpdate(ProgressUpdateEvent event) {
        String destination = "/topic/progress/" + event.getTaskId();
        messagingTemplate.convertAndSend(destination, event.getProgressData());
    }
}
```

## 🗄️ 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    progress INT DEFAULT 0,
    input_file_path VARCHAR(500),
    output_file_path VARCHAR(500),
    config JSON,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 处理步骤表 (task_steps)
```sql
CREATE TABLE task_steps (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    step_order INT NOT NULL,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    progress INT DEFAULT 0,
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    result JSON,
    error_message TEXT,
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);
```

## 🔧 API接口设计

### Spring Boot 主服务接口

#### 用户管理
```
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
GET  /api/auth/profile        # 获取用户信息
```

#### 任务管理
```
POST /api/tasks               # 创建任务
GET  /api/tasks               # 获取任务列表
GET  /api/tasks/{id}          # 获取任务详情
DELETE /api/tasks/{id}        # 删除任务
GET  /api/tasks/{id}/progress # 获取任务进度
```

#### 文件管理
```
POST /api/files/upload        # 上传文件
GET  /api/files/{id}/download # 下载文件
DELETE /api/files/{id}        # 删除文件
```

### Python AI 服务接口

#### 音频处理
```
POST /ai/audio/extract        # 音频提取
POST /ai/audio/recognize      # 语音识别
POST /ai/audio/optimize       # 音频优化
```

#### 翻译服务
```
POST /ai/translate/text       # 文本翻译
POST /ai/translate/subtitle   # 字幕翻译
```

#### TTS服务
```
POST /ai/tts/generate         # 生成语音
POST /ai/tts/batch           # 批量生成
```

#### 视频合成
```
POST /ai/video/compose        # 视频合成
POST /ai/video/add-subtitle   # 添加字幕
```

## 📊 通信方式对比与选择

### 通信方式对比表

| 通信方式 | 使用场景 | 优点 | 缺点 | 适用操作 |
|---------|---------|------|------|----------|
| **HTTP REST API** | 快速查询、状态检查 | 简单直接、易调试 | 同步阻塞、网络延迟 | 健康检查、配置获取 |
| **消息队列** | 长时间处理任务 | 异步解耦、高可靠性 | 复杂性高、调试困难 | 视频处理、批量操作 |
| **HTTP回调** | 异步结果通知 | 简单易懂、标准化 | 需要暴露端点 | 进度更新、结果通知 |
| **WebSocket** | 实时通信 | 双向通信、低延迟 | 连接管理复杂 | 前端实时进度显示 |

### 具体使用策略

#### 1. 任务启动流程
```
前端 → Spring Boot (HTTP) → 消息队列 → Python AI服务
```

#### 2. 进度更新流程
```
Python AI服务 → Spring Boot (HTTP回调) → 前端 (WebSocket)
```

#### 3. 状态查询流程
```
前端 → Spring Boot → Python AI服务 (HTTP) → 返回状态
```

## 🔄 消息队列设计

### 队列定义
```
video.processing.queue        # 视频处理队列
task.progress.queue          # 进度更新队列（备用）
task.result.queue            # 结果通知队列（备用）
```

### 消息格式
```json
{
    "taskId": 12345,
    "userId": 67890,
    "action": "PROCESS_VIDEO",
    "data": {
        "inputFile": "/path/to/input.mp4",
        "config": {
            "sourceLanguage": "en",
            "targetLanguage": "zh",
            "ttsVoice": "zh-CN-XiaoxiaoNeural"
        }
    },
    "timestamp": "2025-01-12T10:30:00Z"
}
```

## 🐳 Docker部署

### docker-compose.yml
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: video_translation
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7.0
    ports:
      - "6379:6379"

  rabbitmq:
    image: rabbitmq:3.11-management
    ports:
      - "5672:5672"
      - "15672:15672"

  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data

  backend:
    build: ./backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      SPRING_DATASOURCE_URL: *****************************************
      SPRING_REDIS_HOST: redis
      SPRING_RABBITMQ_HOST: rabbitmq

  python-ai-service:
    build: ./python-ai-service
    ports:
      - "8000:8000"
    depends_on:
      - rabbitmq
    environment:
      RABBITMQ_HOST: rabbitmq
    volumes:
      - ./data:/app/data

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  mysql_data:
  minio_data:
```

## 📊 处理流程详细设计

### 1. 视频上传流程
```
1. 用户在Vue前端选择视频文件
2. 前端调用Spring Boot上传接口
3. Spring Boot将文件保存到MinIO
4. 创建任务记录，状态为PENDING
5. 发送消息到处理队列
6. 返回任务ID给前端
```

### 2. AI处理流程
```
1. Python服务接收处理消息
2. 音频提取 (FFmpeg)
   - 更新进度: 10%
3. 语音识别 (WhisperX)
   - 更新进度: 30%
4. 文本翻译 (GPT API)
   - 更新进度: 50%
5. TTS生成 (Azure TTS)
   - 更新进度: 70%
6. 音频优化 (pydub)
   - 更新进度: 85%
7. 视频合成 (MoviePy)
   - 更新进度: 100%
8. 上传结果文件
9. 发送完成通知
```

### 3. 进度更新流程
```
1. Python服务每完成一个步骤
2. 发送进度消息到进度队列
3. Spring Boot接收进度消息
4. 更新数据库中的进度
5. 通过WebSocket推送给前端
6. 前端实时更新进度条
```

## 🔧 技术实现要点

### Spring Boot 主要依赖
```xml
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-amqp</artifactId>
    </dependency>
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
    </dependency>
</dependencies>
```

### Python 主要依赖
```txt
fastapi==0.104.1
uvicorn==0.24.0
celery==5.3.4
redis==5.0.1
pika==1.3.2
moviepy==1.0.3
pydub==0.25.1
numpy==1.24.3
openai==1.3.0
azure-cognitiveservices-speech==1.34.0
whisperx==3.1.1
requests==2.31.0
```

## 🚀 开发阶段规划

### 阶段1: 基础架构搭建 (3-4天)
1. 创建Spring Boot项目骨架
2. 创建Python FastAPI项目骨架
3. 创建Vue.js前端项目骨架
4. 配置Docker开发环境
5. 建立基础的数据库表结构

### 阶段2: 核心功能开发 (5-7天)
1. 实现文件上传下载功能
2. 实现任务管理和进度跟踪
3. 实现Python AI处理服务
4. 实现消息队列通信
5. 实现WebSocket实时通信

### 阶段3: 前端界面开发 (3-4天)
1. 实现用户界面
2. 实现文件上传界面
3. 实现任务管理界面
4. 实现进度监控界面
5. 实现结果展示界面

### 阶段4: 测试和优化 (2-3天)
1. 单元测试
2. 集成测试
3. 性能优化
4. 错误处理完善
5. 部署文档编写

## 📝 开发注意事项

### 1. 错误处理
- 所有API都要有统一的错误响应格式
- Python服务要有重试机制
- 文件处理要有异常恢复机制

### 2. 性能优化
- 大文件上传要支持分片上传
- 视频处理要支持并发处理
- 缓存常用的翻译结果

### 3. 安全考虑
- 文件上传要验证文件类型和大小
- API要有认证和授权
- 敏感信息要加密存储

### 4. 监控和日志
- 所有服务要有详细的日志
- 要有系统监控和报警
- 要有性能指标收集

## 🎯 下一步行动

1. **确认架构设计** - 您是否同意这个架构设计？
2. **技术选型确认** - 是否需要调整技术栈？
3. **开始实施** - 从哪个阶段开始？
4. **资源准备** - 需要准备哪些开发资源？

---

*此文档将作为项目开发的指导文档，所有开发活动都将基于此文档进行。* 