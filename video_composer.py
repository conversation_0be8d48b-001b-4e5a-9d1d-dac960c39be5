#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频合成器
将TTS生成的中文配音与原视频合成，生成完整的中文配音视频
"""

import os
import re
import argparse
from pathlib import Path
from typing import List, Tuple, Dict
import subprocess
from pydub import AudioSegment
from moviepy import VideoFileClip, AudioFileClip, CompositeAudioClip, TextClip, CompositeVideoClip

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')  # 兼容不同的毫秒分隔符
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str) -> List[Tuple[int, float, float, str]]:
    """读取SRT字幕文件
    
    Returns:
        List of (index, start_time, end_time, text) tuples
    """
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            time_line = lines[1]
            text = '\n'.join(lines[2:])
            
            # 解析时间
            start_time_str, end_time_str = time_line.split(' --> ')
            start_time = parse_srt_time(start_time_str.strip())
            end_time = parse_srt_time(end_time_str.strip())
            
            subtitles.append((index, start_time, end_time, text.strip()))
    
    return subtitles

def get_audio_duration(file_path: str) -> float:
    """获取音频文件时长"""
    try:
        audio = AudioSegment.from_file(file_path)
        return len(audio) / 1000.0
    except Exception as e:
        print(f"⚠️ 获取音频时长失败: {str(e)}")
        return 0.0

def adjust_audio_speed(input_path: str, output_path: str, speed_factor: float):
    """调整音频播放速度"""
    try:
        if abs(speed_factor - 1.0) < 0.01:
            # 速度接近1.0，直接复制文件
            import shutil
            shutil.copy2(input_path, output_path)
            return True
        
        # 使用ffmpeg调整速度
        cmd = [
            'ffmpeg', '-i', input_path, 
            '-filter:a', f'atempo={speed_factor}', 
            '-y', output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return True
        else:
            print(f"❌ ffmpeg调速失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 音频调速失败: {str(e)}")
        return False

def create_silence(duration: float, output_path: str):
    """创建指定时长的静音片段"""
    try:
        silence = AudioSegment.silent(duration=int(duration * 1000))
        silence.export(output_path, format="wav")
        return True
    except Exception as e:
        print(f"❌ 创建静音失败: {str(e)}")
        return False

def compose_audio_track(subtitles: List[Tuple], audio_dir: str, output_path: str, video_duration: float):
    """合成完整的音频轨道"""
    print("🎵 开始合成音频轨道...")
    
    # 创建临时目录
    temp_dir = "out/temp_audio"
    os.makedirs(temp_dir, exist_ok=True)
    
    audio_segments = []
    current_time = 0.0
    
    for i, (index, start_time, end_time, text) in enumerate(subtitles):
        # 查找对应的音频文件
        audio_file = os.path.join(audio_dir, f"subtitle_{index:03d}.wav")
        
        if not os.path.exists(audio_file):
            print(f"⚠️ 音频文件不存在: {audio_file}")
            continue
        
        # 获取音频时长
        audio_duration = get_audio_duration(audio_file)
        subtitle_duration = end_time - start_time
        
        print(f"🔄 [{i+1}/{len(subtitles)}] 处理字幕 {index}")
        print(f"   时间轴: {start_time:.2f}s - {end_time:.2f}s (目标时长: {subtitle_duration:.2f}s)")
        print(f"   音频时长: {audio_duration:.2f}s")
        
        # 如果需要在开头添加静音
        if start_time > current_time:
            silence_duration = start_time - current_time
            print(f"   ➕ 添加静音: {silence_duration:.2f}s")
            silence_file = os.path.join(temp_dir, f"silence_{i}.wav")
            if create_silence(silence_duration, silence_file):
                audio_segments.append(silence_file)
            current_time = start_time
        
        # 调整音频速度以匹配字幕时长
        speed_factor = audio_duration / subtitle_duration if subtitle_duration > 0 else 1.0
        
        if speed_factor < 0.5:
            speed_factor = 0.5  # 最慢0.5倍速
        elif speed_factor > 2.0:
            speed_factor = 2.0  # 最快2倍速
        
        adjusted_file = os.path.join(temp_dir, f"adjusted_{index:03d}.wav")
        
        if speed_factor != 1.0:
            print(f"   ⚡ 调整速度: {speed_factor:.2f}倍")
            if adjust_audio_speed(audio_file, adjusted_file, speed_factor):
                audio_segments.append(adjusted_file)
            else:
                # 速度调整失败，使用原文件
                audio_segments.append(audio_file)
        else:
            audio_segments.append(audio_file)
        
        current_time = end_time
    
    # 如果视频还有剩余时间，添加静音
    if current_time < video_duration:
        remaining_duration = video_duration - current_time
        print(f"➕ 添加结尾静音: {remaining_duration:.2f}s")
        final_silence = os.path.join(temp_dir, "final_silence.wav")
        if create_silence(remaining_duration, final_silence):
            audio_segments.append(final_silence)
    
    # 合并所有音频片段
    print("🔗 合并音频片段...")
    try:
        combined = AudioSegment.empty()
        for segment_file in audio_segments:
            if os.path.exists(segment_file):
                segment = AudioSegment.from_wav(segment_file)
                combined += segment
            else:
                print(f"⚠️ 音频片段不存在: {segment_file}")
        
        # 导出合成的音频
        combined.export(output_path, format="wav")
        print(f"✅ 音频轨道合成完成: {output_path}")
        print(f"   总时长: {len(combined)/1000:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频合成失败: {str(e)}")
        return False

def create_subtitle_clips(subtitles: List[Tuple], video_size: Tuple[int, int], subtitle_type: str = "chinese"):
    """创建字幕片段"""
    subtitle_clips = []
    
    for index, start_time, end_time, text in subtitles:
        # 字幕样式设置
        if subtitle_type == "chinese":
            fontsize = 36
            color = 'white'
            stroke_color = 'black'
            stroke_width = 2
            font = 'SimHei'  # 黑体，适合中文
            position = ('center', video_size[1] - 100)  # 底部
        else:  # english
            fontsize = 32
            color = 'yellow'
            stroke_color = 'black'
            stroke_width = 2
            font = 'Arial'
            position = ('center', video_size[1] - 150)  # 稍微高一点
        
        try:
            # 创建文本片段 - 使用新版MoviePy API
            txt_clip = TextClip(
                font=font,
                text=text,
                font_size=fontsize,
                color=color,
                stroke_color=stroke_color,
                stroke_width=stroke_width
            ).set_position(position).set_start(start_time).set_end(end_time)
            
            subtitle_clips.append(txt_clip)
            print(f"✅ 创建字幕: {text[:30]}... ({start_time:.2f}s-{end_time:.2f}s)")
            
        except Exception as e:
            print(f"⚠️ 字幕创建失败: {str(e)}, 使用默认设置")
            # 使用更简单的默认设置重试
            try:
                txt_clip = TextClip(
                    text=text,
                    font_size=fontsize,
                    color=color
                ).set_position(position).set_start(start_time).set_end(end_time)
                
                subtitle_clips.append(txt_clip)
                print(f"✅ 创建字幕(简化): {text[:30]}...")
            except Exception as e2:
                print(f"❌ 字幕创建完全失败: {str(e2)}")
                continue
    
    return subtitle_clips

def compose_video(video_path: str, audio_path: str, output_path: str, 
                 chinese_srt_path: str = None, english_srt_path: str = None, 
                 keep_original_audio: bool = False, add_subtitles: bool = True):
    """将音频与视频合成，并添加字幕"""
    try:
        print("🎬 开始视频合成...")
        
        # 加载视频
        video = VideoFileClip(video_path)
        print(f"📹 原视频时长: {video.duration:.2f}s")
        print(f"📱 视频尺寸: {video.size}")
        
        # 加载新音频
        new_audio = AudioFileClip(audio_path)
        print(f"🎵 配音时长: {new_audio.duration:.2f}s")
        
        if keep_original_audio:
            # 保留原音频，降低音量后与配音混合
            original_audio = video.audio.with_volume_scaled(0.1)  # 原音频降至10%音量
            mixed_audio = CompositeAudioClip([original_audio, new_audio])
            final_video = video.with_audio(mixed_audio)
        else:
            # 完全替换音频
            final_video = video.with_audio(new_audio)
        
        # 添加字幕
        if add_subtitles:
            print("📝 开始添加字幕...")
            all_clips = [final_video]
            
            # 添加中文字幕
            if chinese_srt_path and os.path.exists(chinese_srt_path):
                print("🇨🇳 添加中文字幕...")
                chinese_subtitles = read_srt_file(chinese_srt_path)
                chinese_clips = create_subtitle_clips(chinese_subtitles, video.size, "chinese")
                all_clips.extend(chinese_clips)
            
            # 添加英文字幕
            if english_srt_path and os.path.exists(english_srt_path):
                print("🇺🇸 添加英文字幕...")
                english_subtitles = read_srt_file(english_srt_path)
                english_clips = create_subtitle_clips(english_subtitles, video.size, "english")
                all_clips.extend(english_clips)
            
            if len(all_clips) > 1:
                final_video = CompositeVideoClip(all_clips)
                print(f"✅ 字幕添加完成，共 {len(all_clips)-1} 个字幕片段")
            else:
                print("⚠️ 没有找到字幕文件，只输出配音视频")
        
        # 导出最终视频
        print("💾 正在导出视频...")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # 清理资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"✅ 视频合成完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 视频合成失败: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="视频合成器 - 将TTS配音与原视频合成，并添加字幕")
    parser.add_argument("--video", default="translation-service/vedio/test_30s.mp4", help="原视频文件路径")
    parser.add_argument("--chinese_srt", default="VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt", help="中文字幕文件路径")
    parser.add_argument("--english_srt", default="VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output.srt", help="英文字幕文件路径")
    parser.add_argument("--audio_dir", default="out/audio", help="TTS音频文件目录")
    parser.add_argument("--output", default="out/video_chinese_dubbed_with_subs.mp4", help="输出视频文件路径")
    parser.add_argument("--keep_original", action="store_true", help="保留原音频（降低音量）")
    parser.add_argument("--no_subtitles", action="store_true", help="不添加字幕")
    parser.add_argument("--chinese_only", action="store_true", help="只添加中文字幕")
    parser.add_argument("--english_only", action="store_true", help="只添加英文字幕")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return
    
    if not os.path.exists(args.audio_dir):
        print(f"❌ 音频目录不存在: {args.audio_dir}")
        return
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    print("🚀 开始视频合成流程...")
    print(f"📹 原视频: {args.video}")
    print(f"📝 中文字幕: {args.chinese_srt}")
    print(f"📝 英文字幕: {args.english_srt}")
    print(f"🎵 音频目录: {args.audio_dir}")
    print(f"💾 输出视频: {args.output}")
    
    # 获取视频时长
    try:
        video = VideoFileClip(args.video)
        video_duration = video.duration
        video.close()
        print(f"📊 视频时长: {video_duration:.2f}s")
    except Exception as e:
        print(f"❌ 无法读取视频文件: {str(e)}")
        return
    
    # 确定字幕文件
    chinese_srt = args.chinese_srt if not args.english_only else None
    english_srt = args.english_srt if not args.chinese_only else None
    
    if args.no_subtitles:
        chinese_srt = None
        english_srt = None
    
    # 检查字幕文件存在性
    if chinese_srt and not os.path.exists(chinese_srt):
        print(f"⚠️ 中文字幕文件不存在: {chinese_srt}")
        chinese_srt = None
    
    if english_srt and not os.path.exists(english_srt):
        print(f"⚠️ 英文字幕文件不存在: {english_srt}")
        english_srt = None
    
    # 合成音频轨道（如果还没有）
    combined_audio_path = "out/combined_audio.wav"
    if not os.path.exists(combined_audio_path):
        print("🎵 音频轨道不存在，开始重新合成...")
        if chinese_srt:
            subtitles = read_srt_file(chinese_srt)
            success = compose_audio_track(subtitles, args.audio_dir, combined_audio_path, video_duration)
            if not success:
                print("❌ 音频轨道合成失败")
                return
        else:
            print("❌ 需要中文字幕文件来合成音频轨道")
            return
    else:
        print("✅ 使用现有的音频轨道")
    
    # 合成最终视频
    success = compose_video(
        args.video, 
        combined_audio_path, 
        args.output, 
        chinese_srt, 
        english_srt,
        args.keep_original, 
        not args.no_subtitles
    )
    
    if success:
        print(f"\n🎉 带字幕的视频合成完成！")
        print(f"📁 输出文件: {args.output}")
        print(f"🎬 现在你可以播放查看中文配音+字幕效果了！")
        
        if chinese_srt:
            print("📝 包含中文字幕")
        if english_srt:
            print("📝 包含英文字幕")
    else:
        print("❌ 视频合成失败")

if __name__ == "__main__":
    main() 