#!/usr/bin/env python3
"""
检查Python文件语法
"""

import ast
import sys

def check_syntax(filename):
    """检查Python文件语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source, filename=filename)
        print(f"✅ {filename} 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"  行 {e.lineno}: {e.text}")
        print(f"  错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {filename} 检查失败: {e}")
        return False

if __name__ == "__main__":
    files_to_check = [
        "python-ai-service/app/services/video_service.py",
        "python-ai-service/app/services/subtitle_service.py",
        "python-ai-service/app/services/task_service.py"
    ]
    
    all_ok = True
    for filename in files_to_check:
        if not check_syntax(filename):
            all_ok = False
    
    if all_ok:
        print("\n🎉 所有文件语法检查通过!")
    else:
        print("\n⚠️ 有文件存在语法错误")
        sys.exit(1)
