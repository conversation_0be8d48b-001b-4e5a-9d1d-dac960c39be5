<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多媒体翻译服务系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .log-output {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .service-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .service-card:hover {
            transform: translateY(-2px);
        }
        .tab-content {
            min-height: 500px;
        }
        .result-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-language me-2"></i>
                    多媒体翻译服务系统
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="fas fa-globe me-1"></i>
                        文本翻译 | 语音合成 | 视频配音
                    </span>
                </div>
            </div>
        </nav>

        <!-- 服务选项卡 -->
        <ul class="nav nav-tabs mb-4" id="serviceTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="translation-tab" data-bs-toggle="tab" data-bs-target="#translation" type="button" role="tab">
                    <i class="fas fa-language me-2"></i>文本翻译
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tts-tab" data-bs-toggle="tab" data-bs-target="#tts" type="button" role="tab">
                    <i class="fas fa-volume-up me-2"></i>语音合成
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dubbing-tab" data-bs-toggle="tab" data-bs-target="#dubbing" type="button" role="tab">
                    <i class="fas fa-video me-2"></i>视频配音
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="audio-tab" data-bs-toggle="tab" data-bs-target="#audio" type="button" role="tab">
                    <i class="fas fa-music me-2"></i>音频处理
                </button>
            </li>
        </ul>

        <!-- 选项卡内容 -->
        <div class="tab-content" id="serviceTabContent">
            <!-- 文本翻译 -->
            <div class="tab-pane fade show active" id="translation" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-edit me-2"></i>文本翻译</h5>
                            </div>
                            <div class="card-body">
                                <form id="translationForm">
                                    <div class="mb-3">
                                        <label for="sourceText" class="form-label">源文本:</label>
                                        <textarea id="sourceText" class="form-control" rows="6" placeholder="请输入要翻译的文本..."></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="sourceLang" class="form-label">源语言:</label>
                                                <select id="sourceLang" class="form-select">
                                                    <option value="auto">自动检测</option>
                                                    <option value="en">英语</option>
                                                    <option value="zh">中文</option>
                                                    <option value="ja">日语</option>
                                                    <option value="ko">韩语</option>
                                                    <option value="fr">法语</option>
                                                    <option value="de">德语</option>
                                                    <option value="es">西班牙语</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="targetLang" class="form-label">目标语言:</label>
                                                <select id="targetLang" class="form-select">
                                                    <option value="zh">中文</option>
                                                    <option value="en">英语</option>
                                                    <option value="ja">日语</option>
                                                    <option value="ko">韩语</option>
                                                    <option value="fr">法语</option>
                                                    <option value="de">德语</option>
                                                    <option value="es">西班牙语</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="translationProvider" class="form-label">翻译服务:</label>
                                                <select id="translationProvider" class="form-select">
                                                    <option value="">自动选择</option>
                                                    <option value="baidu">百度翻译</option>
                                                    <option value="google">Google翻译</option>
                                                    <option value="deepl">DeepL翻译</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-language me-2"></i>开始翻译
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-file-alt me-2"></i>翻译结果</h5>
                            </div>
                            <div class="card-body">
                                <div id="translationResult" class="result-output">
                                    等待翻译结果...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 语音合成 -->
            <div class="tab-pane fade" id="tts" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-microphone me-2"></i>语音合成</h5>
                            </div>
                            <div class="card-body">
                                <form id="ttsForm">
                                    <div class="mb-3">
                                        <label for="ttsText" class="form-label">合成文本:</label>
                                        <textarea id="ttsText" class="form-control" rows="4" placeholder="请输入要合成语音的文本..."></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="ttsProvider" class="form-label">TTS服务:</label>
                                                <select id="ttsProvider" class="form-select">
                                                    <option value="edge">Edge TTS</option>
                                                    <option value="baidu">百度TTS</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="ttsVoice" class="form-label">语音:</label>
                                                <select id="ttsVoice" class="form-select">
                                                    <option value="">默认语音</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-volume-up me-2"></i>生成语音
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-headphones me-2"></i>音频播放</h5>
                            </div>
                            <div class="card-body">
                                <div id="ttsResult" class="result-output">
                                    等待生成音频...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频配音 -->
            <div class="tab-pane fade" id="dubbing" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-video me-2"></i>视频配音</h5>
                            </div>
                            <div class="card-body">
                                <form id="dubbingForm">
                                    <div class="mb-3">
                                        <label for="videoFile" class="form-label">视频文件:</label>
                                        <input type="file" id="videoFile" class="form-control" accept="video/*">
                                        <div class="form-text">支持格式: MP4, AVI, MOV, MKV等</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="dubbingText" class="form-label">配音文本:</label>
                                        <textarea id="dubbingText" class="form-control" rows="4" placeholder="请输入配音文本..."></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="dubbingProvider" class="form-label">TTS服务:</label>
                                                <select id="dubbingProvider" class="form-select">
                                                    <option value="edge">Edge TTS</option>
                                                    <option value="baidu">百度TTS</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="dubbingVoice" class="form-label">语音:</label>
                                                <select id="dubbingVoice" class="form-select">
                                                    <option value="">默认语音</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-play me-2"></i>开始配音
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-film me-2"></i>配音结果</h5>
                            </div>
                            <div class="card-body">
                                <div id="dubbingResult" class="result-output">
                                    等待配音结果...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 音频处理 -->
            <div class="tab-pane fade" id="audio" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-music me-2"></i>音频处理</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-4">
                                    <h6>从视频提取音频</h6>
                                    <form id="extractForm">
                                        <div class="mb-3">
                                            <input type="file" id="extractVideoFile" class="form-control" accept="video/*">
                                        </div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-extract me-1"></i>提取音频
                                        </button>
                                    </form>
                                </div>
                                
                                <div class="mb-4">
                                    <h6>合并音频和视频</h6>
                                    <form id="mergeForm">
                                        <div class="mb-2">
                                            <label class="form-label">视频文件:</label>
                                            <input type="file" id="mergeVideoFile" class="form-control" accept="video/*">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">音频文件:</label>
                                            <input type="file" id="mergeAudioFile" class="form-control" accept="audio/*">
                                        </div>
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="fas fa-merge me-1"></i>合并文件
                                        </button>
                                    </form>
                                </div>
                                
                                <div>
                                    <h6>获取音频时长</h6>
                                    <form id="durationForm">
                                        <div class="mb-3">
                                            <input type="file" id="durationAudioFile" class="form-control" accept="audio/*">
                                        </div>
                                        <button type="submit" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-clock me-1"></i>获取时长
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs me-2"></i>处理结果</h5>
                            </div>
                            <div class="card-body">
                                <div id="audioResult" class="result-output">
                                    等待处理结果...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>使用说明</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6><i class="fas fa-language me-2"></i>文本翻译</h6>
                        <ul class="small">
                            <li>支持多种语言互译</li>
                            <li>自动检测源语言</li>
                            <li>多个翻译服务可选</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-volume-up me-2"></i>语音合成</h6>
                        <ul class="small">
                            <li>支持Edge TTS和百度TTS</li>
                            <li>多种语音可选</li>
                            <li>高质量音频输出</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-video me-2"></i>视频配音</h6>
                        <ul class="small">
                            <li>上传视频文件</li>
                            <li>输入配音文本</li>
                            <li>自动生成配音视频</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-music me-2"></i>音频处理</h6>
                        <ul class="small">
                            <li>从视频提取音频</li>
                            <li>音频视频合并</li>
                            <li>获取音频时长</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 文本翻译
        document.getElementById('translationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const text = document.getElementById('sourceText').value;
            const sourceLang = document.getElementById('sourceLang').value;
            const targetLang = document.getElementById('targetLang').value;
            const provider = document.getElementById('translationProvider').value;
            
            if (!text.trim()) {
                alert('请输入要翻译的文本');
                return;
            }
            
            const resultDiv = document.getElementById('translationResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 翻译中...';
            
            fetch('/api/translation/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: text,
                    sourceLanguage: sourceLang === 'auto' ? null : sourceLang,
                    targetLanguage: targetLang,
                    provider: provider || null
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>翻译结果:</h6>
                            <p class="mb-2">${data.translatedText}</p>
                            <small class="text-muted">检测到的语言: ${data.detectedLanguage || '未知'}</small>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">翻译失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        });

        // 语音合成
        document.getElementById('ttsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const text = document.getElementById('ttsText').value;
            const provider = document.getElementById('ttsProvider').value;
            const voice = document.getElementById('ttsVoice').value;
            
            if (!text.trim()) {
                alert('请输入要合成的文本');
                return;
            }
            
            const resultDiv = document.getElementById('ttsResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
            
            fetch('/api/translation/tts/synthesize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: text,
                    provider: provider,
                    voice: voice || null
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>语音生成成功:</h6>
                            <audio controls class="w-100 mt-2">
                                <source src="${data.audioUrl}" type="audio/mpeg">
                                您的浏览器不支持音频播放。
                            </audio>
                            <div class="mt-2">
                                <a href="${data.audioUrl}" class="btn btn-sm btn-outline-primary" download>
                                    <i class="fas fa-download me-1"></i>下载音频
                                </a>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">生成失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        });

        // 视频文件选择后自动进行语音识别和翻译
        document.getElementById('videoFile').addEventListener('change', function(e) {
            const videoFile = e.target.files[0];
            if (!videoFile) return;
            
            const dubbingTextArea = document.getElementById('dubbingText');
            dubbingTextArea.value = '正在处理视频，请稍候...';
            dubbingTextArea.disabled = true;
            
            const formData = new FormData();
            formData.append('videoFile', videoFile);
            formData.append('sourceLanguage', 'auto'); // 自动检测源语言
            formData.append('targetLanguage', 'zh'); // 翻译为中文
            
            fetch('/api/translation/video/asr-translate', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    dubbingTextArea.value = data.translatedText;
                    dubbingTextArea.disabled = false;
                    
                    // 显示识别和翻译结果的提示
                    const resultDiv = document.getElementById('dubbingResult');
                    resultDiv.innerHTML = `
                        <div class="alert alert-info">
                            <h6>语音识别和翻译完成:</h6>
                            <p><strong>识别文本:</strong> ${data.recognizedText}</p>
                            <p><strong>翻译结果:</strong> ${data.translatedText}</p>
                            <small class="text-muted">已自动填入配音文本框，您可以进行编辑后开始配音</small>
                        </div>
                    `;
                } else {
                    dubbingTextArea.value = '处理失败: ' + data.message;
                    dubbingTextArea.disabled = false;
                    
                    const resultDiv = document.getElementById('dubbingResult');
                    resultDiv.innerHTML = `<div class="alert alert-danger">语音识别失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                dubbingTextArea.value = '处理失败: ' + error.message;
                dubbingTextArea.disabled = false;
                
                const resultDiv = document.getElementById('dubbingResult');
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        });
        
        // 视频配音
        document.getElementById('dubbingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const videoFile = document.getElementById('videoFile').files[0];
            const text = document.getElementById('dubbingText').value;
            const provider = document.getElementById('dubbingProvider').value;
            const voice = document.getElementById('dubbingVoice').value;
            
            if (!videoFile) {
                alert('请选择视频文件');
                return;
            }
            
            if (!text.trim()) {
                alert('请输入配音文本');
                return;
            }
            
            const resultDiv = document.getElementById('dubbingResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 配音中...';
            
            const formData = new FormData();
            formData.append('videoFile', videoFile);
            formData.append('text', text);
            formData.append('provider', provider);
            if (voice) formData.append('voice', voice);
            
            fetch('/api/translation/video/dubbing', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>配音完成:</h6>
                            <video controls class="w-100 mt-2">
                                <source src="${data.videoUrl}" type="video/mp4">
                                您的浏览器不支持视频播放。
                            </video>
                            <div class="mt-2">
                                <a href="${data.videoUrl}" class="btn btn-sm btn-outline-primary" download>
                                    <i class="fas fa-download me-1"></i>下载视频
                                </a>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">配音失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        });

        // 音频提取
        document.getElementById('extractForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const videoFile = document.getElementById('extractVideoFile').files[0];
            
            if (!videoFile) {
                alert('请选择视频文件');
                return;
            }
            
            const resultDiv = document.getElementById('audioResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提取中...';
            
            const formData = new FormData();
            formData.append('videoFile', videoFile);
            
            fetch('/api/translation/audio/extract', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>音频提取成功:</h6>
                            <audio controls class="w-100 mt-2">
                                <source src="${data.audioUrl}" type="audio/wav">
                                您的浏览器不支持音频播放。
                            </audio>
                            <div class="mt-2">
                                <a href="${data.audioUrl}" class="btn btn-sm btn-outline-primary" download>
                                    <i class="fas fa-download me-1"></i>下载音频
                                </a>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">提取失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        });

        // 音频视频合并
        document.getElementById('mergeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const videoFile = document.getElementById('mergeVideoFile').files[0];
            const audioFile = document.getElementById('mergeAudioFile').files[0];
            
            if (!videoFile || !audioFile) {
                alert('请选择视频和音频文件');
                return;
            }
            
            const resultDiv = document.getElementById('audioResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 合并中...';
            
            const formData = new FormData();
            formData.append('videoFile', videoFile);
            formData.append('audioFile', audioFile);
            
            fetch('/api/translation/audio/merge', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>合并完成:</h6>
                            <video controls class="w-100 mt-2">
                                <source src="${data.videoUrl}" type="video/mp4">
                                您的浏览器不支持视频播放。
                            </video>
                            <div class="mt-2">
                                <a href="${data.videoUrl}" class="btn btn-sm btn-outline-primary" download>
                                    <i class="fas fa-download me-1"></i>下载视频
                                </a>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">合并失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        });

        // 获取音频时长
        document.getElementById('durationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const audioFile = document.getElementById('durationAudioFile').files[0];
            
            if (!audioFile) {
                alert('请选择音频文件');
                return;
            }
            
            const resultDiv = document.getElementById('audioResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';
            
            const formData = new FormData();
            formData.append('audioFile', audioFile);
            
            fetch('/api/translation/audio/duration', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-info">
                            <h6>音频信息:</h6>
                            <p class="mb-1">文件名: ${audioFile.name}</p>
                            <p class="mb-1">时长: ${data.duration} 秒</p>
                            <p class="mb-0">格式化时长: ${formatDuration(data.duration)}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">分析失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            });
        });

        // 格式化时长
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }

        // 加载TTS提供商和语音列表
        function loadTTSProviders() {
            fetch('/api/translation/tts/providers')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 可以在这里更新提供商列表
                    console.log('TTS提供商:', data.providers);
                }
            })
            .catch(error => {
                console.error('加载TTS提供商失败:', error);
            });
        }

        // 加载语音列表
        function loadVoices(provider, selectElement) {
            fetch(`/api/translation/tts/voices?provider=${provider}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.voices) {
                    selectElement.innerHTML = '<option value="">默认语音</option>';
                    data.voices.forEach(voice => {
                        const option = document.createElement('option');
                        // Edge TTS 使用 voice.voice 字段作为语音ID，百度TTS使用 voice.voice 字段
                        option.value = voice.voice || voice.id || voice.name;
                        option.textContent = voice.name || voice.id;
                        selectElement.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('加载语音列表失败:', error);
            });
        }

        // TTS提供商变化时加载语音列表
        document.getElementById('ttsProvider').addEventListener('change', function() {
            loadVoices(this.value, document.getElementById('ttsVoice'));
        });

        document.getElementById('dubbingProvider').addEventListener('change', function() {
            loadVoices(this.value, document.getElementById('dubbingVoice'));
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTTSProviders();
            loadVoices('edge', document.getElementById('ttsVoice'));
            loadVoices('edge', document.getElementById('dubbingVoice'));
        });
    </script>
</body>
</html>