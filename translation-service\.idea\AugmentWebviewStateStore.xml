<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;58636f87-927d-440d-a659-a850ebf784fc&quot;,&quot;conversations&quot;:{&quot;fe3d11e5-a2aa-47ff-b959-c91019aab10b&quot;:{&quot;id&quot;:&quot;fe3d11e5-a2aa-47ff-b959-c91019aab10b&quot;,&quot;createdAtIso&quot;:&quot;2025-07-06T10:34:43.194Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-06T10:34:43.194Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;565a4115-2b10-4e5f-af95-3a56ed02c5ff&quot;},&quot;58636f87-927d-440d-a659-a850ebf784fc&quot;:{&quot;id&quot;:&quot;58636f87-927d-440d-a659-a850ebf784fc&quot;,&quot;createdAtIso&quot;:&quot;2025-07-06T10:34:43.409Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-06T10:34:43.410Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;366d8152-fe19-4b88-8ab8-1c06d915ac4d&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>