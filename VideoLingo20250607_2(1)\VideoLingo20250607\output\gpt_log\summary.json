[{"model": "deepseek-ai/DeepSeek-V3", "prompt": "## Role\nYou are a video translation expert and terminology consultant, specializing in en comprehension and 简体中文 expression optimization.\n\n## Task\nFor the provided en video text:\n1. Summarize main topic in two sentences\n2. Extract professional terms/names with 简体中文 translations (excluding existing terms)\n3. Provide brief explanation for each term\n\n\n### Existing Terms\nPlease exclude these terms in your extraction:\n\n\nSteps:\n1. Topic Summary:\n   - Quick scan for general understanding\n   - Write two sentences: first for main topic, second for key point\n2. Term Extraction:\n   - Mark professional terms and names (excluding those listed in Existing Terms)\n   - Provide 简体中文 translation or keep original\n   - Add brief explanation\n   - Extract less than 15 terms\n\n## INPUT\n<text>\nHey everyone, welcome to this crash course on Phaser Editor V4. Today we'll explore how you can quickly and easily build Phaser 3 games without needing to write a ton of code manually. If you're new to Phaser or game development in general you're in the right place. Phaser Editor V4 is a powerful tool for building HTML5 games visually letting you focus on creating rather than getting lost in code. In this video, I'll guide you step-by-step through setting up a project, using the editor, and creating a simple game. Here's what we'll cover today. What is the Phaser Editor? How to quickly set up a brand new project?\n</text>\n\n## Output in only JSON format and no other text\n{\n  \"theme\": \"Two-sentence video summary\",\n  \"terms\": [\n    {\n      \"src\": \"en term\",\n      \"tgt\": \"简体中文 translation or original\", \n      \"note\": \"Brief explanation\"\n    },\n    ...\n  ]\n}  \n\n## Example\n{\n  \"theme\": \"本视频介绍人工智能在医疗领域的应用现状。重点展示了AI在医学影像诊断和药物研发中的突破性进展。\",\n  \"terms\": [\n    {\n      \"src\": \"Machine Learning\",\n      \"tgt\": \"机器学习\",\n      \"note\": \"AI的核心技术，通过数据训练实现智能决策\"\n    },\n    {\n      \"src\": \"CNN\",\n      \"tgt\": \"CNN\",\n      \"note\": \"卷积神经网络，用于医学图像识别的深度学习模型\"\n    }\n  ]\n}\n\nNote: Start you answer with ```json and end with ```, do not add any other text.", "resp_content": "```json\n{\n  \"theme\": \"本视频是关于Phaser Editor V4的快速入门教程。重点介绍如何使用这个可视化工具快速构建Phaser 3游戏而无需手动编写大量代码。\",\n  \"terms\": [\n    {\n      \"src\": \"Phaser Editor V4\",\n      \"tgt\": \"Phaser编辑器V4\",\n      \"note\": \"用于构建HTML5游戏的可视化开发工具\"\n    },\n    {\n      \"src\": \"Phaser 3\",\n      \"tgt\": \"Phaser 3\",\n      \"note\": \"流行的HTML5游戏框架的最新版本\"\n    },\n    {\n      \"src\": \"HTML5 games\",\n      \"tgt\": \"HTML5游戏\",\n      \"note\": \"基于网页技术开发的跨平台游戏\"\n    },\n    {\n      \"src\": \"crash course\",\n      \"tgt\": \"速成课程\",\n      \"note\": \"快速入门的教学课程\"\n    },\n    {\n      \"src\": \"visual tool\",\n      \"tgt\": \"可视化工具\",\n      \"note\": \"通过图形界面而非代码来创建内容的开发工具\"\n    }\n  ]\n}\n```", "resp_type": "json", "resp": {"theme": "本视频是关于Phaser Editor V4的快速入门教程。重点介绍如何使用这个可视化工具快速构建Phaser 3游戏而无需手动编写大量代码。", "terms": [{"src": "Phaser Editor V4", "tgt": "Phaser编辑器V4", "note": "用于构建HTML5游戏的可视化开发工具"}, {"src": "Phaser 3", "tgt": "Phaser 3", "note": "流行的HTML5游戏框架的最新版本"}, {"src": "HTML5 games", "tgt": "HTML5游戏", "note": "基于网页技术开发的跨平台游戏"}, {"src": "crash course", "tgt": "速成课程", "note": "快速入门的教学课程"}, {"src": "visual tool", "tgt": "可视化工具", "note": "通过图形界面而非代码来创建内容的开发工具"}]}, "message": null}]