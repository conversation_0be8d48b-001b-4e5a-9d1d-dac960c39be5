#!/usr/bin/env python3
"""
测试MoviePy get_frame错误修复
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def test_video_service():
    """测试VideoService的修复"""
    try:
        print("🎬 测试VideoService修复...")
        
        from app.services.video_service import VideoService
        
        # 初始化服务
        video_service = VideoService()
        print("✅ VideoService初始化成功")
        
        # 检查是否有compose_video_to_path方法
        if hasattr(video_service, 'compose_video_to_path'):
            print("✅ compose_video_to_path 方法存在")
        else:
            print("❌ compose_video_to_path 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ VideoService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ffmpeg_availability():
    """测试FFmpeg可用性"""
    try:
        print("\n🔧 测试FFmpeg可用性...")
        
        import subprocess
        
        # 测试FFmpeg命令
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ FFmpeg可用")
            # 显示版本信息的第一行
            version_line = result.stdout.split('\n')[0]
            print(f"  版本: {version_line}")
            return True
        else:
            print("❌ FFmpeg不可用")
            return False
            
    except FileNotFoundError:
        print("❌ FFmpeg未安装或不在PATH中")
        return False
    except Exception as e:
        print(f"❌ FFmpeg测试失败: {e}")
        return False

async def test_directory_structure():
    """测试目录结构是否正常"""
    try:
        print("\n📁 测试目录结构...")
        
        from app.services.directory_service import DirectoryService
        
        # 初始化服务
        dir_service = DirectoryService()
        
        # 创建测试目录
        test_paths = dir_service.create_task_directory("test_fix.mp4", "fix_test_123")
        
        print(f"✅ 测试目录创建: {test_paths['task_name']}")
        
        # 检查所有必要的子目录
        required_dirs = ['source', 'audio', 'transcription', 'translation', 'tts', 'subtitles', 'output']
        
        for dir_name in required_dirs:
            if dir_name in test_paths:
                dir_path = Path(test_paths[dir_name])
                if dir_path.exists():
                    print(f"  ✅ {dir_name}: {dir_path}")
                else:
                    print(f"  ❌ {dir_name}: 目录不存在")
                    return False
            else:
                print(f"  ❌ {dir_name}: 路径未定义")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 目录结构测试失败: {e}")
        return False

async def test_service_integration():
    """测试服务集成"""
    try:
        print("\n🔗 测试服务集成...")
        
        from app.services.task_service import TaskService
        
        # 初始化TaskService
        task_service = TaskService()
        print("✅ TaskService初始化成功")
        
        # 检查所有服务是否正确集成
        services = [
            ('directory_service', 'DirectoryService'),
            ('audio_service', 'AudioService'),
            ('speech_service', 'SpeechService'),
            ('translation_service', 'TranslationService'),
            ('tts_service', 'TTSService'),
            ('video_service', 'VideoService'),
            ('subtitle_service', 'SubtitleService')
        ]
        
        for attr_name, service_name in services:
            if hasattr(task_service, attr_name):
                service = getattr(task_service, attr_name)
                if service is not None:
                    print(f"  ✅ {service_name}: 已集成")
                else:
                    print(f"  ❌ {service_name}: 为None")
                    return False
            else:
                print(f"  ❌ {service_name}: 未集成")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_new_methods():
    """测试新添加的*_to_path方法"""
    try:
        print("\n🆕 测试新方法...")
        
        # 测试各个服务的新方法
        services_and_methods = [
            ('app.services.audio_service', 'AudioService', 'extract_audio_to_path'),
            ('app.services.speech_service', 'SpeechService', 'recognize_speech_to_path'),
            ('app.services.translation_service', 'TranslationService', 'translate_text_to_path'),
            ('app.services.tts_service', 'TTSService', 'synthesize_speech_to_path'),
            ('app.services.video_service', 'VideoService', 'compose_video_to_path'),
            ('app.services.subtitle_service', 'SubtitleService', 'generate_subtitles_to_path')
        ]
        
        for module_name, class_name, method_name in services_and_methods:
            try:
                module = __import__(module_name, fromlist=[class_name])
                service_class = getattr(module, class_name)
                service_instance = service_class()
                
                if hasattr(service_instance, method_name):
                    print(f"  ✅ {class_name}.{method_name}")
                else:
                    print(f"  ❌ {class_name}.{method_name}: 方法不存在")
                    return False
                    
            except Exception as e:
                print(f"  ❌ {class_name}: 初始化失败 - {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 新方法测试失败: {e}")
        return False

async def main():
    print("🚀 开始MoviePy get_frame错误修复测试...")
    
    # 1. 测试FFmpeg可用性
    ffmpeg_ok = await test_ffmpeg_availability()
    
    # 2. 测试VideoService修复
    video_ok = await test_video_service()
    
    # 3. 测试目录结构
    dir_ok = await test_directory_structure()
    
    # 4. 测试服务集成
    integration_ok = await test_service_integration()
    
    # 5. 测试新方法
    methods_ok = await test_new_methods()
    
    print(f"\n📊 测试结果总结:")
    print(f"  FFmpeg可用性: {'✅ 通过' if ffmpeg_ok else '❌ 失败'}")
    print(f"  VideoService修复: {'✅ 通过' if video_ok else '❌ 失败'}")
    print(f"  目录结构: {'✅ 通过' if dir_ok else '❌ 失败'}")
    print(f"  服务集成: {'✅ 通过' if integration_ok else '❌ 失败'}")
    print(f"  新方法: {'✅ 通过' if methods_ok else '❌ 失败'}")
    
    if all([ffmpeg_ok, video_ok, dir_ok, integration_ok, methods_ok]):
        print("\n🎉 MoviePy get_frame错误修复成功!")
        print("\n✨ 修复内容:")
        print("1. 完全避开MoviePy的write_videofile方法")
        print("2. 直接使用FFmpeg进行视频合成")
        print("3. 避免了get_frame属性错误")
        print("4. 提供了完整的错误处理和回退机制")
        print("5. 集成了新的结构化目录系统")
        
        print("\n🚀 下一步:")
        print("1. 重启AI服务")
        print("2. 运行完整翻译流程")
        print("3. 验证视频合成不再出现get_frame错误")
        print("4. 检查生成的结构化目录")
        
    else:
        print("\n⚠️ 修复仍有问题")
        if not ffmpeg_ok:
            print("- FFmpeg不可用，需要安装或配置PATH")
        if not video_ok:
            print("- VideoService有问题")
        if not dir_ok:
            print("- 目录结构有问题")
        if not integration_ok:
            print("- 服务集成有问题")
        if not methods_ok:
            print("- 新方法有问题")

if __name__ == "__main__":
    asyncio.run(main())
