import os
import sys
import json
import time
import subprocess
import warnings
from typing import List, Dict

# 添加必要的库
import torch
import whisperx
import librosa
from moviepy.editor import VideoFileClip
import re

# 添加项目根目录到Python路径，以便导入core模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置工作目录为项目根目录，以便找到config.yaml
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

warnings.filterwarnings("ignore")

# 确保out目录存在
def ensure_output_dir():
    """确保输出目录存在"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    out_dir = os.path.join(script_dir, "out")
    os.makedirs(out_dir, exist_ok=True)
    return out_dir

def extract_audio_from_video(video_path, output_filename="temp_audio.wav"):
    """从视频中提取音频"""
    print(f"🎵 开始提取音频: {video_path}")
    
    # 创建输出目录并生成完整路径
    out_dir = ensure_output_dir()
    output_path = os.path.join(out_dir, output_filename)
    
    # 首先检查视频文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return None
    
    try:
        print(f"📂 正在打开视频文件...")
        video = VideoFileClip(video_path)
        
        print(f"📊 视频信息: 时长={video.duration:.2f}秒, 分辨率={video.size}")
        
        if video.audio is None:
            print(f"❌ 视频文件没有音频轨道")
            video.close()
            return None
        
        print(f"🎵 正在提取音频到: {output_path}")
        video.audio.write_audiofile(output_path, verbose=False, logger=None)
        video.close()
        
        # 检查文件是否真的生成了
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ 音频提取成功: {output_path} (大小: {file_size} 字节)")
            return output_path
        else:
            print(f"❌ 音频文件未生成: {output_path}")
            return None
            
    except Exception as e:
        print(f"❌ 音频提取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def whisperx_transcribe_with_alignment(audio_path):
    """使用WhisperX进行转录并添加对齐功能"""
    print(f"🎤 开始WhisperX转录（包含对齐）: {audio_path}")
    
    # 检查音频文件是否存在
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return None
    
    print(f"✅ 音频文件确认存在: {audio_path}")
    print(f"📁 当前工作目录: {os.getcwd()}")
    print(f"📂 音频文件绝对路径: {os.path.abspath(audio_path)}")
    
    try:
        # 检查GPU可用性
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🚀 使用设备: {device}")
        
        # 加载模型
        print("📥 正在加载WhisperX模型...")
        model = whisperx.load_model("large-v3", device, compute_type="float16" if device == "cuda" else "int8")
        print("✅ 模型加载成功")
        
        # 转录
        print("📝 开始转录...")
        print(f"🔍 转录文件路径: {os.path.abspath(audio_path)}")
        result = model.transcribe(audio_path)
        
        # 释放GPU资源
        del model
        torch.cuda.empty_cache()
        
        print(f"✅ 转录完成，检测到语言: {result.get('language', 'unknown')}")
        
        # 对齐时间戳
        print("🔗 开始时间戳对齐...")
        model_a, metadata = whisperx.load_align_model(language_code=result["language"], device=device)
        result = whisperx.align(result["segments"], model_a, metadata, audio_path, device, return_char_alignments=False)
        
        # 释放对齐模型
        torch.cuda.empty_cache()
        del model_a
        
        print("✅ 时间戳对齐完成")
        return result
        
    except Exception as e:
        print(f"❌ WhisperX转录失败: {e}")
        import traceback
        print("🔍 详细错误信息:")
        traceback.print_exc()
        raise

def segment_into_sentences(segments):
    """将转录结果分段为句子"""
    sentences = []
    current_sentence = ""
    current_start = None
    current_end = None
    
    for segment in segments:
        text = segment.get('text', '').strip()
        if not text:
            continue
            
        if current_start is None:
            current_start = segment.get('start', 0)
        
        current_sentence += " " + text if current_sentence else text
        current_end = segment.get('end', 0)
        
        # 检查是否句子结束（基于标点符号）
        if text.endswith(('.', '!', '?', '。', '！', '？')):
            sentences.append({
                'start': current_start,
                'end': current_end,
                'text': current_sentence.strip()
            })
            current_sentence = ""
            current_start = None
            current_end = None
    
    # 处理最后一个未完成的句子
    if current_sentence:
        sentences.append({
            'start': current_start,
            'end': current_end,
            'text': current_sentence.strip()
        })
    
    return sentences

def generate_srt(sentences, output_filename="output.srt"):
    """生成SRT字幕文件"""
    # 创建输出目录并生成完整路径
    out_dir = ensure_output_dir()
    output_file = os.path.join(out_dir, output_filename)
    
    print(f"📄 生成SRT字幕文件: {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, sentence in enumerate(sentences, 1):
            start_time = format_timestamp(sentence['start'])
            end_time = format_timestamp(sentence['end'])
            text = sentence['text']
            
            f.write(f"{i}\n")
            f.write(f"{start_time} --> {end_time}\n")
            f.write(f"{text}\n\n")
    
    print(f"✅ SRT文件生成完成: {output_file}")
    return output_file

def format_timestamp(seconds):
    """格式化时间戳为SRT格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def main():
    print("🚀 开始字幕生成流程...")
    print(f"📂 输出目录: {ensure_output_dir()}")
    
    # 视频文件路径 - 处理当前目录的input.mp4
    video_path = "input.mp4"
    print(f"📹 处理视频: {video_path}")
    
    # 1. 提取音频
    audio_path = extract_audio_from_video(video_path)
    if not audio_path:
        print("❌ 音频提取失败，流程终止")
        return
    
    # 2. WhisperX转录（包含对齐）
    try:
        result = whisperx_transcribe_with_alignment(audio_path)
    except Exception as e:
        print(f"❌ WhisperX转录失败，流程终止")
        return
    
    if not result or not result.get("segments"):
        print("❌ 转录结果为空，流程终止")
        return
    
    # 3. 分段为句子
    print("✂️ 分段为句子...")
    sentences = segment_into_sentences(result["segments"])
    print(f"✅ 分段完成，共{len(sentences)}个句子")
    
    # 4. 生成SRT字幕文件
    srt_file = generate_srt(sentences)
    
    print(f"🎉 字幕生成完成！")
    print(f"📄 字幕文件: {srt_file}")
    print(f"🎵 音频文件: {audio_path}")

if __name__ == "__main__":
    main() 