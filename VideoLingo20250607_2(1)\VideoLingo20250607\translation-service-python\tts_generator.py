#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS语音生成器
支持多种TTS引擎：Edge TTS（在线）、pyttsx3（本地）
"""

import os
import re
import sys
import asyncio
import argparse
from pathlib import Path
from typing import List, Tuple
import time

# 尝试导入不同的TTS库
TTS_ENGINES = {}

try:
    import edge_tts
    TTS_ENGINES['edge'] = True
except ImportError:
    TTS_ENGINES['edge'] = False

try:
    import pyttsx3
    TTS_ENGINES['pyttsx3'] = True
except ImportError:
    TTS_ENGINES['pyttsx3'] = False

from pydub import AudioSegment
from pydub.silence import split_on_silence

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')  # 兼容不同的毫秒分隔符
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def format_time_for_srt(seconds: float) -> str:
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"

def read_srt_file(srt_path: str) -> List[Tuple[int, str, str, str]]:
    """读取SRT字幕文件
    
    Returns:
        List of (index, start_time, end_time, text) tuples
    """
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            time_line = lines[1]
            text = '\n'.join(lines[2:])
            
            # 解析时间
            start_time, end_time = time_line.split(' --> ')
            
            subtitles.append((index, start_time.strip(), end_time.strip(), text.strip()))
    
    return subtitles

async def list_chinese_voices():
    """列出可用的中文语音"""
    if not TTS_ENGINES['edge']:
        print("❌ Edge TTS未安装，无法列出在线语音")
        return []
        
    try:
        voices = await edge_tts.list_voices()
        chinese_voices = [v for v in voices if 'zh-CN' in v['Locale']]
        
        print("🎤 可用的中文语音选项：")
        for voice in chinese_voices:
            gender = "女声" if voice['Gender'] == 'Female' else "男声"
            print(f"  {voice['ShortName']:<25} - {voice['FriendlyName']} ({gender})")
        
        return chinese_voices
    except Exception as e:
        print(f"❌ 获取语音列表失败: {str(e)}")
        return []

async def generate_audio_edge_tts(text: str, voice: str, output_path: str, max_retries: int = 3):
    """使用Edge TTS生成语音"""
    for attempt in range(max_retries):
        try:
            communicate = edge_tts.Communicate(text, voice)
            await communicate.save(output_path)
            
            # 验证文件是否成功生成
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                return True
            else:
                print(f"🔄 Edge TTS尝试 {attempt + 1}/{max_retries} 失败: 文件生成失败或为空")
                return False
                
        except Exception as e:
            print(f"🔄 Edge TTS尝试 {attempt + 1}/{max_retries} 失败: {str(e)}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2)  # 等待2秒后重试
            else:
                print(f"❌ Edge TTS最终失败，将尝试备用方案")
                return False

def generate_audio_pyttsx3(text: str, output_path: str, voice_id: int = 0, rate: int = 200):
    """使用pyttsx3本地TTS生成语音"""
    try:
        if not TTS_ENGINES['pyttsx3']:
            return False
            
        engine = pyttsx3.init()
        
        # 设置语音属性
        voices = engine.getProperty('voices')
        if voices and len(voices) > voice_id:
            engine.setProperty('voice', voices[voice_id].id)
        
        engine.setProperty('rate', rate)  # 语速
        engine.setProperty('volume', 0.9)  # 音量
        
        # 保存到文件
        engine.save_to_file(text, output_path)
        engine.runAndWait()
        
        # 检查文件是否生成
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ pyttsx3 TTS失败: {str(e)}")
        return False

def clean_text_for_tts(text: str) -> str:
    """清理文本，移除可能导致TTS问题的字符"""
    # 移除特殊字符
    chars_to_remove = ['&', '®', '™', '©', '\n', '\r']
    for char in chars_to_remove:
        text = text.replace(char, ' ')
    
    # 清理多余空格
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

async def generate_audio_for_text(text: str, voice: str, output_path: str, engine: str = "auto"):
    """为单个文本生成语音，支持多种引擎"""
    success = False
    
    if engine == "auto" or engine == "edge":
        if TTS_ENGINES['edge']:
            success = await generate_audio_edge_tts(text, voice, output_path)
    
    if not success and (engine == "auto" or engine == "pyttsx3"):
        if TTS_ENGINES['pyttsx3']:
            print(f"🔄 尝试使用本地TTS...")
            success = generate_audio_pyttsx3(text, output_path)
    
    return success

async def generate_tts_for_subtitles(srt_path: str, voice: str = "zh-CN-YunxiNeural", output_dir: str = "out/audio", engine: str = "auto"):
    """为字幕生成TTS音频"""
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取字幕
    print(f"📖 读取字幕文件: {srt_path}")
    subtitles = read_srt_file(srt_path)
    print(f"📝 共找到 {len(subtitles)} 条字幕")
    
    # 生成音频文件
    audio_files = []
    
    print(f"🎵 开始生成语音文件...")
    for i, (index, start_time, end_time, text) in enumerate(subtitles):
        # 清理文本
        clean_text = clean_text_for_tts(text)
        
        if not clean_text:
            print(f"⚠️  跳过空文本: 第{index}条字幕")
            continue
        
        print(f"🔄 [{i+1}/{len(subtitles)}] 生成: {clean_text[:30]}...")
        
        # 生成音频文件
        audio_filename = f"subtitle_{index:03d}.wav"
        audio_path = os.path.join(output_dir, audio_filename)
        
        try:
            success = await generate_audio_for_text(clean_text, voice, audio_path, engine)
            
            if success:
                # 检查音频时长
                duration = get_audio_duration_simple(audio_path)
                
                audio_files.append({
                    'index': index,
                    'path': audio_path,
                    'text': clean_text,
                    'duration': duration,
                    'start_time': start_time,
                    'end_time': end_time,
                    'original_duration': parse_srt_time(end_time) - parse_srt_time(start_time)
                })
                
                print(f"✅ 完成: {audio_filename} (时长: {duration:.2f}s)")
            else:
                print(f"❌ 生成失败: 所有TTS引擎都无法生成音频")
                continue
                
        except Exception as e:
            print(f"❌ 生成失败: {str(e)}")
            continue
    
    print(f"🎉 语音生成完成！共生成 {len(audio_files)} 个音频文件")
    
    # 显示时长对比
    if audio_files:
        print("\n📊 时长对比分析:")
        print("-" * 80)
        print(f"{'序号':<4} {'原始时长':<8} {'生成时长':<8} {'倍速':<6} {'文本预览':<30}")
        print("-" * 80)
        
        total_original = 0
        total_generated = 0
        
        for info in audio_files:
            original_dur = info['original_duration']
            generated_dur = info['duration']
            speed_ratio = generated_dur / original_dur if original_dur > 0 else 0
            
            total_original += original_dur
            total_generated += generated_dur
            
            print(f"{info['index']:<4} {original_dur:<8.2f} {generated_dur:<8.2f} {speed_ratio:<6.2f} {info['text'][:30]:<30}")
        
        print("-" * 80)
        avg_speed = total_generated/total_original if total_original > 0 else 0
        print(f"总计: {total_original:.2f}s -> {total_generated:.2f}s (平均倍速: {avg_speed:.2f})")
    
    return audio_files

def show_engine_status():
    """显示TTS引擎状态"""
    print("\n🔧 TTS引擎状态:")
    print("-" * 60)
    print(f"Edge TTS (在线):   {'✅ 可用' if TTS_ENGINES['edge'] else '❌ 不可用'}")
    print(f"pyttsx3 (本地):    {'✅ 可用' if TTS_ENGINES['pyttsx3'] else '❌ 不可用'}")
    print("-" * 60)
    
    if not any(TTS_ENGINES.values()):
        print("⚠️  没有可用的TTS引擎！请安装：")
        print("   pip install edge-tts pyttsx3")
        return False
    
    return True

def show_voice_options():
    """显示语音选择界面"""
    print("\n🎤 TTS语音选项说明:")
    print("-" * 60)
    print("Edge TTS推荐的中文语音:")
    print("  zh-CN-YunxiNeural    - 云希 (男声，自然清晰)")
    print("  zh-CN-XiaoxiaoNeural - 晓晓 (女声，温和甜美)")
    print("  zh-CN-YunyangNeural  - 云扬 (男声，朗读风格)")
    print("  zh-CN-XiaoyiNeural   - 晓伊 (女声，新闻播报)")
    print("  zh-CN-YunjianNeural  - 云健 (男声，新闻播报)")
    print("-" * 60)
    print("特点:")
    print("  • Edge TTS: 免费在线，语音质量接近真人")
    print("  • pyttsx3: 本地生成，不依赖网络，但语音质量一般")
    print("  • 自动回退: 优先使用Edge TTS，失败时使用本地TTS")
    print("-" * 60)

def get_audio_duration_simple(file_path: str) -> float:
    """简单获取音频时长，兼容不同格式"""
    try:
        # 首先尝试使用pydub
        audio = AudioSegment.from_file(file_path)
        return len(audio) / 1000.0
    except Exception as e1:
        try:
            # 如果pydub失败，尝试使用ffprobe
            import subprocess
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-show_entries', 
                'format=duration', '-of', 'csv=p=0', file_path
            ], capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                return float(result.stdout.strip())
            else:
                print(f"⚠️ 无法获取音频时长: {file_path}")
                return 1.0  # 返回默认时长
        except Exception as e2:
            print(f"⚠️ 获取音频时长失败: {str(e2)}")
            return 1.0  # 返回默认时长

async def main():
    parser = argparse.ArgumentParser(description="TTS语音生成器")
    parser.add_argument("--srt", default="out/output_chinese.srt", help="中文SRT字幕文件路径")
    parser.add_argument("--voice", default="zh-CN-YunxiNeural", help="语音选项")
    parser.add_argument("--output", default="out/audio", help="音频输出目录")
    parser.add_argument("--engine", choices=["auto", "edge", "pyttsx3"], default="auto", help="TTS引擎选择")
    parser.add_argument("--list-voices", action="store_true", help="列出可用的中文语音")
    parser.add_argument("--test", action="store_true", help="测试语音效果")
    
    args = parser.parse_args()
    
    # 检查TTS引擎状态
    if not show_engine_status():
        return
    
    if args.list_voices:
        await list_chinese_voices()
        return
    
    if args.test:
        print("🎵 测试语音效果...")
        test_text = "大家好，这是语音测试。测试中英文混读效果：Hello World！"
        test_path = "out/voice_test.wav"
        os.makedirs("out", exist_ok=True)
        
        success = await generate_audio_for_text(test_text, args.voice, test_path, args.engine)
        if success:
            print(f"✅ 测试完成！音频保存到: {test_path}")
            print("请播放音频文件检查语音效果")
        else:
            print("❌ 测试失败！请检查TTS引擎状态")
        return
    
    # 检查字幕文件
    if not os.path.exists(args.srt):
        print(f"❌ 字幕文件不存在: {args.srt}")
        print("请先运行翻译脚本生成中文字幕")
        return
    
    # 显示语音选项
    show_voice_options()
    
    # 开始生成
    print(f"\n🚀 开始TTS配音生成...")
    print(f"📝 字幕文件: {args.srt}")
    print(f"🎤 语音选择: {args.voice}")
    print(f"🔧 TTS引擎: {args.engine}")
    print(f"📁 输出目录: {args.output}")
    
    audio_files = await generate_tts_for_subtitles(args.srt, args.voice, args.output, args.engine)
    
    print(f"\n🎉 TTS生成完成！")
    print(f"📁 音频文件保存在: {args.output}")
    print(f"🎵 共生成 {len(audio_files)} 个音频文件")

if __name__ == "__main__":
    asyncio.run(main()) 