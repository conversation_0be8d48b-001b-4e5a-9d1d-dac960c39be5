"""
Audio processing service for video translation AI service
"""

import os
from pathlib import Path
from typing import Optional, Tuple
import moviepy.editor as mp
import librosa
import soundfile as sf

from .base_service import BaseService
from app.models.schemas import AudioExtractionRequest


class AudioService(BaseService):
    """Audio processing service"""
    
    def __init__(self):
        super().__init__()
        self.temp_dir = Path(self.settings.temp_path)
        self.output_dir = Path(self.settings.outputs_path)
        
        # Create directories if they don't exist
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def extract_audio_to_path(self, video_path: str, output_path: str,
                                   format: str = "wav", sample_rate: int = 16000,
                                   channels: int = 1) -> dict:
        """Extract audio from video to specified path"""
        try:
            self.logger.info(f"Extracting audio from {video_path} to {output_path}")

            # Ensure output directory exists
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Extract audio using existing method
            result = await self.extract_audio(video_path, format, sample_rate, channels)

            # Move the result to the specified path
            import shutil
            shutil.move(result[0], output_path)  # result[0] is the audio file path

            # Return updated result
            return {
                "audio_file_path": output_path,
                "duration": result[1],  # result[1] is the duration
                "sample_rate": sample_rate,
                "channels": channels,
                "format": format
            }

        except Exception as e:
            self.logger.error(f"Audio extraction to path failed: {e}")
            raise

    async def extract_audio_async(self, video_path: str, output_format: str = "wav",
                                 sample_rate: int = 16000, channels: int = 1) -> str:
        """Create async task for audio extraction"""
        task_id = await self.create_task("audio_extraction", {
            "video_path": video_path,
            "output_format": output_format,
            "sample_rate": sample_rate,
            "channels": channels
        })
        return task_id
    
    async def process_audio_extraction(self, task_id: str, request: AudioExtractionRequest):
        """Process audio extraction task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Extract audio from video
            audio_path, duration = await self.extract_audio(
                request.video_file_path,
                request.output_format,
                request.sample_rate,
                request.channels
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "audio_file_path": audio_path,
                "duration": duration,
                "sample_rate": request.sample_rate,
                "channels": request.channels
            })
            
        except Exception as e:
            self.logger.error(f"Audio extraction failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def extract_audio(self, video_path: str, output_format: str = "wav", 
                           sample_rate: int = 16000, channels: int = 1) -> Tuple[str, float]:
        """Extract audio from video file"""
        try:
            # Validate input file
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")
            
            # Generate output filename
            video_name = Path(video_path).stem
            output_filename = f"{video_name}_audio.{output_format}"
            output_path = self.output_dir / output_filename
            
            self.logger.info(f"Extracting audio from {video_path} to {output_path}")
            
            # Load video and extract audio
            video = mp.VideoFileClip(video_path)
            audio = video.audio
            
            if audio is None:
                raise ValueError("No audio track found in video")
            
            # Write audio to temporary file first
            temp_audio_path = self.temp_dir / f"temp_{output_filename}"
            audio.write_audiofile(str(temp_audio_path), verbose=False, logger=None)
            
            # Process audio with librosa for better quality and format control
            y, sr = librosa.load(str(temp_audio_path), sr=sample_rate, mono=(channels == 1))
            
            # Save processed audio
            sf.write(str(output_path), y, sample_rate)
            
            # Get duration
            duration = len(y) / sample_rate
            
            # Clean up
            video.close()
            audio.close()
            if temp_audio_path.exists():
                temp_audio_path.unlink()
            
            self.logger.info(f"Audio extracted successfully: {output_path} (duration: {duration:.2f}s)")
            return str(output_path), duration
            
        except Exception as e:
            self.logger.error(f"Audio extraction failed: {e}")
            raise
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process audio task"""
        # This is called by the base class
        pass
    
    def validate_audio_file(self, audio_path: str) -> bool:
        """Validate audio file"""
        try:
            if not os.path.exists(audio_path):
                return False
            
            # Try to load the audio file
            y, sr = librosa.load(audio_path, sr=None, duration=1.0)
            return len(y) > 0
            
        except Exception:
            return False
    
    def get_audio_info(self, audio_path: str) -> Optional[dict]:
        """Get audio file information"""
        try:
            y, sr = librosa.load(audio_path, sr=None)
            duration = len(y) / sr
            
            return {
                "path": audio_path,
                "duration": duration,
                "sample_rate": sr,
                "channels": 1 if y.ndim == 1 else y.shape[0],
                "samples": len(y)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get audio info: {e}")
            return None 