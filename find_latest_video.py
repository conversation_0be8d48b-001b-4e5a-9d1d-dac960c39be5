#!/usr/bin/env python3
"""
查找最新生成的翻译视频
"""

import os
import json
from pathlib import Path
from datetime import datetime

def find_latest_video():
    """查找最新的翻译视频"""
    
    # 输出目录
    outputs_dir = Path("python-ai-service/storage/outputs")
    
    if not outputs_dir.exists():
        print("❌ 输出目录不存在:", outputs_dir)
        return None
    
    # 获取所有任务目录
    task_dirs = []
    for item in outputs_dir.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            task_dirs.append(item)
    
    if not task_dirs:
        print("❌ 没有找到任务目录")
        return None
    
    # 按修改时间排序，最新的在前
    task_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    print(f"📁 找到 {len(task_dirs)} 个任务目录:")
    
    for i, task_dir in enumerate(task_dirs[:5]):  # 显示最新的5个
        # 检查是否有最终视频
        final_video = task_dir / "output" / "final_video.mp4"
        
        # 获取任务信息
        metadata_file = task_dir / "metadata.json"
        task_info = "未知任务"
        created_time = "未知时间"
        
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    task_info = metadata.get('original_filename', '未知文件')
                    created_time = metadata.get('created_at', '未知时间')
                    if created_time != '未知时间':
                        # 格式化时间显示
                        try:
                            dt = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                            created_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            pass
            except Exception as e:
                print(f"⚠️ 读取元数据失败: {e}")
        
        status = "✅ 完成" if final_video.exists() else "❌ 未完成"
        file_size = ""
        
        if final_video.exists():
            size_bytes = final_video.stat().st_size
            if size_bytes > 1024 * 1024:
                file_size = f" ({size_bytes / (1024*1024):.1f} MB)"
            else:
                file_size = f" ({size_bytes / 1024:.1f} KB)"
        
        print(f"  {i+1}. {status} {task_dir.name}")
        print(f"     原文件: {task_info}")
        print(f"     创建时间: {created_time}")
        if final_video.exists():
            print(f"     视频路径: {final_video}{file_size}")
        print()
    
    # 返回最新的视频路径
    latest_task = task_dirs[0]
    latest_video = latest_task / "output" / "final_video.mp4"
    
    if latest_video.exists():
        print(f"🎬 最新翻译视频:")
        print(f"   任务目录: {latest_task}")
        print(f"   视频文件: {latest_video}")
        print(f"   绝对路径: {latest_video.absolute()}")
        
        # 检查其他相关文件
        print(f"\n📋 相关文件:")
        
        # 字幕文件
        subtitles_dir = latest_task / "subtitles"
        if subtitles_dir.exists():
            for srt_file in subtitles_dir.glob("*.srt"):
                print(f"   字幕: {srt_file}")
        
        # TTS音频
        tts_dir = latest_task / "tts"
        if tts_dir.exists():
            for audio_file in tts_dir.glob("*.wav"):
                print(f"   TTS音频: {audio_file}")
        
        # 元数据
        metadata_file = latest_task / "metadata.json"
        if metadata_file.exists():
            print(f"   元数据: {metadata_file}")
        
        return str(latest_video.absolute())
    else:
        print(f"❌ 最新任务目录中没有找到最终视频: {latest_video}")
        return None

def show_task_details(task_name):
    """显示特定任务的详细信息"""
    
    task_dir = Path("python-ai-service/storage/outputs") / task_name
    
    if not task_dir.exists():
        print(f"❌ 任务目录不存在: {task_dir}")
        return
    
    print(f"📁 任务详情: {task_name}")
    print(f"   目录: {task_dir.absolute()}")
    
    # 读取元数据
    metadata_file = task_dir / "metadata.json"
    if metadata_file.exists():
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            print(f"\n📋 任务信息:")
            print(f"   原始文件: {metadata.get('original_filename', '未知')}")
            print(f"   创建时间: {metadata.get('created_at', '未知')}")
            print(f"   状态: {metadata.get('status', '未知')}")
            print(f"   源语言: {metadata.get('source_language', '未知')}")
            print(f"   目标语言: {metadata.get('target_language', '未知')}")
            print(f"   TTS语音: {metadata.get('tts_voice', '未知')}")
            
        except Exception as e:
            print(f"⚠️ 读取元数据失败: {e}")
    
    # 显示目录结构
    print(f"\n📂 目录结构:")
    for subdir in ['source', 'audio', 'transcription', 'translation', 'tts', 'subtitles', 'output']:
        subdir_path = task_dir / subdir
        if subdir_path.exists():
            files = list(subdir_path.glob("*"))
            print(f"   {subdir}/ ({len(files)} 个文件)")
            for file in files:
                if file.is_file():
                    size = file.stat().st_size
                    if size > 1024 * 1024:
                        size_str = f"{size / (1024*1024):.1f} MB"
                    else:
                        size_str = f"{size / 1024:.1f} KB"
                    print(f"     - {file.name} ({size_str})")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 显示特定任务的详情
        task_name = sys.argv[1]
        show_task_details(task_name)
    else:
        # 查找最新视频
        latest_video = find_latest_video()
        
        if latest_video:
            print(f"\n🚀 要打开视频文件，请运行:")
            print(f"   start \"{latest_video}\"")
            print(f"\n🔍 要查看任务详情，请运行:")
            print(f"   python find_latest_video.py [任务目录名]")
