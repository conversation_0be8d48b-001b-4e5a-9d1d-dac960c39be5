package com.translation.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class AudioVideoMerger {

    private static final Logger logger = LoggerFactory.getLogger(AudioVideoMerger.class);

    /**
     * 检查FFmpeg是否可用
     */
    public boolean isFFmpegAvailable() {
        try {
            ProcessBuilder pb = new ProcessBuilder("ffmpeg", "-version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            return process.exitValue() == 0;
        } catch (Exception e) {
            logger.warn("FFmpeg不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从视频中提取音频
     *
     * @param videoPath  输入视频文件路径
     * @param audioPath  输出音频文件路径
     * @return 提取的音频文件
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    public File extractAudioFromVideo(String videoPath, String audioPath) throws IOException, InterruptedException {
        if (!isFFmpegAvailable()) {
            throw new IOException("FFmpeg不可用，无法提取音频");
        }

        File videoFile = new File(videoPath);
        if (!videoFile.exists()) {
            throw new IOException("视频文件不存在: " + videoPath);
        }

        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-i");
        command.add(videoPath);
        command.add("-vn");  // 不包含视频
        command.add("-acodec");
        command.add("pcm_s16le");  // 使用PCM编码确保兼容性
        command.add("-ar");
        command.add("16000");  // 设置采样率为16kHz，符合百度ASR要求
        command.add("-ac");
        command.add("1");  // 设置为单声道，符合百度ASR要求
        // 添加音频质量和降噪参数
        command.add("-af");
        command.add("highpass=f=200,lowpass=f=3000,volume=2.0");  // 高通滤波、低通滤波、音量增强
        command.add("-y");  // 覆盖输出文件
        command.add(audioPath);

        logger.info("执行提取音频命令: {}", String.join(" ", command));
        return executeFFmpegCommand(command, "提取音频");
    }

    /**
     * 提取指定时间段的音频片段
     *
     * @param inputAudioPath  输入音频文件路径
     * @param outputAudioPath 输出音频文件路径
     * @param startTime       开始时间（秒）
     * @param duration        持续时间（秒）
     * @return 提取的音频片段文件
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    public File extractAudioSegment(String inputAudioPath, String outputAudioPath, double startTime, double duration) throws IOException, InterruptedException {
        if (!isFFmpegAvailable()) {
            throw new IOException("FFmpeg不可用，无法提取音频片段");
        }

        File inputFile = new File(inputAudioPath);
        if (!inputFile.exists()) {
            throw new IOException("输入音频文件不存在: " + inputAudioPath);
        }

        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-i");
        command.add(inputAudioPath);
        command.add("-ss");
        command.add(String.valueOf(startTime));
        command.add("-t");
        command.add(String.valueOf(duration));
        command.add("-acodec");
        command.add("copy");
        command.add("-y");
        command.add(outputAudioPath);

        return executeFFmpegCommand(command, "提取音频片段");
    }

    /**
     * 合并多个音频文件
     *
     * @param audioFiles     要合并的音频文件列表
     * @param outputFilePath 输出文件路径
     * @return 合并后的音频文件
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    public File mergeAudioFiles(List<String> audioFiles, String outputFilePath) throws IOException, InterruptedException {
        if (!isFFmpegAvailable()) {
            throw new IOException("FFmpeg不可用，无法合并音频文件");
        }

        if (audioFiles == null || audioFiles.isEmpty()) {
            throw new IllegalArgumentException("音频文件列表不能为空");
        }

        // 验证所有输入文件存在
        for (String audioFile : audioFiles) {
            File file = new File(audioFile);
            if (!file.exists()) {
                throw new IOException("音频文件不存在: " + audioFile);
            }
        }

        List<String> command = new ArrayList<>();
        command.add("ffmpeg");

        // 添加所有输入文件
        for (String audioFile : audioFiles) {
            command.add("-i");
            command.add(audioFile);
        }

        // 构建filter_complex参数
        StringBuilder filterComplex = new StringBuilder();
        for (int i = 0; i < audioFiles.size(); i++) {
            filterComplex.append("[").append(i).append(":0]");
        }
        filterComplex.append("concat=n=").append(audioFiles.size()).append(":v=0:a=1[out]");

        command.add("-filter_complex");
        command.add(filterComplex.toString());
        command.add("-map");
        command.add("[out]");
        command.add("-y");
        command.add(outputFilePath);

        return executeFFmpegCommand(command, "合并音频文件");
    }

    /**
     * 将新音频与原视频合并
     *
     * @param videoPath     原视频文件路径
     * @param newAudioPath  新音频文件路径
     * @param outputPath    输出视频文件路径
     * @return 合并后的视频文件
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    public File mergeAudioWithVideo(String videoPath, String newAudioPath, String outputPath) throws IOException, InterruptedException {
        if (!isFFmpegAvailable()) {
            throw new IOException("FFmpeg不可用，无法合并音频和视频");
        }

        File videoFile = new File(videoPath);
        File audioFile = new File(newAudioPath);

        if (!videoFile.exists()) {
            throw new IOException("视频文件不存在: " + videoPath);
        }
        if (!audioFile.exists()) {
            throw new IOException("音频文件不存在: " + newAudioPath);
        }

        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-i");
        command.add(videoPath);
        command.add("-i");
        command.add(newAudioPath);
        command.add("-c:v");
        command.add("copy");  // 复制视频流，不重新编码
        command.add("-c:a");
        command.add("aac");   // 音频编码为AAC
        command.add("-map");
        command.add("0:v:0"); // 使用第一个输入的视频流
        command.add("-map");
        command.add("1:a:0"); // 使用第二个输入的音频流
        command.add("-y");
        command.add(outputPath);

        return executeFFmpegCommand(command, "合并音频和视频");
    }

    /**
     * 获取音频文件的时长（秒）
     *
     * @param audioPath 音频文件路径
     * @return 音频时长（秒）
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    public double getAudioDuration(String audioPath) throws IOException, InterruptedException {
        if (!isFFmpegAvailable()) {
            throw new IOException("FFmpeg不可用，无法获取音频时长");
        }

        File audioFile = new File(audioPath);
        if (!audioFile.exists()) {
            throw new IOException("音频文件不存在: " + audioPath);
        }

        List<String> command = new ArrayList<>();
        command.add("ffprobe");
        command.add("-v");
        command.add("quiet");
        command.add("-show_entries");
        command.add("format=duration");
        command.add("-of");
        command.add("csv=p=0");
        command.add(audioPath);

        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true);

        logger.info("获取音频时长: {}", String.join(" ", command));

        Process process = pb.start();
        StringBuilder output = new StringBuilder();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }
        }

        boolean finished = process.waitFor(30, TimeUnit.SECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new InterruptedException("获取音频时长超时");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new IOException("FFprobe执行失败，退出代码: " + exitCode);
        }

        try {
            return Double.parseDouble(output.toString().trim());
        } catch (NumberFormatException e) {
            throw new IOException("无法解析音频时长: " + output.toString());
        }
    }

    /**
     * 执行FFmpeg命令的通用方法
     *
     * @param command     FFmpeg命令列表
     * @param operation   操作描述
     * @return 输出文件
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    private File executeFFmpegCommand(List<String> command, String operation) throws IOException, InterruptedException {
        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true);

        logger.info("执行{}命令: {}", operation, String.join(" ", command));

        Process process = pb.start();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                logger.debug("[FFmpeg]: {}", line);
            }
        }

        boolean finished = process.waitFor(5, TimeUnit.MINUTES);
        if (!finished) {
            process.destroyForcibly();
            throw new InterruptedException(operation + "超时（5分钟）");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new IOException(operation + "失败，FFmpeg退出代码: " + exitCode);
        }

        // 获取输出文件路径（命令的最后一个参数）
        String outputPath = command.get(command.size() - 1);
        File outputFile = new File(outputPath);

        if (!outputFile.exists() || outputFile.length() == 0) {
            throw new IOException(operation + "生成的文件为空或不存在: " + outputPath);
        }

        logger.info("{}成功完成: {}", operation, outputFile.getAbsolutePath());
        return outputFile;
    }
}