package com.translation.controller;

import com.translation.service.TranslationManager;
import com.translation.service.VideoDubbingService;
import com.translation.service.EdgeTTSService;
import com.translation.service.BaiduTTSService;
import com.translation.service.AudioVideoMerger;
import com.translation.service.BaiduASRService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.nio.file.Paths;

/**
 * 翻译服务REST API控制器
 */
@RestController
@RequestMapping("/api/translation")
@CrossOrigin(origins = "*")
public class TranslationController {
    private static final Logger logger = LoggerFactory.getLogger(TranslationController.class);
    
    @Autowired
    private TranslationManager translationManager;
    
    @Autowired
    private VideoDubbingService videoDubbingService;
    
    @Autowired
    private EdgeTTSService edgeTTSService;
    
    @Autowired
    private BaiduTTSService baiduTTSService;
    
    @Autowired
    private AudioVideoMerger audioVideoMerger;
    
    @Autowired
    private BaiduASRService baiduASRService;
    
    /**
     * 翻译文本
     */
    @PostMapping("/translate")
    public ResponseEntity<Map<String, Object>> translateText(@RequestBody TranslationRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 参数验证
            if (request.getText() == null || request.getText().trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "文本不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            String result;
            if (request.getProvider() != null && !request.getProvider().trim().isEmpty()) {
                // 使用指定的翻译服务
                result = translationManager.translateText(
                    request.getText(), 
                    request.getSourceLanguage(), 
                    request.getTargetLanguage(), 
                    request.getProvider()
                );
            } else {
                // 自动选择翻译服务
                result = translationManager.translateTextAuto(
                    request.getText(), 
                    request.getSourceLanguage(), 
                    request.getTargetLanguage()
                );
            }
            
            response.put("success", true);
            response.put("translatedText", result);
            response.put("sourceLanguage", request.getSourceLanguage());
            response.put("targetLanguage", request.getTargetLanguage());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("翻译失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 批量翻译
     */
    @PostMapping("/translate/batch")
    public ResponseEntity<Map<String, Object>> translateBatch(@RequestBody BatchTranslationRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (request.getTexts() == null || request.getTexts().isEmpty()) {
                response.put("success", false);
                response.put("error", "文本列表不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            String provider = request.getProvider() != null ? request.getProvider() : "auto";
            List<String> results;
            
            if ("auto".equals(provider)) {
                // 自动选择服务进行批量翻译
                results = request.getTexts().stream()
                    .map(text -> {
                        try {
                            return translationManager.translateTextAuto(
                                text, 
                                request.getSourceLanguage(), 
                                request.getTargetLanguage()
                            );
                        } catch (Exception e) {
                            logger.error("批量翻译中的单个文本失败: {}", e.getMessage());
                            return "[翻译失败: " + e.getMessage() + "]";
                        }
                    })
                    .toList();
            } else {
                results = translationManager.translateBatch(
                    request.getTexts(), 
                    request.getSourceLanguage(), 
                    request.getTargetLanguage(), 
                    provider
                );
            }
            
            response.put("success", true);
            response.put("translatedTexts", results);
            response.put("sourceLanguage", request.getSourceLanguage());
            response.put("targetLanguage", request.getTargetLanguage());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("批量翻译失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 检测语言
     */
    @PostMapping("/detect")
    public ResponseEntity<Map<String, Object>> detectLanguage(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String text = request.get("text");
            if (text == null || text.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "文本不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            String detectedLanguage = translationManager.detectLanguage(text);
            
            response.put("success", true);
            response.put("detectedLanguage", detectedLanguage);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("语言检测失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 视频语音识别并翻译
     */
    @PostMapping("/video/asr-translate")
    public ResponseEntity<Map<String, Object>> videoASRTranslate(@RequestParam("videoFile") MultipartFile videoFile,
                                                                @RequestParam("sourceLanguage") String sourceLanguage,
                                                                @RequestParam("targetLanguage") String targetLanguage) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (videoFile.isEmpty()) {
                response.put("success", false);
                response.put("message", "视频文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 验证文件大小
            long maxFileSize = 500 * 1024 * 1024; // 500MB
            if (videoFile.getSize() > maxFileSize) {
                response.put("success", false);
                response.put("message", "视频文件过大，最大支持500MB");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 创建临时目录
            Path tempDir = Paths.get("temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
            }
            
            // 保存上传的视频文件
            String originalFilename = videoFile.getOriginalFilename();
            String videoFileName = "video_" + System.currentTimeMillis() + "_" + originalFilename;
            Path videoPath = tempDir.resolve(videoFileName);
            Files.copy(videoFile.getInputStream(), videoPath);
            
            logger.info("开始处理视频文件: {}, 大小: {}MB", originalFilename, videoFile.getSize() / 1024 / 1024);
            
            // 提取音频
            String audioFileName = videoFileName.replaceAll("\\.[^.]+$", ".wav");
            Path audioPath = tempDir.resolve(audioFileName);
            
            File audioFile = audioVideoMerger.extractAudioFromVideo(videoPath.toString(), audioPath.toString());
            if (audioFile == null || !audioFile.exists()) {
                response.put("success", false);
                response.put("message", "音频提取失败，请检查视频文件格式");
                return ResponseEntity.internalServerError().body(response);
            }
            
            logger.info("音频提取成功，音频文件大小: {}MB", audioFile.length() / 1024 / 1024);
            
            // 智能语言检测：如果源语言是auto，使用混合语言模式
            String asrLanguage = sourceLanguage;
            if ("auto".equals(sourceLanguage) || sourceLanguage == null || sourceLanguage.trim().isEmpty()) {
                asrLanguage = "auto";  // 使用自动检测模式
                logger.info("使用自动语言检测模式进行ASR识别");
            }
            
            // 语音识别
            String recognizedText = baiduASRService.transcribe(audioFile, asrLanguage);
            
            if (recognizedText == null || recognizedText.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "语音识别失败，可能是音频质量问题或无有效语音内容");
                return ResponseEntity.ok(response);
            }
            
            logger.info("语音识别成功，识别文本长度: {} 字符", recognizedText.length());
            
            // 如果源语言是auto，尝试检测识别出的文本的语言
            String detectedLanguage = sourceLanguage;
            if ("auto".equals(sourceLanguage)) {
                try {
                    detectedLanguage = translationManager.detectLanguage(recognizedText);
                    logger.info("语言检测成功，检测到的语言: {}", detectedLanguage);
                } catch (Exception e) {
                    logger.warn("语言检测失败，根据ASR模型推断语言: {}", e.getMessage());
                    // 根据使用的ASR模型推断语言
                    if (recognizedText.matches(".*[a-zA-Z].*")) {
                        detectedLanguage = "en"; // 包含英文字符，推断为英文
                        logger.info("根据文本内容推断为英文");
                    } else {
                        detectedLanguage = "zh"; // 默认中文
                        logger.info("根据文本内容推断为中文");
                    }
                }
            }
            
            logger.info("准备翻译 - 源语言: {}, 目标语言: {}, 识别文本前50字符: {}", 
                       detectedLanguage, targetLanguage, 
                       recognizedText.length() > 50 ? recognizedText.substring(0, 50) + "..." : recognizedText);
            
            // 翻译为目标语言
            String translatedText = "";
            if (!detectedLanguage.equals(targetLanguage)) {
                logger.info("开始翻译，从 {} 到 {}", detectedLanguage, targetLanguage);
                translatedText = translationManager.translateTextAuto(recognizedText, detectedLanguage, targetLanguage);
                logger.info("翻译完成，翻译文本长度: {} 字符，翻译结果前50字符: {}", 
                           translatedText.length(),
                           translatedText.length() > 50 ? translatedText.substring(0, 50) + "..." : translatedText);
            } else {
                translatedText = recognizedText; // 源语言和目标语言相同，不需要翻译
                logger.info("源语言({})和目标语言({})相同，跳过翻译", detectedLanguage, targetLanguage);
            }
            
            response.put("success", true);
            response.put("recognizedText", recognizedText);
            response.put("translatedText", translatedText);
            response.put("detectedLanguage", detectedLanguage);
            response.put("sourceLanguage", sourceLanguage);
            response.put("targetLanguage", targetLanguage);
            response.put("audioFileSize", audioFile.length());
            response.put("processingTime", System.currentTimeMillis());
            
            // 清理临时文件 - 暂时注释掉以保留文件用于调试
            try {
                // Files.deleteIfExists(videoPath);
                // Files.deleteIfExists(audioPath);
                logger.info("保留临时文件用于调试: 视频={}, 音频={}", videoPath, audioPath);
            } catch (Exception e) {
                logger.warn("清理临时文件失败: {}", e.getMessage());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("视频语音识别翻译失败", e);
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServiceStatus() {
        try {
            Map<String, Object> status = translationManager.getServiceStatus();
            List<String> availableProviders = translationManager.getAvailableProviders();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("services", status);
            response.put("availableProviders", availableProviders);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取服务状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Translation Service");
        response.put("timestamp", System.currentTimeMillis());
        
        // 检查各个服务的可用性
        Map<String, Boolean> services = new HashMap<>();
        services.put("translation", true);
        services.put("edgeTTS", edgeTTSService.isServiceAvailable());
        services.put("baiduTTS", baiduTTSService.isServiceAvailable());
        services.put("ffmpeg", audioVideoMerger.isFFmpegAvailable());
        response.put("services", services);
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/")
    public String index() {
        return "index";
    }

    // test页面已移至ViewController
    
    // ==================== TTS相关接口 ====================
    
    /**
     * 文本转语音接口
     */
    @PostMapping("/tts/synthesize")
    public ResponseEntity<Map<String, Object>> synthesizeText(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String text = request.get("text");
            String provider = request.getOrDefault("provider", "edge");
            String language = request.getOrDefault("language", "zh-CN");
            String voice = request.get("voice");
            String outputPath = request.get("outputPath");
            
            if (text == null || text.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "文本内容不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 如果没有提供输出路径，自动生成一个临时文件路径
            if (outputPath == null || outputPath.trim().isEmpty()) {
                String timestamp = String.valueOf(System.currentTimeMillis());
                outputPath = "temp/tts_" + timestamp + ".mp3";
                
                // 确保临时目录存在
                File tempDir = new File("temp");
                if (!tempDir.exists()) {
                    tempDir.mkdirs();
                }
            }
            
            File audioFile = null;
            
            if ("edge".equalsIgnoreCase(provider)) {
                if (!edgeTTSService.isServiceAvailable()) {
                    response.put("success", false);
                    response.put("message", "Edge TTS服务不可用");
                    return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
                }
                
                if (voice == null) {
                    Map<String, String> recommendedVoice = edgeTTSService.getRecommendedVoice(language);
                    voice = recommendedVoice.get("voice");
                }
                
                audioFile = edgeTTSService.synthesizeText(text, voice, "+0%", outputPath);
                
            } else if ("baidu".equalsIgnoreCase(provider)) {
                if (!baiduTTSService.isServiceAvailable()) {
                    response.put("success", false);
                    response.put("message", "百度TTS服务不可用");
                    return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
                }
                
                audioFile = baiduTTSService.synthesizeText(text, language, outputPath);
                
            } else {
                response.put("success", false);
                response.put("message", "不支持的TTS提供商: " + provider);
                return ResponseEntity.badRequest().body(response);
            }
            
            if (audioFile != null && audioFile.exists()) {
                response.put("success", true);
                response.put("message", "语音合成成功");
                response.put("audioPath", audioFile.getAbsolutePath());
                response.put("audioUrl", "/temp/" + audioFile.getName()); // 添加用于前端播放的URL
                response.put("fileSize", audioFile.length());
            } else {
                response.put("success", false);
                response.put("message", "语音合成失败");
            }
            
        } catch (Exception e) {
            logger.error("TTS合成失败", e);
            response.put("success", false);
            response.put("message", "TTS合成失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取支持的TTS提供商
     */
    @GetMapping("/tts/providers")
    public ResponseEntity<Map<String, Object>> getTTSProviders() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<String> providers = videoDubbingService.getSupportedTTSProviders();
            response.put("success", true);
            response.put("providers", providers);
            
        } catch (Exception e) {
            logger.error("获取TTS提供商失败", e);
            response.put("success", false);
            response.put("message", "获取TTS提供商失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取可用的语音列表
     */
    @GetMapping("/tts/voices")
    public ResponseEntity<Map<String, Object>> getVoices(@RequestParam("provider") String provider) {
        try {
            Map<String, Object> result = videoDubbingService.getAvailableVoices(provider);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("获取语音列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取语音列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    // ==================== 视频配音接口 ====================
    
    /**
     * 视频配音接口
     */
    @PostMapping("/video/dubbing")
    public ResponseEntity<Map<String, Object>> processVideoDubbing(
            @RequestParam("videoFile") MultipartFile videoFile,
            @RequestParam("text") String text,
            @RequestParam("provider") String provider,
            @RequestParam(value = "voice", required = false) String voice,
            @RequestParam(value = "sourceLanguage", defaultValue = "auto") String sourceLanguage,
            @RequestParam(value = "targetLanguage", defaultValue = "zh") String targetLanguage) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (videoFile.isEmpty()) {
                response.put("success", false);
                response.put("message", "视频文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (text == null || text.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "配音文本不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 创建临时目录
            Path tempDir = Paths.get("temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
            }
            
            // 保存上传的视频文件
            String originalFilename = videoFile.getOriginalFilename();
            String videoExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                videoExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String videoFilename = "dubbing_video_" + System.currentTimeMillis() + videoExtension;
            Path videoPath = tempDir.resolve(videoFilename);
            Files.copy(videoFile.getInputStream(), videoPath);
            
            // 创建配音请求
            VideoDubbingService.DubbingRequest request = new VideoDubbingService.DubbingRequest();
            request.setVideoPath(videoPath.toAbsolutePath().toString());
            request.setText(text);
            request.setSourceLanguage(sourceLanguage);
            request.setTargetLanguage(targetLanguage);
            request.setTtsProvider(provider);
            request.setVoice(voice);
            request.setOutputPath(tempDir.resolve("dubbing_output_" + System.currentTimeMillis() + ".mp4").toString());
            
            // 处理配音
            VideoDubbingService.DubbingResponse dubbingResponse = videoDubbingService.processVideoDubbing(request);
            
            if (dubbingResponse.isSuccess()) {
                response.put("success", true);
                response.put("message", "配音完成");
                response.put("videoUrl", "/temp/" + Paths.get(dubbingResponse.getOutputVideoPath()).getFileName().toString());
                response.put("translatedText", dubbingResponse.getTranslatedText());
                response.put("totalDuration", dubbingResponse.getTotalDuration());
            } else {
                response.put("success", false);
                response.put("message", dubbingResponse.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("视频配音处理失败", e);
            response.put("success", false);
            response.put("message", "视频配音处理失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    // ==================== 音频处理接口 ====================
    
    /**
     * 从视频中提取音频（支持文件上传）
     */
    @PostMapping("/audio/extract")
    public ResponseEntity<Map<String, Object>> extractAudio(@RequestParam("videoFile") MultipartFile videoFile) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (videoFile.isEmpty()) {
                response.put("success", false);
                response.put("message", "视频文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 创建临时目录
            Path tempDir = Paths.get("temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
            }
            
            // 保存上传的视频文件
            String originalFilename = videoFile.getOriginalFilename();
            String videoExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                videoExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String videoFilename = "video_" + System.currentTimeMillis() + videoExtension;
            Path videoPath = tempDir.resolve(videoFilename);
            Files.copy(videoFile.getInputStream(), videoPath);
            
            // 生成音频文件路径
            String audioFilename = "audio_" + System.currentTimeMillis() + ".wav";
            Path audioPath = tempDir.resolve(audioFilename);
            
            // 提取音频
            File audioFile = audioVideoMerger.extractAudioFromVideo(
                videoPath.toAbsolutePath().toString(), 
                audioPath.toAbsolutePath().toString()
            );
            
            response.put("success", true);
            response.put("message", "音频提取成功");
            response.put("audioPath", audioFile.getAbsolutePath());
            response.put("audioUrl", "/temp/" + audioFilename);
            response.put("fileSize", audioFile.length());
            
        } catch (Exception e) {
            logger.error("音频提取失败", e);
            response.put("success", false);
            response.put("message", "音频提取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 合并音频和视频
     */
    @PostMapping("/audio/merge")
    public ResponseEntity<Map<String, Object>> mergeAudioVideo(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String videoPath = request.get("videoPath");
            String audioPath = request.get("audioPath");
            String outputPath = request.get("outputPath");
            
            if (videoPath == null || audioPath == null || outputPath == null) {
                response.put("success", false);
                response.put("message", "视频路径、音频路径和输出路径不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            File outputFile = audioVideoMerger.mergeAudioWithVideo(videoPath, audioPath, outputPath);
            
            response.put("success", true);
            response.put("message", "音频视频合并成功");
            response.put("outputPath", outputFile.getAbsolutePath());
            response.put("fileSize", outputFile.length());
            
        } catch (Exception e) {
            logger.error("音频视频合并失败", e);
            response.put("success", false);
            response.put("message", "音频视频合并失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取音频时长
     */
    @GetMapping("/audio/duration")
    public ResponseEntity<Map<String, Object>> getAudioDuration(@RequestParam("audioPath") String audioPath) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            double duration = audioVideoMerger.getAudioDuration(audioPath);
            
            response.put("success", true);
            response.put("duration", duration);
            response.put("durationFormatted", formatDuration(duration));
            
        } catch (Exception e) {
            logger.error("获取音频时长失败", e);
            response.put("success", false);
            response.put("message", "获取音频时长失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 文件上传接口
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(@RequestParam("audioFile") MultipartFile file) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 创建临时目录
            Path tempDir = Paths.get("temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
            }
            
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = "upload_" + System.currentTimeMillis() + extension;
            
            // 保存文件
            Path filePath = tempDir.resolve(filename);
            Files.copy(file.getInputStream(), filePath);
            
            response.put("success", true);
            response.put("message", "文件上传成功");
            response.put("filePath", filePath.toAbsolutePath().toString());
            response.put("filename", filename);
            response.put("fileSize", file.getSize());
            
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            response.put("success", false);
            response.put("message", "文件上传失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 格式化时长显示
     */
    private String formatDuration(double seconds) {
        int hours = (int) (seconds / 3600);
        int minutes = (int) ((seconds % 3600) / 60);
        int secs = (int) (seconds % 60);
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, secs);
        } else {
            return String.format("%02d:%02d", minutes, secs);
        }
    }
    
    // 请求数据类
    public static class TranslationRequest {
        private String text;
        private String sourceLanguage;
        private String targetLanguage;
        private String provider;
        
        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        
        public String getSourceLanguage() { return sourceLanguage; }
        public void setSourceLanguage(String sourceLanguage) { this.sourceLanguage = sourceLanguage; }
        
        public String getTargetLanguage() { return targetLanguage; }
        public void setTargetLanguage(String targetLanguage) { this.targetLanguage = targetLanguage; }
        
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
    }
    
    public static class BatchTranslationRequest {
        private List<String> texts;
        private String sourceLanguage;
        private String targetLanguage;
        private String provider;
        
        // Getters and Setters
        public List<String> getTexts() { return texts; }
        public void setTexts(List<String> texts) { this.texts = texts; }
        
        public String getSourceLanguage() { return sourceLanguage; }
        public void setSourceLanguage(String sourceLanguage) { this.sourceLanguage = sourceLanguage; }
        
        public String getTargetLanguage() { return targetLanguage; }
        public void setTargetLanguage(String targetLanguage) { this.targetLanguage = targetLanguage; }
        
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
    }
}