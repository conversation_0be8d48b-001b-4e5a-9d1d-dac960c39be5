"""
Task management service for video translation AI service
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from .base_service import BaseService
from .directory_service import DirectoryService
from .audio_service import AudioService
from .speech_service import SpeechService
from .translation_service import TranslationService
from .subtitle_service import SubtitleService
from .tts_service import TTSService
from .video_service import VideoService
from app.models.schemas import (
    FullPipelineRequest, TaskInfo, TaskListResponse, 
    TaskStatus, TaskType
)


class TaskService(BaseService):
    """Task management service"""
    
    def __init__(self):
        super().__init__()
        # Initialize directory service first
        self.directory_service = DirectoryService()

        # Initialize other services
        self.audio_service = AudioService()
        self.speech_service = SpeechService()
        self.translation_service = TranslationService()
        self.subtitle_service = SubtitleService()
        self.tts_service = TTSService()
        self.video_service = VideoService()
    
    async def create_full_pipeline_task(self, request: FullPipelineRequest) -> str:
        """Create full pipeline task"""
        task_id = await self.create_task("full_pipeline", {
            "request": request.dict()
        })
        
        # 任务信息已经在BaseService.create_task中存储到全局字典
        return task_id
    
    async def process_full_pipeline(self, task_id: str, request: FullPipelineRequest):
        """Process full video translation pipeline with structured directories"""
        task_paths = None
        fallback_messages = []

        try:
            await self._update_task(task_id, "processing", 0.0)

            # Step 1: Create structured directory for this task
            self.logger.info(f"Step 1: Creating structured directory for task {task_id}")
            task_paths = self.directory_service.create_task_directory(
                original_filename=request.video_file_path.split('/')[-1],  # Extract filename
                task_id=task_id
            )

            self.logger.info(f"Task directory created: {task_paths['task_name']}")
            self.logger.info(f"Directories: {list(task_paths.keys())}")

            # Update task metadata with directory info
            self.directory_service.update_task_metadata(
                task_name=task_paths['task_name'],
                updates={
                    'task_id': task_id,
                    'status': 'processing',
                    'request_params': request.dict()
                }
            )

            # Step 2: Copy source video to task directory
            self.logger.info(f"Step 2: Copying source video to task directory")
            source_video_path = self.directory_service.copy_file_to_task_dir(
                source_path=request.video_file_path,
                task_name=task_paths['task_name'],
                target_subdir='source',
                new_filename='original.mp4'
            )
            self.logger.info(f"Source video copied to: {source_video_path}")

            # Pre-flight check: Test TTS service availability
            self.logger.info(f"Pre-flight check: Testing TTS service for task {task_id}")
            try:
                # 简单的TTS健康检查，保存到temp目录
                temp_tts_path = self.directory_service.get_file_path(
                    task_name=task_paths['task_name'],
                    subdir='temp',
                    filename='tts_test.wav'
                )

                test_result = await self.tts_service.synthesize_speech_to_path(
                    text="测试",
                    voice=request.tts_voice,
                    output_path=temp_tts_path,
                    speed=1.0,
                    pitch=0,
                    service="edge-tts"
                )

                if test_result and not test_result.get("fallback"):
                    self.logger.info("TTS pre-flight check passed")
                    # 清理测试文件
                    try:
                        import os
                        if os.path.exists(temp_tts_path):
                            os.remove(temp_tts_path)
                    except:
                        pass
                else:
                    self.logger.warning(f"TTS pre-flight check returned fallback: {test_result.get('message', 'Unknown')}")

            except Exception as e:
                self.logger.warning(f"TTS pre-flight check failed: {e}")
                self.logger.info("Continuing with pipeline, will handle TTS issues during synthesis step")

            # Step 3: Extract audio from video
            self.logger.info(f"Step 3: Extracting audio for task {task_id}")
            audio_output_path = self.directory_service.get_file_path(
                task_name=task_paths['task_name'],
                subdir='audio',
                filename='original.wav'
            )

            audio_result = await self.audio_service.extract_audio_to_path(
                video_path=source_video_path,
                output_path=audio_output_path,
                format="wav",
                sample_rate=16000,
                channels=1
            )
            await self._update_task(task_id, "processing", 20.0)

            # Step 4: Speech recognition
            self.logger.info(f"Step 4: Speech recognition for task {task_id}")
            transcription_output_path = self.directory_service.get_file_path(
                task_name=task_paths['task_name'],
                subdir='transcription',
                filename='original.txt'
            )

            speech_result = await self.speech_service.recognize_speech_to_path(
                audio_path=audio_output_path,
                output_path=transcription_output_path,
                language="auto",
                model="base"
            )
            await self._update_task(task_id, "processing", 40.0)

            # Step 5: Translation
            self.logger.info(f"Step 5: Translation for task {task_id}")
            translation_output_path = self.directory_service.get_file_path(
                task_name=task_paths['task_name'],
                subdir='translation',
                filename='translated.txt'
            )

            translation_result = await self.translation_service.translate_text_to_path(
                text=speech_result["text"],
                source_language=request.source_language,
                target_language=request.target_language,
                output_path=translation_output_path,
                service="openai"
            )
            await self._update_task(task_id, "processing", 60.0)

            # Step 6: Generate subtitles if requested
            subtitle_path = None
            if request.include_subtitles:
                self.logger.info(f"Step 6: Generating subtitles for task {task_id}")

                # Generate original subtitles
                original_subtitle_path = self.directory_service.get_file_path(
                    task_name=task_paths['task_name'],
                    subdir='subtitles',
                    filename='original.srt'
                )

                original_subtitle_result = await self.subtitle_service.generate_subtitles_to_path(
                    segments=speech_result["segments"],
                    output_path=original_subtitle_path,
                    max_chars_per_line=50,
                    max_lines=2
                )

                # Generate translated subtitles
                translated_subtitle_path = self.directory_service.get_file_path(
                    task_name=task_paths['task_name'],
                    subdir='subtitles',
                    filename='translated.srt'
                )

                # Create translated segments
                translated_segments = []
                for segment in speech_result["segments"]:
                    # For simplicity, we'll translate each segment individually
                    # In production, you might want to batch translate for efficiency
                    seg_translation = await self.translation_service.translate_text(
                        segment["text"],
                        request.source_language,
                        request.target_language,
                        "openai"
                    )
                    translated_segments.append({
                        "start": segment["start"],
                        "end": segment["end"],
                        "text": seg_translation["translated_text"]
                    })

                translated_subtitle_result = await self.subtitle_service.generate_subtitles_to_path(
                    segments=translated_segments,
                    output_path=translated_subtitle_path,
                    max_chars_per_line=50,
                    max_lines=2
                )

                subtitle_path = translated_subtitle_path

            await self._update_task(task_id, "processing", 80.0)

            # Step 7: TTS synthesis
            self.logger.info(f"Step 7: TTS synthesis for task {task_id}")

            # 使用更稳定的TTS合成方法，增加重试和更好的错误处理
            max_tts_retries = 3
            tts_result = None

            # 准备TTS输出路径
            tts_output_path = self.directory_service.get_file_path(
                task_name=task_paths['task_name'],
                subdir='tts',
                filename='chinese.wav'
            )

            for tts_attempt in range(max_tts_retries):
                try:
                    self.logger.info(f"TTS attempt {tts_attempt + 1}/{max_tts_retries}")

                    # 详细记录TTS输入参数
                    translated_text = translation_result["translated_text"]
                    self.logger.info(f"TTS输入文本: {translated_text[:100]}...")
                    self.logger.info(f"TTS语音: {request.tts_voice}")
                    self.logger.info(f"TTS服务: edge-tts")
                    self.logger.info(f"TTS输出路径: {tts_output_path}")

                    tts_result = await self.tts_service.synthesize_speech_to_path(
                        text=translated_text,
                        voice=request.tts_voice,
                        output_path=tts_output_path,
                        speed=1.0,
                        pitch=0,
                        service="edge-tts"
                    )

                    # 详细记录TTS输出结果
                    self.logger.info(f"TTS结果: {tts_result}")
                    if tts_result:
                        self.logger.info(f"TTS音频文件: {tts_result.get('audio_file_path', 'None')}")
                        self.logger.info(f"TTS时长: {tts_result.get('duration', 'None')}")
                        self.logger.info(f"TTS回退: {tts_result.get('fallback', False)}")
                        if tts_result.get('fallback'):
                            self.logger.warning(f"TTS回退消息: {tts_result.get('message', 'Unknown')}")

                    # 检查TTS结果是否有效
                    if tts_result and not tts_result.get("fallback"):
                        self.logger.info("TTS synthesis successful")
                        break
                    elif tts_result and tts_result.get("fallback"):
                        self.logger.warning(f"TTS returned fallback result: {tts_result.get('message', 'Unknown reason')}")
                        # 如果是回退结果，继续重试
                        if tts_attempt < max_tts_retries - 1:
                            import asyncio
                            wait_time = (tts_attempt + 1) * 3  # 3s, 6s, 9s
                            self.logger.info(f"Retrying TTS in {wait_time} seconds...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            # 最后一次尝试，接受回退结果
                            self.logger.warning("Accepting TTS fallback result after all retries")
                            break
                    else:
                        raise Exception("TTS returned invalid result")

                except Exception as e:
                    self.logger.error(f"TTS attempt {tts_attempt + 1} failed: {e}")
                    if tts_attempt < max_tts_retries - 1:
                        import asyncio
                        wait_time = (tts_attempt + 1) * 5  # 5s, 10s, 15s
                        self.logger.info(f"Retrying TTS in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        # 最后一次尝试失败，创建文本回退
                        self.logger.error("All TTS attempts failed, creating text fallback")
                        fallback_path = self.directory_service.get_file_path(
                            task_name=task_paths['task_name'],
                            subdir='tts',
                            filename='fallback.txt'
                        )

                        with open(fallback_path, 'w', encoding='utf-8') as f:
                            f.write(translation_result["translated_text"])

                        tts_result = {
                            "audio_file_path": fallback_path,
                            "duration": 0,
                            "fallback": True,
                            "message": "TTS synthesis failed after all retries"
                        }
                        fallback_messages.append("TTS service was unavailable - text content saved instead of audio")
                        break

            if not tts_result:
                # 如果仍然没有结果，创建紧急回退
                self.logger.error("No TTS result available, creating emergency fallback")
                fallback_path = self.directory_service.get_file_path(
                    task_name=task_paths['task_name'],
                    subdir='tts',
                    filename='emergency_fallback.txt'
                )

                with open(fallback_path, 'w', encoding='utf-8') as f:
                    f.write(translation_result["translated_text"])

                tts_result = {
                    "audio_file_path": fallback_path,
                    "duration": 0,
                    "fallback": True,
                    "message": "Emergency TTS fallback"
                }
                fallback_messages.append("TTS service was unavailable - text content saved instead of audio")
            await self._update_task(task_id, "processing", 90.0)

            # Step 8: Video composition
            self.logger.info(f"Step 8: Video composition for task {task_id}")

            # 准备最终输出路径
            final_video_path = self.directory_service.get_file_path(
                task_name=task_paths['task_name'],
                subdir='output',
                filename='final_video.mp4'
            )

            video_result = await self.video_service.compose_video_to_path(
                video_path=source_video_path,
                audio_path=tts_result["audio_file_path"],
                output_path=final_video_path,
                subtitle_path=subtitle_path,
                output_format="mp4"
            )

            # Update task metadata with final results
            self.directory_service.update_task_metadata(
                task_name=task_paths['task_name'],
                updates={
                    'status': 'completed',
                    'processing_completed_at': datetime.now().isoformat(),
                    'source_language': request.source_language,
                    'target_language': request.target_language,
                    'tts_voice': request.tts_voice,
                    'include_subtitles': request.include_subtitles,
                    'duration': video_result["duration"],
                    'file_size': video_result["file_size"],
                    'fallback_messages': fallback_messages
                }
            )

            # Complete task
            result = {
                "task_directory": task_paths['task_name'],
                "original_video": source_video_path,
                "translated_video": video_result["video_file_path"],
                "audio_file": audio_output_path,
                "transcript": speech_result["text"],
                "translation": translation_result["translated_text"],
                "tts_audio": tts_result["audio_file_path"],
                "subtitle_file": subtitle_path,
                "duration": video_result["duration"],
                "file_size": video_result["file_size"],
                "task_paths": task_paths
            }

            # Add fallback information if applicable
            if tts_result.get("fallback"):
                result["tts_fallback"] = True

            if video_result.get("tts_fallback"):
                result["video_tts_fallback"] = True
                
            if fallback_messages:
                result["fallback_info"] = {
                    "has_fallbacks": True,
                    "messages": fallback_messages,
                    "note": "The pipeline completed successfully but with some service limitations"
                }
            
            await self._update_task(task_id, "completed", 100.0, result)
            self.logger.info(f"Full pipeline completed for task {task_id}")
            
            # Log fallback information if any
            if fallback_messages:
                self.logger.warning(f"Task {task_id} completed with fallbacks: {'; '.join(fallback_messages)}")
            
        except Exception as e:
            self.logger.error(f"Full pipeline failed for task {task_id}: {e}")
            await self._update_task(task_id, "failed", 0.0, error=str(e))
    
    async def get_task_status(self, task_id: str) -> Optional[TaskInfo]:
        """Get task status"""
        task_data = self._global_tasks.get(task_id)
        if not task_data:
            return None
        
        return TaskInfo(**task_data)
    
    async def list_tasks(self, page: int = 1, per_page: int = 10, 
                        status: Optional[str] = None, task_type: Optional[str] = None) -> TaskListResponse:
        """List tasks with pagination"""
        # Filter tasks
        filtered_tasks = []
        for task_data in self._global_tasks.values():
            if status and task_data["status"] != status:
                continue
            if task_type and task_data["task_type"] != task_type:
                continue
            filtered_tasks.append(task_data)
        
        # Sort by created_at descending
        filtered_tasks.sort(key=lambda x: x["created_at"], reverse=True)
        
        # Paginate
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        page_tasks = filtered_tasks[start_idx:end_idx]
        
        return TaskListResponse(
            tasks=[TaskInfo(**task_data) for task_data in page_tasks],
            total=len(filtered_tasks),
            page=page,
            per_page=per_page
        )
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        if task_id not in self._global_tasks:
            return False
        
        task_data = self._global_tasks[task_id]
        if task_data["status"] in ["completed", "failed", "cancelled"]:
            return False
        
        await self.update_task_status(task_id, "cancelled", task_data["progress"])
        return True
    
    async def _update_task(self, task_id: str, status: str, progress: float, 
                          result: Optional[Dict[str, Any]] = None, error: Optional[str] = None):
        """Update task status"""
        await self.update_task_status(task_id, status, progress, result, error)
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process task"""
        pass 