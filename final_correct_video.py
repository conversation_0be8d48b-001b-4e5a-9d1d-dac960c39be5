#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终正确的完整视频中文配音合成器
使用分片字幕文件 output_chinese_split.srt
"""

import os
import re
import time
from moviepy import VideoFileClip, AudioFileClip
from pydub import AudioSegment

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                # 尝试解析第一行为数字索引
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 检查时间行格式
                if ' --> ' in time_line:
                    # 解析时间
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
            except ValueError:
                continue
    
    return subtitles

def compose_final_video():
    """使用分片字幕文件合成最终正确的视频"""
    
    # 文件路径 - 使用分片字幕文件
    video_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/input.mp4"
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese_split.srt"
    audio_dir = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/audio"
    output_path = "chinese_video_final.mp4"
    
    print("🎬 开始最终正确的完整视频中文配音合成...")
    print(f"📝 使用分片字幕文件: {srt_path}")
    print(f"🎵 使用音频目录: {audio_dir}")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(srt_path):
        print(f"❌ 字幕文件不存在: {srt_path}")
        return False
        
    if not os.path.exists(audio_dir):
        print(f"❌ 音频目录不存在: {audio_dir}")
        return False
    
    try:
        # 获取所有音频文件并排序
        audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
        audio_files.sort()
        print(f"🎵 找到 {len(audio_files)} 个音频文件")
        
        # 读取分片字幕文件
        subtitles = read_srt_file(srt_path)
        print(f"📝 读取到 {len(subtitles)} 条分片字幕")
        
        print(f"📊 音频文件数量: {len(audio_files)}")
        print(f"📊 字幕片段数量: {len(subtitles)}")
        
        if len(audio_files) != len(subtitles):
            print(f"⚠️  音频文件数量({len(audio_files)})和字幕数量({len(subtitles)})不匹配")
            print(f"⚠️  将使用较小的数量进行处理")
        
        # 显示视频时长信息
        print("📹 检查原视频信息...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        print(f"📊 原视频时长: {original_duration/60:.1f}分钟 ({original_duration:.2f}秒)")
        video.close()
        
        # 重新加载视频进行合成
        print("📹 重新加载原视频...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        
        # 创建静音背景音轨
        print("🎵 创建合成音频...")
        composite_audio = AudioSegment.silent(duration=int(original_duration * 1000))
        
        # 处理每个字幕的音频 - 一对一映射
        successful_audio_count = 0
        skipped_count = 0
        max_process = min(len(subtitles), len(audio_files))
        
        print(f"🔄 开始合成 {max_process} 个音频片段（一对一映射）...")
        
        for i in range(max_process):
            index, start_time_sub, end_time_sub, text = subtitles[i]
            audio_file = os.path.join(audio_dir, audio_files[i])
            
            try:
                # 加载TTS音频（MP3格式但扩展名为wav）
                try:
                    tts_audio = AudioSegment.from_wav(audio_file)
                except Exception:
                    tts_audio = AudioSegment.from_mp3(audio_file)
                
                tts_duration = len(tts_audio) / 1000.0
                
                # 计算字幕时间段长度
                subtitle_duration = end_time_sub - start_time_sub
                
                if (i + 1) % 100 == 0:  # 每100个显示进度
                    print(f"🔄 [{i+1}/{max_process}] 处理进度: {((i+1)/max_process*100):.1f}%")
                    print(f"    片段{index}({subtitle_duration:.2f}s) -> {audio_files[i]}({tts_duration:.2f}s)")
                    print(f"    文本: {text[:30]}...")
                
                # 调整音频时长以匹配字幕时间段（对于分片音频，通常不需要大幅调整）
                if abs(tts_duration - subtitle_duration) > subtitle_duration * 0.3:
                    # 只有当差异超过30%时才调整
                    if tts_duration > subtitle_duration:
                        speed_factor = tts_duration / subtitle_duration
                        if speed_factor > 2.0:  # 分片音频限制较小的加速倍数
                            speed_factor = 2.0
                        tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                    elif tts_duration < subtitle_duration * 0.6:
                        speed_factor = tts_duration / (subtitle_duration * 0.8)
                        if speed_factor < 0.6:
                            speed_factor = 0.6
                        tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                
                # 将TTS音频插入到指定时间位置
                start_ms = int(start_time_sub * 1000)
                
                # 确保不超出总时长
                if start_ms + len(tts_audio) > len(composite_audio):
                    tts_audio = tts_audio[:len(composite_audio) - start_ms]
                
                # 叠加音频
                composite_audio = composite_audio.overlay(tts_audio, position=start_ms)
                successful_audio_count += 1
                
            except Exception as e:
                print(f"❌ 处理音频失败 {audio_files[i]}: {str(e)}")
                skipped_count += 1
                continue
        
        print(f"✅ 成功处理: {successful_audio_count}/{max_process} 个音频文件")
        print(f"⚠️  跳过文件: {skipped_count} 个")
        
        # 将AudioSegment转换为临时文件
        temp_audio_path = "temp_final_audio.wav"
        print("💾 导出合成音频（这可能需要几分钟）...")
        composite_audio.export(temp_audio_path, format="wav")
        print(f"✅ 合成音频导出完成，大小: {os.path.getsize(temp_audio_path)/(1024*1024):.2f} MB")
        
        # 加载合成音频
        print("🎧 加载合成音频到MoviePy...")
        new_audio = AudioFileClip(temp_audio_path)
        
        # 设置视频的新音轨
        print("🎬 合成最终视频（这可能需要10-20分钟）...")
        final_video = video.with_audio(new_audio)
        
        # 输出视频
        print(f"💾 保存视频到: {output_path}")
        start_time = time.time()
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            bitrate='2000k',
            audio_bitrate='128k'
        )
        
        processing_time = time.time() - start_time
        print(f"⏱️  视频处理完成，耗时: {processing_time/60:.1f}分钟")
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
            print("🗑️  临时文件已清理")
        
        # 释放资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"🎉 最终正确的完整视频中文配音创建成功！")
        print(f"📁 输出文件: {output_path}")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            print(f"📊 文件大小: {file_size:.2f} MB")
            print(f"📊 压缩比: {file_size/(original_duration/60):.2f} MB/分钟")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建视频失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 最终正确的完整视频中文配音合成器")
    print("📋 使用VideoLingo生成的分片字幕文件")
    
    success = compose_final_video()
    if success:
        print("✅ 任务完成！这次应该有声音了！")
    else:
        print("❌ 任务失败！")

if __name__ == "__main__":
    main() 