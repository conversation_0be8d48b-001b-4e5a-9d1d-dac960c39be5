#!/usr/bin/env python3
"""
检查uploads文件夹中视频文件的音频状态
"""

import os
import subprocess
from pathlib import Path

def check_video_audio(video_path):
    """检查视频文件的音频信息"""
    print(f"\n检查视频文件: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"❌ 文件不存在: {video_path}")
        return False
    
    file_size = os.path.getsize(video_path)
    print(f"✅ 文件存在，大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    
    try:
        # 使用FFprobe检查视频文件的所有流
        cmd = ['ffprobe', '-v', 'quiet', '-show_streams', '-show_format', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"❌ FFprobe检查失败: {result.stderr}")
            return False
        
        output = result.stdout
        
        # 分析流信息
        video_streams = []
        audio_streams = []
        
        current_stream = {}
        for line in output.split('\n'):
            line = line.strip()
            if line == '[STREAM]':
                current_stream = {}
            elif line == '[/STREAM]':
                if current_stream.get('codec_type') == 'video':
                    video_streams.append(current_stream)
                elif current_stream.get('codec_type') == 'audio':
                    audio_streams.append(current_stream)
            elif '=' in line:
                key, value = line.split('=', 1)
                current_stream[key] = value
        
        # 显示结果
        print(f"📹 视频流数量: {len(video_streams)}")
        for i, stream in enumerate(video_streams):
            codec = stream.get('codec_name', 'unknown')
            width = stream.get('width', 'unknown')
            height = stream.get('height', 'unknown')
            duration = stream.get('duration', 'unknown')
            print(f"  视频流 {i}: {codec}, {width}x{height}, 时长: {duration}s")
        
        print(f"🔊 音频流数量: {len(audio_streams)}")
        if len(audio_streams) == 0:
            print("  ❌ 没有音频流！这就是为什么文件没有声音")
            return False
        else:
            for i, stream in enumerate(audio_streams):
                codec = stream.get('codec_name', 'unknown')
                sample_rate = stream.get('sample_rate', 'unknown')
                channels = stream.get('channels', 'unknown')
                duration = stream.get('duration', 'unknown')
                print(f"  音频流 {i}: {codec}, {sample_rate}Hz, {channels}声道, 时长: {duration}s")
            print("  ✅ 有音频流")
            return True
            
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        return False

def scan_uploads_folder():
    """扫描uploads文件夹中的所有视频文件"""
    uploads_dir = Path("python-ai-service/storage/uploads")
    
    if not uploads_dir.exists():
        print(f"❌ uploads目录不存在: {uploads_dir}")
        return
    
    print(f"扫描目录: {uploads_dir.absolute()}")
    
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
    video_files = []
    
    for file_path in uploads_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in video_extensions:
            video_files.append(file_path)
    
    if not video_files:
        print("❌ 没有找到视频文件")
        return
    
    print(f"找到 {len(video_files)} 个视频文件:")
    
    has_audio_count = 0
    no_audio_count = 0
    
    for video_file in sorted(video_files):
        has_audio = check_video_audio(str(video_file))
        if has_audio:
            has_audio_count += 1
        else:
            no_audio_count += 1
    
    print(f"\n📊 统计结果:")
    print(f"  有音频的文件: {has_audio_count}")
    print(f"  无音频的文件: {no_audio_count}")
    
    if no_audio_count > 0:
        print(f"\n💡 建议:")
        print(f"  1. 检查原始视频文件是否包含音频")
        print(f"  2. 如果原始文件有音频，可能是上传过程中出现问题")
        print(f"  3. 尝试重新上传视频文件")

if __name__ == "__main__":
    scan_uploads_folder()
