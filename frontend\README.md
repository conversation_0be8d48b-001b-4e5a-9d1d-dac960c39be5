# 视频翻译AI服务 - 前端测试界面

这是一个用于测试视频翻译AI服务的Web前端界面。

## 项目结构

```
frontend/
├── index.html          # 主页面
├── css/
│   └── style.css       # 自定义样式
├── js/
│   ├── api.js          # API客户端
│   └── main.js         # 主要逻辑
├── assets/             # 静态资源
└── README.md           # 说明文档
```

## 功能特性

### 🎯 主要功能
- **服务状态监控**: 实时检查后端服务的健康状态
- **完整翻译流程**: 一键完成视频翻译的整个流程
- **分步处理**: 分别测试音频提取、语音识别、文本翻译等单独功能
- **任务管理**: 实时监控任务状态和进度
- **结果展示**: 清晰展示处理结果

### 🎨 界面特色
- **响应式设计**: 支持桌面和移动设备
- **现代化UI**: 使用Bootstrap 5构建美观界面
- **实时更新**: 任务状态实时更新，无需手动刷新
- **友好提示**: Toast通知和状态指示器
- **多语言支持**: 界面和选项支持多种语言

## 使用方法

### 1. 启动后端服务
确保视频翻译AI服务正在运行：
```bash
cd ../python-ai-service
python main.py
```

### 2. 打开前端界面
使用浏览器打开 `index.html` 文件，或使用简单的HTTP服务器：

```bash
# 使用Python内置服务器
python -m http.server 8080

# 或使用Node.js的http-server
npx http-server . -p 8080
```

然后在浏览器中访问 `http://localhost:8080`

### 3. 测试功能

#### 完整翻译流程
1. 点击左侧的"完整翻译流程"卡片
2. 选择视频文件（支持mp4, avi, mov, mkv, wmv格式）
3. 设置源语言和目标语言
4. 选择语音合成声音
5. 决定是否包含字幕
6. 点击"开始完整翻译"

#### 分步处理
右侧的"分步处理"卡片提供了三个独立的功能：

1. **音频提取**: 从视频文件中提取音频
2. **语音识别**: 将音频转换为文字
3. **文本翻译**: 翻译指定的文本

#### 任务监控
- 页面底部的"任务状态和结果"区域会显示：
  - 当前正在处理的任务
  - 任务历史记录
  - 处理结果

## 技术说明

### 依赖项
- **Bootstrap 5.3.0**: UI框架
- **Font Awesome 6.0.0**: 图标库
- **原生JavaScript**: 不依赖额外的JavaScript框架

### API客户端 (api.js)
- `APIClient`: 核心API客户端类
- `FileUploadManager`: 文件上传管理
- `TaskPollingManager`: 任务状态轮询管理

### 主要组件 (main.js)
- 事件处理
- 任务管理
- 界面更新
- 通知系统

### 样式 (style.css)
- 自定义CSS变量
- 响应式设计
- 动画效果
- 主题颜色

## 注意事项

### 文件路径
由于浏览器安全限制，前端无法直接访问用户的文件系统。当前实现使用模拟路径 `/tmp/filename`。在实际使用中，您需要：

1. 实现文件上传功能到服务器
2. 使用服务器返回的文件路径
3. 或者修改后端API支持文件上传

### CORS设置
如果遇到跨域问题，请确保后端服务器的CORS设置正确。

### 服务器配置
前端默认连接到 `http://localhost:8000`。如果您的服务器运行在不同的地址或端口，请修改 `js/api.js` 中的 `API_CONFIG.baseUrl`。

## 故障排除

### 服务状态显示"不可用"
1. 检查后端服务是否正在运行
2. 确认服务地址和端口配置正确
3. 检查防火墙设置

### 文件上传失败
1. 检查文件格式是否支持
2. 确认文件大小未超过限制
3. 检查服务器存储空间

### 任务一直显示"处理中"
1. 检查后端服务日志
2. 确认必要的依赖项已安装
3. 检查API密钥配置

## 扩展功能

您可以基于此前端界面扩展以下功能：
- 批量文件处理
- 任务队列管理
- 用户账户系统
- 处理历史记录
- 文件预览功能
- 高级配置选项

## 许可证

本项目采用 MIT 许可证。 