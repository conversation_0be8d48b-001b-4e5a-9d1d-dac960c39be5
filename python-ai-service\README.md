# Video Translation AI Service

一个基于FastAPI的视频翻译AI服务，支持语音识别、文本翻译、语音合成和视频合成。

## 功能特性

- 🎵 **音频提取**: 从视频文件中提取音频
- 🗣️ **语音识别**: 使用Whisper进行语音转文字
- 🌐 **文本翻译**: 支持多语言翻译（OpenAI API）
- 🎯 **字幕生成**: 自动生成SRT格式字幕
- 🔊 **语音合成**: 使用Edge TTS生成高质量语音
- 🎬 **视频合成**: 将翻译后的音频与原视频合成
- 📋 **任务管理**: 异步任务处理和状态跟踪

## 技术栈

- **Web框架**: FastAPI 0.104+
- **音频处理**: MoviePy, librosa, pydub
- **语音识别**: OpenAI Whisper
- **文本翻译**: OpenAI GPT API
- **语音合成**: Edge TTS
- **日志管理**: Loguru
- **配置管理**: Pydantic Settings
- **测试框架**: pytest

## 项目结构

```
python-ai-service/
├── app/
│   ├── api/                 # API路由
│   ├── core/                # 核心配置和日志
│   ├── models/              # 数据模型
│   ├── services/            # 业务逻辑服务
│   └── utils/               # 工具函数
├── tests/                   # 测试代码
│   ├── unit/               # 单元测试
│   ├── integration/        # 集成测试
│   └── e2e/                # 端到端测试
├── config/                  # 配置文件
├── logs/                    # 日志文件
├── storage/                 # 存储目录
│   ├── temp/               # 临时文件
│   ├── uploads/            # 上传文件
│   └── outputs/            # 输出文件
├── requirements.txt         # 依赖包
├── pyproject.toml          # 项目配置
├── env.example             # 环境变量示例
└── main.py                 # 应用入口
```

## 快速开始

### 1. 环境要求

- Python 3.10+
- FFmpeg (用于音视频处理)
- CUDA (可选，用于GPU加速)

### 2. 安装依赖

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境变量

```bash
# 复制环境变量示例文件
cp env.example .env

# 编辑配置文件
# 设置OpenAI API密钥等必要配置
```

### 4. 启动服务

```bash
# 开发模式
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 访问API文档

服务启动后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API 使用示例

### 完整视频翻译流程

```python
import requests

# 启动完整翻译流程
response = requests.post("http://localhost:8000/api/v1/pipeline/full", json={
    "video_file_path": "/path/to/video.mp4",
    "source_language": "en",
    "target_language": "zh-CN",
    "include_subtitles": True,
    "tts_voice": "zh-CN-XiaoxiaoNeural"
})

task_id = response.json()["task_id"]

# 查询任务状态
status_response = requests.get(f"http://localhost:8000/api/v1/tasks/{task_id}")
print(status_response.json())
```

### 分步处理

```python
# 1. 提取音频
audio_response = requests.post("http://localhost:8000/api/v1/audio/extract", json={
    "video_file_path": "/path/to/video.mp4",
    "output_format": "wav",
    "sample_rate": 16000,
    "channels": 1
})

# 2. 语音识别
speech_response = requests.post("http://localhost:8000/api/v1/speech/recognize", json={
    "audio_file_path": "/path/to/audio.wav",
    "language": "auto",
    "model": "base"
})

# 3. 文本翻译
translation_response = requests.post("http://localhost:8000/api/v1/translation/translate", json={
    "text": "Hello world",
    "source_language": "en",
    "target_language": "zh-CN",
    "service": "openai"
})
```

## 开发指南

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 生成测试覆盖率报告
pytest --cov=app --cov-report=html
```

### 代码格式化

```bash
# 格式化代码
black .

# 检查代码风格
flake8 .

# 类型检查
mypy app/
```

## 配置说明

主要配置项（在`.env`文件中设置）：

```env
# 应用配置
APP_NAME=Video Translation AI Service
DEBUG=true
LOG_LEVEL=INFO

# API密钥
OPENAI_API_KEY=your_openai_api_key_here

# 存储配置
STORAGE_PATH=./storage
MAX_FILE_SIZE=500MB

# 音频处理配置
AUDIO_SAMPLE_RATE=16000
WHISPER_MODEL=base

# TTS配置
TTS_SERVICE=edge-tts
TTS_VOICE=zh-CN-XiaoxiaoNeural
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t video-translation-ai .

# 运行容器
docker run -p 8000:8000 -v $(pwd)/storage:/app/storage video-translation-ai
```

### 生产环境

```bash
# 使用gunicorn部署
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 支持

如有问题或建议，请创建 [Issue](https://github.com/example/video-translation-ai-service/issues)。 