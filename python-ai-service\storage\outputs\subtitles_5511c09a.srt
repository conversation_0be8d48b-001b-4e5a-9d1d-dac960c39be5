1
00:00:00,000 --> 00:00:03,240
大家好，欢迎来到Faser Editor V4速成课程。

2
00:00:03,240 --> 00:00:08,720
今天我们一起来探讨如何快速简便地构建Faser 3游戏，而无需手动编写大量代码。

3
00:00:08,720 --> 00:00:12,000
如果你是Faser或游戏开发的新手，那么你来对地方了。

4
00:00:12,000 --> 00:00:18,800
Faser Editor V4
是一款强大的可视化HTML5游戏开发工具，让您专注于创意而非迷失于代码之中。

5
00:00:18,800 --> 00:00:24,160
在本视频中，我将一步步指导你如何使用编辑器设置项目并创建一款简单的游戏。

6
00:00:24,160 --> 00:00:25,359
今天我们将讨论以下内容。

7
00:00:25,359 --> 00:00:26,640
Faser Editor是什么？

8
00:00:26,640 --> 00:00:28,480
如何快速搭建一个全新项目

9
00:00:28,480 --> 00:00:32,320
我们将对用户界面中各视图下的工作台进行高层概览。

10
00:00:32,320 --> 00:00:36,079
编辑器如何根据您在图形用户界面(GUI)中的操作自动生成代码。

11
00:00:36,079 --> 00:00:38,159
如何本地运行和测试你的游戏。

12
00:00:38,159 --> 00:00:42,159
添加键盘输入并根据这些事件移动游戏对象。

13
00:00:42,159 --> 00:00:44,480
使用街机物理引擎与碰撞系统。

14
00:00:44,480 --> 00:00:46,079
处理对象重叠问题。

15
00:00:46,079 --> 00:00:48,480
使用预制件作为可用的游戏组件。

16
00:00:48,480 --> 00:00:49,920
控制对象图层。

17
00:00:49,920 --> 00:00:51,280
以及渲染顺序。

18
00:00:51,280 --> 00:00:54,079
为你的游戏添加新资源并创建新动画。

19
00:00:54,159 --> 00:00:57,359
要跟随操作，请确保您能使用Faser Editor V4。

20
00:00:57,359 --> 00:01:01,039
我会在描述中附上文档链接，以便获取更详细的设置说明。

21
00:01:01,039 --> 00:01:02,960
在我们开始之前，先简单说明一下。

22
00:01:02,960 --> 00:01:05,120
本教程使用Faser Editor V4版本。

23
00:01:05,120 --> 00:01:08,000
具备一些JavaScript和Faser 3框架的经验会有所帮助，

24
00:01:08,000 --> 00:01:11,039
但即便你是完全的新手，也能轻松跟上。

25
00:01:11,039 --> 00:01:15,920
本视频不会涉及安装部分，但别担心，我已经在描述中附上了文档和相关资源的链接。

26
00:01:15,920 --> 00:01:16,959
现在让我们开始吧。

27
00:01:19,439 --> 00:01:22,959
因此，在这门速成课程中，我们将创建一个非常基础的Faser 3游戏。

28
00:01:22,959 --> 00:01:27,359
这款游戏的目的就是帮助我们学习使用Faser编辑器的基础知识。

29
00:01:27,359 --> 00:01:28,879
创建一个Faser 3游戏。

30
00:01:28,879 --> 00:01:33,199
所以在我们的游戏中，我们将创建一个非常简单的标题场景，玩家可以点击进入下一个场景。

31
00:01:33,199 --> 00:01:34,959
因此我们将学习场景转换。

32
00:01:34,959 --> 00:01:36,799
然后我们将看看如何移动我们的玩家，

33
00:01:36,799 --> 00:01:40,000
所以我们的恐龙在游戏中四处活动，并且会与方块发生碰撞。

34
00:01:40,000 --> 00:01:43,039
然后我们就能在游戏中拾取食物物品了。

35
00:01:43,039 --> 00:01:46,319
因此，我们将探讨如何为我们的食品添加不同的变化形式。

36
00:01:46,319 --> 00:01:49,839
我们还可以加载精灵图集并创建基本动画。

37
00:01:49,840 --> 00:01:55,359
因此，这是一个非常基础的游戏，但它让我们掌握了创建Faser 3游戏的基本要点。

38
00:01:55,359 --> 00:01:56,560
使用Faser编辑器。

39
00:01:59,600 --> 00:02:01,120
那么，Faser Editor是什么呢？

40
00:02:01,120 --> 00:02:07,040
Faser Editor 是一款高层次的游戏开发工具，旨在助您更高效地构建 Faser 3 游戏。

41
00:02:07,040 --> 00:02:12,000
它能协助填充游戏场景、管理资源，甚至生成代码。

42
00:02:12,000 --> 00:02:16,960
更正式的描述是：Faser Editor 是一款功能强大的可视化开发工具。

43
00:02:16,960 --> 00:02:19,520
使用Faser游戏引擎创建2D游戏。

44
00:02:19,840 --> 00:02:22,960
凭借其直观的界面和丰富的功能集，

45
00:02:22,960 --> 00:02:29,120
它使各级技能水平的开发者都能快速轻松地为桌面和移动平台创建高质量游戏。

46
00:02:29,120 --> 00:02:35,199
无论你是初学者还是经验丰富的开发者，Faser编辑器都能提升你的Faser 3游戏开发体验。

47
00:02:35,199 --> 00:02:39,680
Faser编辑器的一些主要特性包括：场景编辑器是一个可视化工具。

48
00:02:39,680 --> 00:02:45,120
让你能够通过摆放图像和其他类型的游戏对象来创建Faser场景或关卡。

49
00:02:45,120 --> 00:02:49,759
该工具支持将多种Faser游戏对象类型拖放至场景中。

50
00:02:49,759 --> 00:02:52,400
因此，这可以是图像、文字等多种形式的内容。

51
00:02:52,400 --> 00:02:56,159
场景编辑器是一个场景预览工具，直接使用Faser进行渲染。

52
00:02:56,159 --> 00:02:59,599
因此，你可以确信所见即所得，最终游戏中的内容将与预览完全一致。

53
00:03:00,640 --> 00:03:02,240
接下来出场的是你们的物理编辑。

54
00:03:02,240 --> 00:03:06,640
因此，这款工具能让您轻松创建和修改街机物理游戏对象。

55
00:03:06,640 --> 00:03:10,159
你将能够直观地修改游戏对象的外形。

56
00:03:10,159 --> 00:03:12,400
你将不再需要通过代码手动完成这一操作。

57
00:03:12,400 --> 00:03:16,240
您仍可通过图形用户界面进行碰撞检测和重叠检查等操作。

58
00:03:17,039 --> 00:03:21,920
接下来是我们的动画编辑器，这是一款工具，可让你手动或自动

59
00:03:21,920 --> 00:03:26,400
从纹理帧创建您的Sprite动画，无需再动态处理

60
00:03:26,400 --> 00:03:30,000
为你的各种动画生成JSON文件。

61
00:03:30,000 --> 00:03:33,759
通过图形用户界面（GUI），您可以调整诸如帧率等配置设置。

62
00:03:33,759 --> 00:03:36,560
你的延迟、你的重复时间尺度，以及更多内容。

63
00:03:37,280 --> 00:03:39,120
接下来登场的是功能丰富的编辑器。

64
00:03:39,120 --> 00:03:45,280
因此，当你向项目中添加新资源时，Faser编辑器将允许你将它们导入到

65
00:03:45,280 --> 00:03:49,200
资源打包文件，随后该资源打包文件可用于加载所有游戏资源。

66
00:03:49,200 --> 00:03:54,159
一个中心位置。这个功能丰富的编辑器让您可以查看这些文件并了解其类型。

67
00:03:54,159 --> 00:03:57,759
游戏资产已包含在内，且您可以修改该JSON配置文件。

68
00:03:58,480 --> 00:04:02,640
最后，我们来看预制件系统。预制件是Faser编辑器中的一个核心概念，

69
00:04:02,640 --> 00:04:07,200
在高层次上，它们允许你创建可重用的游戏对象，并控制这些对象的

70
00:04:07,199 --> 00:04:11,199
外观、属性与设置，集中存储于一处，随后您便可

71
00:04:11,199 --> 00:04:14,879
通过使用该预制体创建该对象的多个实例。

72
00:04:17,199 --> 00:04:21,759
现在让我们打开Faser编辑器并开始创建我们的项目。编辑器打开后，

73
00:04:21,759 --> 00:04:24,959
点击“新建项目”以创建一个全新的Faser编辑器项目。

74
00:04:24,959 --> 00:04:28,800
在下一屏，您可以为项目选择一个起始模板。

75
00:04:28,800 --> 00:04:34,560
因此，这将为你的Faser 3游戏项目搭建框架。你的三个内置项目模板

76
00:04:34,560 --> 00:04:39,519
这些内容作为Faser Editor安装过程的一部分被离线包含在内。启动器，

77
00:04:39,519 --> 00:04:45,199
示例以及更高级的框架示例需要从网上下载。

78
00:04:45,199 --> 00:04:49,680
那么，让我们开始吧，我们将使用这个基础的JavaScript模板。一旦你选择了模板，

79
00:04:49,680 --> 00:04:52,480
系统会提示您使用Relic来将项目保存到本地机器上。

80
00:04:53,519 --> 00:04:57,600
你需要新建一个文件夹，我就把它命名为“Hello World”吧。

81
00:04:58,319 --> 00:05:02,240
创建新项目文件夹后，你需要打开这个项目文件夹，现在Faser

82
00:05:02,240 --> 00:05:06,960
编辑器将为您设置项目模板并搭建所有必需的项目文件。

83
00:05:06,960 --> 00:05:11,439
用于本地运行游戏。一切设置就绪后，将显示欢迎页面。

84
00:05:11,439 --> 00:05:15,199
从这里，我们可以选择“打开”和“场景”，从包含的两个场景中任选其一。

85
00:05:15,199 --> 00:05:20,079
作为项目模板。让我们打开我们的关卡场景。现在我们的编辑器将会更新为

86
00:05:20,079 --> 00:05:26,639
我们的Faser场景以及其中当前存在的游戏对象。一旦你打开了Faser编辑器

87
00:05:26,639 --> 00:05:31,920
在Workbench或IDE中打开项目时，您将看到多个不同的视图。

88
00:05:31,920 --> 00:05:37,280
以及可供您选择的菜单选项。在此处左侧的高层区域，

89
00:05:37,280 --> 00:05:42,960
大纲视图，这里将显示您当前编辑器中所有内容的概览。

90
00:05:42,960 --> 00:05:49,520
在这样的层级结构中。因此，当我们打开一个场景时，也就是在场景编辑器中时，我们会看到

91
00:05:49,520 --> 00:05:54,480
我们将看到场景中包含的各种游戏对象。举个例子，

92
00:05:54,480 --> 00:05:59,120
如果我添加更多的游戏对象，我们会看到它们现在出现在这边的大纲视图中。

93
00:05:59,840 --> 00:06:04,800
因此，当我们切换到不同的编辑器时，此处内容将随该编辑器的内容而更新。

94
00:06:05,360 --> 00:06:10,319
因此，当我们编辑诸如资源包这类内容时，就能看到构成该资源包的所有资源。

95
00:06:10,319 --> 00:06:15,199
接下来在右侧，我们有检查器面板。通过检查器视图，您可以查看

96
00:06:15,199 --> 00:06:20,399
当前在编辑器中选中的对象的所有属性均处于活动状态。

97
00:06:20,399 --> 00:06:24,720
举个例子，当前我们未选中任何内容，因此默认显示的是我们的场景（Scene）。

98
00:06:24,800 --> 00:06:29,360
因此，在我们的场景中，我们将看到一系列可用于修改场景的选项。

99
00:06:29,360 --> 00:06:34,160
属性。而如果我选择这个图像游戏对象，面板就会更新，现在我们看到

100
00:06:34,160 --> 00:06:38,880
我们游戏对象的所有属性。现在我们可以做一些事情，比如修改变量名，

101
00:06:38,880 --> 00:06:44,160
我们可以更新游戏对象的位置，以及更多功能。因此，根据您使用的编辑器

102
00:06:44,160 --> 00:06:48,960
在，您的面板将反映当前所选对象的属性。因此，

103
00:06:48,960 --> 00:06:53,440
屏幕底部，您会看到文件视图。文件视图将显示您所有的文件列表。

104
00:06:53,439 --> 00:06:58,639
项目文件。和其他文件编辑器一样，您可以执行添加新文件等基本操作，

105
00:06:58,639 --> 00:07:03,680
删除、重命名，甚至移动项目中的文件。当你打开其中一个文件时，

106
00:07:03,680 --> 00:07:08,560
你将在IDE中打开它，他们就能进行修改。就像处理其他任何对象一样

107
00:07:08,560 --> 00:07:13,519
当您选择其中一个文件时，我们的检查器面板将更新显示

108
00:07:13,519 --> 00:07:18,319
与该文件相关的信息。旁边是我们的区块视图。接下来将会

109
00:07:18,319 --> 00:07:23,120
展示当前活跃编辑器中可用于构建对象的元素。因此，当我们拥有

110
00:07:23,120 --> 00:07:27,680
打开场景编辑器后，这里将向我们展示Phaser 3内置的各种游戏对象类型。

111
00:07:27,680 --> 00:07:32,560
我们可以添加到场景中的内容。它会显示我们已加载的任何图像或其他资源，

112
00:07:32,560 --> 00:07:37,199
我们也可以将这些拖入我们的场景中。如果你使用的是不同的活动编辑器，比如资源编辑器，

113
00:07:37,199 --> 00:07:41,680
打包编辑器将向您展示可以导入到资源包中的各种文件。

114
00:07:42,560 --> 00:07:46,480
接下来，在右上角是我们的主菜单。这将让你能够

115
00:07:46,480 --> 00:07:51,439
打开不同的项目，开启一个新的项目窗口，以及类似的各种操作。你也可以

116
00:07:51,439 --> 00:07:56,399
可以更改场景的默认布局、选择主题颜色等等。

117
00:07:56,399 --> 00:08:00,639
除此之外，我们还设有支持聊天按钮，方便您联系phasier Studio团队。

118
00:08:00,639 --> 00:08:04,240
在左上角，我们有几个创建新内容的快捷方式，

119
00:08:04,240 --> 00:08:07,680
播放按钮，可让您运行项目并在浏览器中查看。

120
00:08:07,680 --> 00:08:12,639
然后我们有一段快速对话来开启一个场景。接着我们在内部打开项目。

121
00:08:12,639 --> 00:08:16,720
Visual Studio Code。因此，当你点击这个按钮时，它将会用VS Code打开你的项目。

122
00:08:16,720 --> 00:08:21,360
Visual Studio Code 中当前活动的文件。因此，当你点击该按钮时，VS Code
现在应当会打开它。

123
00:08:21,360 --> 00:08:25,120
如果你已本地安装，启动后就能看到构成你项目的代码。

124
00:08:25,760 --> 00:08:30,080
最后，在屏幕顶部，我们将看到几个不同的工具，它们将

125
00:08:30,080 --> 00:08:34,560
根据当前打开的编辑器不同而变化。例如，在欢迎界面上，没有

126
00:08:34,560 --> 00:08:39,200
工具在这里。如果我们使用的是资源打包的编辑器，现在可以选择不同的导入选项。

127
00:08:39,200 --> 00:08:43,920
文件。现在当我们处于场景编辑器时，我们拥有了适用于场景工作的所有工具。

128
00:08:43,919 --> 00:08:48,159
因此，对于我们的场景编辑器工具，第一个是平移工具。平移工具让你能够

129
00:08:48,159 --> 00:08:52,399
选择游戏对象后，即可在场景中拖动它，X和Y坐标将随之更新。

130
00:08:52,399 --> 00:08:58,000
该游戏对象的位置。接下来是我们的缩放工具。缩放工具允许你通过拖动来调整

131
00:08:58,000 --> 00:09:02,559
在X和Y坐标轴上放大你的游戏对象。当我们修改这些属性时，

132
00:09:02,559 --> 00:09:06,399
我们将放大游戏对象的比例。稍后就能在此看到效果。接下来的工具，

133
00:09:06,399 --> 00:09:11,120
旋转工具，允许你选择一个游戏对象，然后可以对其进行旋转和修改。

134
00:09:11,279 --> 00:09:16,879
游戏对象的角度。接下来是原点工具，它允许你修改原点。

135
00:09:16,879 --> 00:09:21,279
对于你的游戏对象来说，默认情况下，大多数游戏对象的原点位于游戏中心。

136
00:09:21,279 --> 00:09:28,159
对象，因此X和Y的坐标均为0.5。这样一来，你的原点便是Phaser将游戏对象放置于你

137
00:09:28,159 --> 00:09:34,560
场景。它将该点作为游戏对象的起始点。因此，这里的X和Y值，

138
00:09:34,560 --> 00:09:38,960
这是基于这个原点的设定。因此，如果我将原点放在左上角，

139
00:09:38,960 --> 00:09:44,560
而我做了一些调整，比如将我的位置设为0,0，现在我们会看到它正以此为基准。

140
00:09:44,560 --> 00:09:50,000
游戏对象的起始点。如果我将原点放回中间，现在我们会看到

141
00:09:50,000 --> 00:09:54,400
该位置现在位于0, 0，并以此作为我们游戏对象的原点。

142
00:09:55,200 --> 00:09:59,440
接下来，我们介绍区域选择工具。这个工具可以让你通过拖拽操作选中区域内所有对象。

143
00:09:59,440 --> 00:10:03,680
一个供您选择游戏对象的区域，这样您就可以进行多选操作。

144
00:10:03,679 --> 00:10:09,519
最后，我们还有平移工具。这个工具让你只需简单地操作就能在视口中自由移动浏览。

145
00:10:09,519 --> 00:10:14,159
点击并拖动你的场景。因此，如果你需要查看快捷键，只需将鼠标悬停

146
00:10:14,159 --> 00:10:19,120
悬停在该工具上，您就能看到对应的快捷键。现在，

147
00:10:19,120 --> 00:10:23,439
在我们的图形用户界面中，许多属性在悬停时都会显示提示信息，说明其用途。

148
00:10:23,439 --> 00:10:30,879
这是实际上被相位编辑器所使用的。因此，当我们在项目中工作并移动时

149
00:10:30,960 --> 00:10:35,279
围绕我们场景的一切，在幕后，Phaser编辑器正在动态生成代码。

150
00:10:35,279 --> 00:10:40,240
针对我们的相位器项目。要查看这段代码，我们实际上需要打开那个.js文件，它位于

151
00:10:40,240 --> 00:10:45,519
与我们层级相关的。因此，在图形用户界面（GUI）中，当我们为项目创建新文件时，通常，

152
00:10:45,519 --> 00:10:50,799
将会有两个文件。一个是.scene文件，这个文件会被用于

153
00:10:50,799 --> 00:10:57,200
编辑器在此处将其呈现在图形用户界面中。然后你的.js或.ts文件将成为代码部分。

154
00:10:57,280 --> 00:11:02,000
这是由编辑器根据我们采取的操作生成的。因此，当我们使用我们的项目时

155
00:11:02,000 --> 00:11:07,040
模板，这会自动创建level.js和level.scene文件。但如果我想制作一个

156
00:11:07,040 --> 00:11:13,040
以新建场景为例，如果我执行新建操作，选择场景文件，我会将其命名为“测试”。我们来看看效果。

157
00:11:13,040 --> 00:11:17,759
首先，我们测试了场景文件的创建。现在我们在图形用户界面中看到了我们的场景。接着，我们进行了测试。

158
00:11:17,759 --> 00:11:22,480
那个JS文件也被创建了。因此，在生成的代码中，实际上有两个主要部分。

159
00:11:22,480 --> 00:11:27,360
第一部分是编译器生成的代码。而第二部分则是

160
00:11:27,360 --> 00:11:31,200
安全区域，您可以在其中添加自己的代码。因此，在我们的整个代码中，我们会看到

161
00:11:31,200 --> 00:11:34,720
这两个不同的代码块。第一个将是编译代码的开始和结束部分

162
00:11:34,720 --> 00:11:39,200
编译后的代码。然后我们将有用户可操作部分的起点和终点

163
00:11:39,200 --> 00:11:44,800
用户可以在这些起始区块之外的任何地方进行操作

164
00:11:44,800 --> 00:11:49,600
用户。您不想在那里添加代码，因为编译后的代码将自动生成。

165
00:11:49,600 --> 00:11:54,320
每当我们采取行动时，任何新增的内容都可能被覆盖。当你添加自己的代码时

166
00:11:54,320 --> 00:11:59,600
在这些区块内，从用户到用户的起始区域，编辑器将不会进行修改。

167
00:11:59,600 --> 00:12:04,639
你的代码在每次编译之间都会持续存在。让我们通过一个例子来看看这一点，如果我们打开

168
00:12:04,639 --> 00:12:10,320
提升我们的level.js文件。在这里，我们编写的代码与游戏对象紧密关联，这些对象是

169
00:12:10,320 --> 00:12:14,800
在关卡场景中，我们目前有两个游戏对象。一个是这个图像游戏对象，还有这个

170
00:12:14,879 --> 00:12:20,319
另一个是这个文本游戏对象。因此，在我们的代码中，我们有两个变量。我们有dyno。所以这是

171
00:12:20,319 --> 00:12:25,279
引用我们的图像游戏对象，然后“welcome”引用的是那个文本游戏对象。目前

172
00:12:25,279 --> 00:12:32,000
它们拥有各自的x和y坐标位置。因此，我们看到图像的尺寸是329乘以235。所以如果我们选择我们的图像游戏

173
00:12:32,000 --> 00:12:37,439
对象，我们看到我们的位置也随之反映。所以，如果我拿起我们的恐龙并像拖动它一样移动

174
00:12:37,439 --> 00:12:43,279
在这里。现在我们看到可以修改这些属性。如果我们回到 level.js 文件，现在会看到

175
00:12:43,360 --> 00:12:48,319
我们的代码已更新以反映该变化。因此，在您自行添加时这一点很重要。

176
00:12:48,319 --> 00:12:52,399
将自定义代码添加到你的项目中。你需要确保将其放置在正确的区域。因此，接下来

177
00:12:52,399 --> 00:12:59,759
这样一来，它就不会被覆盖。要在本地运行测试游戏，你需要启动一个开发服务器。

178
00:12:59,759 --> 00:13:05,600
默认情况下，Phaser编辑器提供了一个内置的HTTP服务器来完成这一任务。因此，当我们使用基本的

179
00:13:05,600 --> 00:13:09,600
JavaScript项目模板，如果您点击左上角的播放按钮，

180
00:13:09,600 --> 00:13:13,279
这将启动开发服务器并在你的网页中运行游戏

181
00:13:13,279 --> 00:13:18,480
浏览器。一旦发生这种情况，你应该能看到我们的本地主机端口附带编辑器外部，而我们应该

182
00:13:18,480 --> 00:13:23,040
查看我们的Phaser游戏在这里。所以当你运行开发服务器时，如果移动游戏

183
00:13:23,040 --> 00:13:27,040
场景周围的物体，你点击保存后，如果回到浏览器，我们将需要

184
00:13:27,040 --> 00:13:32,720
刷新后，我们应该能看到游戏已根据我们所做的更改进行了更新。有一点需要注意

185
00:13:32,720 --> 00:13:37,600
当你使用默认的JavaScript项目模板时，我们内置的HTTP服务器无需额外配置即可工作

186
00:13:38,480 --> 00:13:41,840
没有任何问题。不过，如果您使用的是其他项目模板，它是基于现代网页的。

187
00:13:41,840 --> 00:13:46,639
诸如webpack、parcel或vite这类技术。在这些情况下，你需要运行一个开发服务器。

188
00:13:46,639 --> 00:13:51,279
手动操作。当你点击上方的播放按钮时，如果Phaser编辑器未能检测到有

189
00:13:51,279 --> 00:13:56,320
基于您提供的配置运行的开发服务器，将会显示一个对话框，内容是

190
00:13:56,320 --> 00:14:01,120
将为您提供启动开发服务器的选项，之后您便可以运行游戏。

191
00:14:01,120 --> 00:14:08,960
适合你的浏览器。Phaser框架支持多种输入类型，包括键盘、

192
00:14:08,960 --> 00:14:13,919
鼠标、游戏手柄，甚至在移动设备上的触摸操作。我们将首先探讨的输入类型是

193
00:14:13,919 --> 00:14:19,279
点击事件。因此在你的Phaser游戏中，点击事件通常是一种机制，让你能够允许

194
00:14:19,279 --> 00:14:24,000
玩家可以直接与你的Phaser场景或游戏对象互动，当他们点击这些对象时，

195
00:14:24,000 --> 00:14:29,360
我们可以在游戏中做一些事情。默认情况下，这里的Phaser模板支持点击操作。

196
00:14:29,840 --> 00:14:33,519
我们来到运行游戏的浏览器界面。如果我们点击恐龙游戏，

197
00:14:33,519 --> 00:14:38,960
对象，我们会看到文本在这里更新为新内容。其工作原理是当你选择一个

198
00:14:38,960 --> 00:14:45,759
在场景编辑器中为游戏对象定义一个碰撞区域。你的碰撞区域是

199
00:14:45,759 --> 00:14:49,840
游戏对象中玩家实际可点击的区域，随后我们可以监听该点击事件

200
00:14:49,840 --> 00:14:55,519
事件及其他多种事件类型。对于您的点击区域，您可以选择多种不同的形状。

201
00:14:55,519 --> 00:14:59,919
因此，默认情况下，通常建议为大部分图像游戏对象使用矩形。

202
00:14:59,919 --> 00:15:05,120
当你选择矩形时，默认的宽度和高度将是该图像的尺寸。

203
00:15:05,120 --> 00:15:10,079
你在场景中加载的内容。而如果我们选择这里的这个文本游戏对象，现在就能看到我们的

204
00:15:10,079 --> 00:15:14,639
命中区域为空。因此，当你向场景中添加新的游戏对象时，通常不会有一个

205
00:15:14,639 --> 00:15:20,000
为该对象定义的点击区域，你需要手动进行设定。因此在这个场景中

206
00:15:20,000 --> 00:15:25,039
在这里，我们只需再添加一个图像游戏对象dyno1。如果我们跳转到level.js文件，

207
00:15:25,039 --> 00:15:29,199
如果我们转到 dyno1 的定义处，就会看到这里定义了我们的游戏对象。

208
00:15:29,199 --> 00:15:33,439
而原版测功机与这台新机型的一个关键区别就在于这一点

209
00:15:33,439 --> 00:15:38,719
在此设置交互方法。为了使Phaser框架能够为游戏对象启用输入功能，

210
00:15:38,719 --> 00:15:44,639
我们需要调用那个方法。而在相位编辑器里，一旦我们定义了那个发丝区域，

211
00:15:44,639 --> 00:15:50,639
这将自动为我们添加实现该功能的代码。既然我们已经添加了这个矩形点击区域，

212
00:15:50,639 --> 00:15:56,000
如果我们回到 level.js 文件，现在可以看到 set interact
已经被调用并传入了定义参数。

213
00:15:56,000 --> 00:16:02,000
我们提供的那个矩形。关于点击事件的第二部分，你需要监听

214
00:16:02,000 --> 00:16:08,240
那个事件并对其作出反应。而在这里的创建方法中，这是放置自定义代码的地方，这些代码是

215
00:16:08,240 --> 00:16:13,439
模板的一部分被添加来监听此事件。因此，Phaser框架拥有多种

216
00:16:13,439 --> 00:16:18,639
系统内置的事件会针对系统中的各种不同情况发出。因此，当我们

217
00:16:18,639 --> 00:16:23,759
监听输入事件，这些事件可能包括指针按下、指针抬起等多种操作，我们现在可以提供

218
00:16:23,759 --> 00:16:29,039
我们希望在此时运行的回调函数。这很好地展示了我们如何能够

219
00:16:29,039 --> 00:16:34,240
将现有的已生成的相位编辑器代码进行增强，添加更多功能。

220
00:16:34,240 --> 00:16:40,159
我们可以添加一个事件监听器，并修改我们在场景编辑器中创建的其他游戏对象。

221
00:16:40,159 --> 00:16:45,120
举个例子，我们直接复制这段代码。然后粘贴它。接着我们

222
00:16:45,120 --> 00:16:50,000
我们将监听dyno one变量上的指针按下事件。所以我们要这样做。

223
00:16:50,000 --> 00:16:55,600
我们将进行动态测试一。通过评估“hello world”来执行指针按下操作。我们简单打个招呼。

224
00:16:55,600 --> 00:17:01,600
因此，如果我们回到关卡场景，为了使用并运行那段代码，我们需要更新

225
00:17:01,600 --> 00:17:06,079
我们的变量作用域。因此，对于创建的JavaScript代码，它与我们的变量相关联。

226
00:17:06,079 --> 00:17:10,480
检查器中这里的定义。默认情况下，当我们添加新的游戏对象时，

227
00:17:10,480 --> 00:17:16,400
作用域始终局限于创建它的类内部。这意味着我们只能在

228
00:17:16,400 --> 00:17:22,559
能够在我们代码所在的本地上下文中使用它，在此编辑器的创建方法中添加此处。

229
00:17:22,559 --> 00:17:27,120
这意味着我们将无法在课堂的其他地方使用它。而且一旦我们，

230
00:17:27,120 --> 00:17:31,759
除非我们改变这一属性。因此，如果我们查看原始的测功机数据，就会发现这里有

231
00:17:31,759 --> 00:17:38,240
类的作用域。因此，我们需要在这里更新作用域。我们新动态实例的变量作用域。

232
00:17:38,240 --> 00:17:42,319
那么让我们改变这一点，我们将把这个类变量化。这意味着它将变成

233
00:17:42,319 --> 00:17:48,160
可供我们在此层级类内部的任何逻辑使用。因此，我们来看一下这段代码，它曾

234
00:17:48,160 --> 00:17:52,880
生成。现在我们可以看到这个新的dyno one属性。这将使我们能够

235
00:17:52,880 --> 00:17:58,559
在我们的创建方法中在这里引用它。因此，要查看一个示例，我们无需更改正在进行的操作。

236
00:17:58,559 --> 00:18:02,400
我们会在游戏中遇到一个错误。所以如果我们保存更改，回到浏览器。

237
00:18:02,400 --> 00:18:06,799
让我们重新开始游戏。现在我们要打开开发者工具。我们只需右键点击并执行。

238
00:18:06,799 --> 00:18:12,879
检查。我们去议会见我们的开发者议会。现在我们会看到有一个错误关联

239
00:18:12,879 --> 00:18:18,240
这段代码是在我们的level.js文件中生成的。它显示无法重新定义未定义的属性。

240
00:18:18,799 --> 00:18:24,000
这是因为在我们的层级代码中，实际上并没有一个名为dyno one的属性。

241
00:18:24,000 --> 00:18:31,039
因此这是未定义的。而这正是导致我们错误的原因。现在，如果我们将其从局部改回类作用域，

242
00:18:31,039 --> 00:18:36,240
现在我们的代码中已经有了这个属性。现在我们的代码应该能正常运行了。那么现在如果我们点击

243
00:18:36,240 --> 00:18:41,440
在我们的第二个动态测试仪上，我们打个招呼。如果我们点击第一个，就会看到“你好，世界”。正如我之前提到的，

244
00:18:41,440 --> 00:18:46,000
在Phaser框架文档中，我们可以监听多种内置事件。

245
00:18:46,000 --> 00:18:50,000
网站上有我们支持的活动列表。目前我们感兴趣的是

246
00:18:50,000 --> 00:18:56,079
作为我们的相位器输入事件。因此，在我们的输入中，我们支持诸如点击和拖动等操作。

247
00:18:56,079 --> 00:19:02,079
围绕我们场景中的一个物体。我们可以将一个物体放到另一个物体上。我们可以监听指针

248
00:19:02,079 --> 00:19:06,960
下指针向上和指针悬停事件。这样我们就可以实现类似按钮的效果，当我们的

249
00:19:06,960 --> 00:19:13,599
鼠标悬停其上时，我们应用效果；若鼠标移开，它又会发生变化，诸如此类，不一而足。

250
00:19:13,599 --> 00:19:18,879
除了基本输入事件外，我们还有针对游戏手柄和键盘的事件。

251
00:19:18,879 --> 00:19:23,279
那么我们就可以监听这些按键的按下时机，并据此作出相应的反应。因此，由于所有这些

252
00:19:23,279 --> 00:19:27,519
这些事件在Phaser框架中得到支持，意味着它们在其中均可使用。

253
00:19:27,519 --> 00:19:32,079
相位编辑器，而我们只需要编写代码来响应它们。接下来我们要处理的输入类型是

254
00:19:32,079 --> 00:19:36,319
接下来要介绍的是我们的键盘事件。我们将要进行的操作是添加

255
00:19:36,319 --> 00:19:40,000
支持监听方向键操作，然后我们将移动游戏中的物体。

256
00:19:40,000 --> 00:19:46,000
我们的场景。因此，为了在内置块中添加对键盘事件的支持，其中一种类型是输入。

257
00:19:46,000 --> 00:19:51,279
我们扩展这一功能。我们可以在场景中添加一个键盘按键选项。现在，我们来拖动这个模块。

258
00:19:51,279 --> 00:19:55,759
进入我们的场景，看看是否新增了一个名为“input”的文件夹。默认情况下，我们的

259
00:19:55,839 --> 00:20:01,039
变量名将对应键盘按键。我们将其更改为upkey。现在对于我们的按键，它有一个默认

260
00:20:01,039 --> 00:20:06,480
课程范围，我们需要将其设为课程，因为我们需要监听输入并作出反应。

261
00:20:06,480 --> 00:20:12,160
在我们的场景更新方法中对其进行处理。接下来，我们可以选择想要监听输入的按键。

262
00:20:12,160 --> 00:20:16,720
因此，我们将设置我们的上键。一旦选定后，让我们保存场景。接下来跳转到

263
00:20:16,720 --> 00:20:22,559
level.js 文件，在编辑器中我们可以看到这里创建的方法，我们的 upkey
变量已被生成，而且我们正在

264
00:20:22,639 --> 00:20:28,399
监听我们的输入插件，我们的键盘管理器，正在执行添加按键操作，这将允许

265
00:20:28,399 --> 00:20:33,440
使我们能够在按键被按下、按住不放以及释放时监听输入。这

266
00:20:33,440 --> 00:20:38,000
将被分配到我们为类添加的upkey属性上。因此，现在要响应我们的

267
00:20:38,000 --> 00:20:42,559
按键被按下时，我们实际上需要在代码中添加一个新方法，我们将添加更新功能。

268
00:20:42,559 --> 00:20:46,079
方法。因此，这里的方法是您可用的内置方法之一。

269
00:20:46,079 --> 00:20:52,079
相量场景类。这个方法将在每次游戏滴答或每次游戏更新时被调用，或者每当

270
00:20:52,079 --> 00:20:56,720
对游戏进行的更新。因此，我们现在在这个方法里能做的就是检查是否

271
00:20:56,720 --> 00:21:01,519
如果我们的按键被按下。为此，我们只需添加一个if语句。我们会写：if

272
00:21:01,519 --> 00:21:07,439
我们将引用我们的上键。因此，如果上键处于按下状态，就意味着它正被按住。

273
00:21:07,439 --> 00:21:12,319
当前由玩家操作。因此，现在我们可以选取一个游戏对象并更新它的

274
00:21:12,399 --> 00:21:18,079
根据按下的这个键来定位。因此，让我们引用我们的恐龙游戏对象，我们将

275
00:21:18,079 --> 00:21:25,279
参考我们的Y属性。因此，当我们按下向上键时，我们希望游戏对象在场景中向上移动。

276
00:21:25,279 --> 00:21:31,039
因此，在你的场景中，左上角的位置将是00，即X为0，

277
00:21:31,039 --> 00:21:37,519
Y为0。随着我们在场景中向右移动，X值将随之增加。而我们移动

278
00:21:37,599 --> 00:21:42,639
向左或减少我们的X值。而当我们向下移动时，将会增加我们的Y值。

279
00:21:42,639 --> 00:21:48,319
如果我们向上移动，我们希望减少那个值。这意味着当我们想要移动时

280
00:21:48,319 --> 00:21:52,720
当按下上键时，我们实际上需要从Y值中减去一个数值。因此，我们只需进行减法操作——

281
00:21:52,720 --> 00:21:59,039
等于四。所以如果我们保存代码，确保我已经保存了，让我们回到浏览器。

282
00:21:59,039 --> 00:22:03,920
让我们重新加载场景。现在，如果我们按住上方向键，可以看到游戏对象可以移动了。

283
00:22:03,920 --> 00:22:08,880
它在我们的场景中向上移动。现在，我们将对剩余的箭头执行相同的操作。

284
00:22:08,880 --> 00:22:15,519
按键。那么我们再拖入几个键盘模块。接下来，处理我们的下键操作。

285
00:22:17,519 --> 00:22:22,880
所以我们选择“下”作为我们的关键代码。接下来，我们将处理“左”键。那么，我们将设置左键。

286
00:22:23,920 --> 00:22:26,800
我们来找左边。最后，我们有右边的钥匙。

287
00:22:27,279 --> 00:22:34,559
让我们保存一下，跳转到我们的level.js文件。现在我们要做同样的事情，但我们想要

288
00:22:34,559 --> 00:22:39,839
参考我们创建的其他键。因此，我们在编辑器的创建方法中快速查看一下。

289
00:22:39,839 --> 00:22:44,399
我们会看到，其他按键已根据我们指定的键码定义好了。

290
00:22:44,960 --> 00:22:50,559
那么让我们回到我们的更新方法。我们将处理else的情况。让我们把这段代码复制到这里。

291
00:22:50,879 --> 00:23:00,799
因此，如果我们的向下键被按下，现在我们要继续增加y值。

292
00:23:00,799 --> 00:23:07,839
现在，我们把这段代码整体复制到这里，然后来处理左右方向键。如果按下左键

293
00:23:07,839 --> 00:23:17,200
如果按下左键，我们就减少x值；如果按下右键，我们就增加x值。

294
00:23:17,519 --> 00:23:21,759
所以如果我们保存了，让我们回到浏览器。现在如果我们按下方向键，

295
00:23:21,759 --> 00:23:25,039
我们现在可以在场景中移动游戏对象了。

296
00:23:28,559 --> 00:23:31,759
现在我们可以通过键盘事件在场景中移动物体了。

297
00:23:31,759 --> 00:23:34,799
接下来我们要探讨的是如何让游戏对象之间实现互动。

298
00:23:34,799 --> 00:23:41,440
通过物理系统相互交互。因此，开箱即用的Phaser编辑器支持Arcade物理引擎。

299
00:23:41,440 --> 00:23:46,160
来自Phaser的物理系统，使我们能够执行诸如检测碰撞等操作。

300
00:23:46,160 --> 00:23:51,200
两个不同的游戏对象。我们可以检查它们何时重叠以及更多功能。这就是Phaser框架。

301
00:23:51,200 --> 00:23:57,440
支持多种物理系统，如街机物理、冲击物理以及物质物理。

302
00:23:57,440 --> 00:24:02,960
JS. 街机物理系统非常适合那些我们仅需检测碰撞的简单游戏。

303
00:24:02,960 --> 00:24:08,080
类似这样的各种事情。因此，为了让我们的游戏支持物理效果，我们需要更新游戏。

304
00:24:08,080 --> 00:24:13,840
配置以告诉Phaser我们想使用哪种物理系统。在我们的游戏配置中，

305
00:24:13,839 --> 00:24:18,319
这将包含在我们的主摘要文件中。在代码内部，我们将

306
00:24:18,319 --> 00:24:22,079
有了这个新的Phaser游戏，我们将创建一个新的Phaser游戏实例，并提供

307
00:24:22,079 --> 00:24:26,959
我们游戏的配置在这里。这里可以设置物理系统等参数。

308
00:24:26,959 --> 00:24:31,359
他们确实使用的。因此，如果我们添加物理属性，这将是一个对象。

309
00:24:31,359 --> 00:24:35,199
我们想要添加的第一个属性是default。这将是一个字符串，表示我们使用的系统。

310
00:24:35,199 --> 00:24:40,559
默认想要使用的是街机风格。接下来，我们提供一个物理属性

311
00:24:40,559 --> 00:24:45,279
我们正在使用的系统。因此，在这种情况下，我们将采用街机模式。现在我们需要提供我们的配置

312
00:24:45,279 --> 00:24:50,799
为此，首先需要调试并将此设置为true。这将使我们能够看到

313
00:24:50,799 --> 00:24:55,759
我们物理体的轮廓。这样一来，与物理系统协作就变得非常容易。

314
00:24:55,759 --> 00:24:59,679
在我们设计游戏时。一旦你完成了设计，你会想要将其设置为false。

315
00:24:59,679 --> 00:25:04,799
游戏。接下来，我们可以定义默认的重力。它将应用于我们的x和y属性。因此

316
00:25:04,879 --> 00:25:10,960
我们希望将两者都设为零。然后让我们继续保存。现在我们已经启用了我们的

317
00:25:10,960 --> 00:25:17,279
为了在我们的游戏中实现物理系统，我们需要更新游戏对象以实际运用这一系统。因此，这里有一个

318
00:25:17,279 --> 00:25:21,519
我们可以通过几种不同的方法来实现这一点。第一种方法是给现有的游戏添加一个物理实体。

319
00:25:21,519 --> 00:25:26,720
物体。而物理体就是我们用来检测这些碰撞的方式。第二种方法是，我们

320
00:25:26,720 --> 00:25:32,000
可以更改我们的默认对象类型。因此，当我们默认拖动图像时，它将会是一个

321
00:25:32,000 --> 00:25:37,359
图像游戏对象。所以如果我们点击我们的恐龙，你可以将其更改为街机图像。现在一个

322
00:25:37,359 --> 00:25:42,640
街机图像（arcade image）是你游戏对象中图像（image）的扩展，但它拥有更多属性。

323
00:25:42,640 --> 00:25:48,079
受限于物理规则。那么快速看一下我们的检查器，现在我们会看到

324
00:25:48,079 --> 00:25:53,440
新增与物理实体相关的区域，以及物理效果如何与游戏互动

325
00:25:53,440 --> 00:25:58,880
对象。而如果我们选择另一个动态对象，就会在检查器中看到这些属性并不存在。

326
00:25:58,960 --> 00:26:04,640
现在，添加物理效果的第三种方法是利用内置的对象类型并进行选择。

327
00:26:04,640 --> 00:26:10,400
将我们的街机图像或街机精灵拖放到场景中。现在让我们选中它

328
00:26:10,400 --> 00:26:15,680
我们喜欢使用的纹理。默认情况下，该类型现在为街机图像，我们无需再手动设置。

329
00:26:15,680 --> 00:26:21,520
手动更改该设置。因此，在我们已将游戏对象的类型更新为街机图像后，

330
00:26:21,520 --> 00:26:26,640
如果我们看一下面板，默认情况下，我们会有一个街机物理体。因此，如果我们保存，

331
00:26:26,640 --> 00:26:32,160
让我们再次打开网页浏览器，刷新一下。现在我们可以看到这个紫色的方框了。

332
00:26:32,160 --> 00:26:38,560
围绕着我们的测功机。那个紫色方框代表的是我们的街机物理体。因此，通过

333
00:26:38,560 --> 00:26:44,880
默认情况下，这将使用我们游戏对象的宽度和高度。因此，我们当前的图像

334
00:26:44,880 --> 00:26:50,080
这些维度。因此，我们的紫色盒子是一个更圆润的游戏对象，而不是完全匹配的形状。

335
00:26:50,080 --> 00:26:56,160
直接控制恐龙。因此，这个物理实体，也就是紫色的轮廓线，现在就是我们用来检测的方式。

336
00:26:56,320 --> 00:27:00,400
对于与同样启用了物理特性的其他游戏对象的碰撞。

337
00:27:01,040 --> 00:27:06,400
那么如果我们回到场景中，选择第二个动态对象，就让我们继续更新它的类型吧。

338
00:27:06,400 --> 00:27:11,440
同样，我们将类型更改为街机图像，然后进行替换。开始吧。

339
00:27:11,440 --> 00:27:16,320
保存。我们回来吧。现在，我们有两个启用了物理效果的游戏对象。

340
00:27:16,320 --> 00:27:22,000
就像我们的输入事件一样，一旦拥有了物理实体，我们实际上需要告知编辑器

341
00:27:22,079 --> 00:27:26,240
我们想要检测与之碰撞的对象。因此，我们可以针对一个游戏对象进行检查，

342
00:27:26,240 --> 00:27:30,319
我们可以检查与一组游戏对象的碰撞，甚至还能检测碰撞情况。

343
00:27:30,319 --> 00:27:37,359
在这里，我们画布元素的外部边界。为此，请为我们的玩家选择dyno。

344
00:27:37,359 --> 00:27:41,359
接下来，我们将进入面板设置街机物理体的碰撞属性。这里有一系列

345
00:27:41,359 --> 00:27:46,079
这里有一些属性会影响物理效果如何应用到我们的身体上。而我们正在

346
00:27:46,079 --> 00:27:51,759
就在这里勾选“与世界边界碰撞”这个选项。如果我们保存并返回浏览器，

347
00:27:52,000 --> 00:27:56,720
现在如果我们尝试将游戏对象移出画布元素之外，就会发现物理效果的表现。

348
00:27:56,720 --> 00:28:03,599
系统现在阻止我们的游戏对象离开。为了检查一个物体之间的碰撞

349
00:28:03,599 --> 00:28:08,720
我们的游戏对象与另一个对象之间，需要使用所谓的碰撞体。因此，从我们的方块

350
00:28:08,720 --> 00:28:15,119
在内置的街机模式下，我们把碰撞块拖进来。这个块的作用是让我们能够

351
00:28:15,119 --> 00:28:21,039
我们可以选择想要检查碰撞的两个游戏对象或组别。然后

352
00:28:21,039 --> 00:28:26,799
我们可以提供一个回调函数，以便在此情况发生时参与投票。因此对于对象

353
00:28:26,799 --> 00:28:32,720
一，我们选择我们的测功机，然后对于对象二，让我们选择我们的测功机到我们的测功机一。好了，

354
00:28:32,720 --> 00:28:37,200
因此，如果我们保存并返回浏览器，刷新后尝试移动游戏对象，

355
00:28:37,200 --> 00:28:42,639
我们的另一个游戏对象，碰撞检测似乎没有生效。这里出现的情况是

356
00:28:42,639 --> 00:28:48,240
为了让碰撞正常运作，我们需要使用物理系统来移动游戏对象。

357
00:28:48,240 --> 00:28:53,680
所以目前在我们的level.js文件中，我们正在手动设置x和y的值，并且我们正在

358
00:28:53,680 --> 00:28:58,640
递增一个递减器时未检查是否有实际碰撞。因此当我们这样做时

359
00:28:58,640 --> 00:29:03,120
这样，我们正在覆盖那个物理系统，并声明，嘿，我们仍然想要移动我们的游戏对象，

360
00:29:03,120 --> 00:29:09,920
这就是为什么我们可以穿过其他游戏对象。而不是使用我们的.y.x属性，

361
00:29:09,920 --> 00:29:16,079
我们想要更新游戏对象的速度，以便在场景中移动它们。

362
00:29:16,079 --> 00:29:20,559
因此，为了利用物理系统实现移动，我们将引用我们的动态游戏对象。

363
00:29:20,559 --> 00:29:27,359
让我们设置速度，并根据所查看的属性来决定是设置速度的x分量还是y分量。

364
00:29:27,359 --> 00:29:32,159
所以在我们的上升键中，因为我们在看y轴，所以我们会处理y方向的速度。现在我们可以提供

365
00:29:32,159 --> 00:29:38,720
我们想要设定的速度值。那么，就设为负200吧。同样地，类似于我们在这里对y轴的操作，

366
00:29:38,720 --> 00:29:42,799
当我们向上移动时，我们希望将其设为负值；而向下移动时，则希望将其设为正值。

367
00:29:42,799 --> 00:29:48,480
转为正数。那么让我们复制这个。粘贴一下，然后我们设为正200。注释掉这部分。

368
00:29:48,480 --> 00:29:55,599
以下是我们的代码，用于处理y值。我们保存一下。刷新场景。现在来到动态对象下方。

369
00:29:55,599 --> 00:30:00,480
现在如果我们尝试向上移动，就会看到我们正在推动另一个游戏对象。此外，当我们按下

370
00:30:00,480 --> 00:30:04,960
按下一次上方向键，我们会看到物体持续朝该方向移动，因为我们正在设定

371
00:30:04,960 --> 00:30:10,480
那个速度。因此，实际情况是我们的物理系统会多次调用你的更新方法。

372
00:30:10,480 --> 00:30:16,240
一旦我们为游戏对象设置了速度，就可以任意修改我们的物理效果，

373
00:30:16,240 --> 00:30:20,960
我们的速度将保持一致，它将是一个恒定值，我们将持续前进。

374
00:30:20,960 --> 00:30:27,759
来回切换。因此，我们实际上需要告诉Phaser框架，嘿，我们不应该

375
00:30:27,759 --> 00:30:33,519
如果我们的密钥未被持有以修复此问题，我们只需在后面添加一个else代码块。

376
00:30:33,519 --> 00:30:39,120
在此处的if else检查中，如果既不是上也不是下，我们只需将速度重置为零。

377
00:30:39,279 --> 00:30:45,679
按键被按下时。所以如果我们现在刷新页面，松开按键，游戏对象就会停止移动。

378
00:30:45,679 --> 00:30:50,959
现在我们可以在碰撞时推动另一个游戏对象了。那么接下来让我们做同样的修改。

379
00:30:50,959 --> 00:30:57,359
对于我们x属性的设置。那么，我们把它复制到这里。然后把这部分注释掉。现在，我们去粘贴它。

380
00:30:57,359 --> 00:31:05,519
我们将设置速度x。复制这段代码。粘贴后将其改为正值。最后，

381
00:31:05,599 --> 00:31:12,480
让我们来编写else代码块。接着我们重置它，并将其改为x。我们保存一下。那么在我们进行这些操作的同时，

382
00:31:12,480 --> 00:31:17,599
在这里，我们可能需要考虑的一点是，这个值被多次重复使用了。

383
00:31:17,599 --> 00:31:22,400
因此，将这个存储在我们文件的属性或变量中可能是合理的。于是，

384
00:31:22,400 --> 00:31:28,079
我们只需在一个地方进行修改，无需在多处更改。因此，为了实现这一点，

385
00:31:28,079 --> 00:31:32,639
让我们来到代码这里，它显示我们可以安全地编写更多代码。现在，我们来添加一个新的

386
00:31:32,640 --> 00:31:38,240
将属性添加到我们的类中。我们将这个属性称为玩家速度。并且我们会赋予

387
00:31:38,240 --> 00:31:44,400
恢复到默认值200。现在我们已经添加了这个属性，接下来让我们继续更新代码。

388
00:31:44,400 --> 00:31:49,840
在这里。所以我们要处理这个玩家的速度。我们会将其乘以负一。我们得到了我们的

389
00:31:49,840 --> 00:31:56,080
负值。让我们复制这个。我们将粘贴到这里。粘贴到这里。然后我们将其乘以负。

390
00:31:56,080 --> 00:32:00,080
一。好的，如果我们说我们应该能够处理变更。那么如果我们回到浏览器

391
00:32:00,079 --> 00:32:05,439
刷新后，我们依然可以像之前那样移动游戏对象。但现在我们使用的是

392
00:32:05,439 --> 00:32:10,480
物理学的应用使我们能够实现这些碰撞检测，并与游戏中的其他元素互动。

393
00:32:10,480 --> 00:32:15,599
物体。好的，有一点需要了解的是，一旦我们触碰到那个其他游戏物体，会发生什么

394
00:32:15,599 --> 00:32:20,799
我们物理游戏对象中的力现在是否正与另一个对象碰撞并施加力

395
00:32:20,799 --> 00:32:25,679
到另一个游戏对象。因为没有东西阻止它或减慢它的速度，那个游戏对象

396
00:32:25,680 --> 00:32:30,720
也在不断移动。而我们从未更新那个游戏对象，因此我们无法离开场景。而且

397
00:32:30,720 --> 00:32:35,680
这就是为什么它能够退出，而我们的游戏对象却不能。如果我们想让游戏对象也

398
00:32:35,680 --> 00:32:41,600
物体实际上可以移动，我们在编辑器内可以做的就是选择让我们的恐龙下来。

399
00:32:41,600 --> 00:32:46,320
给我们的物理体属性。当发生碰撞时，我们可以勾选这里的这个方框

400
00:32:46,320 --> 00:32:52,799
不可移动。这样一来，当我们与该游戏对象互动时，就不会

401
00:32:52,799 --> 00:32:58,000
实际上能够移动它。但现在的情况就像我们撞上了一个无法撼动的静止物体。

402
00:32:58,000 --> 00:33:02,639
推。所以这对墙壁或其他我们不希望被推动的角色来说再合适不过了。

403
00:33:02,639 --> 00:33:11,200
在我们的游戏中移动。因此，除了使用物理系统来检测碰撞之外，

404
00:33:11,200 --> 00:33:17,119
我们的游戏对象，也可以用它来检查重叠。而重叠是我们允许

405
00:33:17,119 --> 00:33:21,919
我们的游戏对象要穿过另一个游戏对象，但同时仍能利用我们的物理系统来

406
00:33:21,920 --> 00:33:27,279
当我们的物理体与另一个重叠时收到通知。这对于例如

407
00:33:27,279 --> 00:33:32,160
我们游戏中玩家可以拾取物品的捡拾点。我不确定我们是否有相关事件

408
00:33:32,160 --> 00:33:35,840
并且我们有区域供玩家移动并通过，以触发游戏中的其他内容，

409
00:33:35,840 --> 00:33:40,800
重叠效果正适合这种情况。那么在我们的游戏中，假设有一只饥饿的恐龙，它

410
00:33:40,800 --> 00:33:45,120
想吃点东西，所以我们想在游戏中能够捡起这些食物。为此，

411
00:33:45,120 --> 00:33:50,560
让我们回到场景编辑器，向场景中添加一个新的街机图像游戏对象。那么，我们开始吧。

412
00:33:50,559 --> 00:33:56,159
拖动我们的街机图像块。我们将选择我们的食物项目。因此，如果我们选中那个图像，

413
00:33:56,159 --> 00:34:01,279
哦，我们的图片有点大。所以把它缩小一点。我们只需将其缩放至0.4倍。

414
00:34:02,480 --> 00:34:07,679
既然我们的食物物品稍微缩小了一些，我们现在要为其添加一个碰撞器。

415
00:34:07,679 --> 00:34:13,039
玩家和我们想要重叠的对象。因此，从我们的街机积木中，让我们添加

416
00:34:13,039 --> 00:34:20,079
向我们的场景中添加另一个碰撞器。在这个碰撞器上，我们选择我们的动态对象。然后对于第二个物体，

417
00:34:20,079 --> 00:34:26,480
让我们来选择我们的街机游戏图像。现在，从我们的碰撞器中，我们还有另一个可用的选项是这个。

418
00:34:26,480 --> 00:34:33,039
这里有所重叠。所以如果我们点击这个并保存，然后回到浏览器。现在应该会发生什么呢

419
00:34:33,039 --> 00:34:38,000
尽管这个食物对象在我们碰撞时有一个相位点，但我们实际上并没有

420
00:34:38,000 --> 00:34:43,599
移动或推动它。相反，我们只是与之有所重叠。而为了做到

421
00:34:43,599 --> 00:34:51,119
对此，我们现在需要监听回调，一旦玩家与之发生碰撞。

422
00:34:51,119 --> 00:34:55,599
游戏对象。因此，这里我们将提供我们希望调用的回调函数。

423
00:34:55,599 --> 00:35:01,360
好的。那么到目前为止，关于回调，我们现在能做的是在我们的层级类中提供一个方法。

424
00:35:01,360 --> 00:35:06,559
我们乐于被召唤去做那件事。我们将这样做以引用此背景。

425
00:35:06,559 --> 00:35:13,119
当前场景。然后我们要吃水果。所以如果我们保存，需要添加这个方法到

426
00:35:13,119 --> 00:35:19,039
我们的班级。因此，如果我们进入level.js文件中的update方法下，让我们添加一个新方法到

427
00:35:19,039 --> 00:35:24,400
我们班。我们将这个方法命名为“吃水果”。好的。然后在这个方法内部，我们只需做

428
00:35:24,400 --> 00:35:30,079
咨询日志。我们只需说“命中”，目的只是想看到某些反应发生。

429
00:35:30,079 --> 00:35:34,559
当我们与食物发生碰撞时。于是我们刷新浏览器。一旦我们的物理引擎

430
00:35:34,559 --> 00:35:40,000
当身体与我们的食物重叠时，我们的回调函数被调用。因此，我们将看到的是这种情况

431
00:35:40,000 --> 00:35:45,840
每秒被调用多次。因此，游戏循环的每次更新周期都会调用

432
00:35:45,840 --> 00:35:51,440
当这两个游戏对象重叠时，会触发此回调。这一点需要注意。因此

433
00:35:51,440 --> 00:35:56,799
如果你有逻辑，只想运行一次，你需要在代码中处理以停止

434
00:35:56,799 --> 00:36:02,480
重叠检查不再发生。既然现在看到这个功能正常运作，我们想要清理我们的

435
00:36:02,480 --> 00:36:10,320
食物游戏对象并将其从场景中移除。为了实现这一点，在我们的碰撞器中，当

436
00:36:10,320 --> 00:36:15,440
调用我们的碰撞回调函数时，我们将接收到的两个参数会是两个游戏对象

437
00:36:15,440 --> 00:36:20,800
发生碰撞重叠的对象。根据我们添加这些对象的顺序，

438
00:36:20,800 --> 00:36:25,199
这里的对象，这将是我们参数的顺序。因此，由于我们的第一个对象是我们的

439
00:36:25,199 --> 00:36:30,320
dyno，我们的第一个参数将引用我们的dyno图像游戏对象。接着，第二个参数将

440
00:36:30,320 --> 00:36:36,400
要引用我们的食物。那么，如果我们回到level.js文件，让我们添加那两个参数。

441
00:36:36,400 --> 00:36:41,920
按照我们的方法，接下来我们会加入测功机，然后加入食物。好了，现在在控制台下方

442
00:36:41,920 --> 00:36:47,600
日志，让我们引用我们的食物变量。因此，对于我们的食物游戏对象，我们想要禁用它

443
00:36:47,600 --> 00:36:53,039
身体。通过禁用我们的身体，这将使我们能够停止检查碰撞，同时

444
00:36:53,039 --> 00:36:58,400
在我们代码中执行任何其他操作之前。因此，如果我们回到浏览器并刷新，现在应该看到的是

445
00:36:58,400 --> 00:37:04,240
尽管我们的游戏对象此刻仍在与其他对象重叠，但我们的命中判定仅被调用了一次。

446
00:37:04,240 --> 00:37:10,000
一。那么现在我们能做的就是销毁这个游戏对象并将其从游戏中移除。

447
00:37:10,800 --> 00:37:16,400
为了表明我们的恐龙现在已经提供了帮助。所以如果我们处理食物，那会破坏，这将说明

448
00:37:16,400 --> 00:37:21,039
使用phaser销毁这个游戏对象并进行清理。好的，如果我们刷新浏览器，

449
00:37:21,039 --> 00:37:28,160
如果我们与食物物品重叠，就会看到它从场景中消失。好的，那么在游戏中，

450
00:37:28,159 --> 00:37:32,960
吃完一个食物后，我们的恐龙仍然感到饥饿。于是让我们再添加一个食物拾取点。

451
00:37:32,960 --> 00:37:37,920
欢迎来到我们的游戏。那么从我们的方块中，选一个街机风格的图像吧，我们会选择我们的食物道具。

452
00:37:37,920 --> 00:37:42,319
哦，我们会看到我们的食物现在又回到了原来的大小。现在我们必须

453
00:37:42,319 --> 00:37:47,759
手动调整此对象的大小以匹配我们的其他食物项目。随着我们向游戏中添加更多该对象的实例，

454
00:37:47,759 --> 00:37:52,799
在我们的场景中，我们将不得不重复这种需要不断调整的模式

455
00:37:52,800 --> 00:37:57,920
单独设置每一项。但假设我们完成后，突然觉得，其实我们更想要

456
00:37:57,920 --> 00:38:02,960
我想更改我们食品项目的另一项设置。比如说，如果我们想给自己添加一个帐篷，我们可以调整

457
00:38:02,960 --> 00:38:10,160
它的颜色。举个例子，假设我想做成这样，这里的蓝色。那么如果我复制

458
00:38:10,160 --> 00:38:14,960
这个我来操作，我会为我们所有的帐篷播放这个。现在可以说我们有了一个独特的物品，但目前我还需要

459
00:38:14,960 --> 00:38:20,160
同样将此应用于我们所有的其他游戏对象。为了避免所有这些情况，我们可以使用

460
00:38:20,159 --> 00:38:26,639
所谓的预制体内面或编辑器。预制体是我们用来创建类似的一种方式。

461
00:38:26,639 --> 00:38:32,079
我们游戏对象中所有属性的快照。然后我们可以创建

462
00:38:32,079 --> 00:38:36,960
该预制体的所有实例。之后，我们对预制体所做的任何更改都会同步反映出来。

463
00:38:36,960 --> 00:38:42,159
在这些游戏对象中。为了看一个例子，我们先清理掉所有这些水果

464
00:38:42,159 --> 00:38:47,519
这里的物体。至于这个水果物体，我们想创建一个预制体。有几种方法

465
00:38:47,519 --> 00:38:52,480
为此，若当前不存在游戏对象，我们可以右键点击该对象，

466
00:38:52,480 --> 00:38:57,280
我们可以进入预制件界面，然后选择用对象创建预制件。这样操作将会打开

467
00:38:57,280 --> 00:39:01,519
一个模态窗口，我们可以在其中定义预制体的保存位置。我打算将我的保存在

468
00:39:01,519 --> 00:39:06,880
暂时将源文件夹命名为“食物预制体”。我们点击创建即可。

469
00:39:06,880 --> 00:39:12,639
因此，如果我们保存，首先发生的变化是我们的游戏对象在轮廓中变成了绿色。

470
00:39:12,639 --> 00:39:18,559
我们来看看如果展开这个，它引用的是这个预制体实例。这意味着我们已经

471
00:39:18,559 --> 00:39:24,960
现在创建了这个新的食物预制场景和食物预制JS文件。因此，我们食物预制的

472
00:39:24,960 --> 00:39:31,599
刚刚创建，这将只是一个继承自该对象原有游戏对象类型的类。

473
00:39:31,599 --> 00:39:36,639
因此，当我们这样做时，就能直接修改那个游戏对象。现在我们可以应用

474
00:39:36,639 --> 00:39:41,920
我们对场景所做的那些改动。举个例子，假设，哦，我们其实不太喜欢那个帐篷在

475
00:39:41,920 --> 00:39:49,599
我们的食物。让我们将其重置为白色。所以如果我们复制它，应用到我们所有的值上。如果我们保存

476
00:39:49,599 --> 00:39:54,400
我们的食物现在又变回了紫色，我们跳转到关卡场景。哦，我们看到了我们的食物

477
00:39:54,400 --> 00:39:59,599
物品现在变为紫色。这正是我们预制件的闪光点，因为现在我们所做的任何改动

478
00:39:59,599 --> 00:40:06,319
在这里，我们可以将其应用到任何实例和任何场景中。因此，现在如果我们想要实现多重

479
00:40:06,320 --> 00:40:12,000
食品取货点替代了拖动我们的街机图像，现在我们有了这个新的预设块。因此，如果

480
00:40:12,000 --> 00:40:18,880
我们将食物图片拖拽过来，现在可以看到它与其他游戏对象实例的大小一致了。而且

481
00:40:18,880 --> 00:40:23,920
其具有相同的应用属性。因此，我们现在可以将多个实例拖拽到场景中。

482
00:40:23,920 --> 00:40:28,720
如果我们想要做出改变并影响所有人，可以来到我们的食品预制区。那么让我们

483
00:40:28,720 --> 00:40:34,400
也许我们希望食物看起来更大一些。那么就把缩放比例设为1.2和1.2吧。保存后，回到我们的

484
00:40:34,400 --> 00:40:39,680
level. 我们会看到游戏对象的所有实例都已被修改。因此，我只需

485
00:40:39,680 --> 00:40:46,720
将我们的比例和预制件恢复到之前的设置。我们将回退到0.4。另一部分是

486
00:40:46,720 --> 00:40:52,240
预制件真正酷的地方在于，当我们创建这些实例时，我们将在

487
00:40:52,240 --> 00:40:59,039
属性旁边有个小锁图标。这意味着当这个属性被锁定时，

488
00:40:59,039 --> 00:41:05,920
创建的类实例将使用该类的默认属性。

489
00:41:05,920 --> 00:41:12,719
当它解锁时，意味着我们在图形界面中设置的任何值，该实例都将拥有这个值。

490
00:41:12,719 --> 00:41:17,360
这就是为什么我们能在场景中移动这个物体，而不会影响到其他的物体。

491
00:41:17,360 --> 00:41:22,719
周围的那些也是因为它们是解锁状态。如果我们锁定这个位置，它将会转到

492
00:41:22,799 --> 00:41:30,079
预制体实例的默认位置在此处。即0，0。如果我移动它，我们会看到这个

493
00:41:30,079 --> 00:41:35,759
仅移动设置了锁定图标的那个。那么让我们将其恢复为0，0。所以它是

494
00:41:35,759 --> 00:41:41,519
在我们左上角。然后我们保存一下，回到我们的关卡场景。现在如果解锁它，

495
00:41:41,519 --> 00:41:46,559
我们可以拖动这个而它不会被修改。因此，我们能够对任何这样的对象进行此操作。

496
00:41:46,559 --> 00:41:52,639
我们实例上的属性。也许我想要这个食物对象更大一些。所以我们可以设置为0.6。

497
00:41:53,359 --> 00:41:57,679
现在，我们其他的将保持它们设定的尺寸不变。我们还可以做一些事情，比如

498
00:41:57,679 --> 00:42:03,919
修改我们的alpha或帐篷。这不会影响我们的其他实例。因此，预制件确实

499
00:42:03,919 --> 00:42:07,439
强大且适合需要创建多个游戏实例的场景

500
00:42:07,439 --> 00:42:12,799
对象。好的。现在我们已经向场景中添加了多个预制体。如果我们回到浏览器中

501
00:42:12,799 --> 00:42:18,159
测试时，现在应该发生的是我们可以享用我们的单一食物项。但新添加的那些，

502
00:42:18,159 --> 00:42:24,639
我们实际上无法进行拾取。原因在于我们的对撞机目前仅

503
00:42:24,639 --> 00:42:30,319
瞄准我们添加的一个食物游戏对象。我们需要做的是检查是否

504
00:42:30,319 --> 00:42:37,440
我们玩家与所有食物游戏对象之间的碰撞。因此我们需要传递一个组或一个

505
00:42:37,440 --> 00:42:42,319
游戏对象数组。我们需要检测碰撞。因此传入一个游戏对象数组

506
00:42:42,319 --> 00:42:46,719
我们喜欢用来检测碰撞的，可以使用另一个内置模块。因此，在我们的内置模块下

507
00:42:46,719 --> 00:42:51,679
块，让我们进入组，然后向游戏场景中添加一个列表。这将新增一个

508
00:42:51,679 --> 00:42:57,039
部分名为列表。列表是一种让你创建数组内容的方式。然后在你的

509
00:42:57,039 --> 00:43:00,559
数组，你可以提供任何你想要的游戏对象。然后我们就会拥有这些可用的资源。

510
00:43:00,559 --> 00:43:05,679
在我们的代码中。所以我打算把这个称为一个变量food，然后我们会进行保存。如果我们跳转

511
00:43:05,679 --> 00:43:11,039
转到我们的 level.js 文件，应该能看到类中新增了一个名为 food 的属性。于是

512
00:43:11,039 --> 00:43:16,399
在我们的列表下可以看到，我们有一个食物数组，目前它是空的。现在，为了实际添加对象到其中，

513
00:43:16,480 --> 00:43:22,079
在我们的列表中，可以选择一个游戏对象。现在，从这里的下拉属性菜单中，我们可以进行

514
00:43:22,079 --> 00:43:27,920
列表。现在如果我们选择列表，就可以选择食物。如果我们进行保存，回到我们的level.js。

515
00:43:27,920 --> 00:43:33,039
现在我们将看到我们的食物预制件已被添加到食物数组中，之后就可以引用了。

516
00:43:33,039 --> 00:43:37,680
在我们的代码上。好的，那么要将我们的游戏对象添加到列表中，如果我们选择我们的游戏对象，

517
00:43:37,680 --> 00:43:42,320
来自我们的检查小组，我们将列出清单。现在我们可以选择想要添加的食物清单。

518
00:43:42,400 --> 00:43:48,320
我们的目标对象。你也可以选择多个游戏对象并同样操作。所以如果我们保存，

519
00:43:48,320 --> 00:43:52,720
让我们回到对撞机的话题。现在，我们的目标不再是针对某个特定的实例进行操作，而是

520
00:43:52,720 --> 00:43:58,080
食物，让我们来挑选我们的食物清单。我们会进行选择。好的，如果我们保存了，回到浏览器，

521
00:43:58,080 --> 00:44:03,360
让我们回顾一下。现在如果我们尝试拾取食物对象，会发现现在可以逐个拾取了。

522
00:44:03,360 --> 00:44:10,559
我们的回调方法现在被调用了。接下来，我们游戏的下一个功能将是

523
00:44:10,559 --> 00:44:15,119
我们将添加一个计分机制。这样，当我们的饥饿恐龙拾取食物时，

524
00:44:15,119 --> 00:44:19,360
当我们吃掉元素时，我们将增加分数，并将其分配给玩家。

525
00:44:19,360 --> 00:44:24,000
那么加入这个机制。我们将在场景中添加一个新的文本游戏对象。因此，在我们的内置

526
00:44:24,000 --> 00:44:28,639
在字符串类型下，我们添加文本。因此，对于我们的文本游戏对象，我们将调用我们的

527
00:44:28,639 --> 00:44:34,639
变量得分。接下来，我们位于左上角中心的位置。让我们更新我们的

528
00:44:34,639 --> 00:44:40,960
我们将说“得分”然后从零开始。所以初始值为零。现在来更新我们的尺寸。

529
00:44:40,960 --> 00:44:46,319
所以对于我们的尺寸，就设为48像素吧，并且我们会在之间留出一点间距。

530
00:44:46,319 --> 00:44:51,279
游戏的边缘。因此，对于我们的位置，让我们向右移动10像素，再向下移动10像素。

531
00:44:51,279 --> 00:44:55,440
因此，如果我们保存并返回浏览器，刷新后，就能看到我们的分数文本现在显示出来了。

532
00:44:55,440 --> 00:45:00,480
给玩家。现在，当我们拾取其中一个食物对象时，我们需要实际更新我们的

533
00:45:00,480 --> 00:45:07,199
我们的字符串上的文本。那么如果我们来到我们的级别摘要文件，首先，让我们添加一个新的

534
00:45:07,199 --> 00:45:12,079
用于追踪玩家分数的变量。因此，在我们自定义代码中设置玩家速度的位置下方，

535
00:45:12,079 --> 00:45:17,519
让我们加入分数。我们将默认值设为零。现在在我们的吃水果方法中，

536
00:45:17,519 --> 00:45:22,880
让我们清除我们的议会日志。销毁游戏对象后，我们将增加分数。

537
00:45:22,880 --> 00:45:28,800
我们会这样做。我们将引用分数。然后我们将进行加等于一的操作。之后我们

538
00:45:28,880 --> 00:45:34,640
更新我们的分数变量，我们需要实际更新分数显示。因此对于我们的分数文本，

539
00:45:34,640 --> 00:45:40,320
我们需要引用已创建的分数游戏对象。因此，在我们的分数游戏对象上目前

540
00:45:40,320 --> 00:45:45,039
我们的作用域设置为本地。现在让我们将其更改为类。这样，我们就能实际获取

541
00:45:45,039 --> 00:45:50,320
添加到我们类中的一个属性，可供我们引用。因此，如果我们查看属性，现在可以看到

542
00:45:50,320 --> 00:45:54,800
我们有一个分数属性，它将与我们在此处添加的属性发生冲突。于是

543
00:45:54,960 --> 00:45:59,440
在检查器中添加变量名时，请记住这些名称也将

544
00:45:59,440 --> 00:46:04,560
成为添加到您类中的属性名称。由于它与score属性相同，我们

545
00:46:04,560 --> 00:46:11,519
添加了，让我们来做分数文本。我们会保存。现在如果我们回到我们的level.js文件，现在我们会看到

546
00:46:11,519 --> 00:46:17,039
有一个分数文本属性。回到我们的吃水果方法中，让我们引用我们的分数文本游戏。

547
00:46:17,039 --> 00:46:22,400
对象。为此，我们将使用set text方法来更新游戏对象上的文本。

548
00:46:22,400 --> 00:46:27,920
我们要确保保持我们的单词得分。现在只需将我们的得分值加进去。

549
00:46:27,920 --> 00:46:33,119
那么我们就按这个分数来操作。好的，我们保存后回到浏览器。现在当我们前往

550
00:46:33,119 --> 00:46:38,480
去拿我们的一件食品，哦，出错了。所以这是无法重新设置的未定义属性

551
00:46:38,480 --> 00:46:45,599
这里发生的情况是，当我们处于这个方法内部时，我们当前正尝试

552
00:46:45,679 --> 00:46:52,799
参考我们当前等级场景的范围。因此，我们正尝试逐步提高分数，并且

553
00:46:52,799 --> 00:46:58,559
在此类上设置我们的分数文本属性。然而，在我们的关卡场景中，如果查看碰撞体，

554
00:46:59,440 --> 00:47:06,159
当我们默认在这里调用回调函数时，将会丢失当前执行的上下文。

555
00:47:06,159 --> 00:47:12,159
调用来源。那么来看一个例子，在我们禁用body之前，我先执行一下console.log。

556
00:47:12,239 --> 00:47:17,039
我们这就开始操作。如果你回到浏览器界面，我们来试着挑选一个食品项目。

557
00:47:17,039 --> 00:47:24,000
现在我们来看看这是否在我们当前函数的上下文中被调用，也就是我们的回调函数。

558
00:47:24,000 --> 00:47:30,239
功能。如果我们想使用整个类的上下文，就需要将其传入。因此，在我们的回调函数中

559
00:47:30,239 --> 00:47:36,399
在此上下文中，我们希望传入这个引用来指向我们的场景。如果我们回到浏览器，现在如果

560
00:47:36,399 --> 00:47:41,920
我们拿起食物物品时，会看到上下文指向我们的关卡场景。而现在我们已经

561
00:47:41,920 --> 00:47:47,200
在我们的破裂范围内，我们将看到食物被摧毁，同时分数文本也会更新。现在是

562
00:47:47,200 --> 00:47:54,000
我们应该能够拾取所有的游戏对象和场景。现在我们已经添加了分数功能。

563
00:47:54,000 --> 00:47:58,559
在我们游戏中引入新机制的同时，我想花点时间谈谈我们的场景分层和渲染技术。

564
00:47:58,559 --> 00:48:02,960
顺序。因此，当我们在场景中移动游戏对象时，可能会注意到的一件事是

565
00:48:02,960 --> 00:48:09,119
我们的动态游戏对象在两个UI元素中都显示在文本背后。而且，

566
00:48:09,119 --> 00:48:15,920
这里发生的是默认情况下，Phaser会按照我们添加的顺序渲染游戏对象。

567
00:48:15,920 --> 00:48:21,279
将它们加入我们的场景中。而使用相位编辑器时，这将基于它们被添加到我们的方式来进行。

568
00:48:21,279 --> 00:48:28,960
在这里为我们的游戏对象建立层级关系，因为分数文本显示在恐龙上方，而欢迎文本则位于

569
00:48:28,960 --> 00:48:35,440
在我们的测功机上方。这意味着它们将被渲染覆盖在玩家的游戏对象之上。

570
00:48:35,519 --> 00:48:41,360
要想改变这一点，我们就需要调整这些对象在层级结构中的顺序。

571
00:48:42,240 --> 00:48:50,159
例如，如果我使用我们的测功机，进入布局界面并进行排序操作，比如选择上移，

572
00:48:50,159 --> 00:48:56,000
这将使测功机上升，并在我们的层级结构中占据更高位置。如果我们确实保存并返回到场景中，

573
00:48:56,000 --> 00:49:02,480
现在我们将看到，在UI文本之前，我们的dyno游戏对象被渲染在其他游戏对象之上。

574
00:49:02,480 --> 00:49:07,280
仍在后台渲染。这一点很重要，因为在添加游戏对象时需要注意。

575
00:49:07,280 --> 00:49:12,000
在这里，这些元素的添加顺序将影响玩家所见的呈现效果。

576
00:49:12,559 --> 00:49:16,880
因此，还有其他方法来控制渲染顺序，其中一种我们可以采用的方式是

577
00:49:16,880 --> 00:49:22,800
通过游戏对象的深度来管理。在Phaser编辑器中，更简便的方法是我们可以

578
00:49:22,800 --> 00:49:27,599
使用“层”是什么？层是一种让我们将游戏对象分组的方式，然后我们可以

579
00:49:27,599 --> 00:49:31,839
调整图层顺序，使其按照游戏中我们希望的显示顺序排列。

580
00:49:32,480 --> 00:49:37,039
因此，要在我们内置的“组”模块下查看一个示例，让我们拖入“图层”。

581
00:49:37,599 --> 00:49:42,880
这将为我们的场景添加一个新图层，我们暂且称这个图层为UI。

582
00:49:44,000 --> 00:49:51,839
现在，我们需要将游戏对象实际添加到这个图层中。默认情况下，

583
00:49:51,839 --> 00:49:57,039
我们所有的游戏对象都将有一个共同的父级，而这个父级就是我们的场景。

584
00:49:57,039 --> 00:50:03,599
默认情况下。我们可以在对象上进行操作，比如我选中这里的欢迎文字，右键点击时，

585
00:50:03,599 --> 00:50:08,960
我可以转到父级，现在我可以移动到父级。这将允许我们选择一个不同的父级。

586
00:50:08,960 --> 00:50:13,759
我们为游戏对象选择并在此处选定UI层。这将作用于我们的

587
00:50:13,759 --> 00:50:19,599
层级结构会将那个游戏对象嵌套在我们的层级之下，这样就能显示出那个

588
00:50:19,599 --> 00:50:25,119
这个游戏对象的父级是这里的这个游戏对象。然后我们可以对分数文本进行同样的操作。

589
00:50:25,119 --> 00:50:31,599
因此，如果我们执行父级操作，我们会移动到父级，现在让我们来处理UI层，由于当前层的原因

590
00:50:31,599 --> 00:50:37,119
层级结构位于我们其他游戏对象之上，这两个游戏对象将始终显示在最前面。

591
00:50:37,119 --> 00:50:42,000
我们的其他对象。因此，我们可以将游戏中的其余对象添加到另一个图层中，然后那

592
00:50:42,000 --> 00:50:47,599
这样整理和移动它们就非常容易。但如果我们回到浏览器，现在如果移动的话，

593
00:50:47,599 --> 00:50:52,880
将游戏对象置于文本上方时，我们会看到它出现在文本后方，而对于我们的UI界面，游戏对象则

594
00:50:52,880 --> 00:51:00,000
出现在它后面。因此，在我们的游戏中，饥饿的小恐龙除了……之外还想吃点东西。

595
00:51:00,000 --> 00:51:05,519
水果。为此，我们需要向项目中添加新的资源。接下来，我们将向Phaser编辑器添加资源。

596
00:51:05,519 --> 00:51:10,640
项目，其中一种方法是点击这里的资产文件夹，我们会在检查器中看到

597
00:51:10,640 --> 00:51:15,519
面板现在为我们打开了一个添加文件的位置。因此，我们可以将文件拖放到此处。

598
00:51:15,519 --> 00:51:19,920
它会将这些文件复制到我们的项目中。我们可以通过点击按钮来浏览文件。

599
00:51:20,400 --> 00:51:25,360
在这里。最后，如果您右键点击我们的文件夹，我们还可以添加文件，这同样会打开一个

600
00:51:25,360 --> 00:51:30,400
模态框，我们可以在项目中添加文件。所以对于我的Dyno，我决定给他们一个汉堡。

601
00:51:30,400 --> 00:51:34,559
所以我这里有一个汉堡素材，它是一个精灵图。所以在视频的描述中，

602
00:51:34,559 --> 00:51:38,159
这里会有一个链接，你可以下载这个汉堡的精灵图集，同时也要大声感谢一下

603
00:51:38,159 --> 00:51:42,559
Cascades Games。这位创作者制作了这份精美的精灵图集，并免费发布于

604
00:51:42,559 --> 00:51:47,280
HIO。因此，在下面的描述中会有一个链接指向那里。好了，接下来我们只需要...

605
00:51:47,360 --> 00:51:53,040
目前为止，我们只是往项目文件夹里添加了一个新资源，以便实际使用该资源。

606
00:51:53,040 --> 00:51:57,760
为了将Phaser编辑器集成到我们的Phaser游戏中并使其可用，我们需要告诉Phaser编辑器该做什么。

607
00:51:57,760 --> 00:52:02,640
使用此资源。因此，在我们加载新的精灵表后，会发现出现了一个新的选项在

608
00:52:02,640 --> 00:52:08,960
我们的检查员用于我们的文件。因此，如果我们选择我们的文件，这里就会出现这个资产打包条目。这就是如何

609
00:52:08,960 --> 00:52:14,080
相位器编辑器将知道解析我们刚刚添加的文件。既然我们正在导入一个精灵

610
00:52:14,159 --> 00:52:18,799
在这里，我们想选择这个选项。如果我们只需要原始图像，可以选择导入图像。

611
00:52:18,799 --> 00:52:23,440
因此，如果我们确实导入了精灵表，我们不会选择要使用哪个资源包的选项

612
00:52:23,440 --> 00:52:29,119
暂时先添加这些，我们只需选择asset packed.json文件即可。一旦完成，

613
00:52:29,119 --> 00:52:33,519
我们的编辑器现已更新，因为我们已在资源包中存储了对此的引用。因此现在如果我们

614
00:52:33,519 --> 00:52:39,199
在资源包中选择打开汉堡精灵表，它不会打开我们的资源包JSON文件。因此，在

615
00:52:39,279 --> 00:52:46,159
这里的视图是我们的资源包编辑器。在此编辑器中，我们可以查看所有属于我们的文件。

616
00:52:46,159 --> 00:52:52,079
项目已经意识到并正在尝试加载的内容。目前它正在加载我们的dyno镜像。

617
00:52:52,079 --> 00:52:57,039
以及我们刚添加的新汉堡精灵表。因此我们的资源包JSON，这只是一个JSON

618
00:52:57,039 --> 00:53:03,839
编辑器和Phaser将使用的配置文件，用于指导如何加载我们的资源。

619
00:53:03,839 --> 00:53:08,879
举个例子，如果我右键点击我们的资源包JSON文件，选择“打开方式”，然后使用文本编辑器打开，

620
00:53:08,880 --> 00:53:13,360
我们将看到正在使用的原始JSON数据。可以看到有两个文件。我们有我们的

621
00:53:13,360 --> 00:53:18,000
汉堡精灵表附带一些解析该精灵表的相关信息。接着我们有

622
00:53:18,000 --> 00:53:23,119
我们的测功机PNG，由于它只是一张图片，所以只会加载该图片。然后你可以

623
00:53:23,119 --> 00:53:27,440
现在你可以选择你的一个资产，然后在检查器中查看与之关联的元数据。

624
00:53:27,440 --> 00:53:31,840
资产。因此，这包括诸如该资产存储位置的缓存键等信息，

625
00:53:31,840 --> 00:53:36,640
该资产需要从中检索数据的URL，以及一些额外的

626
00:53:37,519 --> 00:53:41,679
根据资产类型而定，比如对于图片，我们有内置高度或精灵表，我们还有

627
00:53:41,679 --> 00:53:47,119
精灵配置用于定义我们精灵的实际大小。目前，我们可以暂时保留

628
00:53:47,119 --> 00:53:51,440
仅靠配置本身。如果我们确实有一个不同大小的精灵，就需要更新我们的帧。

629
00:53:51,440 --> 00:53:55,759
在此高度范围内。那么让我们关闭资产包JSON文件，然后回到我们的关卡。

630
00:53:55,759 --> 00:54:00,799
场景。既然我们已经将资源更新至资源包中，现在便有了这个精灵表选项。

631
00:54:00,799 --> 00:54:05,679
在我们的区块中可用。如果我们扩展了精灵表，现在就可以选择其中一个

632
00:54:05,679 --> 00:54:10,960
我们想要添加到场景中的各个帧。举个例子，我可以选择

633
00:54:10,960 --> 00:54:16,239
此处框架用于在我们的场景中创建一个图像游戏对象。因此，要在游戏中使用我们的汉堡，

634
00:54:16,239 --> 00:54:20,960
让我们创建一个新的预制体实例。那么，从我们的源文件夹中，右键点击，选择新建，

635
00:54:20,960 --> 00:54:27,359
我们将为我们的预制件创建一个新的预制文件。我们将这个预制件命名为汉堡预制件。然后点击创建。

636
00:54:27,359 --> 00:54:31,599
这将打开我们的默认预制件视图。这样一来，

637
00:54:31,679 --> 00:54:36,079
这样就会设置好我们的预制体实例。但现在我们需要告诉Phaser编辑器我们想要

638
00:54:36,079 --> 00:54:41,279
以我们拖入预制场景中的方块为基础，用作我们的基础

639
00:54:41,279 --> 00:54:46,480
根。这就是我们的预制体将要扩展的内容。所以，如果我将我们的汉堡拖入汉堡预制体中

640
00:54:46,480 --> 00:54:51,679
场景中，我们现在可以看到变量是一个图像游戏对象。接着我们保存了汉堡包。

641
00:54:51,679 --> 00:54:57,119
prefab.js文件已创建。这将扩展该游戏对象类型。因此，我们的汉堡会稍微有所不同。

642
00:54:57,599 --> 00:55:01,839
小。那么我们就将其放大三倍。对于x轴，我们放大三倍；y轴也同样放大三倍。

643
00:55:01,839 --> 00:55:06,639
让我们把这个位置调回零点，这样它就在我们的左上角了。好的，最后一步。

644
00:55:06,639 --> 00:55:11,119
我们为汉堡要做的事情是添加碰撞器。因此，我们需要将其从

645
00:55:11,119 --> 00:55:18,079
一张图片。我们来做一张街机风格的图片。就选街机风格的。我们来替换掉当前类型。现在我们要

646
00:55:18,079 --> 00:55:23,519
拥有我们的身体。现在如果我们保存，让我们回到我们的层级场景。让我们拖入一个实例

647
00:55:23,679 --> 00:55:28,800
我们的汉堡。接下来我们要替换这里的食品项。先删掉这个。然后加入我们的汉堡。

648
00:55:28,800 --> 00:55:33,840
在这里。最后，我们只需要把这个加到我们的清单里。所以我们需要把这个添加到我们的食物中。

649
00:55:33,840 --> 00:55:37,840
物品列表。尽管这是一个不同的预设体，它仍然包含相位器游戏对象，因此我们的玩家

650
00:55:37,840 --> 00:55:42,079
应该能接收到。那么现在如果我们跳转到浏览器，让我们刷新一下场景。

651
00:55:42,079 --> 00:55:46,159
我们会看到新的汉堡现在出现在场景中，并且它具备了物理属性。当我们的玩家

652
00:55:46,159 --> 00:55:53,360
捡起它，我们的分数就会增加。因此，除了我们的汉堡资产，我们还会再添加一个资产到我们的

653
00:55:53,360 --> 00:55:57,360
游戏里我们要添加一些方块。这样一来，玩家就有东西可以互动了。

654
00:55:57,360 --> 00:56:01,519
与障碍物发生碰撞。因此，在此之前，让我们先稍微清理一下场景。我们

655
00:56:01,519 --> 00:56:06,079
我们将移除第二个dyno。然后，我们在这里删除第二条文本并保存。

656
00:56:06,079 --> 00:56:11,440
让我们跳到level.js文件。由于我们删除了一些游戏对象，对于任何手动

657
00:56:11,440 --> 00:56:16,240
我们添加的引用这些游戏对象的代码需要清理掉。这样我们就可以清理这部分了。

658
00:56:16,240 --> 00:56:21,519
dyno one。既然我们不再更新这里的文本，这个也可以删除了。最后，

659
00:56:21,519 --> 00:56:26,880
既然我们已经移除了文本，那就把恐龙的点击区域也去掉吧。这样它就不再可点击了。

660
00:56:26,880 --> 00:56:30,880
那么如果我们保存了，就继续添加另一个资产吧。现在让我们进入资产文件夹。

661
00:56:30,880 --> 00:56:35,039
对于第二项资产，我们将采用像素平台中现成的一个区块。

662
00:56:35,039 --> 00:56:40,480
这些积木来自肯尼。我要把这个积木块拖到我们的文件里。现在我正在操作

663
00:56:40,480 --> 00:56:45,679
单个图像对应单个区块。因此，现在当我们的检查器更新时，希望将其导入为

664
00:56:45,679 --> 00:56:49,679
图片，因为只有一张图片。因此，对于这个素材，我们需要选择我们的素材包，

665
00:56:49,679 --> 00:56:54,799
JSON文件也是如此。现在如果我们查看我们的资源包JSON文件，应该能看到新增的图片。

666
00:56:54,799 --> 00:56:59,599
已加载至我们的图像部分。现在让我们跳转回场景层级，并将一个

667
00:56:59,599 --> 00:57:04,559
我们瓷砖的一个实例。现在我们已经将方块放入了场景中。它看起来非常小，因此我们需要调整其大小。

668
00:57:04,559 --> 00:57:09,679
把这个放大。所以我们要将x和y属性放大五倍。实际上我们想要让

669
00:57:09,679 --> 00:57:14,719
这具有物理实体。那么让我们进入类型设置。我们将选择图像类型。将其更改为街机模式。

670
00:57:14,719 --> 00:57:18,559
图片，我们将进行替换操作。我们需要创建多个块实例。让我们

671
00:57:18,559 --> 00:57:23,920
创建一个预制件。如果我们右键点击，选择“预制件”。我们将用这个对象创建预制件。

672
00:57:23,920 --> 00:57:29,199
在我们的源文件夹下。我们将制作方块预制体。既然现在已经有了这些方块，接下来让我们创建一个

673
00:57:29,199 --> 00:57:34,480
这里有几个例子。我只需在场景中拖动一个，复制粘贴并制作

674
00:57:34,480 --> 00:57:40,880
再来一个。我们在这里也放一个。然后把我们的玩家移到这里。接着，我们让——

675
00:57:40,880 --> 00:57:46,480
再一个区块。好了。现在我们有四个区块了，需要让它们能够

676
00:57:46,559 --> 00:57:51,679
与我们玩家的碰撞。就像我们处理食物那样，让我们创建一个新的列表来存放

677
00:57:51,679 --> 00:57:56,719
这些对象。因此，在我们的群组块下，让我们为名称添加另一个列表。我们只需

678
00:57:56,719 --> 00:58:01,519
将此称为墙壁。然后，对于我们每个方块，我们希望将其添加到新列表中。那么让我们选择

679
00:58:01,519 --> 00:58:05,920
我们列表中的所有四个区块。我们将把它们添加到我们的墙列表中。所以现在我们需要添加

680
00:58:05,920 --> 00:58:10,159
我们的碰撞。所以如果我们扩展街机，就转到碰撞器，我们要把这个从

681
00:58:10,159 --> 00:58:16,319
降至一个。我们要将其更改为新的墙列表对象。所以我们进行选择。让我们保存吧。

682
00:58:16,559 --> 00:58:22,159
我们来到场景前。刷新浏览器后，试着移动一下，就能看到我们的物体正在

683
00:58:22,159 --> 00:58:28,239
可移动的。那么让我们来解决这个问题。在我们的源文件夹下，打开我们的方块预制体场景文件。

684
00:58:28,239 --> 00:58:33,679
如果我们点击游戏对象，向下找到它的主体部分，将其设为不可移动。然后我们

685
00:58:33,679 --> 00:58:38,879
还禁用了可推送功能，因为我们只想让它保持静态。所以如果我们保存，让我们回到我们的

686
00:58:38,880 --> 00:58:44,240
场景为我们刷新。现在恐龙试图穿过我们的障碍。我们实际上无法做到。

687
00:58:44,240 --> 00:58:47,920
所以我们的玩家角色有点大。让我们把恐龙调小一点吧。那么，

688
00:58:47,920 --> 00:58:55,440
点击我们的动力测试机，我们将把他缩小到0.75倍。好了，如果我们保存并刷新，就会看到

689
00:58:55,440 --> 00:58:58,960
我们找到了我们的恐龙。它只是稍微小了一点，但一切功能都运转正常。

690
00:59:01,840 --> 00:59:05,599
目前我们的游戏看起来有点静态，这些游戏对象

691
00:59:05,599 --> 00:59:11,199
可以四处移动，但没有动画效果。当我们加入汉堡资源时，因为它是一个

692
00:59:11,199 --> 00:59:16,239
在精灵图中，我们有一系列帧用于制作汉堡闲置状态的动画。

693
00:59:16,239 --> 00:59:22,079
四处移动。为了给我们的游戏增添一些趣味性，我们将加入汉堡动画。所以添加

694
00:59:22,079 --> 00:59:27,199
在Phaser编辑器中创建动画需要生成一个所谓的动画文件。这个动画文件

695
00:59:27,199 --> 00:59:32,319
这将使我们能够使用动画编辑器为游戏添加动画，以加入此文件。我们将前往

696
00:59:32,400 --> 00:59:38,000
在资源文件夹上右键点击，选择新建，然后创建一个名为“animations”的文件。

697
00:59:38,000 --> 00:59:41,360
我就先这样放着，动画效果会自动生成。好的。这样就会打开我们的

698
00:59:41,360 --> 00:59:45,840
动画JSON文件，这将作为我们的动画编辑器。因此我们可以确认这就是动画部分。

699
00:59:45,840 --> 00:59:51,680
编辑，因为在我们用户界面的顶部需要添加动画按钮。因此，要添加动画，我们需要

700
00:59:51,680 --> 00:59:56,000
选择我们喜欢使用的框架。所以我们打算一路选用我们的汉堡框架六号。

701
00:59:56,000 --> 01:00:00,320
到11。如果我们按住Shift键，可以选择多个帧。现在我们将调出检查器面板。

702
01:00:00,320 --> 01:00:04,240
我们获得了创建动画的选项。接下来，我们将点击“创建一个动画”。

703
01:00:05,039 --> 01:00:10,240
现在进行这一步时，我们需要为新动画的名称输入一个前缀。我们正在

704
01:00:10,240 --> 01:00:15,280
我打算把这个称为闲置状态。所以一旦我们完成这一步，就会添加一个全新的动画。然后它就会

705
01:00:15,280 --> 01:00:20,160
在我们的轮廓中将会在这里展示。它会显示出哪些帧组成了我们的动画。而且我们

706
01:00:20,160 --> 01:00:25,360
可以在编辑器中预览动画。这样在我们获得动画后，当我们选择

707
01:00:25,599 --> 01:00:30,640
在我们的检查器中处理它后，我们将能够修改该特定配置的设置。

708
01:00:30,640 --> 01:00:35,920
动画。我们可以做一些事情，比如调整帧率。如果动画移动得太快，我们可以减慢它

709
01:00:35,920 --> 01:00:41,200
下调。我们还可以更新重复次数等操作。这样一来，就能控制动画的播放次数。

710
01:00:41,200 --> 01:00:46,960
将会播放。因此，我们可以执行一次，当我们指示代码播放时，我们的动画将播放一次。

711
01:00:46,960 --> 01:00:51,120
我们的空闲动画。我们想要加速负一。这样它就会循环播放。

712
01:00:51,199 --> 01:00:55,759
无限期地。而且你可以用你的配置做其他事情。一旦我们创建了动画，

713
01:00:55,759 --> 01:01:00,559
JSON文件，现在我们需要告诉Phaser编辑器我们需要将其作为游戏的一部分加载。因此，如果我们

714
01:01:00,559 --> 01:01:05,679
点击我们的动画，JSON文件，我们想要以动画形式导入，并希望将其添加到我们的

715
01:01:05,679 --> 01:01:10,880
资产包。如果我们回到我们的关卡场景以便实际播放我们的动画，我们将需要

716
01:01:10,880 --> 01:01:17,199
需要更新我们的游戏对象类型，以便能够实际播放动画。在Phaser中，

717
01:01:17,279 --> 01:01:21,919
缩小你的图像游戏对象实际上只用于显示一张图片或一种纹理。然后你

718
01:01:21,919 --> 01:01:27,119
让你的精灵游戏对象能够播放动画。到目前为止，我们的汉堡还需要

719
01:01:27,119 --> 01:01:32,399
更新我们的游戏对象类型。那么，如果我们进入汉堡预制体，让我们在这里选择汉堡类型。

720
01:01:32,399 --> 01:01:38,799
我们要将变量类型从街机图像更改为街机精灵。一旦完成这一操作，

721
01:01:38,799 --> 01:01:44,960
我们现在需要告诉 Phaser 我们希望为精灵播放一个动画。因此，如果我们打开我们的

722
01:01:44,960 --> 01:01:50,000
在我们的构造函数中的burger
prefab.js文件里，可以在此处添加额外代码。我们需要告知phaser

723
01:01:50,000 --> 01:01:55,280
我们希望默认播放动画，为此需要引用此部分以引用我们的

724
01:01:55,280 --> 01:02:00,559
精灵游戏对象。接着我们将使用play方法。好的，那么对于我们的play方法，现在我们

725
01:02:00,559 --> 01:02:06,880
需要提供一个动画配置，或者我们想要播放的动画的名称或键值。

726
01:02:06,880 --> 01:02:12,079
现在来看我们的动画键，如果我们打开动画的JSON文件，这里正好有个闲置的汉堡动画，

727
01:02:12,159 --> 01:02:17,279
这个键，就是我们想要使用的值。那么让我们复制空闲状态的汉堡精灵表。我们来到

728
01:02:17,279 --> 01:02:22,559
回到我们预制件内部的play方法。让我们添加这个并保存。现在如果我们回到

729
01:02:22,559 --> 01:02:28,319
我们的水平场景，现在切换到浏览器。如果保存并刷新浏览器，现在我们将看到

730
01:02:28,319 --> 01:02:35,840
我们的汉堡是动态的，我们仍然可以拾取游戏对象。好的，那么在我们工作的同时

731
01:02:35,840 --> 01:02:40,960
在导入我们的资源时，我们不得不反复选择的一个文件就是资源包的JSON文件。

732
01:02:41,039 --> 01:02:46,000
现在我们的模板中有两个资源包文件。一个是这个资源包JSON文件，另一个是

733
01:02:46,000 --> 01:02:50,960
其他的是预加载资源包JSON。因此，在你的Phaser编辑器和Phaser项目中，你可以拥有

734
01:02:50,960 --> 01:02:55,920
你可以随意添加任意数量的资产包JSON文件，它让你能够打包不同的资产。

735
01:02:55,920 --> 01:03:00,480
然后通过这些不同的配置加载它们。在我们现有的模板中，有两个

736
01:03:00,480 --> 01:03:05,440
由于我们项目当前的设置方式，因此在偶尔测试游戏时，

737
01:03:05,519 --> 01:03:10,720
在我们加载资源时，你可能会看到这个预加载场景弹出。因此在Phaser中，

738
01:03:10,720 --> 01:03:15,679
你可以拥有一个或多个场景，我们也可以在Phaser编辑器中实现这一点。

739
01:03:15,679 --> 01:03:21,200
因此，我们的项目设置是预加载场景就像我们的加载屏幕，在这里我们将展示

740
01:03:21,200 --> 01:03:26,079
一个加载条，用于加载我们将用于游戏的所有资源，完成后即可

741
01:03:26,079 --> 01:03:31,760
所有这些资产，我们过渡到我们的关卡场景，现在玩家可以开始游戏了。

742
01:03:31,760 --> 01:03:36,800
对于你的预加载场景，如果有任何你想使用的资源，你需要制作

743
01:03:36,800 --> 01:03:42,080
确保在我们开始预加载场景之前这些都准备好了。否则，我们可能会遇到图片无法显示的问题。

744
01:03:42,080 --> 01:03:47,920
我们尝试引用它们。因此，我们的主摘要文件中包含了两套资源包。

745
01:03:47,920 --> 01:03:53,120
我们目前有这个启动场景。这是一个自定义的 Phaser 场景，被添加在这里，但尚未

746
01:03:53,120 --> 01:03:59,280
作为我们这里的场景之一。这个场景所做的只是引用了我们的预加载资源包。

747
01:03:59,360 --> 01:04:04,720
而在我们的预加载资源包中，仅包含预加载器中使用的那张图片。

748
01:04:05,440 --> 01:04:11,440
然后在预加载场景中，我们正在加载另一个资源包的JSON文件，这个文件包含了我们所有的

749
01:04:11,440 --> 01:04:16,400
其他资产。因此，关于Phaser需要注意的另一点是，一旦你加载了你的资产，

750
01:04:16,400 --> 01:04:20,560
它们被存入缓存，你可以在任何场景中使用它们。尽管在我们的预加载场景中，

751
01:04:20,560 --> 01:04:24,800
我们正在加载食物物品，由于Phaser引擎将其从中提取出来，我们可以在关卡场景中使用它。

752
01:04:24,880 --> 01:04:29,760
缓存，我们只需加载一次。因此，这是一个很好的模式，适用于当你处于

753
01:04:29,760 --> 01:04:33,760
拥有更大的游戏。这样一来，在您试图展示给玩家时，就能有一个漂亮的屏幕。

754
01:04:33,760 --> 01:04:37,200
一次性加载所有内容，而不是试图一次性加载所有东西，然后他们只会看到一片黑屏。

755
01:04:37,200 --> 01:04:44,560
屏幕因为玩家将看不到任何东西。所以我们最后要做的是

756
01:04:44,560 --> 01:04:48,560
一个饥饿的舞蹈游戏，我们将添加一个非常简单的标题界面。因此，在我们的标题

757
01:04:48,560 --> 01:04:51,920
屏幕上，我们将仅显示包含游戏名称的文本，一旦玩家

758
01:04:52,000 --> 01:04:57,360
点击它，我们将切换到我们的关卡场景。之前我们在这里创建了这个测试场景。

759
01:04:57,360 --> 01:05:00,559
那我们快速清理一下。我们右键点击它，然后选择删除。

760
01:05:01,280 --> 01:05:06,079
现在我们想创建一个全新的场景。所以我们要操作场景，选择新建，让我们来创建一个场景文件。

761
01:05:06,079 --> 01:05:11,119
我们将这个标题命名为。一旦创建了新场景，我们只需简单地添加一个

762
01:05:11,119 --> 01:05:17,039
文本游戏对象。如果我们来到字符串下方，让我们添加一个文本。对于我们的文本，我们只需

763
01:05:17,039 --> 01:05:22,800
要画一只饥饿的恐龙。至于前面的尺寸，我们把它放大一点。那就这样——

764
01:05:22,800 --> 01:05:28,880
64像素，我们想要继续并将中心屏幕的中心点居中。目前我们的原点

765
01:05:28,880 --> 01:05:33,279
游戏对象位于左手角落。我们希望它位于中心位置。因此，让我们更新我们的

766
01:05:33,279 --> 01:05:39,840
原点。我们将设为0.5，再设为0.5。现在对于标题文本，我们希望它居中显示。

767
01:05:39,840 --> 01:05:44,000
我们的屏幕。因此，我们可以采用的方法之一是利用一些布局工具。如果我们

768
01:05:44,000 --> 01:05:48,559
右键点击我们的游戏对象，然后进入布局选项，我们会看到多种不同的布局方案可供选择。

769
01:05:48,559 --> 01:05:53,199
可供我们使用的资源中，有一个很好的选择就是这条边界线。它的作用在于让我们能够

770
01:05:53,199 --> 01:05:58,079
我们可以让一个游戏对象与游戏中的某个边界完美对齐。这样，

771
01:05:58,079 --> 01:06:01,760
我们可以设置左右边框，然后就能看到游戏对象的末端与之对齐了。

772
01:06:01,760 --> 01:06:07,760
如果我们进行布局并选择居中，它将会把我们的游戏对象置于两者之间的中心位置。

773
01:06:07,760 --> 01:06:13,199
我们游戏的边界。同样，如果我们进行布局，我们会画一条线作为边界。如果我们处理上边框或

774
01:06:13,759 --> 01:06:18,960
底部边框，我们得到了同样的效果，游戏对象的顶部和底部都如此。而如果我们仅仅

775
01:06:18,960 --> 01:06:24,960
将边界中间对齐，现在我们的游戏对象正好位于画布元素的中心。这样一来，

776
01:06:24,960 --> 01:06:28,719
你的布局工具远不止于此，因此你还可以选择进行

777
01:06:28,719 --> 01:06:32,639
比如创建网格布局。如果你有一堆游戏对象需要排列

778
01:06:32,639 --> 01:06:38,079
在网格中，已经内置了工具来帮你完成这个操作。好了，如果我们保存并返回

779
01:06:38,079 --> 01:06:43,519
到我们的浏览器中，如果我们刷新页面，我们的关卡场景依然存在。因此，每当我们添加一个全新的

780
01:06:43,519 --> 01:06:48,799
要为我们的游戏添加场景，我们需要更新Phaser的配置，以便Phaser能够识别该场景。

781
01:06:48,799 --> 01:06:55,119
为此，我们需要打开我们的main.js文件。无论你在哪里定义你的Phaser游戏配置，

782
01:06:55,119 --> 01:06:59,840
这将是我们这里的main.js文件，我们会自动添加我们正在使用的场景。

783
01:06:59,840 --> 01:07:05,039
我们的游戏。因此，现在我们想为标题场景添加一个新的。那么，让我们复制这个逻辑。

784
01:07:05,199 --> 01:07:11,119
这里，放在这里。我们将键更新为标题，并将类更新为

785
01:07:11,119 --> 01:07:18,639
标题。因此，要使用我们的标题类，我们需要将其导入到代码中。让我们执行：从...导入标题

786
01:07:18,639 --> 01:07:26,000
场景标题.js。一旦完成这一步，这只是告诉Phaser游戏配置。我们还有另一个

787
01:07:26,000 --> 01:07:30,800
场景，它将创建我们场景的一个实例。但如果我们真正想要启动我们的场景，

788
01:07:30,800 --> 01:07:35,600
我们需要在代码中引用这一点。举个例子，我们的启动场景一旦出现

789
01:07:35,600 --> 01:07:41,600
一切准备就绪，我们调用scene.start，开始我们的预加载场景。所以如果我们继续

790
01:07:41,600 --> 01:07:47,280
将以下文本从英语翻译为中文（简体）。只需返回翻译内容，无需解释：
进入我们的预加载场景，让我们打开preload.js文件，我们应该在这里的底部看到，在我们

791
01:07:47,280 --> 01:07:52,560
完成所有步骤后，进入我们的创建方法并启动我们的关卡场景。现在，我们实际上想要

792
01:07:52,560 --> 01:07:58,560
开始我们的标题场景。这样一来，一旦预加载场景完成，我们就会进入标题界面。

793
01:07:58,559 --> 01:08:03,039
因此，如果我们在浏览器中刷新游戏，现在就能看到新添加的文字——“饥饿的恐龙”。

794
01:08:03,679 --> 01:08:08,079
现在我们只需要那个机制来启动我们的游戏场景。为此，我们只需要

795
01:08:08,079 --> 01:08:15,440
监听场景中的点击事件。让我们打开title.js文件。如果我们进入create函数中

796
01:08:15,440 --> 01:08:20,640
在我们的编辑创建方法之后，让我们为事件监听器添加逻辑。接下来我们要做的是

797
01:08:20,640 --> 01:08:26,559
这将引用我们的Phaser场景。我们将输入以引用我们的输入插件。然后我们会

798
01:08:26,560 --> 01:08:31,680
调用once方法。这个方法的作用是允许我们添加一个一次性的事件监听器。

799
01:08:31,680 --> 01:08:36,640
对于给定的事件。这将使我们能够一次性监听场景中的点击事件，然后执行

800
01:08:36,640 --> 01:08:43,360
与之相比，如果我们执行操作，每次该事件发生时我们都可以调用这个回调函数。

801
01:08:43,360 --> 01:08:47,760
因此，由于我们只需点击一次即可进入我们的关卡场景，我们将使用

802
01:08:47,760 --> 01:08:54,880
`once` 方法。现在我们需要定义事件。因此，如果我们处理
`phaser`，我们将处理输入事件。而现在我们

803
01:08:54,960 --> 01:09:00,400
想要执行指针按下操作。因此，当我们收到这个事件时，我们提供的回调函数将被调用。

804
01:09:00,400 --> 01:09:06,000
因此在我们的回调函数中，现在只需启动我们的关卡场景。之前我们在

805
01:09:06,000 --> 01:09:10,400
在启动场景之前，我们的预加载场景中，我们将在标题场景中执行相同的操作。

806
01:09:10,400 --> 01:09:15,600
现在我们想把这个改成水平。所以更改之后，如果我们回到浏览器，

807
01:09:15,600 --> 01:09:20,960
让我们刷新一下场景。如果我们点击场景，游戏关卡就会开始，而玩家仍然可以

808
01:09:21,039 --> 01:09:28,319
收集我们的物品。因此，我们要对游戏做的最后一个改动是更新

809
01:09:28,319 --> 01:09:33,840
我们游戏中显示在浏览器中的标题。目前显示的是“我的游戏”。对于我们的项目模板来说，

810
01:09:33,840 --> 01:09:39,199
如果我们进入我们的index.html文件，这里的title元素就是我们更新它的地方。所以我们

811
01:09:39,199 --> 01:09:46,399
改变了我的游戏，然后改成饥饿的恐龙。我们确实保存了。回到我们的浏览器。

812
01:09:46,479 --> 01:09:48,879
现在我们为游戏确定了新名称。

813
01:09:52,399 --> 01:09:57,119
好的，关于Phaser编辑器V4的速成课程就到这里。今天我们涵盖了所有内容。

814
01:09:57,119 --> 01:10:01,759
从创建你的第一个项目到添加物理效果、动画和物体互动。

815
01:10:01,759 --> 01:10:06,559
使用Phaser编辑器，您无需深入代码即可直观地快速创建和测试游戏。

816
01:10:06,559 --> 01:10:11,519
深入编程之中。所以这个选择一直为你保留。你可以跟着学。现在你已经

817
01:10:11,520 --> 01:10:16,400
为使用Phaser Editor V4构建自己的Phaser 3游戏打下坚实基础

818
01:10:16,400 --> 01:10:21,040
记住，游戏开发的核心在于不断实验与学习。所以，别害怕尝试新事物。

819
01:10:21,040 --> 01:10:25,280
功能并拓展我们今天所讨论的内容。一如既往，我期待听到你们的想法并看到

820
01:10:25,280 --> 01:10:30,080
你所创造的作品。把你的问题留在下面的评论区。如果你觉得这个教程有帮助的话，

821
01:10:30,080 --> 01:10:34,400
别忘了给这个视频点赞并订阅，获取更多游戏开发内容。还有，

822
01:10:34,400 --> 01:10:39,120
查看描述中提供的Phaser编辑器文档链接及其他有用资源。

823
01:10:39,119 --> 01:10:42,479
感谢观看，祝游戏制作愉快。我们下期教程再见。

