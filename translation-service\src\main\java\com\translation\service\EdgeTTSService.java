package com.translation.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class EdgeTTSService {

    private static final Logger logger = LoggerFactory.getLogger(EdgeTTSService.class);
    private static final String PYTHON_SCRIPT_PATH = "src/main/resources/scripts/edge_tts_client.py";

    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            // 检查Python是否可用
            ProcessBuilder pb = new ProcessBuilder("python", "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            return process.exitValue() == 0;
        } catch (Exception e) {
            logger.warn("Edge TTS服务不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 使用Edge TTS Python脚本合成语音
     *
     * @param text       要合成的文本
     * @param voice      语音（例如 "zh-CN-XiaoxiaoNeural"）
     * @param rate       语速（例如 "+0%"）
     * @param outputFile 输出MP3文件路径
     * @return 生成的音频文件
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    public File synthesizeText(String text, String voice, String rate, String outputFile) throws IOException, InterruptedException {
        File scriptFile = new File(PYTHON_SCRIPT_PATH);
        if (!scriptFile.exists()) {
            throw new IOException("Python脚本未找到: " + scriptFile.getAbsolutePath());
        }

        ProcessBuilder pb = new ProcessBuilder(
                "python",
                scriptFile.getAbsolutePath(),
                "--text", text,
                "--voice", voice,
                "--rate", rate,
                "--output", outputFile
        );
        pb.redirectErrorStream(true);

        logger.info("执行Edge TTS命令: {}", String.join(" ", pb.command()));

        Process process = pb.start();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                logger.info("[EdgeTTS脚本]: {}", line);
            }
        }

        boolean finished = process.waitFor(2, TimeUnit.MINUTES);
        if (!finished) {
            process.destroy();
            throw new InterruptedException("Edge TTS进程超时（2分钟）");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new IOException("Edge TTS脚本退出，错误代码: " + exitCode);
        }

        File resultFile = new File(outputFile);
        if (!resultFile.exists() || resultFile.length() == 0) {
            throw new IOException("生成的音频文件为空或不存在");
        }

        logger.info("成功合成音频到 {}", resultFile.getAbsolutePath());
        return resultFile;
    }

    /**
     * 从Edge TTS脚本获取可用的中文语音列表
     *
     * @return 语音列表，每个map代表一个语音
     * @throws IOException          如果发生I/O错误
     * @throws InterruptedException 如果进程被中断
     */
    public List<Map<String, String>> getAvailableVoices() throws IOException, InterruptedException {
        File scriptFile = new File(PYTHON_SCRIPT_PATH);
        if (!scriptFile.exists()) {
            // 尝试在绝对路径中查找脚本（如果从JAR运行）
            File absoluteScriptFile = new File(new File(".").getAbsolutePath(), PYTHON_SCRIPT_PATH);
            if (!absoluteScriptFile.exists()) {
                throw new IOException("Python脚本未找到: " + absoluteScriptFile.getAbsolutePath());
            }
            scriptFile = absoluteScriptFile;
        }

        ProcessBuilder pb = new ProcessBuilder(
                "python",
                scriptFile.getAbsolutePath(),
                "--list-voices"
        );
        pb.redirectErrorStream(true);

        logger.info("获取可用的Edge TTS语音...");

        Process process = pb.start();

        StringBuilder jsonOutput = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                jsonOutput.append(line);
            }
        }

        boolean finished = process.waitFor(30, TimeUnit.SECONDS);
        if (!finished) {
            process.destroy();
            throw new InterruptedException("获取语音进程超时");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new IOException("Edge TTS脚本在列出语音时退出，错误代码: " + exitCode);
        }

        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(jsonOutput.toString(), new TypeReference<List<Map<String, String>>>() {});
    }
    
    /**
     * 获取推荐的语音设置
     */
    public Map<String, String> getRecommendedVoice(String languageCode) {
        Map<String, String> voice = new java.util.HashMap<>();
        
        switch (languageCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
                voice.put("voice", "zh-CN-XiaoxiaoNeural");
                voice.put("name", "晓晓（女声）");
                voice.put("rate", "+0%");
                break;
            case "en":
            case "en-us":
                voice.put("voice", "en-US-JennyNeural");
                voice.put("name", "Jenny（女声）");
                voice.put("rate", "+0%");
                break;
            case "ja":
            case "jp":
                voice.put("voice", "ja-JP-NanamiNeural");
                voice.put("name", "Nanami（女声）");
                voice.put("rate", "+0%");
                break;
            default:
                voice.put("voice", "zh-CN-XiaoxiaoNeural");
                voice.put("name", "晓晓（女声）");
                voice.put("rate", "+0%");
                break;
        }
        
        return voice;
    }
}