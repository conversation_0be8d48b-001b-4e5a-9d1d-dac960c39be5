"""
Video processing service for video translation AI service
"""

import os
import subprocess
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import moviepy.editor as mp
from .base_service import BaseService
from app.models.schemas import VideoCompositionRequest


class VideoService(BaseService):
    """Video processing service"""
    
    def __init__(self):
        super().__init__()
        self.output_dir = Path(self.settings.outputs_path)
        self.temp_dir = Path(self.settings.temp_path)
        
        # Create directories if they don't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    async def compose_video_async(self, video_path: str, audio_path: str, 
                                subtitle_path: Optional[str] = None, output_format: str = "mp4") -> str:
        """Create async task for video composition"""
        task_id = await self.create_task("video_composition", {
            "video_path": video_path,
            "audio_path": audio_path,
            "subtitle_path": subtitle_path,
            "output_format": output_format
        })
        return task_id
    
    async def process_video_composition(self, task_id: str, request: VideoCompositionRequest):
        """Process video composition task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Compose video
            result = await self.compose_video(
                request.video_file_path,
                request.audio_file_path,
                request.subtitle_file_path,
                request.output_format
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "video_file_path": result["video_file_path"],
                "duration": result["duration"],
                "file_size": result["file_size"]
            })
            
        except Exception as e:
            self.logger.error(f"Video composition failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def compose_video_to_path(self, video_path: str, audio_path: str, output_path: str,
                                   subtitle_path: Optional[str] = None, output_format: str = "mp4",
                                   burn_subtitles: bool = False, subtitle_style: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Compose video with new audio and save to specified path"""
        try:
            self.logger.info(f"Composing video: {video_path} + {audio_path} -> {output_path}")
            self.logger.info(f"Burn subtitles: {burn_subtitles}, Subtitle path: {subtitle_path}")

            # Ensure output directory exists
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            if burn_subtitles and subtitle_path:
                # Use enhanced compose_video_with_subtitles method
                result = await self.compose_video_with_subtitles(
                    video_path, audio_path, subtitle_path, output_path, subtitle_style
                )
            else:
                # Use existing compose_video method
                result = await self.compose_video(video_path, audio_path, subtitle_path, output_format)

                # Move the result to the specified path
                import shutil
                shutil.move(result["video_file_path"], output_path)

                # Update result with new path
                result["video_file_path"] = output_path

            return result

        except Exception as e:
            self.logger.error(f"Video composition to path failed: {e}")
            raise

    async def compose_video(self, video_path: str, audio_path: str,
                          subtitle_path: Optional[str] = None, output_format: str = "mp4") -> Dict[str, Any]:
        """Compose video with new audio and optional subtitles"""
        import sys

        # 在函数开始就定义这些变量，避免作用域问题
        is_tts_fallback = False
        audio_file_exists = False
        video = None
        final_video = None

        try:
            # Validate input files
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")
            if subtitle_path and not os.path.exists(subtitle_path):
                raise FileNotFoundError(f"Subtitle file not found: {subtitle_path}")

            # Check if audio_path is actually a text file (TTS fallback)
            is_tts_fallback = audio_path.endswith('.txt')

            # 检查音频文件是否真的存在且有效
            audio_file_exists = os.path.exists(audio_path) and not is_tts_fallback
            if audio_file_exists:
                try:
                    # 快速检查音频文件是否有效
                    import wave
                    with wave.open(audio_path, 'rb') as wav_file:
                        frames = wav_file.getnframes()
                        if frames == 0:
                            audio_file_exists = False
                            self.logger.warning(f"音频文件为空: {audio_path}")
                except Exception as e:
                    audio_file_exists = False
                    self.logger.warning(f"音频文件无效: {audio_path}, 错误: {e}")
            
            # 转换为绝对路径，确保所有组件都能正确访问文件
            abs_video_path = os.path.abspath(video_path)
            abs_audio_path = os.path.abspath(audio_path)

            # Generate output filename
            video_name = Path(abs_video_path).stem
            output_filename = f"{video_name}_translated.{output_format}"
            output_path = self.output_dir / output_filename
            abs_output_path = os.path.abspath(str(output_path))

            self.logger.info(f"Composing video: {abs_video_path} + {abs_audio_path}")
            self.logger.info(f"输出路径: {abs_output_path}")

            # Load video using absolute path
            video = mp.VideoFileClip(abs_video_path)
            
            if is_tts_fallback:
                # Handle TTS fallback case - create video without audio (silent video)
                self.logger.warning(f"TTS fallback detected, creating silent video (removing original audio)")

                # Read the fallback text for subtitle
                with open(abs_audio_path, 'r', encoding='utf-8') as f:
                    fallback_content = f.read()

                # Remove audio from video to create silent video
                # This is better than keeping the original English audio
                try:
                    final_video = video.without_audio()
                    self.logger.info("Created silent video (original audio removed)")
                except Exception as e:
                    self.logger.warning(f"Failed to remove audio, keeping original video: {e}")
                    final_video = video

                # Log the fallback content for reference
                self.logger.info(f"TTS fallback content: {fallback_content[:100]}...")
                self.logger.info(f"Full fallback text available in: {abs_audio_path}")

            elif audio_file_exists:
                # Normal case - replace audio
                new_audio = None
                try:
                    new_audio = mp.AudioFileClip(abs_audio_path)

                    # Check if audio clip is valid
                    if new_audio is None or new_audio.duration is None:
                        raise ValueError(f"Invalid audio file: {abs_audio_path}")

                    # Trim audio to match video duration if necessary
                    if new_audio.duration > video.duration:
                        new_audio = new_audio.subclip(0, video.duration)

                    # Create new video with replaced audio
                    final_video = video.set_audio(new_audio)

                    # Validate the result
                    if final_video is None:
                        raise ValueError("video.set_audio() returned None")

                    self.logger.info("Audio replacement successful")

                except Exception as e:
                    self.logger.warning(f"Failed to replace audio: {e}")
                    self.logger.info("Using original audio instead")
                    final_video = video
                finally:
                    # Clean up audio clip safely
                    if new_audio is not None:
                        try:
                            new_audio.close()
                        except Exception as e:
                            self.logger.warning(f"Failed to close audio clip: {e}")

            else:
                # 音频文件不存在或无效，保持原始音频
                self.logger.warning(f"音频文件不存在或无效: {audio_path}，保持原始音频")
                final_video = video
            
            # Skip subtitle overlay to avoid ImageMagick dependency
            if subtitle_path:
                self.logger.info(f"Subtitle file generated: {subtitle_path}")
                self.logger.info(f"Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.")
            
            # Write final video - 完全使用FFmpeg，避免MoviePy的get_frame问题
            try:
                self.logger.info(f"开始写入视频文件: {abs_output_path}")

                # MoviePy在某些情况下会出现get_frame错误，特别是在Windows服务环境中
                # 为了避免这个问题，我们直接使用FFmpeg进行视频合成
                self.logger.info("使用FFmpeg直接合成视频，避免MoviePy兼容性问题")

                # 检查音频文件状态
                audio_file_valid = audio_file_exists and not is_tts_fallback

                if audio_file_valid:
                    # 验证音频文件
                    try:
                        probe_cmd = ['ffprobe', '-v', 'quiet', '-show_streams', '-select_streams', 'a', abs_audio_path]
                        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
                        audio_file_valid = probe_result.returncode == 0 and 'codec_type=audio' in probe_result.stdout
                        self.logger.info(f"音频文件验证结果: {audio_file_valid}")
                    except Exception as probe_error:
                        self.logger.warning(f"音频文件验证失败: {probe_error}")
                        audio_file_valid = False

                # 直接使用FFmpeg，完全避开MoviePy的get_frame问题
                if audio_file_valid:
                    # 有有效音频文件，替换音频
                    ffmpeg_cmd = [
                        'ffmpeg', '-y',  # 覆盖输出文件
                        '-i', abs_video_path,  # 输入视频（绝对路径）
                        '-i', abs_audio_path,  # 输入音频（绝对路径）
                        '-c:v', 'copy',        # 复制视频流（避免重新编码）
                        '-c:a', 'aac',         # 音频编码为AAC
                        '-map', '0:v:0',       # 映射视频流
                        '-map', '1:a:0',       # 映射音频流
                        '-shortest',           # 以最短流为准
                        abs_output_path        # 输出文件（绝对路径）
                    ]
                    self.logger.info(f"使用音频替换模式")
                else:
                    # 没有有效音频文件，只复制视频
                    ffmpeg_cmd = [
                        'ffmpeg', '-y',
                        '-i', abs_video_path,  # 输入视频（绝对路径）
                        '-c', 'copy',          # 复制所有流
                        abs_output_path        # 输出文件（绝对路径）
                    ]
                    self.logger.info(f"使用视频复制模式")

                self.logger.info(f"执行FFmpeg命令: {' '.join(ffmpeg_cmd)}")
                self.logger.info(f"输入视频: {abs_video_path}")
                if audio_file_valid:
                    self.logger.info(f"输入音频: {abs_audio_path}")
                self.logger.info(f"输出文件: {abs_output_path}")

                # 执行FFmpeg命令（在项目根目录中执行）
                result = subprocess.run(
                    ffmpeg_cmd,
                    capture_output=True,
                    text=True,
                    timeout=300,  # 5分钟超时
                    cwd=os.getcwd()  # 在当前工作目录中执行
                )

                if result.returncode == 0:
                    self.logger.info(f"FFmpeg命令执行成功: {abs_output_path}")
                else:
                    self.logger.error(f"FFmpeg命令失败，返回码: {result.returncode}")
                    self.logger.error(f"FFmpeg错误输出: {result.stderr}")
                    raise subprocess.CalledProcessError(result.returncode, ffmpeg_cmd, result.stderr)

            except Exception as composition_error:
                self.logger.error(f"视频合成失败: {composition_error}")

                # 最后的备选方案：直接复制原视频
                try:
                    shutil.copy2(abs_video_path, abs_output_path)
                    self.logger.warning(f"使用原始视频作为后备: {abs_output_path}")
                except Exception as copy_error:
                    self.logger.error(f"所有视频合成方法均失败: {copy_error}")
                    raise composition_error
            # 视频合成完成，继续处理结果

            # Get file info using absolute path
            file_size = os.path.getsize(abs_output_path)
            # 安全获取duration，避免None错误
            try:
                duration = final_video.duration if final_video else video.duration
            except Exception as e:
                self.logger.warning(f"无法获取视频时长: {e}")
                duration = 0

            result = {
                "video_file_path": abs_output_path,  # 返回绝对路径
                "duration": duration,
                "file_size": file_size
            }

            # Add fallback information if applicable
            if is_tts_fallback:
                result["tts_fallback"] = True
                result["message"] = "Video created with original audio due to TTS service unavailability"

    async def compose_video_with_subtitles(self, video_path: str, audio_path: str,
                                          subtitle_path: str, output_path: str,
                                          subtitle_style: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Compose video with new audio and burn-in subtitles using FFmpeg

        Args:
            video_path: Input video file path
            audio_path: Input audio file path
            subtitle_path: SRT subtitle file path
            output_path: Output video file path
            subtitle_style: Subtitle styling options

        Returns:
            Dict with result information
        """
        try:
            self.logger.info(f"Composing video with subtitles: {video_path} + {audio_path} + {subtitle_path} -> {output_path}")

            # Convert to absolute paths
            abs_video_path = os.path.abspath(video_path)
            abs_audio_path = os.path.abspath(audio_path)
            abs_subtitle_path = os.path.abspath(subtitle_path)
            abs_output_path = os.path.abspath(output_path)

            # Validate input files
            if not os.path.exists(abs_video_path):
                raise FileNotFoundError(f"Video file not found: {abs_video_path}")
            if not os.path.exists(abs_audio_path):
                raise FileNotFoundError(f"Audio file not found: {abs_audio_path}")
            if not os.path.exists(abs_subtitle_path):
                raise FileNotFoundError(f"Subtitle file not found: {abs_subtitle_path}")

            # Build subtitle filter
            subtitle_filter = self._build_subtitle_filter(abs_subtitle_path, subtitle_style)

            # Build FFmpeg command with audio replacement and subtitle burn-in
            ffmpeg_cmd = [
                'ffmpeg', '-y',  # Overwrite output file
                '-i', abs_video_path,  # Input video
                '-i', abs_audio_path,  # Input audio
                '-vf', subtitle_filter,  # Video filter with subtitles
                '-c:a', 'aac',  # Audio codec
                '-c:v', 'libx264',  # Video codec (required for subtitle burn-in)
                '-preset', 'medium',  # Encoding preset
                '-crf', '23',  # Quality setting
                '-map', '0:v:0',  # Map video from first input
                '-map', '1:a:0',  # Map audio from second input
                '-shortest',  # Use shortest stream duration
                abs_output_path  # Output file
            ]

            self.logger.info(f"Executing FFmpeg command: {' '.join(ffmpeg_cmd)}")

            # Execute FFmpeg command
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                timeout=1800,  # 30 minutes timeout
                cwd=os.getcwd()
            )

            if result.returncode == 0:
                self.logger.info(f"Video composition with subtitles successful: {abs_output_path}")

                # Get output file info
                file_size = os.path.getsize(abs_output_path)

                # Get video duration using FFprobe
                duration = self._get_video_duration(abs_output_path)

                return {
                    "video_file_path": abs_output_path,
                    "duration": duration,
                    "file_size": file_size,
                    "subtitles_burned": True,
                    "message": "Video composed with burned-in subtitles successfully"
                }
            else:
                self.logger.error(f"FFmpeg failed with return code: {result.returncode}")
                self.logger.error(f"FFmpeg stderr: {result.stderr}")
                raise subprocess.CalledProcessError(result.returncode, ffmpeg_cmd, result.stderr)

        except Exception as e:
            self.logger.error(f"Video composition with subtitles failed: {e}")
            raise

    def _build_subtitle_filter(self, subtitle_path: str, style: Optional[Dict[str, Any]] = None) -> str:
        """
        Build FFmpeg subtitle filter with styling options

        Args:
            subtitle_path: Path to subtitle file
            style: Styling options

        Returns:
            FFmpeg subtitle filter string
        """
        # Default subtitle style (Netflix-like)
        default_style = {
            'font_name': 'Arial',
            'font_size': 24,
            'primary_color': '&Hffffff',  # White text
            'outline_color': '&H000000',  # Black outline
            'back_color': '&*********',   # Semi-transparent background
            'outline': 2,                 # Outline width
            'shadow': 1,                  # Shadow
            'alignment': 2,               # Bottom center
            'margin_v': 30                # Vertical margin from bottom
        }

        # Merge with user-provided style
        if style:
            default_style.update(style)

        # Escape the subtitle path for FFmpeg (handle spaces and special characters)
        # Convert Windows paths to forward slashes and escape colons
        escaped_path = subtitle_path.replace('\\', '/').replace(':', '\\:')

        # Build subtitle filter
        # Using subtitles filter which supports SRT files
        subtitle_filter = f"subtitles='{escaped_path}'"

        # Add style parameters using force_style
        style_params = []

        if 'font_name' in default_style:
            style_params.append(f"FontName={default_style['font_name']}")
        if 'font_size' in default_style:
            style_params.append(f"FontSize={default_style['font_size']}")
        if 'primary_color' in default_style:
            style_params.append(f"PrimaryColour={default_style['primary_color']}")
        if 'outline_color' in default_style:
            style_params.append(f"OutlineColour={default_style['outline_color']}")
        if 'back_color' in default_style:
            style_params.append(f"BackColour={default_style['back_color']}")
        if 'outline' in default_style:
            style_params.append(f"Outline={default_style['outline']}")
        if 'shadow' in default_style:
            style_params.append(f"Shadow={default_style['shadow']}")
        if 'alignment' in default_style:
            style_params.append(f"Alignment={default_style['alignment']}")
        if 'margin_v' in default_style:
            style_params.append(f"MarginV={default_style['margin_v']}")

        if style_params:
            force_style = ','.join(style_params)
            subtitle_filter += f":force_style='{force_style}'"

        return subtitle_filter

    def _get_video_duration(self, video_path: str) -> float:
        """
        Get video duration using FFprobe

        Args:
            video_path: Path to video file

        Returns:
            Duration in seconds
        """
        try:
            cmd = [
                'ffprobe', '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                duration = float(result.stdout.strip())
                return duration
            else:
                self.logger.warning(f"Failed to get video duration: {result.stderr}")
                return 0.0

        except Exception as e:
            self.logger.warning(f"Error getting video duration: {e}")
            return 0.0
            
            # Clean up video clips - 只关闭一次
            try:
                if final_video and final_video != video:
                    final_video.close()
                if video:
                    video.close()
            except Exception as e:
                self.logger.warning(f"清理视频对象时出现警告: {e}")
            
            self.logger.info(f"Video composition completed: {abs_output_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"Video composition failed: {e}")
            # Clean up resources in case of error
            try:
                if final_video is not None:
                    final_video.close()
                if video is not None:
                    video.close()
            except Exception as cleanup_error:
                self.logger.warning(f"清理资源时出现警告: {cleanup_error}")
            raise
    
    def _parse_srt_file(self, srt_file_path: str) -> list:
        """Parse SRT subtitle file and return list of subtitle dictionaries"""
        try:
            subtitles = []
            with open(srt_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # Split by double newlines to get subtitle blocks
            blocks = content.split('\n\n')
            
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    try:
                        # Parse timing (format: 00:00:01,000 --> 00:00:04,000)
                        timing_line = lines[1]
                        start_time, end_time = timing_line.split(' --> ')
                        
                        start_seconds = self._time_to_seconds(start_time)
                        end_seconds = self._time_to_seconds(end_time)
                        
                        # Get subtitle text (could be multiple lines)
                        text = ' '.join(lines[2:])
                        
                        subtitles.append({
                            'start': start_seconds,
                            'end': end_seconds,
                            'text': text
                        })
                    except Exception as e:
                        self.logger.warning(f"Failed to parse subtitle block: {e}")
                        continue
            
            return subtitles
            
        except Exception as e:
            self.logger.error(f"Failed to parse SRT file: {e}")
            return []
    
    def _time_to_seconds(self, time_str: str) -> float:
        """Convert SRT time format to seconds"""
        try:
            # Format: 00:00:01,000
            time_part, ms_part = time_str.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
            
        except Exception as e:
            self.logger.error(f"Failed to convert time: {e}")
            return 0.0
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process video task"""
        pass
    
    def get_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """Get video file information"""
        try:
            video = mp.VideoFileClip(video_path)
            
            info = {
                "path": video_path,
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "width": video.w,
                "height": video.h,
                "has_audio": video.audio is not None
            }
            
            video.close()
            return info
            
        except Exception as e:
            self.logger.error(f"Failed to get video info: {e}")
            return None
    
    def validate_video_file(self, video_path: str) -> bool:
        """Validate video file"""
        try:
            if not os.path.exists(video_path):
                return False
            
            # Try to load the video file
            video = mp.VideoFileClip(video_path)
            duration = video.duration
            video.close()
            
            return duration > 0
            
        except Exception:
            return False 