"""
Video processing service for video translation AI service
"""

import os
import subprocess
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import moviepy.editor as mp
from .base_service import BaseService
from app.models.schemas import VideoCompositionRequest


class VideoService(BaseService):
    """Video processing service"""
    
    def __init__(self):
        super().__init__()
        self.output_dir = Path(self.settings.outputs_path)
        self.temp_dir = Path(self.settings.temp_path)
        
        # Create directories if they don't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    async def compose_video_async(self, video_path: str, audio_path: str, 
                                subtitle_path: Optional[str] = None, output_format: str = "mp4") -> str:
        """Create async task for video composition"""
        task_id = await self.create_task("video_composition", {
            "video_path": video_path,
            "audio_path": audio_path,
            "subtitle_path": subtitle_path,
            "output_format": output_format
        })
        return task_id
    
    async def process_video_composition(self, task_id: str, request: VideoCompositionRequest):
        """Process video composition task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Compose video
            result = await self.compose_video(
                request.video_file_path,
                request.audio_file_path,
                request.subtitle_file_path,
                request.output_format
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "video_file_path": result["video_file_path"],
                "duration": result["duration"],
                "file_size": result["file_size"]
            })
            
        except Exception as e:
            self.logger.error(f"Video composition failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def compose_video(self, video_path: str, audio_path: str,
                          subtitle_path: Optional[str] = None, output_format: str = "mp4") -> Dict[str, Any]:
        """Compose video with new audio and optional subtitles"""
        import sys

        # 在函数开始就定义这些变量，避免作用域问题
        is_tts_fallback = False
        audio_file_exists = False
        video = None
        final_video = None

        try:
            # Validate input files
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")
            if subtitle_path and not os.path.exists(subtitle_path):
                raise FileNotFoundError(f"Subtitle file not found: {subtitle_path}")

            # Check if audio_path is actually a text file (TTS fallback)
            is_tts_fallback = audio_path.endswith('.txt')

            # 检查音频文件是否真的存在且有效
            audio_file_exists = os.path.exists(audio_path) and not is_tts_fallback
            if audio_file_exists:
                try:
                    # 快速检查音频文件是否有效
                    import wave
                    with wave.open(audio_path, 'rb') as wav_file:
                        frames = wav_file.getnframes()
                        if frames == 0:
                            audio_file_exists = False
                            self.logger.warning(f"音频文件为空: {audio_path}")
                except Exception as e:
                    audio_file_exists = False
                    self.logger.warning(f"音频文件无效: {audio_path}, 错误: {e}")
            
            # 转换为绝对路径，确保所有组件都能正确访问文件
            abs_video_path = os.path.abspath(video_path)
            abs_audio_path = os.path.abspath(audio_path)

            # Generate output filename
            video_name = Path(abs_video_path).stem
            output_filename = f"{video_name}_translated.{output_format}"
            output_path = self.output_dir / output_filename
            abs_output_path = os.path.abspath(str(output_path))

            self.logger.info(f"Composing video: {abs_video_path} + {abs_audio_path}")
            self.logger.info(f"输出路径: {abs_output_path}")

            # Load video using absolute path
            video = mp.VideoFileClip(abs_video_path)
            
            if is_tts_fallback:
                # Handle TTS fallback case - create video without audio (silent video)
                self.logger.warning(f"TTS fallback detected, creating silent video (removing original audio)")

                # Read the fallback text for subtitle
                with open(abs_audio_path, 'r', encoding='utf-8') as f:
                    fallback_content = f.read()

                # Remove audio from video to create silent video
                # This is better than keeping the original English audio
                try:
                    final_video = video.without_audio()
                    self.logger.info("Created silent video (original audio removed)")
                except Exception as e:
                    self.logger.warning(f"Failed to remove audio, keeping original video: {e}")
                    final_video = video

                # Log the fallback content for reference
                self.logger.info(f"TTS fallback content: {fallback_content[:100]}...")
                self.logger.info(f"Full fallback text available in: {abs_audio_path}")

            elif audio_file_exists:
                # Normal case - replace audio
                new_audio = None
                try:
                    new_audio = mp.AudioFileClip(abs_audio_path)

                    # Check if audio clip is valid
                    if new_audio is None or new_audio.duration is None:
                        raise ValueError(f"Invalid audio file: {abs_audio_path}")

                    # Trim audio to match video duration if necessary
                    if new_audio.duration > video.duration:
                        new_audio = new_audio.subclip(0, video.duration)

                    # Create new video with replaced audio
                    final_video = video.set_audio(new_audio)

                    # Validate the result
                    if final_video is None:
                        raise ValueError("video.set_audio() returned None")

                    self.logger.info("Audio replacement successful")

                except Exception as e:
                    self.logger.warning(f"Failed to replace audio: {e}")
                    self.logger.info("Using original audio instead")
                    final_video = video
                finally:
                    # Clean up audio clip safely
                    if new_audio is not None:
                        try:
                            new_audio.close()
                        except Exception as e:
                            self.logger.warning(f"Failed to close audio clip: {e}")

            else:
                # 音频文件不存在或无效，保持原始音频
                self.logger.warning(f"音频文件不存在或无效: {audio_path}，保持原始音频")
                final_video = video
            
            # Skip subtitle overlay to avoid ImageMagick dependency
            if subtitle_path:
                self.logger.info(f"Subtitle file generated: {subtitle_path}")
                self.logger.info(f"Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.")
            
            # Write final video
            try:
                # 确保final_video不为None
                if final_video is None:
                    self.logger.error("final_video is None, using original video as fallback")
                    final_video = video

                # 再次验证final_video对象
                if final_video is None:
                    raise ValueError("Both final_video and video are None")

                self.logger.info(f"开始写入视频文件: {output_path}")

                # 使用更安全的方式处理Windows服务环境

                # 设置FFmpeg环境变量，避免stdout问题
                env = os.environ.copy()
                env['IMAGEIO_FFMPEG_EXE'] = 'ffmpeg'

                # 创建临时音频文件路径
                temp_audio_path = str(self.temp_dir / f'temp_audio_{os.getpid()}.m4a')

                try:
                    # 在Windows服务环境中安全处理stdout/stderr
                    import sys
                    from contextlib import redirect_stdout, redirect_stderr
                    from io import StringIO

                    # 创建安全的输出流
                    safe_stdout = StringIO()
                    safe_stderr = StringIO()

                    # 保存原始流
                    original_stdout = sys.stdout
                    original_stderr = sys.stderr

                    try:
                        # 重定向输出流到安全的StringIO对象
                        with redirect_stdout(safe_stdout), redirect_stderr(safe_stderr):
                            final_video.write_videofile(
                                abs_output_path,  # 使用绝对路径
                                codec='libx264',
                                audio_codec='aac',
                                temp_audiofile=temp_audio_path,
                                remove_temp=True,
                                verbose=False,
                                logger=None,
                                ffmpeg_params=['-hide_banner', '-loglevel', 'error']
                            )
                    finally:
                        # 确保恢复原始流
                        sys.stdout = original_stdout
                        sys.stderr = original_stderr

                    self.logger.info(f"视频文件写入成功: {output_path}")

                except Exception as write_error:
                    self.logger.error(f"write_videofile failed: {write_error}")

                    # 检查是否是stdout相关的错误
                    if "'NoneType' object has no attribute 'stdout'" in str(write_error):
                        self.logger.warning("检测到Windows服务环境stdout问题，直接使用FFmpeg命令")

                    # 立即使用FFmpeg直接命令作为备选方案
                    self.logger.info("MoviePy失败，立即使用FFmpeg直接命令")

                    try:
                        # 转换为绝对路径，确保FFmpeg能找到文件
                        abs_video_path = os.path.abspath(video_path)
                        abs_audio_path = os.path.abspath(audio_path) if audio_file_exists and not is_tts_fallback else None
                        abs_output_path = os.path.abspath(str(output_path))

                        # 验证输入文件存在
                        if not os.path.exists(abs_video_path):
                            raise FileNotFoundError(f"视频文件不存在: {abs_video_path}")

                        if abs_audio_path and not os.path.exists(abs_audio_path):
                            self.logger.warning(f"音频文件不存在，将只复制视频: {abs_audio_path}")
                            abs_audio_path = None

                        # 构建FFmpeg命令
                        if abs_audio_path:
                            # 有有效音频文件，替换音频
                            ffmpeg_cmd = [
                                'ffmpeg', '-y',  # 覆盖输出文件
                                '-i', abs_video_path,  # 输入视频（绝对路径）
                                '-i', abs_audio_path,  # 输入音频（绝对路径）
                                '-c:v', 'copy',        # 复制视频流（避免重新编码）
                                '-c:a', 'aac',         # 音频编码为AAC
                                '-map', '0:v:0',       # 映射视频流
                                '-map', '1:a:0',       # 映射音频流
                                '-shortest',           # 以最短流为准
                                abs_output_path        # 输出文件（绝对路径）
                            ]
                            self.logger.info(f"使用音频替换模式")
                        else:
                            # 没有有效音频文件，只复制视频
                            ffmpeg_cmd = [
                                'ffmpeg', '-y',
                                '-i', abs_video_path,  # 输入视频（绝对路径）
                                '-c', 'copy',          # 复制所有流
                                abs_output_path        # 输出文件（绝对路径）
                            ]
                            self.logger.info(f"使用视频复制模式")

                        self.logger.info(f"执行FFmpeg命令: {' '.join(ffmpeg_cmd)}")
                        self.logger.info(f"输入视频: {abs_video_path}")
                        if abs_audio_path:
                            self.logger.info(f"输入音频: {abs_audio_path}")
                        self.logger.info(f"输出文件: {abs_output_path}")

                        # 执行FFmpeg命令（在项目根目录中执行）
                        result = subprocess.run(
                            ffmpeg_cmd,
                            capture_output=True,
                            text=True,
                            timeout=300,  # 5分钟超时
                            cwd=os.getcwd()  # 在当前工作目录中执行
                        )

                        if result.returncode == 0:
                            self.logger.info(f"FFmpeg命令执行成功: {output_path}")
                        else:
                            self.logger.error(f"FFmpeg命令失败，返回码: {result.returncode}")
                            self.logger.error(f"FFmpeg错误输出: {result.stderr}")
                            raise subprocess.CalledProcessError(result.returncode, ffmpeg_cmd, result.stderr)

                    except Exception as ffmpeg_error:
                        self.logger.error(f"FFmpeg备选方案也失败: {ffmpeg_error}")
                        # 最后的备选方案：直接复制原视频
                        try:
                            shutil.copy2(video_path, output_path)
                            self.logger.warning(f"使用原始视频作为最终备选方案: {output_path}")
                        except Exception as copy_error:
                            self.logger.error(f"所有方案都失败: {copy_error}")
                            raise write_error  # 抛出原始的MoviePy错误

                # FFmpeg成功执行，跳过重复的处理
                self.logger.info("FFmpeg视频合成完成，跳过重复处理")

                # 注释掉重复的FFmpeg执行逻辑，因为第一次FFmpeg已经成功
                # 这个重复的执行会覆盖第一次的正确结果，导致音频丢失
                #
                # 原来的重复逻辑：
                # 1. 再次检查音频文件有效性
                # 2. 使用相对路径和额外参数重新执行FFmpeg
                # 3. 覆盖第一次的正确结果
                #
                # 修复：直接使用第一次FFmpeg的结果

            except Exception as e:
                self.logger.error(f"写入视频文件失败: {e}")
                # Fallback: 使用最简单的复制方法
                try:
                    shutil.copy2(abs_video_path, abs_output_path)
                    self.logger.warning(f"使用原始视频作为后备: {abs_output_path}")
                except Exception as e2:
                    self.logger.error(f"所有视频写入方法均失败: {e2}")
                    raise

            # Get file info using absolute path
            file_size = os.path.getsize(abs_output_path)
            # 安全获取duration，避免None错误
            try:
                duration = final_video.duration if final_video else video.duration
            except Exception as e:
                self.logger.warning(f"无法获取视频时长: {e}")
                duration = 0
            
            result = {
                "video_file_path": abs_output_path,  # 返回绝对路径
                "duration": duration,
                "file_size": file_size
            }
            
            # Add fallback information if applicable
            if is_tts_fallback:
                result["tts_fallback"] = True
                result["message"] = "Video created with original audio due to TTS service unavailability"
            
            # Clean up video clips - 只关闭一次
            try:
                if final_video and final_video != video:
                    final_video.close()
                if video:
                    video.close()
            except Exception as e:
                self.logger.warning(f"清理视频对象时出现警告: {e}")
            
            self.logger.info(f"Video composition completed: {abs_output_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"Video composition failed: {e}")
            # Clean up resources in case of error
            try:
                if final_video is not None:
                    final_video.close()
                if video is not None:
                    video.close()
            except Exception as cleanup_error:
                self.logger.warning(f"清理资源时出现警告: {cleanup_error}")
            raise
    
    def _parse_srt_file(self, srt_file_path: str) -> list:
        """Parse SRT subtitle file and return list of subtitle dictionaries"""
        try:
            subtitles = []
            with open(srt_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # Split by double newlines to get subtitle blocks
            blocks = content.split('\n\n')
            
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    try:
                        # Parse timing (format: 00:00:01,000 --> 00:00:04,000)
                        timing_line = lines[1]
                        start_time, end_time = timing_line.split(' --> ')
                        
                        start_seconds = self._time_to_seconds(start_time)
                        end_seconds = self._time_to_seconds(end_time)
                        
                        # Get subtitle text (could be multiple lines)
                        text = ' '.join(lines[2:])
                        
                        subtitles.append({
                            'start': start_seconds,
                            'end': end_seconds,
                            'text': text
                        })
                    except Exception as e:
                        self.logger.warning(f"Failed to parse subtitle block: {e}")
                        continue
            
            return subtitles
            
        except Exception as e:
            self.logger.error(f"Failed to parse SRT file: {e}")
            return []
    
    def _time_to_seconds(self, time_str: str) -> float:
        """Convert SRT time format to seconds"""
        try:
            # Format: 00:00:01,000
            time_part, ms_part = time_str.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
            
        except Exception as e:
            self.logger.error(f"Failed to convert time: {e}")
            return 0.0
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process video task"""
        pass
    
    def get_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """Get video file information"""
        try:
            video = mp.VideoFileClip(video_path)
            
            info = {
                "path": video_path,
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "width": video.w,
                "height": video.h,
                "has_audio": video.audio is not None
            }
            
            video.close()
            return info
            
        except Exception as e:
            self.logger.error(f"Failed to get video info: {e}")
            return None
    
    def validate_video_file(self, video_path: str) -> bool:
        """Validate video file"""
        try:
            if not os.path.exists(video_path):
                return False
            
            # Try to load the video file
            video = mp.VideoFileClip(video_path)
            duration = video.duration
            video.close()
            
            return duration > 0
            
        except Exception:
            return False 