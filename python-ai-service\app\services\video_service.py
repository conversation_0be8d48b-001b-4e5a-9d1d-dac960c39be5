"""
Video processing service for video translation AI service
"""

import os
import subprocess
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import moviepy.editor as mp
from .base_service import BaseService
from app.models.schemas import VideoCompositionRequest


class VideoService(BaseService):
    """Video processing service"""
    
    def __init__(self):
        super().__init__()
        self.output_dir = Path(self.settings.outputs_path)
        self.temp_dir = Path(self.settings.temp_path)
        
        # Create directories if they don't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    async def compose_video_async(self, video_path: str, audio_path: str, 
                                subtitle_path: Optional[str] = None, output_format: str = "mp4") -> str:
        """Create async task for video composition"""
        task_id = await self.create_task("video_composition", {
            "video_path": video_path,
            "audio_path": audio_path,
            "subtitle_path": subtitle_path,
            "output_format": output_format
        })
        return task_id
    
    async def process_video_composition(self, task_id: str, request: VideoCompositionRequest):
        """Process video composition task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Compose video
            result = await self.compose_video(
                request.video_file_path,
                request.audio_file_path,
                request.subtitle_file_path,
                request.output_format
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "video_file_path": result["video_file_path"],
                "duration": result["duration"],
                "file_size": result["file_size"]
            })
            
        except Exception as e:
            self.logger.error(f"Video composition failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def compose_video(self, video_path: str, audio_path: str, 
                          subtitle_path: Optional[str] = None, output_format: str = "mp4") -> Dict[str, Any]:
        """Compose video with new audio and optional subtitles"""
        import sys
        
        try:
            # Validate input files
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")
            if subtitle_path and not os.path.exists(subtitle_path):
                raise FileNotFoundError(f"Subtitle file not found: {subtitle_path}")
            
            # Check if audio_path is actually a text file (TTS fallback)
            is_tts_fallback = audio_path.endswith('.txt')

            # 检查音频文件是否真的存在且有效
            audio_file_exists = os.path.exists(audio_path) and not is_tts_fallback
            if audio_file_exists:
                try:
                    # 快速检查音频文件是否有效
                    import wave
                    with wave.open(audio_path, 'rb') as wav_file:
                        frames = wav_file.getnframes()
                        if frames == 0:
                            audio_file_exists = False
                            self.logger.warning(f"音频文件为空: {audio_path}")
                except Exception as e:
                    audio_file_exists = False
                    self.logger.warning(f"音频文件无效: {audio_path}, 错误: {e}")
            
            # Generate output filename
            video_name = Path(video_path).stem
            output_filename = f"{video_name}_translated.{output_format}"
            output_path = self.output_dir / output_filename
            
            self.logger.info(f"Composing video: {video_path} + {audio_path}")
            
            # Load video
            video = mp.VideoFileClip(video_path)
            
            if is_tts_fallback:
                # Handle TTS fallback case - create video without audio replacement
                self.logger.warning(f"TTS fallback detected, creating video without audio replacement")

                # Read the fallback text for subtitle
                with open(audio_path, 'r', encoding='utf-8') as f:
                    fallback_content = f.read()

                # Keep original audio
                final_video = video

                # Skip text overlay for TTS fallback (to avoid ImageMagick dependency)
                self.logger.info(f"TTS fallback detected - text content available in {audio_path}")
                final_video = video

            elif audio_file_exists:
                # Normal case - replace audio
                new_audio = None
                try:
                    new_audio = mp.AudioFileClip(audio_path)

                    # Check if audio clip is valid
                    if new_audio is None or new_audio.duration is None:
                        raise ValueError(f"Invalid audio file: {audio_path}")

                    # Trim audio to match video duration if necessary
                    if new_audio.duration > video.duration:
                        new_audio = new_audio.subclip(0, video.duration)

                    # Create new video with replaced audio
                    final_video = video.set_audio(new_audio)

                    # Validate the result
                    if final_video is None:
                        raise ValueError("video.set_audio() returned None")

                    self.logger.info("Audio replacement successful")

                except Exception as e:
                    self.logger.warning(f"Failed to replace audio: {e}")
                    self.logger.info("Using original audio instead")
                    final_video = video
                finally:
                    # Clean up audio clip safely
                    if new_audio is not None:
                        try:
                            new_audio.close()
                        except Exception as e:
                            self.logger.warning(f"Failed to close audio clip: {e}")

            else:
                # 音频文件不存在或无效，保持原始音频
                self.logger.warning(f"音频文件不存在或无效: {audio_path}，保持原始音频")
                final_video = video
            
            # Skip subtitle overlay to avoid ImageMagick dependency
            if subtitle_path:
                self.logger.info(f"Subtitle file generated: {subtitle_path}")
                self.logger.info(f"Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.")
            
            # Write final video
            try:
                # 确保final_video不为None
                if final_video is None:
                    self.logger.error("final_video is None, using original video as fallback")
                    final_video = video

                # 再次验证final_video对象
                if final_video is None:
                    raise ValueError("Both final_video and video are None")

                self.logger.info(f"开始写入视频文件: {output_path}")

                # 使用更安全的方式处理Windows服务环境

                # 设置FFmpeg环境变量，避免stdout问题
                env = os.environ.copy()
                env['IMAGEIO_FFMPEG_EXE'] = 'ffmpeg'

                # 创建临时音频文件路径
                temp_audio_path = str(self.temp_dir / f'temp_audio_{os.getpid()}.m4a')

                try:
                    # 使用更保守的参数设置
                    final_video.write_videofile(
                        str(output_path),
                        codec='libx264',
                        audio_codec='aac',
                        temp_audiofile=temp_audio_path,
                        remove_temp=True,
                        verbose=False,
                        logger=None,
                        ffmpeg_params=['-hide_banner', '-loglevel', 'error']
                    )
                    self.logger.info(f"视频文件写入成功: {output_path}")

                except Exception as write_error:
                    self.logger.error(f"write_videofile failed: {write_error}")

                    # 尝试使用FFmpeg直接命令
                    self.logger.info("尝试使用FFmpeg直接命令作为备选方案")
                    try:
                        # 使用之前已经检查过的音频文件状态
                        audio_file_valid = audio_file_exists and not is_tts_fallback

                        # 如果之前的检查不够详细，再用FFprobe检查一次
                        if audio_file_valid:
                            try:
                                # 使用FFprobe检查音频文件
                                probe_cmd = ['ffprobe', '-v', 'quiet', '-show_streams', '-select_streams', 'a', audio_path]
                                probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
                                audio_file_valid = probe_result.returncode == 0 and 'codec_type=audio' in probe_result.stdout
                                self.logger.info(f"FFprobe音频文件检查结果: {audio_file_valid}")
                            except Exception as probe_error:
                                self.logger.warning(f"FFprobe音频文件检查失败: {probe_error}")
                                audio_file_valid = False

                        if audio_file_valid and not is_tts_fallback:
                            # 有效音频文件 - 替换音频
                            cmd = [
                                'ffmpeg', '-y',
                                '-i', video_path,
                                '-i', audio_path,
                                '-c:v', 'copy',  # 复制视频流，避免重新编码
                                '-c:a', 'aac',
                                '-map', '0:v:0',  # 视频流来自第一个输入
                                '-map', '1:a:0',  # 音频流来自第二个输入
                                '-shortest',  # 以最短流为准
                                '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
                                str(output_path)
                            ]
                            self.logger.info(f"使用新音频替换: {audio_path}")
                        else:
                            # 无效音频文件或TTS回退 - 保持原音频
                            cmd = [
                                'ffmpeg', '-y',
                                '-i', video_path,
                                '-c:v', 'copy',  # 复制视频流
                                '-c:a', 'copy',  # 复制原音频流
                                str(output_path)
                            ]
                            self.logger.info("保持原始音频")

                        self.logger.info(f"执行FFmpeg命令: {' '.join(cmd)}")

                        # 在Windows服务环境中安全执行
                        result = subprocess.run(
                            cmd,
                            capture_output=True,
                            text=True,
                            timeout=300,  # 5分钟超时
                            env=env
                        )

                        if result.returncode == 0:
                            self.logger.info(f"FFmpeg直接命令执行成功: {output_path}")
                        else:
                            self.logger.error(f"FFmpeg命令失败，返回码: {result.returncode}")
                            self.logger.error(f"FFmpeg错误输出: {result.stderr}")
                            raise subprocess.CalledProcessError(result.returncode, cmd, result.stderr)

                    except Exception as ffmpeg_error:
                        self.logger.error(f"FFmpeg直接命令也失败: {ffmpeg_error}")
                        raise write_error  # 抛出原始错误

            except Exception as e:
                self.logger.error(f"写入视频文件失败: {e}")
                # Fallback: 使用最简单的复制方法
                try:
                    shutil.copy2(video_path, output_path)
                    self.logger.warning(f"使用原始视频作为后备: {output_path}")
                except Exception as e2:
                    self.logger.error(f"所有视频写入方法均失败: {e2}")
                    raise
            
            # Get file info
            file_size = os.path.getsize(output_path)
            # 安全获取duration，避免None错误
            try:
                duration = final_video.duration if final_video else video.duration
            except Exception as e:
                self.logger.warning(f"无法获取视频时长: {e}")
                duration = 0
            
            result = {
                "video_file_path": str(output_path),
                "duration": duration,
                "file_size": file_size
            }
            
            # Add fallback information if applicable
            if is_tts_fallback:
                result["tts_fallback"] = True
                result["message"] = "Video created with original audio due to TTS service unavailability"
            
            # Clean up video clips - 只关闭一次
            try:
                if final_video and final_video != video:
                    final_video.close()
                if video:
                    video.close()
            except Exception as e:
                self.logger.warning(f"清理视频对象时出现警告: {e}")
            
            self.logger.info(f"Video composition completed: {output_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"Video composition failed: {e}")
            # Clean up resources in case of error
            try:
                if 'final_video' in locals():
                    final_video.close()
                if 'video' in locals():
                    video.close()
            except:
                pass
            raise
    
    def _parse_srt_file(self, srt_file_path: str) -> list:
        """Parse SRT subtitle file and return list of subtitle dictionaries"""
        try:
            subtitles = []
            with open(srt_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # Split by double newlines to get subtitle blocks
            blocks = content.split('\n\n')
            
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    try:
                        # Parse timing (format: 00:00:01,000 --> 00:00:04,000)
                        timing_line = lines[1]
                        start_time, end_time = timing_line.split(' --> ')
                        
                        start_seconds = self._time_to_seconds(start_time)
                        end_seconds = self._time_to_seconds(end_time)
                        
                        # Get subtitle text (could be multiple lines)
                        text = ' '.join(lines[2:])
                        
                        subtitles.append({
                            'start': start_seconds,
                            'end': end_seconds,
                            'text': text
                        })
                    except Exception as e:
                        self.logger.warning(f"Failed to parse subtitle block: {e}")
                        continue
            
            return subtitles
            
        except Exception as e:
            self.logger.error(f"Failed to parse SRT file: {e}")
            return []
    
    def _time_to_seconds(self, time_str: str) -> float:
        """Convert SRT time format to seconds"""
        try:
            # Format: 00:00:01,000
            time_part, ms_part = time_str.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
            
        except Exception as e:
            self.logger.error(f"Failed to convert time: {e}")
            return 0.0
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process video task"""
        pass
    
    def get_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """Get video file information"""
        try:
            video = mp.VideoFileClip(video_path)
            
            info = {
                "path": video_path,
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "width": video.w,
                "height": video.h,
                "has_audio": video.audio is not None
            }
            
            video.close()
            return info
            
        except Exception as e:
            self.logger.error(f"Failed to get video info: {e}")
            return None
    
    def validate_video_file(self, video_path: str) -> bool:
        """Validate video file"""
        try:
            if not os.path.exists(video_path):
                return False
            
            # Try to load the video file
            video = mp.VideoFileClip(video_path)
            duration = video.duration
            video.close()
            
            return duration > 0
            
        except Exception:
            return False 