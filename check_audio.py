#!/usr/bin/env python3
"""
检查音频文件的工具
"""

import subprocess
import sys
import os

def check_audio_file(audio_path):
    """检查音频文件是否有效"""
    print(f"检查音频文件: {audio_path}")
    
    if not os.path.exists(audio_path):
        print(f"❌ 文件不存在: {audio_path}")
        return False
    
    print(f"✅ 文件存在，大小: {os.path.getsize(audio_path)} 字节")
    
    try:
        # 使用FFprobe检查音频文件
        cmd = ['ffprobe', '-v', 'quiet', '-show_streams', '-select_streams', 'a', audio_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            if 'codec_type=audio' in result.stdout:
                print("✅ 音频文件有效")
                print("音频流信息:")
                for line in result.stdout.split('\n'):
                    if line.strip() and ('codec' in line or 'duration' in line or 'sample_rate' in line):
                        print(f"  {line}")
                return True
            else:
                print("❌ 文件中没有音频流")
                return False
        else:
            print(f"❌ FFprobe检查失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        return False

def test_ffmpeg_command(video_path, audio_path, output_path):
    """测试FFmpeg命令"""
    print(f"\n测试FFmpeg命令:")
    print(f"视频: {video_path}")
    print(f"音频: {audio_path}")
    print(f"输出: {output_path}")
    
    # 检查音频文件
    audio_valid = check_audio_file(audio_path)
    
    if audio_valid:
        cmd = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-i', audio_path,
            '-c:v', 'copy',
            '-c:a', 'aac',
            '-map', '0:v:0',
            '-map', '1:a:0',
            '-shortest',
            '-avoid_negative_ts', 'make_zero',
            output_path
        ]
    else:
        cmd = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-c:v', 'copy',
            '-c:a', 'copy',
            output_path
        ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ FFmpeg命令执行成功")
            if os.path.exists(output_path):
                print(f"✅ 输出文件已生成: {output_path}")
                print(f"文件大小: {os.path.getsize(output_path)} 字节")
            else:
                print("❌ 输出文件未生成")
        else:
            print(f"❌ FFmpeg命令失败，返回码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行过程出错: {e}")

if __name__ == "__main__":
    # 使用您的文件路径进行测试
    video_path = "storage/uploads/a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4"
    audio_path = "storage/outputs/tts_b36f9c25.wav"
    output_path = "test_output.mp4"
    
    if len(sys.argv) > 1:
        audio_path = sys.argv[1]
    if len(sys.argv) > 2:
        video_path = sys.argv[2]
    if len(sys.argv) > 3:
        output_path = sys.argv[3]
    
    test_ffmpeg_command(video_path, audio_path, output_path)
