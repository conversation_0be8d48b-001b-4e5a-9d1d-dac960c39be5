{"name": "docs", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "next", "build": "next build", "start": "next start"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@next/third-parties": "^14.2.13", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.6.0", "lucide-react": "^0.446.0", "next": "^14.2.13", "nextra": "^2.13.4", "nextra-theme-docs": "^2.13.4", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "22.7.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "^5.6.2"}}