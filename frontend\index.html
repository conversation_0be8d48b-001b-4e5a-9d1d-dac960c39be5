<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频翻译AI服务 - 测试界面</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-video"></i> 视频翻译AI服务
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="#" id="healthCheck">
                        <i class="fas fa-heartbeat"></i> 健康检查
                    </a>
                    <a class="nav-link" href="http://localhost:8000/docs" target="_blank">
                        <i class="fas fa-book"></i> API文档
                    </a>
                </div>
            </div>
        </nav>

        <div class="container">
            <!-- 服务状态指示器 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-server"></i> 服务状态
                            </h5>
                            <div id="serviceStatus" class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm text-warning me-2" role="status">
                                    <span class="visually-hidden">检查中...</span>
                                </div>
                                <span class="text-warning">正在检查服务状态...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能测试区域 -->
            <div class="row">
                <!-- 完整翻译流程 -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-magic"></i> 完整翻译流程
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="fullPipelineForm">
                                <div class="mb-3">
                                    <label for="videoFile" class="form-label">选择视频文件</label>
                                    <input type="file" class="form-control" id="videoFile" accept="video/*" required>
                                    <div class="form-text">支持的格式: mp4, avi, mov, mkv, wmv</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="sourceLanguage" class="form-label">源语言</label>
                                        <select class="form-select" id="sourceLanguage" required>
                                            <option value="en">英语 (English)</option>
                                            <option value="zh-CN">中文 (Chinese)</option>
                                            <option value="ja">日语 (Japanese)</option>
                                            <option value="ko">韩语 (Korean)</option>
                                            <option value="fr">法语 (French)</option>
                                            <option value="de">德语 (German)</option>
                                            <option value="es">西班牙语 (Spanish)</option>
                                            <option value="auto">自动检测</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="targetLanguage" class="form-label">目标语言</label>
                                        <select class="form-select" id="targetLanguage" required>
                                            <option value="zh-CN">中文 (Chinese)</option>
                                            <option value="en">英语 (English)</option>
                                            <option value="ja">日语 (Japanese)</option>
                                            <option value="ko">韩语 (Korean)</option>
                                            <option value="fr">法语 (French)</option>
                                            <option value="de">德语 (German)</option>
                                            <option value="es">西班牙语 (Spanish)</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 语音选择已简化为默认本地TTS -->

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="includeSubtitles" checked>
                                    <label class="form-check-label" for="includeSubtitles">
                                        包含字幕
                                    </label>
                                </div>

                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-play"></i> 开始完整翻译
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 分步处理 -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-cogs"></i> 分步处理
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 音频提取 -->
                            <div class="mb-4">
                                <h6 class="text-info">
                                    <i class="fas fa-volume-up"></i> 1. 音频提取
                                </h6>
                                <form id="audioExtractionForm">
                                    <div class="mb-3">
                                        <input type="file" class="form-control form-control-sm" id="audioVideoFile" accept="video/*">
                                    </div>
                                    <button type="submit" class="btn btn-info btn-sm">
                                        <i class="fas fa-extract"></i> 提取音频
                                    </button>
                                </form>
                            </div>

                            <!-- 语音识别 -->
                            <div class="mb-4">
                                <h6 class="text-warning">
                                    <i class="fas fa-microphone"></i> 2. 语音识别
                                </h6>
                                <form id="speechRecognitionForm">
                                    <div class="mb-3">
                                        <input type="file" class="form-control form-control-sm" id="audioFile" accept="audio/*">
                                    </div>
                                    <button type="submit" class="btn btn-warning btn-sm">
                                        <i class="fas fa-comments"></i> 识别语音
                                    </button>
                                </form>
                            </div>

                            <!-- 文本翻译 -->
                            <div class="mb-4">
                                <h6 class="text-primary">
                                    <i class="fas fa-language"></i> 3. 文本翻译
                                </h6>
                                <form id="translationForm">
                                    <div class="mb-3">
                                        <textarea class="form-control form-control-sm" id="textToTranslate" rows="3" placeholder="输入要翻译的文本..."></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-globe"></i> 翻译文本
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务状态和结果显示区域 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-tasks"></i> 任务状态和结果
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 当前任务 -->
                            <div id="currentTask" class="mb-4" style="display: none;">
                                <h6 class="text-success">当前任务</h6>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span><strong>任务ID:</strong> <span id="currentTaskId">-</span></span>
                                            <span><strong>状态:</strong> <span id="currentTaskStatus" class="badge bg-secondary">-</span></span>
                                        </div>
                                        <div class="progress mb-2">
                                            <div id="currentTaskProgress" class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                                        </div>
                                        <div><strong>类型:</strong> <span id="currentTaskType">-</span></div>
                                        <div><strong>创建时间:</strong> <span id="currentTaskCreated">-</span></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 任务列表 -->
                            <div id="taskList">
                                <h6 class="text-info">任务历史</h6>
                                <div id="taskHistory" class="row">
                                    <div class="col-12 text-muted text-center">
                                        <i class="fas fa-info-circle"></i> 暂无任务记录
                                    </div>
                                </div>
                            </div>

                            <!-- 结果显示 -->
                            <div id="resultArea" class="mt-4" style="display: none;">
                                <h6 class="text-success">处理结果</h6>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <pre id="resultContent" class="mb-0"></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i id="toastIcon" class="fas fa-info-circle text-primary me-2"></i>
                <strong id="toastTitle" class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div id="toastBody" class="toast-body">
                消息内容
            </div>
        </div>
    </div>

    <!-- 加载 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/main.js"></script>
</body>
</html> 