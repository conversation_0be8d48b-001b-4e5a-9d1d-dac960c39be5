#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import torch
import whisperx
import librosa
import json
from tqdm import tqdm

def process_audio_in_chunks(audio_path, chunk_duration=300):  # 5分钟一块
    """分块处理大音频文件"""
    print(f"🎵 开始分块处理音频: {audio_path}")
    
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return None
    
    # 检查设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🚀 使用设备: {device}")
    
    try:
        # 加载完整音频获取总时长
        print("📊 分析音频文件...")
        audio_info = librosa.load(audio_path, sr=None, duration=1)  # 只加载1秒获取信息
        full_duration = librosa.get_duration(path=audio_path)
        print(f"⏱️ 音频总时长: {full_duration:.1f}秒 ({full_duration/60:.1f}分钟)")
        
        # 计算需要的块数
        num_chunks = int(full_duration / chunk_duration) + 1
        print(f"🧩 将分为 {num_chunks} 块处理 (每块{chunk_duration/60:.1f}分钟)")
        
        # 加载模型
        print("📥 正在加载WhisperX模型...")
        model = whisperx.load_model("large-v3", device, compute_type="float16" if device == "cuda" else "int8")
        print("✅ 模型加载成功")
        
        all_segments = []
        
        # 分块处理
        for i in range(num_chunks):
            start_time = i * chunk_duration
            end_time = min((i + 1) * chunk_duration, full_duration)
            duration = end_time - start_time
            
            if duration < 1:  # 跳过太短的块
                continue
                
            print(f"\n🔄 处理第 {i+1}/{num_chunks} 块: {start_time:.1f}s - {end_time:.1f}s ({duration:.1f}s)")
            
            try:
                # 加载音频块
                audio_chunk, sr = librosa.load(audio_path, sr=16000, offset=start_time, duration=duration)
                print(f"📁 音频块大小: {len(audio_chunk)/sr:.1f}秒")
                
                # 转录
                result = model.transcribe(audio_chunk)
                
                # 调整时间戳
                if result.get('segments'):
                    for segment in result['segments']:
                        segment['start'] += start_time
                        segment['end'] += start_time
                    
                    all_segments.extend(result['segments'])
                    print(f"✅ 块 {i+1} 转录完成，得到 {len(result['segments'])} 个片段")
                else:
                    print(f"⚠️ 块 {i+1} 没有转录内容")
                
                # 清理GPU内存
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"❌ 块 {i+1} 处理失败: {e}")
                continue
        
        # 清理模型
        del model
        torch.cuda.empty_cache()
        
        print(f"\n🎉 所有块处理完成！总共得到 {len(all_segments)} 个片段")
        
        # 构建结果
        result = {
            'segments': all_segments,
            'language': all_segments[0].get('language', 'en') if all_segments else 'en'
        }
        
        return result
        
    except Exception as e:
        print(f"❌ 分块处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_srt_from_segments(segments, output_file="out/output.srt"):
    """从片段生成SRT文件"""
    print(f"📄 生成SRT文件: {output_file}")
    
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, segment in enumerate(segments, 1):
            start_time = format_timestamp(segment['start'])
            end_time = format_timestamp(segment['end'])
            text = segment['text'].strip()
            
            if text:  # 只写入非空文本
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{text}\n\n")
    
    print(f"✅ SRT文件生成完成: {output_file}")
    return output_file

def format_timestamp(seconds):
    """格式化时间戳为SRT格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def main():
    print("🚀 开始分块音频处理...")
    
    audio_path = "out/temp_audio.wav"
    
    # 分块处理音频
    result = process_audio_in_chunks(audio_path, chunk_duration=300)  # 5分钟一块
    
    if not result or not result.get('segments'):
        print("❌ 转录失败")
        return
    
    # 生成SRT文件
    srt_file = generate_srt_from_segments(result['segments'])
    
    # 保存原始结果
    json_file = "out/transcription_result.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"🎉 处理完成！")
    print(f"📄 SRT文件: {srt_file}")
    print(f"📊 JSON文件: {json_file}")
    print(f"🔢 总片段数: {len(result['segments'])}")

if __name__ == "__main__":
    main() 