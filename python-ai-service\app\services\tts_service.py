"""
TTS (Text-to-Speech) service for video translation AI service
"""

import os
import asyncio
from pathlib import Path
from typing import Dict, Any
import edge_tts
from .base_service import BaseService
from app.models.schemas import TTSRequest

# 尝试导入本地TTS服务
try:
    from .local_tts_service import local_tts_service
    LOCAL_TTS_AVAILABLE = True
except ImportError:
    LOCAL_TTS_AVAILABLE = False
    local_tts_service = None


class TTSService(BaseService):
    """TTS service using Edge TTS"""
    
    def __init__(self):
        super().__init__()
        self.output_dir = Path(self.settings.outputs_path)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def synthesize_speech_to_path(self, text: str, voice: str, output_path: str,
                                       speed: float = 1.0, pitch: int = 0,
                                       service: str = "edge-tts") -> dict:
        """Synthesize speech and save to specified path"""
        try:
            self.logger.info(f"Synthesizing speech to {output_path}")

            # Ensure output directory exists
            from pathlib import Path
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Synthesize using existing method
            result = await self.synthesize_speech(text, voice, speed, pitch, service)

            # Move the result to the specified path
            if not result.get("fallback"):
                import shutil
                shutil.move(result["audio_file_path"], output_path)
                result["audio_file_path"] = output_path
            else:
                # For fallback, save text to the specified path
                fallback_path = output_path.replace('.wav', '_fallback.txt')
                with open(fallback_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                result["audio_file_path"] = fallback_path

            return result

        except Exception as e:
            self.logger.error(f"TTS synthesis to path failed: {e}")
            raise

    async def synthesize_speech_async(self, text: str, voice: str = "zh-CN-XiaoxiaoNeural",
                                    speed: float = 1.0, pitch: int = 0, service: str = "edge-tts") -> str:
        """Create async task for TTS synthesis"""
        task_id = await self.create_task("tts_generation", {
            "text": text,
            "voice": voice,
            "speed": speed,
            "pitch": pitch,
            "service": service
        })
        return task_id
    
    async def process_tts(self, task_id: str, request: TTSRequest):
        """Process TTS synthesis task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Synthesize speech
            result = await self.synthesize_speech(
                request.text,
                request.voice,
                request.speed,
                request.pitch,
                request.service
            )
            
            # Prepare completion data
            completion_data = {
                "audio_file_path": result["audio_file_path"],
                "duration": result["duration"],
                "text": result["text"]
            }
            
            # Add fallback information if applicable
            if result.get("fallback"):
                completion_data["fallback"] = True
                completion_data["message"] = result.get("message", "Service fallback was used")
                self.logger.warning(f"TTS task {task_id} completed with fallback: {result['message']}")
            
            await self.update_task_status(task_id, "completed", 100.0, completion_data)
            
        except Exception as e:
            self.logger.error(f"TTS synthesis failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def synthesize_speech(self, text: str, voice: str = "zh-CN-XiaoxiaoNeural",
                              speed: float = 1.0, pitch: int = 0, service: str = "edge-tts") -> Dict[str, Any]:
        """Synthesize speech from text using Edge TTS with fallback to local TTS"""
        try:
            self.logger.info(f"Starting TTS synthesis with service: {service}")

            # 清理文本
            clean_text = self._clean_text_for_tts(text)
            if not clean_text:
                raise ValueError("Text is empty after cleaning")

            # 优先使用Edge TTS
            if service == "edge-tts" or service == "auto":
                try:
                    self.logger.info("Attempting Edge TTS synthesis...")
                    return await self._synthesize_with_edge_tts(clean_text, voice, speed, pitch)
                except Exception as e:
                    self.logger.warning(f"Edge TTS failed: {e}")
                    if service == "edge-tts":
                        # 如果明确指定Edge TTS但失败，尝试本地TTS作为回退
                        self.logger.info("Falling back to local TTS...")

            # 尝试本地TTS
            if LOCAL_TTS_AVAILABLE and local_tts_service is not None:
                try:
                    self.logger.info("Attempting local TTS synthesis...")
                    return await self._synthesize_with_local_tts(clean_text, voice, speed, pitch)
                except Exception as e:
                    self.logger.warning(f"Local TTS failed: {e}")

            # 如果所有TTS都失败，创建文本回退
            self.logger.warning("All TTS services failed, creating text fallback")
            return await self._create_text_fallback(clean_text)

        except Exception as e:
            self.logger.error(f"TTS synthesis completely failed: {e}")
            # 创建文本fallback
            return await self._create_text_fallback(text)

    def _clean_text_for_tts(self, text: str) -> str:
        """清理文本，移除可能导致TTS问题的字符"""
        import re

        # 移除特殊字符
        chars_to_remove = ['&', '®', '™', '©', '\n', '\r']
        for char in chars_to_remove:
            text = text.replace(char, ' ')

        # 清理多余空格
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    async def _create_text_fallback(self, text: str) -> Dict[str, Any]:
        """创建文本回退文件"""
        output_filename = f"tts_text_{self.generate_task_id()[:8]}.txt"
        output_path = self.output_dir / output_filename

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"TTS Audio Content (Service Unavailable):\n\n{text}\n\n")
            f.write("Note: This is a text fallback due to TTS service unavailability.\n")

        return {
            "audio_file_path": str(output_path),
            "duration": len(text) * 0.1,
            "text": text,
            "fallback": True,
            "message": "TTS service was unavailable - text content saved instead of audio"
        }
    
    async def _synthesize_with_edge_tts(self, text: str, voice: str, speed: float, pitch: int) -> Dict[str, Any]:
        """Synthesize speech using Edge TTS (VideoLingo style - simple and reliable)"""
        import asyncio

        # Generate output filename - 使用WAV格式以确保兼容性
        output_filename = f"tts_{self.generate_task_id()[:8]}.wav"
        output_path = self.output_dir / output_filename

        self.logger.info(f"Synthesizing speech with Edge TTS, voice: {voice}")

        # 使用与VideoLingo完全相同的简单重试机制
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 创建Edge TTS通信对象（与VideoLingo一致）
                communicate = edge_tts.Communicate(text, voice)

                # 保存音频文件（与VideoLingo一致）
                await communicate.save(str(output_path))

                # 验证文件是否成功生成（与VideoLingo一致）
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    # 获取实际音频时长
                    duration = self._get_audio_duration(str(output_path))

                    self.logger.info(f"Edge TTS synthesis completed: {output_path} (duration: {duration:.2f}s)")

                    return {
                        "audio_file_path": str(output_path),
                        "duration": duration,
                        "text": text,
                        "service": "edge-tts"
                    }
                else:
                    self.logger.warning(f"Edge TTS attempt {attempt + 1}/{max_retries} failed: file not generated or empty")

            except Exception as e:
                self.logger.warning(f"Edge TTS attempt {attempt + 1}/{max_retries} failed: {str(e)}")

                # 如果是403错误，可能是网络或服务限制，等待更长时间
                if "403" in str(e) or "Invalid response status" in str(e):
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 5  # 递增等待时间：5s, 10s, 15s
                        self.logger.info(f"403错误，等待{wait_time}秒后重试...")
                        await asyncio.sleep(wait_time)
                    continue

                # 其他错误，等待2秒后重试
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue

        # 所有重试都失败了
        raise Exception(f"Edge TTS failed after {max_retries} attempts. This may be due to network issues or service limitations.")

    def _get_audio_duration(self, file_path: str) -> float:
        """获取音频文件时长"""
        try:
            # 首先尝试使用wave模块（对于WAV文件）
            import wave
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                if sample_rate > 0:
                    return frames / sample_rate
        except Exception:
            pass

        try:
            # 如果wave失败，尝试使用ffprobe
            import subprocess
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-show_entries',
                'format=duration', '-of', 'csv=p=0', file_path
            ], capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                return float(result.stdout.strip())
        except Exception:
            pass

        # 如果都失败，返回估算时长
        return len(self._clean_text_for_tts(file_path)) * 0.1
    
    async def _create_text_fallback(self, text: str, output_path) -> Dict[str, Any]:
        """Create a text file fallback when TTS is unavailable"""
        try:
            # Change extension to .txt for text fallback
            text_path = output_path.with_suffix('.txt')
            
            # Write text to file with proper encoding
            with open(text_path, 'w', encoding='utf-8', newline='\n') as f:
                f.write(f"TTS Audio Content (Edge TTS Service Unavailable):\n\n{text}\n\n")
                f.write("Note: This is a text fallback due to TTS service unavailability.\n")
                f.write("The original text would have been converted to speech audio.\n")
            
            self.logger.info(f"Created text fallback: {text_path}")
            
            return {
                "audio_file_path": str(text_path),
                "duration": len(text) * 0.1,
                "text": text,
                "fallback": True,
                "message": "Edge TTS service is currently unavailable. Text content saved instead."
            }
            
        except Exception as e:
            self.logger.error(f"Failed to create text fallback: {e}")
            raise
    
    async def _synthesize_with_gtts(self, text: str, voice: str, speed: float, pitch: int) -> Dict[str, Any]:
        """Synthesize speech using Google TTS (gTTS) as fallback"""
        try:
            from gtts import gTTS
            import asyncio
            
            # Generate output filename
            output_filename = f"tts_{self.generate_task_id()[:8]}.mp3"
            output_path = self.output_dir / output_filename
            
            self.logger.info(f"Synthesizing speech with gTTS")
            
            # Map voice to language for gTTS
            voice_to_lang_map = {
                # Chinese voices
                "zh-CN-XiaoxiaoNeural": "zh",
                "zh-CN-YunxiNeural": "zh", 
                "zh-CN-YunyangNeural": "zh",
                # English voices  
                "en-US-AriaNeural": "en",
                "en-US-DavisNeural": "en",
                "en-US-JennyNeural": "en",
                # Other languages
                "ja-JP": "ja",
                "ko-KR": "ko", 
                "fr-FR": "fr",
                "de-DE": "de",
                "es-ES": "es",
                "ru-RU": "ru"
            }
            
            # Extract language from voice name
            lang = voice_to_lang_map.get(voice, "zh")  # Default to Chinese
            
            # If no exact match, try partial matching
            if lang == "zh" and voice not in voice_to_lang_map:
                if "en-" in voice or "en_" in voice:
                    lang = "en"
                elif "ja-" in voice or "ja_" in voice:
                    lang = "ja"
                elif "ko-" in voice or "ko_" in voice:
                    lang = "ko"
                elif "fr-" in voice or "fr_" in voice:
                    lang = "fr"
                elif "de-" in voice or "de_" in voice:
                    lang = "de"
                elif "es-" in voice or "es_" in voice:
                    lang = "es"
                elif "ru-" in voice or "ru_" in voice:
                    lang = "ru"
            
            # Create gTTS object and save
            def _create_gtts():
                tts = gTTS(text=text, lang=lang, slow=False)
                tts.save(str(output_path))
            
            # Run in thread to avoid blocking
            await asyncio.get_event_loop().run_in_executor(None, _create_gtts)
            
            # Get audio duration (approximate)
            duration = len(text) * 0.1  # Rough estimation
            
            self.logger.info(f"gTTS synthesis completed: {output_path}")
            
            return {
                "audio_file_path": str(output_path),
                "duration": duration,
                "text": text,
                "fallback": True,
                "message": "Used Google TTS as fallback due to Edge TTS unavailability"
            }
            
        except Exception as e:
            self.logger.error(f"gTTS synthesis failed: {e}")
            # If both Edge TTS and gTTS fail, create text fallback
            return await self._create_text_fallback(text, self.output_dir / f"tts_{self.generate_task_id()[:8]}.txt")
    
    async def get_available_voices(self) -> Dict[str, list]:
        """Get available voices"""
        try:
            voices = await edge_tts.list_voices()
            
            # Group voices by language
            voices_by_lang = {}
            for voice in voices:
                lang = voice["Locale"]
                if lang not in voices_by_lang:
                    voices_by_lang[lang] = []
                voices_by_lang[lang].append({
                    "name": voice["Name"],
                    "short_name": voice["ShortName"],
                    "gender": voice["Gender"],
                    "locale": voice["Locale"]
                })
            
            return voices_by_lang
            
        except Exception as e:
            self.logger.error(f"Failed to get available voices: {e}")
            return {}
    
    async def _synthesize_with_local_tts(self, text: str, voice: str, speed: float, pitch: int) -> Dict[str, Any]:
        """Synthesize speech using local TTS (pyttsx3) as final fallback"""
        if not LOCAL_TTS_AVAILABLE:
            self.logger.error("Local TTS service not available")
            # Create text fallback as last resort
            return await self._create_text_fallback(text, self.output_dir / f"tts_{self.generate_task_id()[:8]}.txt")
        
        try:
            self.logger.info("Using local TTS service as fallback")
            
            # Generate output filename
            output_filename = f"tts_{self.generate_task_id()[:8]}.wav"
            output_path = self.output_dir / output_filename
            
            # Convert speed to local TTS rate (50-300 range)
            local_rate = int(150 * speed)  # Base rate 150, adjust with speed
            
            # Map voice to local TTS voice (simplified mapping)
            local_voice = None
            if "zh" in voice.lower() or "chinese" in voice.lower():
                # Try to use Chinese voice if available
                voices = local_tts_service.get_available_voices()
                for v in voices:
                    if v.get('is_chinese', False):
                        local_voice = v['id']
                        break
            
            # Run local TTS synthesis in executor to avoid blocking
            def _create_local_tts():
                return local_tts_service.generate_speech(
                    text=text,
                    output_file=str(output_path),
                    voice=local_voice,
                    rate=local_rate,
                    volume=1.0
                )
            
            # Execute in thread pool
            await asyncio.get_event_loop().run_in_executor(None, _create_local_tts)
            
            # Get audio duration (approximate)
            duration = len(text) * 0.15  # Local TTS is usually slightly slower
            
            self.logger.info(f"Local TTS synthesis completed: {output_path}")
            
            return {
                "audio_file_path": str(output_path),
                "duration": duration,
                "text": text,
                "fallback": True,
                "message": "Used local TTS as fallback due to online services unavailability"
            }
            
        except Exception as e:
            self.logger.error(f"Local TTS synthesis failed: {e}")
            # Create text fallback as absolute last resort
            return await self._create_text_fallback(text, self.output_dir / f"tts_{self.generate_task_id()[:8]}.txt")
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process TTS task"""
        pass 