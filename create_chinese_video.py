#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文配音视频合成器
将TTS生成的中文音频文件合成到原视频中
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Tuple
import argparse

from moviepy import VideoFileClip, AudioFileClip, CompositeAudioClip
from pydub import AudioSegment
import numpy as np

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str) -> List[Tuple[int, float, float, str]]:
    """读取SRT字幕文件
    
    Returns:
        List of (index, start_time, end_time, text) tuples
    """
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            time_line = lines[1]
            text = '\n'.join(lines[2:])
            
            # 解析时间
            start_time_str, end_time_str = time_line.split(' --> ')
            start_time = parse_srt_time(start_time_str)
            end_time = parse_srt_time(end_time_str)
            
            subtitles.append((index, start_time, end_time, text.strip()))
    
    return subtitles

def create_composite_audio(audio_dir: str, subtitles: List[Tuple[int, float, float, str]], 
                          original_duration: float) -> AudioSegment:
    """创建合成音频轨道"""
    
    # 创建静音背景音轨
    composite_audio = AudioSegment.silent(duration=int(original_duration * 1000))
    
    print(f"🎵 开始合成音频轨道...")
    print(f"📊 原视频时长: {original_duration:.2f}s")
    print(f"📝 字幕数量: {len(subtitles)}")
    
    for i, (index, start_time, end_time, text) in enumerate(subtitles):
        audio_file = os.path.join(audio_dir, f"subtitle_{index:03d}.wav")
        
        if not os.path.exists(audio_file):
            print(f"⚠️  音频文件不存在: {audio_file}")
            continue
        
        try:
            # 加载TTS音频
            tts_audio = AudioSegment.from_wav(audio_file)
            tts_duration = len(tts_audio) / 1000.0  # 转换为秒
            
            # 计算字幕时间段长度
            subtitle_duration = end_time - start_time
            
            # 如果TTS音频比字幕时间段长，需要加速
            if tts_duration > subtitle_duration:
                speed_factor = tts_duration / subtitle_duration
                # 加速音频
                tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                tts_duration = len(tts_audio) / 1000.0
                print(f"🔄 [{i+1}/{len(subtitles)}] 加速音频 {speed_factor:.2f}x: {text[:30]}...")
            elif tts_duration < subtitle_duration:
                # 如果TTS音频比字幕时间段短，保持原速度
                print(f"✅ [{i+1}/{len(subtitles)}] 音频时长合适: {text[:30]}...")
            else:
                print(f"✅ [{i+1}/{len(subtitles)}] 音频时长完美匹配: {text[:30]}...")
            
            # 将TTS音频插入到指定时间位置
            start_ms = int(start_time * 1000)
            
            # 确保不超出总时长
            if start_ms + len(tts_audio) > len(composite_audio):
                tts_audio = tts_audio[:len(composite_audio) - start_ms]
            
            # 叠加音频
            composite_audio = composite_audio.overlay(tts_audio, position=start_ms)
            
        except Exception as e:
            print(f"❌ 处理音频失败 {audio_file}: {str(e)}")
            continue
    
    return composite_audio

def create_chinese_video(video_path: str, srt_path: str, audio_dir: str, output_path: str):
    """创建中文配音视频"""
    
    print(f"🎬 开始创建中文配音视频...")
    print(f"📹 原视频: {video_path}")
    print(f"📝 字幕文件: {srt_path}")
    print(f"🎵 音频目录: {audio_dir}")
    print(f"📁 输出文件: {output_path}")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(srt_path):
        print(f"❌ 字幕文件不存在: {srt_path}")
        return False
    
    if not os.path.exists(audio_dir):
        print(f"❌ 音频目录不存在: {audio_dir}")
        return False
    
    try:
        # 加载原视频
        print("📹 加载原视频...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        
        # 读取字幕
        print("📖 读取字幕...")
        subtitles = read_srt_file(srt_path)
        
        # 创建合成音频
        print("🎵 创建合成音频...")
        composite_audio_segment = create_composite_audio(audio_dir, subtitles, original_duration)
        
        # 将AudioSegment转换为临时文件
        temp_audio_path = "temp_composite_audio.wav"
        composite_audio_segment.export(temp_audio_path, format="wav")
        
        # 加载合成音频
        print("🎧 加载合成音频...")
        composite_audio = AudioFileClip(temp_audio_path)
        
        # 设置视频的新音轨
        print("🎬 合成最终视频...")
        final_video = video.set_audio(composite_audio)
        
        # 输出视频
        print(f"💾 保存视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        
        # 释放资源
        video.close()
        composite_audio.close()
        final_video.close()
        
        print(f"🎉 中文配音视频创建成功！")
        print(f"📁 输出文件: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建视频失败: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='中文配音视频合成器')
    parser.add_argument('--video', required=True, help='原视频文件路径')
    parser.add_argument('--srt', required=True, help='中文字幕文件路径')
    parser.add_argument('--audio_dir', required=True, help='TTS音频文件目录')
    parser.add_argument('--output', required=True, help='输出视频文件路径')
    
    args = parser.parse_args()
    
    success = create_chinese_video(args.video, args.srt, args.audio_dir, args.output)
    
    if success:
        print("✅ 任务完成！")
        sys.exit(0)
    else:
        print("❌ 任务失败！")
        sys.exit(1)

if __name__ == "__main__":
    main() 