package com.translation.service;

import okhttp3.*;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 百度API访问令牌服务
 * 用于获取百度AI开放平台的访问令牌
 */
@Service
public class BaiduTokenService {
    private static final Logger logger = LoggerFactory.getLogger(BaiduTokenService.class);
    
    @Value("${baidu.api.client.id:}")
    private String clientId;
    
    @Value("${baidu.api.client.secret:}")
    private String clientSecret;
    
    private final OkHttpClient httpClient;
    
    private static final String TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
    
    public BaiduTokenService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
    }
    
    /**
     * 检查服务是否已配置
     */
    public boolean isConfigured() {
        return clientId != null && !clientId.trim().isEmpty() && 
               clientSecret != null && !clientSecret.trim().isEmpty();
    }
    
    /**
     * 获取访问令牌
     * @return 访问令牌
     * @throws IOException 如果请求失败
     */
    public String getAccessToken() throws IOException {
        if (!isConfigured()) {
            throw new IOException("百度API服务未配置：请设置 BAIDU_API_CLIENT_ID 和 BAIDU_API_CLIENT_SECRET 环境变量");
        }
        
        logger.info("开始获取百度API访问令牌");
        
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "");
        
        String url = TOKEN_URL + "?client_id=" + clientId + 
                     "&client_secret=" + clientSecret + 
                     "&grant_type=client_credentials";
        
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("获取百度API令牌失败: " + response.code());
            }
            
            String responseBody = response.body().string();
            logger.debug("百度API令牌响应: {}", responseBody);
            
            JSONObject jsonObject = new JSONObject(responseBody);
            
            if (jsonObject.has("error")) {
                String error = jsonObject.getString("error");
                String errorDescription = jsonObject.optString("error_description", "未知错误");
                logger.error("百度API令牌获取错误: error={}, description={}", error, errorDescription);
                throw new IOException("获取百度API令牌失败: " + errorDescription);
            }
            
            if (jsonObject.has("access_token")) {
                String accessToken = jsonObject.getString("access_token");
                int expiresIn = jsonObject.optInt("expires_in", 0);
                logger.info("百度API令牌获取成功，有效期: {} 秒", expiresIn);
                return accessToken;
            }
            
            throw new IOException("百度API令牌响应格式错误");
        }
    }
    
    /**
     * 测试方法 - 获取并打印访问令牌
     */
    public void testGetToken() {
        try {
            String token = getAccessToken();
            System.out.println("百度API访问令牌: " + token);
        } catch (IOException e) {
            logger.error("获取百度API令牌失败", e);
            System.err.println("获取百度API令牌失败: " + e.getMessage());
        }
    }
}