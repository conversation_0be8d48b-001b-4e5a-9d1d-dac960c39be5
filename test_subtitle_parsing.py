#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from add_subtitles_fixed import parse_srt_file

# 测试字幕解析
chinese_subs = parse_srt_file('VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt')
english_subs = parse_srt_file('VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output.srt')

print('解析的中文字幕:')
for i, sub in enumerate(chinese_subs):
    print(f'{i+1}. {sub["start"]:.3f}s - {sub["end"]:.3f}s: {sub["text"][:50]}...')

print('\n解析的英文字幕:')
for i, sub in enumerate(english_subs):
    print(f'{i+1}. {sub["start"]:.3f}s - {sub["end"]:.3f}s: {sub["text"][:50]}...')

print(f'\n总结:')
print(f'中文字幕: {len(chinese_subs)} 条')
print(f'英文字幕: {len(english_subs)} 条') 