#!/usr/bin/env python3
"""
修复视频翻译问题 - 重新生成TTS和字幕烧录
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def fix_translation(task_name):
    """修复指定任务的翻译问题"""
    try:
        print(f"🔧 开始修复任务: {task_name}")
        
        from app.services.directory_service import DirectoryService
        from app.services.tts_service import TTSService
        from app.services.video_service import VideoService
        from app.services.subtitle_service import SubtitleService
        
        # 初始化服务
        dir_service = DirectoryService()
        tts_service = TTSService()
        video_service = VideoService()
        subtitle_service = SubtitleService()
        
        # 获取任务路径
        task_paths = dir_service.get_task_paths(task_name)
        if not task_paths:
            print(f"❌ 任务不存在: {task_name}")
            return False
        
        print(f"📁 任务目录: {task_paths['task_dir']}")
        
        # 检查翻译文本是否存在
        translation_file = Path(task_paths['translation']) / 'translated.txt'
        if not translation_file.exists():
            print(f"❌ 翻译文件不存在: {translation_file}")
            return False
        
        # 读取翻译文本
        with open(translation_file, 'r', encoding='utf-8') as f:
            translated_text = f.read().strip()
        
        print(f"📝 翻译文本: {translated_text[:100]}...")
        
        # Step 1: 重新生成TTS（使用正确的语音）
        print(f"\n🎵 Step 1: 重新生成TTS...")
        
        tts_output_path = dir_service.get_file_path(
            task_name=task_name,
            subdir='tts',
            filename='chinese.wav'
        )
        
        # 使用正确的中文语音
        correct_voice = "zh-CN-XiaoxiaoNeural"
        print(f"   使用语音: {correct_voice}")
        
        try:
            tts_result = await tts_service.synthesize_speech_to_path(
                text=translated_text,
                voice=correct_voice,
                output_path=tts_output_path,
                speed=1.0,
                pitch=0,
                service="edge-tts"
            )
            
            if tts_result and not tts_result.get("fallback"):
                print(f"   ✅ TTS生成成功: {tts_output_path}")
                tts_success = True
            else:
                print(f"   ❌ TTS生成失败: {tts_result.get('message', '未知错误')}")
                tts_success = False
                
        except Exception as e:
            print(f"   ❌ TTS生成异常: {e}")
            tts_success = False
        
        if not tts_success:
            print("   ⚠️ TTS失败，将使用原视频音频")
            tts_output_path = str(translation_file)  # 使用文本文件作为fallback
        
        # Step 2: 重新合成视频（带字幕烧录）
        print(f"\n🎬 Step 2: 重新合成视频...")
        
        source_video_path = dir_service.get_file_path(
            task_name=task_name,
            subdir='source',
            filename='original.mp4'
        )
        
        subtitle_path = dir_service.get_file_path(
            task_name=task_name,
            subdir='subtitles',
            filename='translated.srt'
        )
        
        final_video_path = dir_service.get_file_path(
            task_name=task_name,
            subdir='output',
            filename='final_video_fixed.mp4'
        )
        
        # 字幕样式（Netflix风格）
        subtitle_style = {
            'font_name': 'Arial',
            'font_size': 28,
            'primary_color': '&Hffffff',  # 白色文字
            'outline_color': '&H000000',  # 黑色描边
            'back_color': '&H80000000',   # 半透明背景
            'outline': 2,                 # 描边宽度
            'shadow': 1,                  # 阴影
            'alignment': 2,               # 底部居中
            'margin_v': 40                # 底部边距
        }
        
        print(f"   源视频: {source_video_path}")
        print(f"   TTS音频: {tts_output_path}")
        print(f"   字幕文件: {subtitle_path}")
        print(f"   输出视频: {final_video_path}")
        
        try:
            # 使用带字幕烧录的视频合成
            video_result = await video_service.compose_video_to_path(
                video_path=source_video_path,
                audio_path=tts_output_path,
                output_path=final_video_path,
                subtitle_path=subtitle_path,
                output_format="mp4",
                burn_subtitles=True,  # 启用字幕烧录
                subtitle_style=subtitle_style
            )
            
            print(f"   ✅ 视频合成成功: {final_video_path}")
            print(f"   文件大小: {video_result.get('file_size', 0) / (1024*1024):.1f} MB")
            print(f"   视频时长: {video_result.get('duration', 0):.1f} 秒")
            
            if video_result.get('subtitles_burned'):
                print(f"   ✅ 字幕已烧录到视频中")
            
        except Exception as e:
            print(f"   ❌ 视频合成失败: {e}")
            return False
        
        # Step 3: 更新元数据
        print(f"\n📋 Step 3: 更新元数据...")
        
        dir_service.update_task_metadata(
            task_name=task_name,
            updates={
                'status': 'fixed',
                'tts_voice': correct_voice,
                'burn_subtitles': True,
                'fixed_at': datetime.now().isoformat(),
                'tts_success': tts_success,
                'final_video_path': final_video_path
            }
        )
        
        print(f"   ✅ 元数据已更新")
        
        print(f"\n🎉 修复完成!")
        print(f"📍 修复后的视频位置: {final_video_path}")
        print(f"🎬 现在视频应该包含:")
        if tts_success:
            print(f"   ✅ 中文TTS语音")
        else:
            print(f"   ⚠️ 原始音频（TTS失败）")
        print(f"   ✅ 烧录的中文字幕")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def list_failed_tasks():
    """列出需要修复的任务"""
    try:
        print("🔍 查找需要修复的任务...")
        
        outputs_dir = Path("python-ai-service/storage/outputs")
        if not outputs_dir.exists():
            print("❌ 输出目录不存在")
            return []
        
        failed_tasks = []
        
        for task_dir in outputs_dir.iterdir():
            if not task_dir.is_dir():
                continue
            
            # 检查是否有TTS失败的标志
            tts_dir = task_dir / "tts"
            if tts_dir.exists():
                # 检查是否只有fallback文件，没有wav文件
                wav_files = list(tts_dir.glob("*.wav"))
                fallback_files = list(tts_dir.glob("*fallback.txt"))
                
                if len(wav_files) == 0 and len(fallback_files) > 0:
                    failed_tasks.append(task_dir.name)
                    print(f"   ❌ {task_dir.name} - TTS失败")
        
        if not failed_tasks:
            print("✅ 没有发现需要修复的任务")
        else:
            print(f"\n📋 发现 {len(failed_tasks)} 个需要修复的任务:")
            for i, task in enumerate(failed_tasks, 1):
                print(f"   {i}. {task}")
        
        return failed_tasks
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        return []

async def main():
    if len(sys.argv) > 1:
        # 修复指定任务
        task_name = sys.argv[1]
        success = await fix_translation(task_name)
        if success:
            print(f"\n🚀 要播放修复后的视频，请运行:")
            print(f"   start \"python-ai-service/storage/outputs/{task_name}/output/final_video_fixed.mp4\"")
    else:
        # 列出需要修复的任务
        failed_tasks = await list_failed_tasks()
        
        if failed_tasks:
            print(f"\n🔧 要修复任务，请运行:")
            print(f"   python fix_video_translation.py [任务名]")
            print(f"\n例如:")
            print(f"   python fix_video_translation.py {failed_tasks[0]}")

if __name__ == "__main__":
    import datetime
    asyncio.run(main())
