#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建音频文件和字幕的映射关系
找出每个字幕对应的正确音频文件
"""

import os
import re
from pydub import AudioSegment

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                if ' --> ' in time_line:
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
            except ValueError:
                continue
    
    return subtitles

def create_audio_mapping():
    """创建音频文件和字幕的映射关系"""
    print("🗺️ 创建音频文件和字幕映射关系")
    
    # 文件路径
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt"
    audio_dir = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/audio"
    
    # 读取字幕
    subtitles = read_srt_file(srt_path)
    print(f"📝 读取到 {len(subtitles)} 条字幕")
    
    # 获取所有音频文件
    audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
    audio_files.sort()
    print(f"🎵 找到 {len(audio_files)} 个音频文件")
    
    print(f"📊 音频文件范围: {audio_files[0]} 到 {audio_files[-1]}")
    
    # 尝试不同的映射策略
    
    # 策略1: 直接顺序映射（字幕1对应第1个音频文件）
    print("\n🔍 策略1: 顺序映射")
    mapping1 = {}
    for i, (index, start_time, end_time, text) in enumerate(subtitles):
        if i < len(audio_files):
            mapping1[index] = audio_files[i]
            if i < 5:  # 显示前5个映射
                print(f"  字幕{index} -> {audio_files[i]}")
    
    # 策略2: 基于音频文件编号的数学映射
    # 音频文件从001到999，字幕从1到166
    # 可能是音频文件编号 = 字幕索引的某个偏移或倍数
    
    print("\n🔍 策略2: 查找数学关系")
    
    # 检查第一个字幕对应的可能音频文件
    first_subtitle = subtitles[0]
    print(f"第一个字幕: 索引{first_subtitle[0]}, 时间{first_subtitle[1]:.2f}-{first_subtitle[2]:.2f}s")
    print(f"文本: {first_subtitle[3][:50]}...")
    
    # 尝试找出音频文件的起始编号
    # 检查前几个音频文件，看哪个最可能对应第一个字幕
    
    print("\n🎵 检查前10个音频文件的时长:")
    for i in range(min(10, len(audio_files))):
        audio_file = os.path.join(audio_dir, audio_files[i])
        try:
            # 尝试加载音频
            try:
                audio = AudioSegment.from_wav(audio_file)
            except:
                audio = AudioSegment.from_mp3(audio_file)
            
            duration = len(audio) / 1000.0
            print(f"  {audio_files[i]}: {duration:.2f}s")
            
        except Exception as e:
            print(f"  {audio_files[i]}: 无法读取 - {str(e)}")
    
    # 策略3: 基于文件创建时间或者其他规律
    print(f"\n📊 字幕时长分布:")
    subtitle_durations = [(end - start) for _, start, end, _ in subtitles[:5]]
    for i, (index, start, end, text) in enumerate(subtitles[:5]):
        print(f"  字幕{index}: {end-start:.2f}s - {text[:30]}...")
    
    # 保存映射结果
    print(f"\n💾 保存映射关系...")
    
    # 使用策略1作为默认映射
    with open("audio_subtitle_mapping.txt", "w", encoding="utf-8") as f:
        f.write("# 音频文件和字幕映射关系\n")
        f.write("# 格式: 字幕索引 -> 音频文件名\n\n")
        
        for i, (index, start_time, end_time, text) in enumerate(subtitles):
            if i < len(audio_files):
                f.write(f"{index} -> {audio_files[i]}\n")
    
    print(f"✅ 映射关系已保存到 audio_subtitle_mapping.txt")
    return mapping1

def main():
    """主函数"""
    print("🎯 音频字幕映射创建器")
    mapping = create_audio_mapping()
    
    print(f"\n📊 创建了 {len(mapping)} 个映射关系")

if __name__ == "__main__":
    main() 