#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整视频中文配音合成器（使用已有音频文件）
直接使用VideoLingo生成的166个音频片段进行高质量合成
"""

import os
import re
import time
from moviepy import VideoFileClip, AudioFileClip
from pydub import AudioSegment

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                # 尝试解析第一行为数字索引
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 检查时间行格式
                if ' --> ' in time_line:
                    # 解析时间
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
                else:
                    print(f"⚠️  跳过无效时间格式: {time_line}")
            except ValueError:
                print(f"⚠️  跳过无效索引: {lines[0]}")
                continue
    
    return subtitles

def compose_full_video_chinese():
    """使用已有音频文件合成完整视频的高质量中文配音"""
    
    # 文件路径
    video_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/input.mp4"
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt"
    audio_dir = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/audio"
    output_path = "chinese_video_full_complete.mp4"
    
    print("🎬 开始合成完整视频的高质量中文配音...")
    print(f"📝 使用字幕文件: {srt_path}")
    print(f"🎵 使用音频目录: {audio_dir}")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(srt_path):
        print(f"❌ 字幕文件不存在: {srt_path}")
        return False
        
    if not os.path.exists(audio_dir):
        print(f"❌ 音频目录不存在: {audio_dir}")
        return False
    
    try:
        # 检查音频文件数量
        audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
        print(f"🎵 找到 {len(audio_files)} 个音频文件")
        
        # 读取字幕文件
        subtitles = read_srt_file(srt_path)
        print(f"📝 读取到 {len(subtitles)} 条字幕")
        
        # 显示视频时长信息
        print("📹 检查原视频信息...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        print(f"📊 原视频时长: {original_duration/60:.1f}分钟 ({original_duration:.2f}秒)")
        video.close()
        
        # 重新加载视频进行合成
        print("📹 重新加载原视频...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        
        # 创建静音背景音轨
        print("🎵 创建合成音频...")
        composite_audio = AudioSegment.silent(duration=int(original_duration * 1000))
        
        # 处理每个字幕的音频
        successful_audio_count = 0
        skipped_count = 0
        print(f"🔄 开始合成 {len(subtitles)} 个音频片段...")
        
        for i, (index, start_time_sub, end_time_sub, text) in enumerate(subtitles):
            # 查找对应的音频文件 - 优先使用subtitle_index.wav格式
            audio_file = os.path.join(audio_dir, f"subtitle_{index}.wav")
            
            if not os.path.exists(audio_file):
                # 尝试其他可能的文件名格式
                alternative_names = [
                    f"subtitle_{index:03d}.wav",
                    f"{index:03d}.wav",
                    f"{index}.wav",
                    f"audio_{index:03d}.wav"
                ]
                
                found = False
                for alt_name in alternative_names:
                    alt_path = os.path.join(audio_dir, alt_name)
                    if os.path.exists(alt_path):
                        audio_file = alt_path
                        found = True
                        break
                
                if not found:
                    print(f"⚠️  未找到音频文件 subtitle_{index}.wav")
                    skipped_count += 1
                    continue
            
            try:
                # 加载TTS音频 - 实际是MP3格式但扩展名为wav
                try:
                    # 先尝试作为WAV格式加载
                    tts_audio = AudioSegment.from_wav(audio_file)
                except Exception:
                    # 如果WAV格式失败，尝试作为MP3格式加载
                    tts_audio = AudioSegment.from_mp3(audio_file)
                
                tts_duration = len(tts_audio) / 1000.0
                
                # 计算字幕时间段长度
                subtitle_duration = end_time_sub - start_time_sub
                
                if (i + 1) % 20 == 0:  # 每20个显示进度
                    print(f"🔄 [{i+1}/{len(subtitles)}] 处理进度: {((i+1)/len(subtitles)*100):.1f}%")
                
                # 调整音频时长以匹配字幕时间段
                if tts_duration > subtitle_duration:
                    # 需要加速
                    speed_factor = tts_duration / subtitle_duration
                    # 限制最大加速倍数为2.5，避免语音过快
                    if speed_factor > 2.5:
                        speed_factor = 2.5
                        print(f"⚠️  字幕{index}加速受限至2.5倍 (原需{speed_factor:.2f}倍)")
                    tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                elif tts_duration < subtitle_duration * 0.7:
                    # 如果音频明显短于字幕时间，可以稍微减速
                    speed_factor = tts_duration / (subtitle_duration * 0.8)
                    if speed_factor < 0.7:
                        speed_factor = 0.7
                    tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                
                # 将TTS音频插入到指定时间位置
                start_ms = int(start_time_sub * 1000)
                
                # 确保不超出总时长
                if start_ms + len(tts_audio) > len(composite_audio):
                    tts_audio = tts_audio[:len(composite_audio) - start_ms]
                
                # 叠加音频
                composite_audio = composite_audio.overlay(tts_audio, position=start_ms)
                successful_audio_count += 1
                
            except Exception as e:
                print(f"❌ 处理音频失败 {audio_file}: {str(e)}")
                skipped_count += 1
                continue
        
        print(f"✅ 成功处理: {successful_audio_count}/{len(subtitles)} 个音频文件")
        print(f"⚠️  跳过文件: {skipped_count} 个")
        
        # 将AudioSegment转换为临时文件
        temp_audio_path = "temp_full_video_audio_complete.wav"
        print("💾 导出合成音频（这可能需要几分钟）...")
        composite_audio.export(temp_audio_path, format="wav")
        print(f"✅ 合成音频导出完成，大小: {os.path.getsize(temp_audio_path)/(1024*1024):.2f} MB")
        
        # 加载合成音频
        print("🎧 加载合成音频到MoviePy...")
        new_audio = AudioFileClip(temp_audio_path)
        
        # 设置视频的新音轨
        print("🎬 合成最终视频（这可能需要10-20分钟）...")
        final_video = video.with_audio(new_audio)
        
        # 输出视频
        print(f"💾 保存视频到: {output_path}")
        start_time = time.time()
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            bitrate='2000k',  # 提高视频质量
            audio_bitrate='128k'  # 音频质量
        )
        
        processing_time = time.time() - start_time
        print(f"⏱️  视频处理完成，耗时: {processing_time/60:.1f}分钟")
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
            print("🗑️  临时文件已清理")
        
        # 释放资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"🎉 完整视频中文配音创建成功！")
        print(f"📁 输出文件: {output_path}")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            print(f"📊 文件大小: {file_size:.2f} MB")
            print(f"📊 压缩比: {file_size/original_duration:.2f} MB/分钟")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建视频失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 完整视频中文配音合成器")
    print("📋 直接使用已有的166个音频片段进行合成")
    
    success = compose_full_video_chinese()
    if success:
        print("✅ 任务完成！")
    else:
        print("❌ 任务失败！")

if __name__ == "__main__":
    main() 