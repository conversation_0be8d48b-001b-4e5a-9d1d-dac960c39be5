# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class IntProperty(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = IntProperty()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsIntProperty(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def IntPropertyBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x44\x54\x43", size_prefixed=size_prefixed)

    # IntProperty
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # IntProperty
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # IntProperty
    def Value(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int64Flags, o + self._tab.Pos)
        return 0

def IntPropertyStart(builder):
    builder.StartObject(2)

def Start(builder):
    IntPropertyStart(builder)

def IntPropertyAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def AddName(builder, name):
    IntPropertyAddName(builder, name)

def IntPropertyAddValue(builder, value):
    builder.PrependInt64Slot(1, value, 0)

def AddValue(builder, value):
    IntPropertyAddValue(builder, value)

def IntPropertyEnd(builder):
    return builder.EndObject()

def End(builder):
    return IntPropertyEnd(builder)
