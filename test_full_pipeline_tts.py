#!/usr/bin/env python3
"""
测试完整流程中的TTS问题修复
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def test_tts_in_pipeline():
    """测试完整流程中的TTS调用"""
    try:
        from app.services.tts_service import TTSService
        from app.services.task_service import TaskService
        from app.models.schemas import FullPipelineRequest
        
        print("🚀 测试完整流程中的TTS调用...")
        
        # 初始化服务
        tts_service = TTSService()
        task_service = TaskService()
        
        print("✅ 服务初始化成功")
        
        # 1. 测试单独的TTS调用（模拟完整流程中的调用方式）
        print("\n1. 测试单独TTS调用...")
        test_text = "这是完整流程中的TTS测试。我们正在验证Edge TTS功能。"
        voice = "zh-CN-XiaoxiaoNeural"
        
        try:
            result = await tts_service.synthesize_speech(
                test_text,
                voice,
                1.0,
                0,
                "edge-tts"
            )
            
            print(f"📋 TTS结果:")
            print(f"  音频文件: {result['audio_file_path']}")
            print(f"  时长: {result['duration']:.2f}秒")
            print(f"  是否回退: {result.get('fallback', False)}")
            
            if result.get('fallback'):
                print(f"⚠️ 使用了回退方案: {result.get('message', 'Unknown')}")
                return False
            else:
                print("✅ TTS调用成功!")
                return True
                
        except Exception as e:
            print(f"❌ TTS调用失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tts_retry_mechanism():
    """测试TTS重试机制"""
    try:
        from app.services.tts_service import TTSService
        
        print("\n🔄 测试TTS重试机制...")
        
        tts_service = TTSService()
        
        # 模拟完整流程中的重试逻辑
        max_retries = 3
        test_text = "重试机制测试"
        voice = "zh-CN-XiaoxiaoNeural"
        
        for attempt in range(max_retries):
            try:
                print(f"  尝试 {attempt + 1}/{max_retries}...")
                
                result = await tts_service.synthesize_speech(
                    test_text,
                    voice,
                    1.0,
                    0,
                    "edge-tts"
                )
                
                if result and not result.get("fallback"):
                    print(f"  ✅ 第{attempt + 1}次尝试成功")
                    return True
                elif result and result.get("fallback"):
                    print(f"  ⚠️ 第{attempt + 1}次尝试返回回退: {result.get('message', 'Unknown')}")
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 3
                        print(f"  等待{wait_time}秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print("  接受回退结果")
                        return False
                        
            except Exception as e:
                print(f"  ❌ 第{attempt + 1}次尝试失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    print(f"  等待{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    print("  所有重试都失败了")
                    return False
        
        return False
        
    except Exception as e:
        print(f"❌ 重试机制测试失败: {e}")
        return False

async def test_edge_tts_health():
    """测试Edge TTS健康状态"""
    try:
        print("\n🏥 测试Edge TTS健康状态...")
        
        import edge_tts
        
        # 测试网络连接
        try:
            voices = await edge_tts.list_voices()
            chinese_voices = [v for v in voices if 'zh-CN' in v['Locale']]
            print(f"✅ 网络连接正常，找到{len(chinese_voices)}个中文语音")
        except Exception as e:
            print(f"❌ 网络连接测试失败: {e}")
            return False
        
        # 测试简单合成
        try:
            communicate = edge_tts.Communicate("健康检查", "zh-CN-XiaoxiaoNeural")
            test_file = "health_check.wav"
            await communicate.save(test_file)
            
            import os
            if os.path.exists(test_file) and os.path.getsize(test_file) > 0:
                print("✅ Edge TTS合成测试成功")
                os.remove(test_file)
                return True
            else:
                print("❌ Edge TTS合成测试失败: 文件未生成或为空")
                return False
                
        except Exception as e:
            print(f"❌ Edge TTS合成测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Edge TTS健康检查失败: {e}")
        return False

async def main():
    print("🎵 开始完整流程TTS问题修复测试...")
    
    # 1. Edge TTS健康检查
    health_ok = await test_edge_tts_health()
    
    # 2. 单独TTS调用测试
    single_ok = await test_tts_in_pipeline()
    
    # 3. 重试机制测试
    retry_ok = await test_tts_retry_mechanism()
    
    print(f"\n📊 测试结果总结:")
    print(f"  Edge TTS健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"  单独TTS调用: {'✅ 通过' if single_ok else '❌ 失败'}")
    print(f"  重试机制: {'✅ 通过' if retry_ok else '❌ 失败'}")
    
    if health_ok and single_ok:
        print("\n🎉 TTS修复测试成功!")
        print("现在完整流程应该能够正常生成有声音的视频了")
        print("\n建议:")
        print("1. 重启AI服务")
        print("2. 重新运行完整翻译流程")
        print("3. 检查生成的视频是否有声音")
    else:
        print("\n⚠️ TTS仍有问题，需要进一步调试")
        if not health_ok:
            print("- Edge TTS服务不可用，检查网络连接")
        if not single_ok:
            print("- TTS调用失败，检查服务配置")

if __name__ == "__main__":
    asyncio.run(main())
