"""
Pydantic schemas for video translation AI service
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    """Task type enumeration"""
    AUDIO_EXTRACTION = "audio_extraction"
    SPEECH_RECOGNITION = "speech_recognition"
    TRANSLATION = "translation"
    SUBTITLE_GENERATION = "subtitle_generation"
    TTS_GENERATION = "tts_generation"
    VIDEO_COMPOSITION = "video_composition"
    FULL_PIPELINE = "full_pipeline"


class AudioExtractionRequest(BaseModel):
    """Audio extraction request schema"""
    video_file_path: str = Field(..., description="Path to the video file")
    output_format: str = Field(default="wav", description="Output audio format")
    sample_rate: int = Field(default=16000, description="Audio sample rate")
    channels: int = Field(default=1, description="Number of audio channels")


class AudioExtractionResponse(BaseModel):
    """Audio extraction response schema"""
    task_id: str = Field(..., description="Task ID")
    audio_file_path: str = Field(..., description="Path to extracted audio file")
    duration: float = Field(..., description="Audio duration in seconds")
    sample_rate: int = Field(..., description="Audio sample rate")
    channels: int = Field(..., description="Number of audio channels")


class SpeechRecognitionRequest(BaseModel):
    """Speech recognition request schema"""
    audio_file_path: str = Field(..., description="Path to the audio file")
    language: str = Field(default="auto", description="Source language")
    model: str = Field(default="base", description="Whisper model to use")


class SpeechRecognitionResponse(BaseModel):
    """Speech recognition response schema"""
    task_id: str = Field(..., description="Task ID")
    text: str = Field(..., description="Recognized text")
    language: str = Field(..., description="Detected language")
    confidence: float = Field(..., description="Recognition confidence")
    segments: List[Dict[str, Any]] = Field(..., description="Text segments with timestamps")


class TranslationRequest(BaseModel):
    """Translation request schema"""
    text: str = Field(..., description="Text to translate")
    source_language: str = Field(default="en", description="Source language")
    target_language: str = Field(default="zh-CN", description="Target language")
    service: str = Field(default="openai", description="Translation service")


class TranslationResponse(BaseModel):
    """Translation response schema"""
    task_id: str = Field(..., description="Task ID")
    translated_text: str = Field(..., description="Translated text")
    source_language: str = Field(..., description="Source language")
    target_language: str = Field(..., description="Target language")
    confidence: float = Field(..., description="Translation confidence")


class SubtitleSegment(BaseModel):
    """Subtitle segment schema"""
    start_time: float = Field(..., description="Start time in seconds")
    end_time: float = Field(..., description="End time in seconds")
    text: str = Field(..., description="Subtitle text")
    index: int = Field(..., description="Segment index")


class SubtitleGenerationRequest(BaseModel):
    """Subtitle generation request schema"""
    segments: List[Dict[str, Any]] = Field(..., description="Text segments with timestamps")
    max_chars_per_line: int = Field(default=50, description="Maximum characters per line")
    max_lines: int = Field(default=2, description="Maximum lines per subtitle")


class SubtitleGenerationResponse(BaseModel):
    """Subtitle generation response schema"""
    task_id: str = Field(..., description="Task ID")
    subtitles: List[SubtitleSegment] = Field(..., description="Generated subtitles")
    subtitle_file_path: str = Field(..., description="Path to SRT file")


class TTSRequest(BaseModel):
    """TTS request schema"""
    text: str = Field(..., description="Text to synthesize")
    voice: str = Field(default="zh-CN-XiaoxiaoNeural", description="Voice to use")
    speed: float = Field(default=1.0, description="Speech speed")
    pitch: int = Field(default=0, description="Speech pitch")
    service: str = Field(default="edge-tts", description="TTS service")


class TTSResponse(BaseModel):
    """TTS response schema"""
    task_id: str = Field(..., description="Task ID")
    audio_file_path: str = Field(..., description="Path to generated audio file")
    duration: float = Field(..., description="Audio duration in seconds")
    text: str = Field(..., description="Original text")


class VideoCompositionRequest(BaseModel):
    """Video composition request schema"""
    video_file_path: str = Field(..., description="Path to original video file")
    audio_file_path: str = Field(..., description="Path to new audio file")
    subtitle_file_path: Optional[str] = Field(None, description="Path to subtitle file")
    output_format: str = Field(default="mp4", description="Output video format")


class VideoCompositionResponse(BaseModel):
    """Video composition response schema"""
    task_id: str = Field(..., description="Task ID")
    video_file_path: str = Field(..., description="Path to composed video file")
    duration: float = Field(..., description="Video duration in seconds")
    file_size: int = Field(..., description="File size in bytes")


class FullPipelineRequest(BaseModel):
    """Full pipeline request schema"""
    video_file_path: str = Field(..., description="Path to input video file")
    source_language: str = Field(default="en", description="Source language")
    target_language: str = Field(default="zh-CN", description="Target language")
    include_subtitles: bool = Field(default=True, description="Include subtitles in output")
    tts_voice: str = Field(default="zh-CN-XiaoxiaoNeural", description="TTS voice")
    
    @validator('video_file_path')
    def validate_video_file_path(cls, v):
        if not v.endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv')):
            raise ValueError('Invalid video file format')
        return v


class TaskInfo(BaseModel):
    """Task information schema"""
    task_id: str = Field(..., description="Task ID")
    task_type: TaskType = Field(..., description="Task type")
    status: TaskStatus = Field(..., description="Task status")
    progress: float = Field(default=0.0, description="Task progress (0-100)")
    created_at: datetime = Field(..., description="Task creation time")
    updated_at: datetime = Field(..., description="Task last update time")
    result: Optional[Dict[str, Any]] = Field(None, description="Task result")
    error: Optional[str] = Field(None, description="Error message if failed")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class TaskListResponse(BaseModel):
    """Task list response schema"""
    tasks: List[TaskInfo] = Field(..., description="List of tasks")
    total: int = Field(..., description="Total number of tasks")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")


class HealthCheckResponse(BaseModel):
    """Health check response schema"""
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(..., description="Check timestamp")
    version: str = Field(..., description="Service version")
    dependencies: Dict[str, str] = Field(..., description="Dependency status")


class ErrorResponse(BaseModel):
    """Error response schema"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Error details")
    timestamp: datetime = Field(..., description="Error timestamp") 