# Application Settings
APP_NAME=Video Translation AI Service
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=4

# Database Settings
DATABASE_URL=postgresql://user:password@localhost:5432/video_translation
REDIS_URL=redis://localhost:6379/0

# Message Queue Settings
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# AI Service Settings
OPENAI_API_KEY=sk-xwldnvvsghqncbafxuiomptzfymorthcbbsexyrzoajhakaf
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
OPENAI_MODEL=deepseek-ai/DeepSeek-V3
GOOGLE_CLOUD_CREDENTIALS_PATH=/path/to/google-credentials.json

# Storage Settings
STORAGE_PATH=./storage
TEMP_PATH=./storage/temp
UPLOADS_PATH=./storage/uploads
OUTPUTS_PATH=./storage/outputs
MAX_FILE_SIZE=500MB

# Audio Processing Settings
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_FORMAT=wav

# Speech Recognition Settings
WHISPER_MODEL=base
WHISPER_LANGUAGE=auto

# Translation Settings
TRANSLATION_SERVICE=openai
TARGET_LANGUAGE=zh-CN
SOURCE_LANGUAGE=en

# TTS Settings
TTS_SERVICE=edge-tts
TTS_VOICE=zh-CN-XiaoxiaoNeural
TTS_SPEED=1.0
TTS_PITCH=0

# Security Settings
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_METHODS=["GET", "POST", "PUT", "DELETE"]
CORS_HEADERS=["*"] 