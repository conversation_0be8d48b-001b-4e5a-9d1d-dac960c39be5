#!/usr/bin/env python3
"""
测试字幕烧录功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def test_subtitle_service():
    """测试SubtitleService的字幕烧录功能"""
    try:
        print("📝 测试SubtitleService字幕烧录功能...")
        
        from app.services.subtitle_service import SubtitleService
        
        # 初始化服务
        subtitle_service = SubtitleService()
        print("✅ SubtitleService初始化成功")
        
        # 检查新方法是否存在
        methods_to_check = [
            'burn_subtitles_to_video',
            'create_dual_subtitles',
            '_build_subtitle_filter'
        ]
        
        for method_name in methods_to_check:
            if hasattr(subtitle_service, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ SubtitleService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_video_service():
    """测试VideoService的字幕集成功能"""
    try:
        print("\n🎬 测试VideoService字幕集成功能...")
        
        from app.services.video_service import VideoService
        
        # 初始化服务
        video_service = VideoService()
        print("✅ VideoService初始化成功")
        
        # 检查新方法是否存在
        methods_to_check = [
            'compose_video_with_subtitles',
            '_build_subtitle_filter',
            '_get_video_duration'
        ]
        
        for method_name in methods_to_check:
            if hasattr(video_service, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法不存在")
                return False
        
        # 检查compose_video_to_path方法是否支持新参数
        import inspect
        sig = inspect.signature(video_service.compose_video_to_path)
        expected_params = ['burn_subtitles', 'subtitle_style']
        
        for param in expected_params:
            if param in sig.parameters:
                print(f"  ✅ compose_video_to_path 支持 {param} 参数")
            else:
                print(f"  ❌ compose_video_to_path 不支持 {param} 参数")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ VideoService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_task_service():
    """测试TaskService的字幕集成"""
    try:
        print("\n🔧 测试TaskService字幕集成...")
        
        from app.services.task_service import TaskService
        
        # 初始化服务
        task_service = TaskService()
        print("✅ TaskService初始化成功")
        
        # 检查是否正确集成了SubtitleService
        if hasattr(task_service, 'subtitle_service'):
            subtitle_service = task_service.subtitle_service
            if hasattr(subtitle_service, 'burn_subtitles_to_video'):
                print("  ✅ TaskService集成了增强的SubtitleService")
            else:
                print("  ❌ TaskService的SubtitleService缺少字幕烧录功能")
                return False
        else:
            print("  ❌ TaskService未集成SubtitleService")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ TaskService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_request_schema():
    """测试请求模型的字幕选项"""
    try:
        print("\n📋 测试请求模型字幕选项...")
        
        from app.models.schemas import FullPipelineRequest
        
        # 测试创建请求对象
        request_data = {
            "video_file_path": "test.mp4",
            "source_language": "en",
            "target_language": "zh-CN",
            "include_subtitles": True,
            "burn_subtitles": True,
            "tts_voice": "zh-CN-XiaoxiaoNeural"
        }
        
        request = FullPipelineRequest(**request_data)
        print("✅ FullPipelineRequest创建成功")
        
        # 检查新字段
        if hasattr(request, 'burn_subtitles'):
            print(f"  ✅ burn_subtitles 字段存在: {request.burn_subtitles}")
        else:
            print("  ❌ burn_subtitles 字段不存在")
            return False
        
        # 测试默认值
        default_request = FullPipelineRequest(video_file_path="test.mp4")
        if hasattr(default_request, 'burn_subtitles') and default_request.burn_subtitles == False:
            print("  ✅ burn_subtitles 默认值正确 (False)")
        else:
            print("  ❌ burn_subtitles 默认值不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 请求模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ffmpeg_subtitle_support():
    """测试FFmpeg字幕支持"""
    try:
        print("\n🔧 测试FFmpeg字幕支持...")
        
        import subprocess
        
        # 测试FFmpeg是否支持subtitles滤镜
        result = subprocess.run(['ffmpeg', '-filters'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            if 'subtitles' in result.stdout:
                print("✅ FFmpeg支持subtitles滤镜")
            else:
                print("❌ FFmpeg不支持subtitles滤镜")
                return False
                
            if 'libass' in result.stdout:
                print("✅ FFmpeg支持libass")
            else:
                print("⚠️ FFmpeg可能不支持libass (字幕样式可能受限)")
        else:
            print("❌ 无法检查FFmpeg滤镜支持")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FFmpeg测试失败: {e}")
        return False

async def create_test_subtitle_file():
    """创建测试字幕文件"""
    try:
        print("\n📄 创建测试字幕文件...")
        
        test_srt_content = """1
00:00:00,000 --> 00:00:03,000
Hello, this is a test subtitle.

2
00:00:03,000 --> 00:00:06,000
这是一个测试字幕。

3
00:00:06,000 --> 00:00:10,000
Testing subtitle burn-in functionality.
"""
        
        test_srt_path = "test_subtitle.srt"
        with open(test_srt_path, 'w', encoding='utf-8') as f:
            f.write(test_srt_content)
        
        print(f"✅ 测试字幕文件创建: {test_srt_path}")
        
        return test_srt_path
        
    except Exception as e:
        print(f"❌ 创建测试字幕文件失败: {e}")
        return None

async def main():
    print("🚀 开始字幕烧录功能测试...")
    
    # 1. 测试SubtitleService
    subtitle_ok = await test_subtitle_service()
    
    # 2. 测试VideoService
    video_ok = await test_video_service()
    
    # 3. 测试TaskService
    task_ok = await test_task_service()
    
    # 4. 测试请求模型
    schema_ok = await test_request_schema()
    
    # 5. 测试FFmpeg支持
    ffmpeg_ok = await test_ffmpeg_subtitle_support()
    
    # 6. 创建测试字幕文件
    test_srt = await create_test_subtitle_file()
    
    print(f"\n📊 测试结果总结:")
    print(f"  SubtitleService: {'✅ 通过' if subtitle_ok else '❌ 失败'}")
    print(f"  VideoService: {'✅ 通过' if video_ok else '❌ 失败'}")
    print(f"  TaskService: {'✅ 通过' if task_ok else '❌ 失败'}")
    print(f"  请求模型: {'✅ 通过' if schema_ok else '❌ 失败'}")
    print(f"  FFmpeg支持: {'✅ 通过' if ffmpeg_ok else '❌ 失败'}")
    print(f"  测试文件: {'✅ 创建' if test_srt else '❌ 失败'}")
    
    if all([subtitle_ok, video_ok, task_ok, schema_ok, ffmpeg_ok]):
        print("\n🎉 字幕烧录功能测试成功!")
        print("\n✨ 新功能:")
        print("1. 字幕烧录到视频 (hardsubs)")
        print("2. Netflix风格的字幕样式")
        print("3. 双语字幕支持")
        print("4. 可自定义字幕样式")
        print("5. 集成到完整翻译流程")
        
        print("\n🚀 使用方法:")
        print("1. 在API请求中设置 burn_subtitles: true")
        print("2. 系统会自动将字幕烧录到视频中")
        print("3. 生成的视频包含永久字幕")
        
        print("\n📝 API示例:")
        print("""
{
    "video_file_path": "input.mp4",
    "source_language": "en",
    "target_language": "zh-CN",
    "include_subtitles": true,
    "burn_subtitles": true,
    "tts_voice": "zh-CN-XiaoxiaoNeural"
}
        """)
        
    else:
        print("\n⚠️ 字幕烧录功能仍有问题")
        if not subtitle_ok:
            print("- SubtitleService有问题")
        if not video_ok:
            print("- VideoService有问题")
        if not task_ok:
            print("- TaskService有问题")
        if not schema_ok:
            print("- 请求模型有问题")
        if not ffmpeg_ok:
            print("- FFmpeg支持有问题")

if __name__ == "__main__":
    asyncio.run(main())
