2025-07-13 08:41:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-13 08:41:05 | INFO     | main:lifespan:25 - Starting Video Translation AI Service
2025-07-13 08:41:05 | INFO     | main:lifespan:26 - Version: 1.0.0
2025-07-13 08:41:05 | INFO     | main:lifespan:27 - Environment: Development
2025-07-14 20:37:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:37:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:37:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:37:54 | INFO     | main:lifespan:25 - Starting Video Translation AI Service
2025-07-14 20:37:54 | INFO     | main:lifespan:26 - Version: 1.0.0
2025-07-14 20:37:54 | INFO     | main:lifespan:27 - Environment: Development
2025-07-14 20:40:12 | INFO     | main:lifespan:33 - Shutting down Video Translation AI Service
2025-07-14 20:40:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:40:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:40:18 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 20:40:18 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 20:40:18 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:09:22 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:12:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:12:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:12:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:12:49 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:12:49 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:12:49 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:15:53 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:16:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:16:06 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:16:06 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:16:06 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:16:06 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:16:06 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:16:16 | INFO     | app.services.base_service:create_task:32 - Created task bef3eca5-42ba-4103-9ebe-b36c96598714 of type full_pipeline
2025-07-14 21:16:16 | INFO     | app.services.task_service:process_full_pipeline:63 - Step 1: Extracting audio for task bef3eca5-42ba-4103-9ebe-b36c96598714
2025-07-14 21:16:16 | ERROR    | app.services.audio_service:extract_audio:108 - Audio extraction failed: Video file not found: /tmp/test_30s.mp4
2025-07-14 21:16:16 | ERROR    | app.services.task_service:process_full_pipeline:158 - Full pipeline failed for task bef3eca5-42ba-4103-9ebe-b36c96598714: Video file not found: /tmp/test_30s.mp4
2025-07-14 21:17:11 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:17:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:16 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:17:16 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:17:16 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:17:18 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:17:24 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:24 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:24 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:17:24 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:17:24 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:18:33 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:18:39 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:18:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:18:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:18:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:18:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:18:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:19:06 | INFO     | app.api.routes:upload_file:85 - 上传文件: test_30s.mp4 -> storage\uploads\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s.mp4
2025-07-14 21:19:06 | INFO     | app.api.routes:upload_file:92 - 文件上传成功: storage\uploads\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 21:19:06 | INFO     | app.services.base_service:create_task:32 - Created task cddc6cf0-e747-4a56-933a-4fe5a9ca2a01 of type audio_extraction
2025-07-14 21:19:06 | INFO     | app.services.base_service:update_task_status:40 - Task cddc6cf0-e747-4a56-933a-4fe5a9ca2a01 status updated to processing (progress: 0.0%)
2025-07-14 21:19:06 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s.mp4 to storage\outputs\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav
2025-07-14 21:19:20 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav (duration: 30.06s)
2025-07-14 21:19:20 | INFO     | app.services.base_service:update_task_status:40 - Task cddc6cf0-e747-4a56-933a-4fe5a9ca2a01 status updated to completed (progress: 100.0%)
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:25 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:25 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:26 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:27 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:27 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:29 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:30 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:30 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:48 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:48 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:48 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:50 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:50 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:50 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:51 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:51 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:56 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:56 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:56 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:58 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:58 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:58 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:59 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:59 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:02 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:02 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:06 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:07 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:10 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:10 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:11 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:12 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:26 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:20:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:32 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:20:32 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:20:32 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:20:39 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:20:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:20:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:20:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:20:48 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:20:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:53 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:20:53 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:20:53 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:20:59 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:05 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:05 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:05 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:10 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:15 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:15 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:15 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:15 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:15 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:15 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 'TaskService' object has no attribute 'tasks'
2025-07-14 21:21:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 'TaskService' object has no attribute 'tasks'
2025-07-14 21:21:19 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 'TaskService' object has no attribute 'tasks'
2025-07-14 21:21:20 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:26 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:26 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:26 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:29 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:34 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:34 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:34 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:51 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:57 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:07 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:13 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:16 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:21 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:21 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:21 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:21 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:21 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:23 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:29 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:29 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:29 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:31 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:36 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:36 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:36 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:39 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:45 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:45 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:45 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:47 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:52 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:52 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:52 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:52 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:52 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:55 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:23:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:00 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:23:00 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:23:00 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:23:08 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:08 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:08 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:23:08 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:23:08 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:10 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:23:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:16 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:23:16 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:23:16 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:25 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:26 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:26 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:29 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:29 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:30 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:31 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:39 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:39 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:41 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:24:13 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:24:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:24:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:25:12 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:25:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:25:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:12 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:26:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:26:55 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:26:55 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:26:55 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:26:55 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:27:13 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:27:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:27:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:03 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:19 | INFO     | app.api.routes:upload_file:85 - 上传文件: test_30s.mp4 -> storage\uploads\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s.mp4
2025-07-14 21:28:19 | INFO     | app.api.routes:upload_file:92 - 文件上传成功: storage\uploads\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 21:28:19 | INFO     | app.services.base_service:create_task:35 - Created task b0d82d58-2266-4ce6-ad34-e1f53160ced3 of type audio_extraction
2025-07-14 21:28:19 | INFO     | app.services.base_service:update_task_status:54 - Task b0d82d58-2266-4ce6-ad34-e1f53160ced3 status updated to processing (progress: 0.0%)
2025-07-14 21:28:19 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s.mp4 to storage\outputs\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s_audio.wav
2025-07-14 21:28:22 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s_audio.wav (duration: 30.06s)
2025-07-14 21:28:22 | INFO     | app.services.base_service:update_task_status:54 - Task b0d82d58-2266-4ce6-ad34-e1f53160ced3 status updated to completed (progress: 100.0%)
2025-07-14 21:29:28 | ERROR    | app.api.routes:upload_file:102 - 文件上传失败: 
2025-07-14 21:30:09 | INFO     | app.services.base_service:create_task:35 - Created task 7355bd93-ae52-40e8-8d0d-b5a836f9c015 of type translation
2025-07-14 21:30:09 | INFO     | app.services.base_service:update_task_status:54 - Task 7355bd93-ae52-40e8-8d0d-b5a836f9c015 status updated to processing (progress: 0.0%)
2025-07-14 21:30:09 | ERROR    | app.services.translation_service:_translate_with_openai:109 - OpenAI translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 21:30:09 | ERROR    | app.services.translation_service:translate_text:64 - Translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 21:30:09 | ERROR    | app.services.translation_service:process_translation:51 - Translation failed for task 7355bd93-ae52-40e8-8d0d-b5a836f9c015: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 21:30:09 | INFO     | app.services.base_service:update_task_status:54 - Task 7355bd93-ae52-40e8-8d0d-b5a836f9c015 status updated to failed (progress: 0.0%)
2025-07-14 21:34:31 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:34:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:38 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:34:38 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:34:38 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:34:42 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:34:47 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:47 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:47 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:34:47 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:34:47 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:53:28 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:53:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:53:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:53:34 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:53:34 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:53:34 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:56:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:56:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:56:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:56:16 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:56:16 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:56:16 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:56:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:57:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:57:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:58:10 | ERROR    | app.api.routes:upload_file:102 - 文件上传失败: 
2025-07-14 22:00:00 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:00:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:00:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:00:05 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:00:05 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:00:05 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:03:08 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:03:13 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:03:13 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:03:13 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:03:13 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:03:13 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:03:31 | INFO     | app.api.routes:upload_file:88 - 上传文件: 1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav -> storage\uploads\c22be5bc-aa9b-4e71-a1b4-650c6374424c_1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav
2025-07-14 22:03:31 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c22be5bc-aa9b-4e71-a1b4-650c6374424c_1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav (大小: 961964 bytes)
2025-07-14 22:03:31 | INFO     | app.services.base_service:create_task:35 - Created task 8d0d60b4-e5be-45f3-b3a2-5f0b9625c4ce of type speech_recognition
2025-07-14 22:03:31 | INFO     | app.services.base_service:update_task_status:54 - Task 8d0d60b4-e5be-45f3-b3a2-5f0b9625c4ce status updated to processing (progress: 0.0%)
2025-07-14 22:03:31 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:03:50 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\uploads\c22be5bc-aa9b-4e71-a1b4-650c6374424c_1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav
2025-07-14 22:03:57 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:03:57 | INFO     | app.services.base_service:update_task_status:54 - Task 8d0d60b4-e5be-45f3-b3a2-5f0b9625c4ce status updated to completed (progress: 100.0%)
2025-07-14 22:04:26 | INFO     | app.services.base_service:create_task:35 - Created task 168e8fbc-c75b-41f6-ba03-491679f71a43 of type translation
2025-07-14 22:04:26 | INFO     | app.services.base_service:update_task_status:54 - Task 168e8fbc-c75b-41f6-ba03-491679f71a43 status updated to processing (progress: 0.0%)
2025-07-14 22:04:26 | ERROR    | app.services.translation_service:_translate_with_openai:109 - OpenAI translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 22:04:26 | ERROR    | app.services.translation_service:translate_text:64 - Translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 22:04:26 | ERROR    | app.services.translation_service:process_translation:51 - Translation failed for task 168e8fbc-c75b-41f6-ba03-491679f71a43: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 22:04:26 | INFO     | app.services.base_service:update_task_status:54 - Task 168e8fbc-c75b-41f6-ba03-491679f71a43 status updated to failed (progress: 0.0%)
2025-07-14 22:05:48 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:05:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:05:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:05:53 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:05:53 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:05:53 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:05:57 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:06:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:02 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:06:02 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:06:02 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:06:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:06:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:06:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:27:21 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:27:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:27:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:27:29 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:27:29 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:27:29 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:28:02 | INFO     | app.services.base_service:create_task:35 - Created task 6ab4adb7-7827-4a29-8114-419401479991 of type translation
2025-07-14 22:28:02 | INFO     | app.services.base_service:update_task_status:54 - Task 6ab4adb7-7827-4a29-8114-419401479991 status updated to processing (progress: 0.0%)
2025-07-14 22:28:04 | INFO     | app.services.base_service:update_task_status:54 - Task 6ab4adb7-7827-4a29-8114-419401479991 status updated to completed (progress: 100.0%)
2025-07-14 22:28:22 | INFO     | app.services.base_service:create_task:35 - Created task 3479a310-c0c6-4d51-990c-238e258abec7 of type translation
2025-07-14 22:28:22 | INFO     | app.services.base_service:update_task_status:54 - Task 3479a310-c0c6-4d51-990c-238e258abec7 status updated to processing (progress: 0.0%)
2025-07-14 22:28:24 | INFO     | app.services.base_service:update_task_status:54 - Task 3479a310-c0c6-4d51-990c-238e258abec7 status updated to completed (progress: 100.0%)
2025-07-14 22:28:46 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s.mp4
2025-07-14 22:28:46 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 22:28:46 | INFO     | app.services.base_service:create_task:35 - Created task e0c28da2-bc8a-4ac0-bd72-69d1a33800de of type full_pipeline
2025-07-14 22:28:46 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 0.0%)
2025-07-14 22:28:46 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:28:46 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s.mp4 to storage\outputs\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s_audio.wav
2025-07-14 22:28:49 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s_audio.wav (duration: 30.06s)
2025-07-14 22:28:49 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 20.0%)
2025-07-14 22:28:49 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:28:49 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:28:50 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s_audio.wav
2025-07-14 22:28:57 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:28:57 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 40.0%)
2025-07-14 22:28:57 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:29:02 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 60.0%)
2025-07-14 22:29:02 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:30:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_d914f092.srt
2025-07-14 22:30:05 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 80.0%)
2025-07-14 22:30:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:30:05 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:78 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-14 22:30:10 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:99 - Edge TTS synthesis failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f60f12a32f3848bfaa126f8ecb6cbfec'
2025-07-14 22:30:10 | ERROR    | app.services.tts_service:synthesize_speech:68 - TTS synthesis failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f60f12a32f3848bfaa126f8ecb6cbfec'
2025-07-14 22:30:10 | ERROR    | app.services.task_service:process_full_pipeline:143 - Full pipeline failed for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f60f12a32f3848bfaa126f8ecb6cbfec'
2025-07-14 22:30:10 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to failed (progress: 0.0%)
2025-07-14 22:37:14 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:37:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:23 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:37:23 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:37:23 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:37:27 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:37:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:36 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:37:36 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:37:36 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:37:49 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:37:59 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:59 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:59 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:37:59 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:37:59 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:38:03 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:38:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:38:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:38:11 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:38:11 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:38:11 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:47:29 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:47:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:47:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:47:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:47:45 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:47:45 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:47:45 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:47:52 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4
2025-07-14 22:47:52 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 22:47:52 | INFO     | app.services.base_service:create_task:35 - Created task ed28fc8f-98be-4fee-9612-3500abd05b78 of type full_pipeline
2025-07-14 22:47:52 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 0.0%)
2025-07-14 22:47:52 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:47:52 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4 to storage\outputs\90204720-f2d2-429f-b904-ebc739420cdf_test_30s_audio.wav
2025-07-14 22:47:54 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\90204720-f2d2-429f-b904-ebc739420cdf_test_30s_audio.wav (duration: 30.06s)
2025-07-14 22:47:54 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 20.0%)
2025-07-14 22:47:54 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:47:54 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:47:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\90204720-f2d2-429f-b904-ebc739420cdf_test_30s_audio.wav
2025-07-14 22:48:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:48:02 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 40.0%)
2025-07-14 22:48:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:08 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 60.0%)
2025-07-14 22:48:08 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:26 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_5a6c980d.srt
2025-07-14 22:48:26 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 80.0%)
2025-07-14 22:48:26 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:26 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:88 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-14 22:48:32 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=11cd5cb82b5f44a4806973b45af059d4'
2025-07-14 22:48:32 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 2 seconds...
2025-07-14 22:48:40 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=2724d0adcacd411ba250d2cc20df142e'
2025-07-14 22:48:40 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 4 seconds...
2025-07-14 22:48:49 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=d997290446b24c97a102190515a6f3c3'
2025-07-14 22:48:49 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:136 - Edge TTS service unavailable, creating text fallback
2025-07-14 22:48:49 | INFO     | app.services.tts_service:_create_text_fallback:155 - Created text fallback: storage\outputs\tts_58ca81c0.txt
2025-07-14 22:48:49 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 90.0%)
2025-07-14 22:48:49 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:49 | INFO     | app.services.video_service:compose_video:76 - Composing video: storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4 + storage\outputs\tts_58ca81c0.txt
2025-07-14 22:48:50 | ERROR    | app.services.video_service:compose_video:127 - Video composition failed: MoviePy error: failed to read the duration of file storage\outputs\tts_58ca81c0.txt.
Here are the file infos returned by ffmpeg:

ffmpeg version 7.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers

  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)

  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband

  libavutil      59. 39.100 / 59. 39.100

  libavcodec     61. 19.100 / 61. 19.100

  libavformat    61.  7.100 / 61.  7.100

  libavdevice    61.  3.100 / 61.  3.100

  libavfilter    10.  4.100 / 10.  4.100

  libswscale      8.  3.100 /  8.  3.100

  libswresample   5.  3.100 /  5.  3.100

  libpostproc    58.  3.100 / 58.  3.100

[in#0 @ 000002351c277280] Error opening input: Invalid data found when processing input

Error opening input file storage\outputs\tts_58ca81c0.txt.

Error opening input files: Invalid data found when processing input


2025-07-14 22:48:50 | ERROR    | app.services.task_service:process_full_pipeline:143 - Full pipeline failed for task ed28fc8f-98be-4fee-9612-3500abd05b78: MoviePy error: failed to read the duration of file storage\outputs\tts_58ca81c0.txt.
Here are the file infos returned by ffmpeg:

ffmpeg version 7.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers

  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)

  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband

  libavutil      59. 39.100 / 59. 39.100

  libavcodec     61. 19.100 / 61. 19.100

  libavformat    61.  7.100 / 61.  7.100

  libavdevice    61.  3.100 / 61.  3.100

  libavfilter    10.  4.100 / 10.  4.100

  libswscale      8.  3.100 /  8.  3.100

  libswresample   5.  3.100 /  5.  3.100

  libpostproc    58.  3.100 / 58.  3.100

[in#0 @ 000002351c277280] Error opening input: Invalid data found when processing input

Error opening input file storage\outputs\tts_58ca81c0.txt.

Error opening input files: Invalid data found when processing input


2025-07-14 22:48:50 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to failed (progress: 0.0%)
2025-07-14 22:50:24 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:50:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:50:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:50:32 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:50:32 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:50:32 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:50:53 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:52:50 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:52:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:52:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:52:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:52:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:52:57 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:53:10 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4
2025-07-14 22:53:10 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 22:53:10 | INFO     | app.services.base_service:create_task:35 - Created task a853327d-7409-4037-9f14-22c225b57039 of type full_pipeline
2025-07-14 22:53:10 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 0.0%)
2025-07-14 22:53:10 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:53:10 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4 to storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_audio.wav
2025-07-14 22:53:12 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_audio.wav (duration: 30.06s)
2025-07-14 22:53:12 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 20.0%)
2025-07-14 22:53:12 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:53:12 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:53:13 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_audio.wav
2025-07-14 22:53:20 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:53:20 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 40.0%)
2025-07-14 22:53:20 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:53:26 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 60.0%)
2025-07-14 22:53:26 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:56:45 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_9be3b58f.srt
2025-07-14 22:56:45 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 80.0%)
2025-07-14 22:56:45 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:56:45 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:88 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-14 22:56:50 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=44e4f1ae845645608e5ca295082998a7'
2025-07-14 22:56:50 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 2 seconds...
2025-07-14 22:56:54 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=a8a577a22d6340039f104431e05b0cbd'
2025-07-14 22:56:54 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 4 seconds...
2025-07-14 22:56:59 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=0fab5d71d9444f63b8203f6aa3ac29d0'
2025-07-14 22:56:59 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:136 - Edge TTS service unavailable, creating text fallback
2025-07-14 22:56:59 | INFO     | app.services.tts_service:_create_text_fallback:155 - Created text fallback: storage\outputs\tts_20592537.txt
2025-07-14 22:56:59 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 90.0%)
2025-07-14 22:56:59 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:56:59 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4 + storage\outputs\tts_20592537.txt
2025-07-14 22:56:59 | WARNING  | app.services.video_service:compose_video:86 - TTS fallback detected, creating video without audio replacement
2025-07-14 22:56:59 | WARNING  | app.services.video_service:compose_video:118 - Failed to add text overlay: MoviePy Error: creation of None failed because of the following error:

[WinError 2] 系统找不到指定的文件。.

.This error can be due to the fact that ImageMagick is not installed on your computer, or (for Windows users) that you didn't specify the path to the ImageMagick binary in file conf.py, or that the path you specified is incorrect
2025-07-14 22:56:59 | INFO     | app.services.video_service:compose_video:131 - Adding subtitles: storage\outputs\subtitles_9be3b58f.srt
2025-07-14 22:56:59 | WARNING  | app.services.video_service:compose_video:136 - Failed to add subtitles: module 'moviepy.editor' has no attribute 'SubtitlesClip'
2025-07-14 22:57:14 | INFO     | app.services.video_service:compose_video:168 - Video composition completed: storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_translated.mp4
2025-07-14 22:57:14 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to completed (progress: 100.0%)
2025-07-14 22:57:14 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:57:14 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task a853327d-7409-4037-9f14-22c225b57039 completed with fallbacks: TTS service was unavailable - text content saved instead of audio; Video created with original audio due to TTS unavailability
2025-07-15 20:10:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:10:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:10:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:10:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:10:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:10:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:11:22 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4
2025-07-15 20:11:22 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:11:22 | INFO     | app.services.base_service:create_task:35 - Created task 7fe79985-040f-4ffd-ab8b-de1fde856f7f of type full_pipeline
2025-07-15 20:11:22 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 0.0%)
2025-07-15 20:11:22 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:11:22 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4 to storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_audio.wav
2025-07-15 20:11:26 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:11:26 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 20.0%)
2025-07-15 20:11:26 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:11:26 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:11:27 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_audio.wav
2025-07-15 20:11:33 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:11:33 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 40.0%)
2025-07-15 20:11:33 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:11:39 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 60.0%)
2025-07-15 20:11:39 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:02 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_bb3f672e.srt
2025-07-15 20:12:02 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 80.0%)
2025-07-15 20:12:02 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:02 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:88 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:12:08 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=733bfedb9e8f43dfae18b04f74f70e15'
2025-07-15 20:12:08 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:12:16 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=bab290597fe5441cb7fa82877c05905e'
2025-07-15 20:12:16 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:12:25 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=5e219da5801e4bc3878e9fc122f4481b'
2025-07-15 20:12:25 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:136 - Edge TTS service unavailable, creating text fallback
2025-07-15 20:12:25 | INFO     | app.services.tts_service:_create_text_fallback:155 - Created text fallback: storage\outputs\tts_da91097b.txt
2025-07-15 20:12:25 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 90.0%)
2025-07-15 20:12:25 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:25 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4 + storage\outputs\tts_da91097b.txt
2025-07-15 20:12:26 | WARNING  | app.services.video_service:compose_video:86 - TTS fallback detected, creating video without audio replacement
2025-07-15 20:12:26 | WARNING  | app.services.video_service:compose_video:118 - Failed to add text overlay: MoviePy Error: creation of None failed because of the following error:

[WinError 2] 系统找不到指定的文件。.

.This error can be due to the fact that ImageMagick is not installed on your computer, or (for Windows users) that you didn't specify the path to the ImageMagick binary in file conf.py, or that the path you specified is incorrect
2025-07-15 20:12:26 | INFO     | app.services.video_service:compose_video:131 - Adding subtitles: storage\outputs\subtitles_bb3f672e.srt
2025-07-15 20:12:26 | WARNING  | app.services.video_service:compose_video:136 - Failed to add subtitles: module 'moviepy.editor' has no attribute 'SubtitlesClip'
2025-07-15 20:12:41 | INFO     | app.services.video_service:compose_video:168 - Video composition completed: storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_translated.mp4
2025-07-15 20:12:41 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to completed (progress: 100.0%)
2025-07-15 20:12:41 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:41 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f completed with fallbacks: TTS service was unavailable - text content saved instead of audio; Video created with original audio due to TTS unavailability
2025-07-15 20:16:34 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:16:42 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:42 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:42 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:16:42 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:16:42 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:16:48 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:16:56 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:56 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:56 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:16:56 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:16:56 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:17:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:17:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:09 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:17:09 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:17:09 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:17:17 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:17:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:17:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:17:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:18:26 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:18:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:35 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:18:35 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:18:35 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:18:49 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:18:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:18:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:18:57 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:21:32 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:21:40 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:40 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:40 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:21:40 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:21:40 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:21:43 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:21:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:21:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:21:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:21:57 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:22:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:04 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:22:04 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:22:04 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:22:10 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:22:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:17 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:22:17 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:22:17 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:22:37 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:22:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:54 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:22:54 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:22:54 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:22:57 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4
2025-07-15 20:22:57 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:22:57 | INFO     | app.services.base_service:create_task:35 - Created task 47d57faf-4394-457a-95fa-b77b89888e1a of type full_pipeline
2025-07-15 20:22:57 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 0.0%)
2025-07-15 20:22:57 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:22:57 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4 to storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_audio.wav
2025-07-15 20:22:59 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:22:59 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 20.0%)
2025-07-15 20:22:59 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:22:59 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:23:00 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_audio.wav
2025-07-15 20:23:06 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:23:06 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 40.0%)
2025-07-15 20:23:06 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:13 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 60.0%)
2025-07-15 20:23:13 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:33 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_1fc2d981.srt
2025-07-15 20:23:33 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 80.0%)
2025-07-15 20:23:33 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:33 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:94 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:23:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=bd646e4c435d4efc8d494e90ce1ce71d'
2025-07-15 20:23:39 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:23:47 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=b3a7620097054883af04cb4c8571f563'
2025-07-15 20:23:47 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:23:57 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=ce8e317bb8e244b48f0fe3f591492060'
2025-07-15 20:23:57 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:142 - Edge TTS service unavailable, creating text fallback
2025-07-15 20:23:57 | INFO     | app.services.tts_service:_create_text_fallback:161 - Created text fallback: storage\outputs\tts_128461c0.txt
2025-07-15 20:23:57 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 90.0%)
2025-07-15 20:23:57 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:57 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4 + storage\outputs\tts_128461c0.txt
2025-07-15 20:23:58 | WARNING  | app.services.video_service:compose_video:86 - TTS fallback detected, creating video without audio replacement
2025-07-15 20:23:58 | INFO     | app.services.video_service:compose_video:96 - TTS fallback detected - text content available in storage\outputs\tts_128461c0.txt
2025-07-15 20:23:58 | INFO     | app.services.video_service:compose_video:109 - Subtitle file generated: storage\outputs\subtitles_1fc2d981.srt
2025-07-15 20:23:58 | INFO     | app.services.video_service:compose_video:110 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 20:24:12 | INFO     | app.services.video_service:compose_video:142 - Video composition completed: storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_translated.mp4
2025-07-15 20:24:12 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to completed (progress: 100.0%)
2025-07-15 20:24:12 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:24:12 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 47d57faf-4394-457a-95fa-b77b89888e1a completed with fallbacks: TTS service was unavailable - text content saved instead of audio; Video created with original audio due to TTS unavailability
2025-07-15 20:25:21 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:25:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:25:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:25:30 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:25:30 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:25:30 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:28:14 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:28:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:28:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:28:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:28:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:28:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:29:04 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:29:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:29:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:29:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:29:25 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:29:37 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:37 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:37 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:29:37 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:29:37 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:30:52 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:31:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:31:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:31:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:31:09 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:31:09 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:31:09 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:31:24 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4
2025-07-15 20:31:24 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:31:24 | INFO     | app.services.base_service:create_task:35 - Created task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 of type full_pipeline
2025-07-15 20:31:24 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 0.0%)
2025-07-15 20:31:24 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:31:24 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4 to storage\outputs\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s_audio.wav
2025-07-15 20:31:26 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:31:26 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 20.0%)
2025-07-15 20:31:26 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:31:26 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:31:27 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s_audio.wav
2025-07-15 20:31:33 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:31:33 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 40.0%)
2025-07-15 20:31:33 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:31:41 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 60.0%)
2025-07-15 20:31:41 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:32:04 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_b070580d.srt
2025-07-15 20:32:04 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 80.0%)
2025-07-15 20:32:04 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:32:04 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:94 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:32:09 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=dff30961603948058733d3b3b8ef8a2f'
2025-07-15 20:32:09 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:32:17 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=1e303e50e9d54311a06f244a5c347be5'
2025-07-15 20:32:17 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:32:26 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=81bcbeda903c4d5baaef5658aecfdeb2'
2025-07-15 20:32:26 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:142 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 20:32:26 | INFO     | app.services.tts_service:_synthesize_with_gtts:185 - Synthesizing speech with gTTS
2025-07-15 20:32:51 | INFO     | app.services.tts_service:_synthesize_with_gtts:219 - gTTS synthesis completed: storage\outputs\tts_050059b1.mp3
2025-07-15 20:32:51 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 90.0%)
2025-07-15 20:32:51 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:32:51 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4 + storage\outputs\tts_050059b1.mp3
2025-07-15 20:32:51 | INFO     | app.services.video_service:compose_video:109 - Subtitle file generated: storage\outputs\subtitles_b070580d.srt
2025-07-15 20:32:51 | INFO     | app.services.video_service:compose_video:110 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 20:32:51 | ERROR    | app.services.video_service:compose_video:146 - Video composition failed: 'NoneType' object has no attribute 'get_frame'
2025-07-15 20:32:51 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6: 'NoneType' object has no attribute 'get_frame'
2025-07-15 20:32:51 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to failed (progress: 0.0%)
2025-07-15 20:34:41 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:34:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:34:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:34:49 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:34:49 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:34:49 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:34:54 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:35:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:02 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:35:02 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:35:02 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:35:12 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:35:20 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:20 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:20 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:35:20 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:35:20 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:39:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:39:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:39:19 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:39:19 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:39:19 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:39:19 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:39:19 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:42:24 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:42:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:42:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:42:34 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:42:34 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:42:34 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:44:07 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:44:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:44:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:44:17 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:44:17 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:44:17 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:47:39 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4
2025-07-15 20:47:39 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:47:39 | INFO     | app.services.base_service:create_task:35 - Created task f7a436b1-a897-416a-b674-f22c205feaae of type full_pipeline
2025-07-15 20:47:39 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 0.0%)
2025-07-15 20:47:39 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:47:39 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4 to storage\outputs\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s_audio.wav
2025-07-15 20:47:41 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:47:41 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 20.0%)
2025-07-15 20:47:41 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:47:41 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:47:42 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s_audio.wav
2025-07-15 20:47:49 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:47:49 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 40.0%)
2025-07-15 20:47:49 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:47:57 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 60.0%)
2025-07-15 20:47:57 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:48:20 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_132ade3e.srt
2025-07-15 20:48:20 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 80.0%)
2025-07-15 20:48:20 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:48:20 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:94 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:48:25 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=95a4f93a88e341bd9816ae0fe56ec835'
2025-07-15 20:48:25 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:48:33 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=0adddeef473f4aeab4906f320aeebb7a'
2025-07-15 20:48:33 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:48:43 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=93465d550e8741df85350fc32efc8259'
2025-07-15 20:48:43 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:142 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 20:48:43 | INFO     | app.services.tts_service:_synthesize_with_gtts:185 - Synthesizing speech with gTTS
2025-07-15 20:49:02 | INFO     | app.services.tts_service:_synthesize_with_gtts:237 - gTTS synthesis completed: storage\outputs\tts_b4845af4.mp3
2025-07-15 20:49:02 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 90.0%)
2025-07-15 20:49:02 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:49:02 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4 + storage\outputs\tts_b4845af4.mp3
2025-07-15 20:49:03 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_132ade3e.srt
2025-07-15 20:49:03 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 20:49:03 | ERROR    | app.services.video_service:compose_video:161 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 20:49:03 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task f7a436b1-a897-416a-b674-f22c205feaae: 'NoneType' object has no attribute 'stdout'
2025-07-15 20:49:03 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to failed (progress: 0.0%)
2025-07-15 20:56:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:56:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:56:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:56:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:56:27 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:56:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:38 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:56:38 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:56:38 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:56:41 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:56:50 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:50 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:50 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:56:50 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:56:50 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:57:43 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:57:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:57:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:57:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:57:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:57:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:08:06 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:08:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:08:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:08:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:08:31 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:08:31 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:08:31 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:08:36 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4
2025-07-15 21:08:36 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:08:36 | INFO     | app.services.base_service:create_task:35 - Created task 47164a92-6e81-4a5c-acaf-372876c629d0 of type full_pipeline
2025-07-15 21:08:36 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 0.0%)
2025-07-15 21:08:36 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:08:36 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4 to storage\outputs\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s_audio.wav
2025-07-15 21:08:39 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:08:39 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 20.0%)
2025-07-15 21:08:39 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:08:39 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:08:40 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s_audio.wav
2025-07-15 21:08:46 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:08:46 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 40.0%)
2025-07-15 21:08:46 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:08:54 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 60.0%)
2025-07-15 21:08:54 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:09:16 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_13a89206.srt
2025-07-15 21:09:16 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 80.0%)
2025-07-15 21:09:16 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:09:16 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:112 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 21:09:22 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=220ee9a619114687bcb99b7522ed8220'
2025-07-15 21:09:22 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 2 seconds...
2025-07-15 21:09:30 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=2cdddee181174a0fb80e8d36cffd82b6'
2025-07-15 21:09:30 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 4 seconds...
2025-07-15 21:09:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=7c77674cde2a498f954581c8613ee08d'
2025-07-15 21:09:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:160 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 21:09:39 | INFO     | app.services.tts_service:_synthesize_with_gtts:203 - Synthesizing speech with gTTS
2025-07-15 21:10:03 | INFO     | app.services.tts_service:_synthesize_with_gtts:255 - gTTS synthesis completed: storage\outputs\tts_4812dfc9.mp3
2025-07-15 21:10:03 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 90.0%)
2025-07-15 21:10:03 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:10:03 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4 + storage\outputs\tts_4812dfc9.mp3
2025-07-15 21:10:04 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_13a89206.srt
2025-07-15 21:10:04 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:10:04 | ERROR    | app.services.video_service:compose_video:161 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:10:04 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 47164a92-6e81-4a5c-acaf-372876c629d0: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:10:04 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to failed (progress: 0.0%)
2025-07-15 21:12:28 | INFO     | app.services.base_service:create_task:35 - Created task 9c43e902-e50b-4deb-a210-774399a97b40 of type translation
2025-07-15 21:12:28 | INFO     | app.services.base_service:update_task_status:54 - Task 9c43e902-e50b-4deb-a210-774399a97b40 status updated to processing (progress: 0.0%)
2025-07-15 21:12:33 | INFO     | app.services.base_service:update_task_status:54 - Task 9c43e902-e50b-4deb-a210-774399a97b40 status updated to completed (progress: 100.0%)
2025-07-15 21:12:59 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:13:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:07 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:13:07 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:13:07 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:13:17 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:13:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:13:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:13:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:19:18 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:19:28 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:19:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:19:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:19:35 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:19:35 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:19:35 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:19:43 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4
2025-07-15 21:19:43 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:19:43 | INFO     | app.services.base_service:create_task:35 - Created task 8819e12d-13ab-4623-a522-50b58781924d of type full_pipeline
2025-07-15 21:19:43 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 0.0%)
2025-07-15 21:19:43 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:19:43 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4 to storage\outputs\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s_audio.wav
2025-07-15 21:19:45 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:19:45 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 20.0%)
2025-07-15 21:19:45 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:19:45 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:19:46 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s_audio.wav
2025-07-15 21:19:53 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:19:53 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 40.0%)
2025-07-15 21:19:53 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:20:01 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 60.0%)
2025-07-15 21:20:01 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:20:25 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_61a8c264.srt
2025-07-15 21:20:25 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 80.0%)
2025-07-15 21:20:25 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:20:25 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:112 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 21:20:31 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=9e7b38b6499d4e35b82cf013a18e4421'
2025-07-15 21:20:31 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 2 seconds...
2025-07-15 21:20:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=7cc03d26bfcb4d038b6fab826c3d085e'
2025-07-15 21:20:39 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 4 seconds...
2025-07-15 21:20:48 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=4a7dd817243b46c0a0790aa7fb05070b'
2025-07-15 21:20:48 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:160 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 21:20:48 | INFO     | app.services.tts_service:_synthesize_with_gtts:203 - Synthesizing speech with gTTS
2025-07-15 21:21:13 | INFO     | app.services.tts_service:_synthesize_with_gtts:255 - gTTS synthesis completed: storage\outputs\tts_9c055230.mp3
2025-07-15 21:21:13 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 90.0%)
2025-07-15 21:21:13 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:21:13 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4 + storage\outputs\tts_9c055230.mp3
2025-07-15 21:21:14 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_61a8c264.srt
2025-07-15 21:21:14 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:21:14 | ERROR    | app.services.video_service:compose_video:140 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | ERROR    | app.services.video_service:compose_video:153 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | ERROR    | app.services.video_service:compose_video:186 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 8819e12d-13ab-4623-a522-50b58781924d: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to failed (progress: 0.0%)
2025-07-15 21:26:37 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:26:37 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:26:37 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:26:37 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:26:37 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:28:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:28:49 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:28:49 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:28:49 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:29:41 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4
2025-07-15 21:29:41 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:29:41 | INFO     | app.services.base_service:create_task:35 - Created task 73842441-d76c-4200-bd81-90f62b4940db of type full_pipeline
2025-07-15 21:29:41 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 0.0%)
2025-07-15 21:29:41 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:29:41 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4 to storage\outputs\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s_audio.wav
2025-07-15 21:29:44 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:29:44 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 20.0%)
2025-07-15 21:29:44 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:29:44 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:29:45 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s_audio.wav
2025-07-15 21:29:52 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:29:52 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 40.0%)
2025-07-15 21:29:52 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:30:01 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 60.0%)
2025-07-15 21:30:01 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:30:29 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_22f992e1.srt
2025-07-15 21:30:29 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 80.0%)
2025-07-15 21:30:29 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:30:29 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:112 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 21:30:34 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=6aff3451e5494f7ebb3befe024c1969b'
2025-07-15 21:30:34 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 2 seconds...
2025-07-15 21:30:42 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=79e257eab97d4b7497bfff52470f6d00'
2025-07-15 21:30:42 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 4 seconds...
2025-07-15 21:30:52 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=18e9a2d564cb4ac09e06e20bf059920b'
2025-07-15 21:30:52 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:160 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 21:30:52 | INFO     | app.services.tts_service:_synthesize_with_gtts:203 - Synthesizing speech with gTTS
2025-07-15 21:31:17 | INFO     | app.services.tts_service:_synthesize_with_gtts:255 - gTTS synthesis completed: storage\outputs\tts_9f2d504c.mp3
2025-07-15 21:31:17 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 90.0%)
2025-07-15 21:31:17 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:31:17 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4 + storage\outputs\tts_9f2d504c.mp3
2025-07-15 21:31:18 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_22f992e1.srt
2025-07-15 21:31:18 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:31:18 | ERROR    | app.services.video_service:compose_video:140 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | ERROR    | app.services.video_service:compose_video:153 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | ERROR    | app.services.video_service:compose_video:186 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 73842441-d76c-4200-bd81-90f62b4940db: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to failed (progress: 0.0%)
2025-07-15 21:41:30 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:41:39 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:41:39 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:41:39 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:41:39 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:41:46 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4
2025-07-15 21:41:46 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:41:46 | INFO     | app.services.base_service:create_task:35 - Created task 83e023b4-0496-4934-8f3f-14dcc1b6434d of type full_pipeline
2025-07-15 21:41:46 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 0.0%)
2025-07-15 21:41:46 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:41:46 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4 to storage\outputs\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s_audio.wav
2025-07-15 21:41:48 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:41:48 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 20.0%)
2025-07-15 21:41:48 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:41:48 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:41:49 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s_audio.wav
2025-07-15 21:41:56 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:41:56 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 40.0%)
2025-07-15 21:41:56 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:04 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 60.0%)
2025-07-15 21:42:04 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:28 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_e1519e44.srt
2025-07-15 21:42:28 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 80.0%)
2025-07-15 21:42:28 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:28 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 21:42:28 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 21:42:28 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_e04fa480.wav
2025-07-15 21:42:28 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 90.0%)
2025-07-15 21:42:28 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:28 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4 + storage\outputs\tts_e04fa480.wav
2025-07-15 21:42:29 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_e1519e44.srt
2025-07-15 21:42:29 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:42:29 | ERROR    | app.services.video_service:compose_video:140 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | ERROR    | app.services.video_service:compose_video:153 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | ERROR    | app.services.video_service:compose_video:186 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 83e023b4-0496-4934-8f3f-14dcc1b6434d: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to failed (progress: 0.0%)
2025-07-15 21:51:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:51:30 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:51:30 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:51:30 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:52:22 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4
2025-07-15 21:52:22 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:52:22 | INFO     | app.services.base_service:create_task:35 - Created task c9a625e1-be77-4c57-bc42-095c725f7f74 of type full_pipeline
2025-07-15 21:52:22 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 0.0%)
2025-07-15 21:52:22 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:52:22 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4 to storage\outputs\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s_audio.wav
2025-07-15 21:52:25 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:52:25 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 20.0%)
2025-07-15 21:52:25 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:52:25 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:52:26 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s_audio.wav
2025-07-15 21:52:32 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:52:32 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 40.0%)
2025-07-15 21:52:32 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:52:42 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 60.0%)
2025-07-15 21:52:42 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:53:06 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_71414528.srt
2025-07-15 21:53:06 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 80.0%)
2025-07-15 21:53:06 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:53:06 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 21:53:06 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 21:53:06 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_b22d8096.wav
2025-07-15 21:53:06 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 90.0%)
2025-07-15 21:53:06 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:53:06 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4 + storage\outputs\tts_b22d8096.wav
2025-07-15 21:53:07 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_71414528.srt
2025-07-15 21:53:07 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:146 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:157 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:164 - All video writing methods failed: name 'video_file_path' is not defined
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:196 - Video composition failed: name 'video_file_path' is not defined
2025-07-15 21:53:07 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task c9a625e1-be77-4c57-bc42-095c725f7f74: name 'video_file_path' is not defined
2025-07-15 21:53:07 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to failed (progress: 0.0%)
2025-07-15 21:56:34 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:56:42 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:56:42 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:56:42 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:56:42 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:56:48 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4
2025-07-15 21:56:48 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:56:48 | INFO     | app.services.base_service:create_task:35 - Created task fed354db-2512-498f-b5fc-28c0b5847aef of type full_pipeline
2025-07-15 21:56:48 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 0.0%)
2025-07-15 21:56:48 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:56:48 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4 to storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_audio.wav
2025-07-15 21:56:50 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:56:50 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 20.0%)
2025-07-15 21:56:50 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:56:50 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:56:51 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_audio.wav
2025-07-15 21:56:58 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:56:58 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 40.0%)
2025-07-15 21:56:58 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:05 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 60.0%)
2025-07-15 21:57:05 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:29 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_f5c75c4f.srt
2025-07-15 21:57:29 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 80.0%)
2025-07-15 21:57:29 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:29 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 21:57:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 21:57:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_d1b4077d.wav
2025-07-15 21:57:29 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 90.0%)
2025-07-15 21:57:29 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:29 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4 + storage\outputs\tts_d1b4077d.wav
2025-07-15 21:57:30 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_f5c75c4f.srt
2025-07-15 21:57:30 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:57:30 | INFO     | app.services.video_service:compose_video:138 - 开始写入视频文件: storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_translated.mp4
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:153 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:164 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:171 - All video writing methods failed: name 'video_file_path' is not defined
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:203 - Video composition failed: name 'video_file_path' is not defined
2025-07-15 21:57:30 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task fed354db-2512-498f-b5fc-28c0b5847aef: name 'video_file_path' is not defined
2025-07-15 21:57:30 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to failed (progress: 0.0%)
2025-07-15 22:03:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:03:05 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:03:05 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:03:05 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:03:49 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4
2025-07-15 22:03:49 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:03:49 | INFO     | app.services.base_service:create_task:35 - Created task c622dda5-a6cd-4bf4-a92a-130f3647cce5 of type full_pipeline
2025-07-15 22:03:49 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 0.0%)
2025-07-15 22:03:49 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:03:49 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4 to storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_audio.wav
2025-07-15 22:03:51 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:03:51 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 20.0%)
2025-07-15 22:03:51 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:03:51 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:03:52 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_audio.wav
2025-07-15 22:03:59 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:03:59 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 40.0%)
2025-07-15 22:03:59 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:07 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 60.0%)
2025-07-15 22:04:07 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:30 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_2bc38c10.srt
2025-07-15 22:04:30 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 80.0%)
2025-07-15 22:04:30 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:30 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:04:30 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 22:04:30 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_443eee48.wav
2025-07-15 22:04:30 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 90.0%)
2025-07-15 22:04:30 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4 + storage\outputs\tts_443eee48.wav
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_2bc38c10.srt
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:138 - 开始写入视频文件: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_translated.mp4
2025-07-15 22:04:31 | ERROR    | app.services.video_service:compose_video:149 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:04:31 | WARNING  | app.services.video_service:compose_video:154 - Used original video as fallback: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_translated.mp4
2025-07-15 22:04:31 | INFO     | app.services.video_service:compose_video:190 - Video composition completed: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_translated.mp4
2025-07-15 22:04:31 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to completed (progress: 100.0%)
2025-07-15 22:04:31 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:31 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:09:21 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:09:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:09:31 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:09:31 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:09:31 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:09:52 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4
2025-07-15 22:09:52 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:09:52 | INFO     | app.services.base_service:create_task:35 - Created task eaa8dae3-5601-407d-b49d-265e034c4ba5 of type full_pipeline
2025-07-15 22:09:52 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 0.0%)
2025-07-15 22:09:52 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:09:52 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4 to storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_audio.wav
2025-07-15 22:09:55 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:09:55 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 20.0%)
2025-07-15 22:09:55 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:09:55 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:09:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_audio.wav
2025-07-15 22:10:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:10:02 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 40.0%)
2025-07-15 22:10:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:09 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 60.0%)
2025-07-15 22:10:09 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:33 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_0ed3fd3a.srt
2025-07-15 22:10:33 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 80.0%)
2025-07-15 22:10:33 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:33 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:10:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:10:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_cf99d3c5.wav
2025-07-15 22:10:33 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 90.0%)
2025-07-15 22:10:33 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:33 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4 + storage\outputs\tts_cf99d3c5.wav
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_0ed3fd3a.srt
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:138 - 开始写入视频文件: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_translated.mp4
2025-07-15 22:10:34 | ERROR    | app.services.video_service:compose_video:149 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:10:34 | WARNING  | app.services.video_service:compose_video:154 - Used original video as fallback: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_translated.mp4
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:190 - Video composition completed: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_translated.mp4
2025-07-15 22:10:34 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to completed (progress: 100.0%)
2025-07-15 22:10:34 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:34 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:14:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:14:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:14:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:14:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:20:06 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:20:06 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:20:06 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:20:06 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:20:06 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:20:24 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4
2025-07-15 22:20:24 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:20:24 | INFO     | app.services.base_service:create_task:35 - Created task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad of type full_pipeline
2025-07-15 22:20:24 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 0.0%)
2025-07-15 22:20:24 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:20:24 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4 to storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_audio.wav
2025-07-15 22:20:26 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:20:26 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 20.0%)
2025-07-15 22:20:26 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:20:26 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:20:27 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_audio.wav
2025-07-15 22:20:34 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:20:34 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 40.0%)
2025-07-15 22:20:34 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:20:42 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 60.0%)
2025-07-15 22:20:42 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_1274953b.srt
2025-07-15 22:21:05 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 80.0%)
2025-07-15 22:21:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:21:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:21:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_846a7587.wav
2025-07-15 22:21:05 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 90.0%)
2025-07-15 22:21:05 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:05 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4 + storage\outputs\tts_846a7587.wav
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_1274953b.srt
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:134 - 开始写入视频文件: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_translated.mp4
2025-07-15 22:21:06 | ERROR    | app.services.video_service:compose_video:148 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:21:06 | WARNING  | app.services.video_service:compose_video:153 - 使用原始视频作为后备: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_translated.mp4
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:181 - Video composition completed: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_translated.mp4
2025-07-15 22:21:06 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to completed (progress: 100.0%)
2025-07-15 22:21:06 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:06 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:23:15 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:23:15 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:23:15 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:23:15 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:23:31 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s.mp4
2025-07-15 22:23:31 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:23:31 | INFO     | app.services.base_service:create_task:35 - Created task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e of type full_pipeline
2025-07-15 22:23:31 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 0.0%)
2025-07-15 22:23:31 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:23:31 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s.mp4 to storage\outputs\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s_audio.wav
2025-07-15 22:23:34 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:23:34 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 20.0%)
2025-07-15 22:23:34 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:23:34 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:23:35 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s_audio.wav
2025-07-15 22:23:41 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:23:41 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 40.0%)
2025-07-15 22:23:41 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:23:48 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 60.0%)
2025-07-15 22:23:48 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:24:11 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_18baf175.srt
2025-07-15 22:24:11 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 80.0%)
2025-07-15 22:24:11 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:24:11 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:24:11 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:24:11 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_185b469b.wav
2025-07-15 22:24:11 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 90.0%)
2025-07-15 22:24:11 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:24:11 | ERROR    | app.services.video_service:compose_video:193 - Video composition failed: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:24:11 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:24:11 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to failed (progress: 0.0%)
2025-07-15 22:29:20 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:29:20 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:29:20 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:29:20 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:29:51 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4
2025-07-15 22:29:51 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:29:51 | INFO     | app.services.base_service:create_task:35 - Created task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a of type full_pipeline
2025-07-15 22:29:51 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 0.0%)
2025-07-15 22:29:51 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:29:51 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4 to storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_audio.wav
2025-07-15 22:29:54 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:29:54 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 20.0%)
2025-07-15 22:29:54 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:29:54 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:29:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_audio.wav
2025-07-15 22:30:01 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:30:01 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 40.0%)
2025-07-15 22:30:01 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:09 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 60.0%)
2025-07-15 22:30:09 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:34 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_0eaf17f6.srt
2025-07-15 22:30:34 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 80.0%)
2025-07-15 22:30:34 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:34 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:30:34 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:30:34 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_a56f15a8.wav
2025-07-15 22:30:34 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 90.0%)
2025-07-15 22:30:34 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:34 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4 + storage\outputs\tts_a56f15a8.wav
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_0eaf17f6.srt
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:134 - 开始写入视频文件: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_translated.mp4
2025-07-15 22:30:35 | ERROR    | app.services.video_service:compose_video:149 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:30:35 | WARNING  | app.services.video_service:compose_video:154 - 使用原始视频作为后备: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_translated.mp4
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:182 - Video composition completed: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_translated.mp4
2025-07-15 22:30:35 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to completed (progress: 100.0%)
2025-07-15 22:30:35 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:35 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:34:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:34:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:34:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:34:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:35:20 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s.mp4
2025-07-15 22:35:20 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:35:20 | INFO     | app.services.base_service:create_task:35 - Created task 7de0cfbf-ecfd-4837-b765-3419233e962f of type full_pipeline
2025-07-15 22:35:20 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 0.0%)
2025-07-15 22:35:20 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:35:20 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s.mp4 to storage\outputs\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s_audio.wav
2025-07-15 22:35:22 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:35:22 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 20.0%)
2025-07-15 22:35:22 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:35:22 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:35:23 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s_audio.wav
2025-07-15 22:35:29 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:35:29 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 40.0%)
2025-07-15 22:35:29 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:35:38 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 60.0%)
2025-07-15 22:35:38 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:36:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_bf8900f1.srt
2025-07-15 22:36:05 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 80.0%)
2025-07-15 22:36:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:36:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:36:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:36:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_30dd706b.wav
2025-07-15 22:36:05 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 90.0%)
2025-07-15 22:36:05 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:36:05 | ERROR    | app.services.video_service:compose_video:202 - Video composition failed: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:36:05 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 7de0cfbf-ecfd-4837-b765-3419233e962f: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:36:05 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to failed (progress: 0.0%)
2025-07-15 22:39:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:39:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:39:11 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:39:11 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:39:11 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:39:39 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4
2025-07-15 22:39:39 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:39:39 | INFO     | app.services.base_service:create_task:35 - Created task 9c710363-3630-4eee-b349-fd967bafe3a3 of type full_pipeline
2025-07-15 22:39:39 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 0.0%)
2025-07-15 22:39:39 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:39:39 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4 to storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_audio.wav
2025-07-15 22:39:42 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:39:42 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 20.0%)
2025-07-15 22:39:42 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:39:42 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:39:43 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_audio.wav
2025-07-15 22:39:49 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:39:49 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 40.0%)
2025-07-15 22:39:49 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:39:57 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 60.0%)
2025-07-15 22:39:57 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:24 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_d412e056.srt
2025-07-15 22:40:24 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 80.0%)
2025-07-15 22:40:24 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:24 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:40:24 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:40:24 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_7c27ae4f.wav
2025-07-15 22:40:24 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 90.0%)
2025-07-15 22:40:24 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4 + storage\outputs\tts_7c27ae4f.wav
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_d412e056.srt
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:134 - 开始写入视频文件: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_translated.mp4
2025-07-15 22:40:24 | ERROR    | app.services.video_service:compose_video:164 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:40:24 | WARNING  | app.services.video_service:compose_video:169 - 使用原始视频作为后备: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_translated.mp4
2025-07-15 22:40:25 | INFO     | app.services.video_service:compose_video:197 - Video composition completed: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_translated.mp4
2025-07-15 22:40:25 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to completed (progress: 100.0%)
2025-07-15 22:40:25 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:25 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:47:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:47:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:47:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:47:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:48:21 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4
2025-07-15 22:48:21 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:48:21 | INFO     | app.services.base_service:create_task:35 - Created task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c of type full_pipeline
2025-07-15 22:48:21 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 0.0%)
2025-07-15 22:48:21 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:48:21 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4 to storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_audio.wav
2025-07-15 22:48:23 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:48:23 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 20.0%)
2025-07-15 22:48:23 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:48:23 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:48:24 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_audio.wav
2025-07-15 22:48:31 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:48:31 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 40.0%)
2025-07-15 22:48:31 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:48:39 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 60.0%)
2025-07-15 22:48:39 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_d4ea8ebb.srt
2025-07-15 22:49:05 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 80.0%)
2025-07-15 22:49:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:49:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:49:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_74556077.wav
2025-07-15 22:49:05 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 90.0%)
2025-07-15 22:49:05 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:05 | INFO     | app.services.video_service:compose_video:84 - Composing video: storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4 + storage\outputs\tts_74556077.wav
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:129 - Subtitle file generated: storage\outputs\subtitles_d4ea8ebb.srt
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:130 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:139 - 开始写入视频文件: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_translated.mp4
2025-07-15 22:49:06 | ERROR    | app.services.video_service:compose_video:166 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:49:06 | WARNING  | app.services.video_service:compose_video:170 - 使用原始视频作为后备: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_translated.mp4
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:198 - Video composition completed: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_translated.mp4
2025-07-15 22:49:06 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to completed (progress: 100.0%)
2025-07-15 22:49:06 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:06 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:51:21 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:51:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:51:30 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:51:30 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:51:30 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:51:51 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4
2025-07-15 22:51:51 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:51:51 | INFO     | app.services.base_service:create_task:35 - Created task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e of type full_pipeline
2025-07-15 22:51:51 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 0.0%)
2025-07-15 22:51:51 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:51:51 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4 to storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_audio.wav
2025-07-15 22:51:54 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:51:54 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 20.0%)
2025-07-15 22:51:54 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:51:54 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:51:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_audio.wav
2025-07-15 22:52:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:52:02 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 40.0%)
2025-07-15 22:52:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:09 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 60.0%)
2025-07-15 22:52:09 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:33 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_acb16bc7.srt
2025-07-15 22:52:33 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 80.0%)
2025-07-15 22:52:33 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:33 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:52:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:52:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_4fd4ddbd.wav
2025-07-15 22:52:33 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 90.0%)
2025-07-15 22:52:33 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:33 | INFO     | app.services.video_service:compose_video:84 - Composing video: storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4 + storage\outputs\tts_4fd4ddbd.wav
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:129 - Subtitle file generated: storage\outputs\subtitles_acb16bc7.srt
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:130 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:139 - 开始写入视频文件: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_translated.mp4
2025-07-15 22:52:34 | ERROR    | app.services.video_service:compose_video:166 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:52:34 | WARNING  | app.services.video_service:compose_video:170 - 使用原始视频作为后备: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_translated.mp4
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:204 - Video composition completed: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_translated.mp4
2025-07-15 22:52:34 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to completed (progress: 100.0%)
2025-07-15 22:52:34 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:34 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:00:56 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:01:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:01:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:01:07 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:01:07 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:01:07 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:03:38 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4
2025-07-19 10:03:38 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:03:38 | INFO     | app.services.base_service:create_task:35 - Created task 42ddf68e-5fff-422f-9704-a01f55ef0f3b of type full_pipeline
2025-07-19 10:03:38 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 0.0%)
2025-07-19 10:03:38 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:03:38 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4 to storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_audio.wav
2025-07-19 10:03:49 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:03:49 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 20.0%)
2025-07-19 10:03:49 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:03:49 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:03:51 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_audio.wav
2025-07-19 10:03:59 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:03:59 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 40.0%)
2025-07-19 10:03:59 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:03:59 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s.mp4
2025-07-19 10:03:59 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:03:59 | INFO     | app.services.base_service:create_task:35 - Created task 8c5f8045-d104-4816-b270-97ee8591fce8 of type full_pipeline
2025-07-19 10:03:59 | INFO     | app.services.base_service:update_task_status:54 - Task 8c5f8045-d104-4816-b270-97ee8591fce8 status updated to processing (progress: 0.0%)
2025-07-19 10:03:59 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 8c5f8045-d104-4816-b270-97ee8591fce8
2025-07-19 10:03:59 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s.mp4 to storage\outputs\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s_audio.wav
2025-07-19 10:04:00 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:04:00 | INFO     | app.services.base_service:update_task_status:54 - Task 8c5f8045-d104-4816-b270-97ee8591fce8 status updated to processing (progress: 20.0%)
2025-07-19 10:04:00 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 8c5f8045-d104-4816-b270-97ee8591fce8
2025-07-19 10:04:00 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:04:00 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 8c5f8045-d104-4816-b270-97ee8591fce8: 'Whisper' object has no attribute 'name'
2025-07-19 10:04:00 | INFO     | app.services.base_service:update_task_status:54 - Task 8c5f8045-d104-4816-b270-97ee8591fce8 status updated to failed (progress: 0.0%)
2025-07-19 10:04:06 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 60.0%)
2025-07-19 10:04:06 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:20 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_cf5a170b.srt
2025-07-19 10:04:20 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 80.0%)
2025-07-19 10:04:20 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:20 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:04:20 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:04:21 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_b36f9c25.wav
2025-07-19 10:04:21 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 90.0%)
2025-07-19 10:04:21 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:84 - Composing video: storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4 + storage\outputs\tts_b36f9c25.wav
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:129 - Subtitle file generated: storage\outputs\subtitles_cf5a170b.srt
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:130 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:139 - 开始写入视频文件: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_translated.mp4
2025-07-19 10:04:21 | ERROR    | app.services.video_service:compose_video:166 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:04:21 | WARNING  | app.services.video_service:compose_video:170 - 使用原始视频作为后备: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_translated.mp4
2025-07-19 10:04:22 | INFO     | app.services.video_service:compose_video:204 - Video composition completed: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_translated.mp4
2025-07-19 10:04:22 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to completed (progress: 100.0%)
2025-07-19 10:04:22 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:22 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:10:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:10:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:10:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:10:26 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:10:26 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:10:26 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:10:42 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4
2025-07-19 10:10:42 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:10:42 | INFO     | app.services.base_service:create_task:35 - Created task f8199595-4230-4d7e-9aed-d73fce1b13da of type full_pipeline
2025-07-19 10:10:42 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 0.0%)
2025-07-19 10:10:42 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:10:42 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4 to storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_audio.wav
2025-07-19 10:10:44 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:10:44 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 20.0%)
2025-07-19 10:10:44 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:10:44 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:10:45 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_audio.wav
2025-07-19 10:10:53 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:10:53 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 40.0%)
2025-07-19 10:10:53 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:10:59 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 60.0%)
2025-07-19 10:10:59 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:13 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_91bdc60d.srt
2025-07-19 10:11:13 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 80.0%)
2025-07-19 10:11:13 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:13 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:11:13 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:11:13 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_a8d68ceb.wav
2025-07-19 10:11:13 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 90.0%)
2025-07-19 10:11:13 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:13 | INFO     | app.services.video_service:compose_video:83 - Composing video: storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4 + storage\outputs\tts_a8d68ceb.wav
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:124 - Audio replacement successful
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:140 - Subtitle file generated: storage\outputs\subtitles_91bdc60d.srt
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:141 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:154 - 开始写入视频文件: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_translated.mp4
2025-07-19 10:11:14 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:183 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 10:11:20 | INFO     | app.services.video_service:compose_video:208 - FFmpeg直接命令执行成功: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_translated.mp4
2025-07-19 10:11:20 | INFO     | app.services.video_service:compose_video:255 - Video composition completed: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_translated.mp4
2025-07-19 10:11:20 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to completed (progress: 100.0%)
2025-07-19 10:11:20 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:20 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task f8199595-4230-4d7e-9aed-d73fce1b13da completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:16:37 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\90de387d-c2b2-4096-92f1-b0671528d432_test_30s.mp4
2025-07-19 10:16:37 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\90de387d-c2b2-4096-92f1-b0671528d432_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:16:37 | INFO     | app.services.base_service:create_task:35 - Created task 164f34ef-97c2-46a4-8098-ad8b5428e144 of type full_pipeline
2025-07-19 10:16:37 | INFO     | app.services.base_service:update_task_status:54 - Task 164f34ef-97c2-46a4-8098-ad8b5428e144 status updated to processing (progress: 0.0%)
2025-07-19 10:16:37 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 164f34ef-97c2-46a4-8098-ad8b5428e144
2025-07-19 10:16:37 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\90de387d-c2b2-4096-92f1-b0671528d432_test_30s.mp4 to storage\outputs\90de387d-c2b2-4096-92f1-b0671528d432_test_30s_audio.wav
2025-07-19 10:16:38 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\90de387d-c2b2-4096-92f1-b0671528d432_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:16:38 | INFO     | app.services.base_service:update_task_status:54 - Task 164f34ef-97c2-46a4-8098-ad8b5428e144 status updated to processing (progress: 20.0%)
2025-07-19 10:16:38 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 164f34ef-97c2-46a4-8098-ad8b5428e144
2025-07-19 10:16:38 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:16:38 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 164f34ef-97c2-46a4-8098-ad8b5428e144: 'Whisper' object has no attribute 'name'
2025-07-19 10:16:38 | INFO     | app.services.base_service:update_task_status:54 - Task 164f34ef-97c2-46a4-8098-ad8b5428e144 status updated to failed (progress: 0.0%)
2025-07-19 10:17:10 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\129181da-4f09-4282-b207-70f6774d168a_test_30s.mp4
2025-07-19 10:17:10 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\129181da-4f09-4282-b207-70f6774d168a_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:17:10 | INFO     | app.services.base_service:create_task:35 - Created task ca0723b8-2755-43b8-8efa-858d909f1318 of type full_pipeline
2025-07-19 10:17:10 | INFO     | app.services.base_service:update_task_status:54 - Task ca0723b8-2755-43b8-8efa-858d909f1318 status updated to processing (progress: 0.0%)
2025-07-19 10:17:10 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task ca0723b8-2755-43b8-8efa-858d909f1318
2025-07-19 10:17:10 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\129181da-4f09-4282-b207-70f6774d168a_test_30s.mp4 to storage\outputs\129181da-4f09-4282-b207-70f6774d168a_test_30s_audio.wav
2025-07-19 10:17:11 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\129181da-4f09-4282-b207-70f6774d168a_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:17:11 | INFO     | app.services.base_service:update_task_status:54 - Task ca0723b8-2755-43b8-8efa-858d909f1318 status updated to processing (progress: 20.0%)
2025-07-19 10:17:11 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task ca0723b8-2755-43b8-8efa-858d909f1318
2025-07-19 10:17:11 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:17:11 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task ca0723b8-2755-43b8-8efa-858d909f1318: 'Whisper' object has no attribute 'name'
2025-07-19 10:17:11 | INFO     | app.services.base_service:update_task_status:54 - Task ca0723b8-2755-43b8-8efa-858d909f1318 status updated to failed (progress: 0.0%)
2025-07-19 10:18:33 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:18:43 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:18:43 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:18:43 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:18:43 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:18:43 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:18:52 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4
2025-07-19 10:18:52 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:18:52 | INFO     | app.services.base_service:create_task:35 - Created task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 of type full_pipeline
2025-07-19 10:18:52 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 0.0%)
2025-07-19 10:18:52 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:18:52 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4 to storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_audio.wav
2025-07-19 10:18:55 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:18:55 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 20.0%)
2025-07-19 10:18:55 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:18:55 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:18:56 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_audio.wav
2025-07-19 10:19:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:19:02 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 40.0%)
2025-07-19 10:19:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:08 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 60.0%)
2025-07-19 10:19:08 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:23 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_99ffe62e.srt
2025-07-19 10:19:23 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 80.0%)
2025-07-19 10:19:23 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:23 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:19:23 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:19:23 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_14dd905f.wav
2025-07-19 10:19:23 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 90.0%)
2025-07-19 10:19:23 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:23 | INFO     | app.services.video_service:compose_video:83 - Composing video: storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4 + storage\outputs\tts_14dd905f.wav
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:124 - Audio replacement successful
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:140 - Subtitle file generated: storage\outputs\subtitles_99ffe62e.srt
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:141 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:154 - 开始写入视频文件: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_translated.mp4
2025-07-19 10:19:24 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:183 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 10:19:29 | INFO     | app.services.video_service:compose_video:208 - FFmpeg直接命令执行成功: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_translated.mp4
2025-07-19 10:19:30 | INFO     | app.services.video_service:compose_video:255 - Video composition completed: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_translated.mp4
2025-07-19 10:19:30 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to completed (progress: 100.0%)
2025-07-19 10:19:30 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:30 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:23:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:23:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:23:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:23:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:23:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:23:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:23:49 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4
2025-07-19 10:23:49 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:23:49 | INFO     | app.services.base_service:create_task:35 - Created task d09e07d2-59a9-4130-851f-23fc59ad7c24 of type full_pipeline
2025-07-19 10:23:49 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 0.0%)
2025-07-19 10:23:49 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:23:49 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 to storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_audio.wav
2025-07-19 10:23:51 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:23:51 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 20.0%)
2025-07-19 10:23:51 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:23:51 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:23:52 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_audio.wav
2025-07-19 10:23:59 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:23:59 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 40.0%)
2025-07-19 10:23:59 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:05 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 60.0%)
2025-07-19 10:24:05 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:20 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_cd1385a6.srt
2025-07-19 10:24:20 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 80.0%)
2025-07-19 10:24:20 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:20 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:24:20 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:24:20 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_bea4150b.wav
2025-07-19 10:24:20 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 90.0%)
2025-07-19 10:24:20 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:20 | INFO     | app.services.video_service:compose_video:83 - Composing video: storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 + storage\outputs\tts_bea4150b.wav
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:124 - Audio replacement successful
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:140 - Subtitle file generated: storage\outputs\subtitles_cd1385a6.srt
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:141 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:154 - 开始写入视频文件: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:21 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:183 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 10:24:22 | INFO     | app.services.video_service:compose_video:193 - 音频文件检查结果: True
2025-07-19 10:24:22 | INFO     | app.services.video_service:compose_video:211 - 使用新音频替换: storage\outputs\tts_bea4150b.wav
2025-07-19 10:24:22 | INFO     | app.services.video_service:compose_video:223 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 -i storage\outputs\tts_bea4150b.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:23 | INFO     | app.services.video_service:compose_video:235 - FFmpeg直接命令执行成功: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:23 | INFO     | app.services.video_service:compose_video:284 - Video composition completed: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:23 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to completed (progress: 100.0%)
2025-07-19 10:24:23 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:23 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:31:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:31:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:31:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:31:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:31:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:31:57 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:33:01 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s.mp4
2025-07-19 10:33:01 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:33:01 | INFO     | app.services.base_service:create_task:35 - Created task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a of type full_pipeline
2025-07-19 10:33:01 | INFO     | app.services.base_service:update_task_status:54 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a status updated to processing (progress: 0.0%)
2025-07-19 10:33:01 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a
2025-07-19 10:33:01 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s.mp4 to storage\outputs\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s_audio.wav
2025-07-19 10:33:04 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:33:04 | INFO     | app.services.base_service:update_task_status:54 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a status updated to processing (progress: 20.0%)
2025-07-19 10:33:04 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a
2025-07-19 10:33:04 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:33:05 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s_audio.wav
2025-07-19 10:33:13 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:33:13 | INFO     | app.services.base_service:update_task_status:54 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a status updated to processing (progress: 40.0%)
2025-07-19 10:33:13 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a
2025-07-19 10:33:18 | INFO     | app.services.base_service:update_task_status:54 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a status updated to processing (progress: 60.0%)
2025-07-19 10:33:18 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a
2025-07-19 10:33:33 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_c6ab7abb.srt
2025-07-19 10:33:33 | INFO     | app.services.base_service:update_task_status:54 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a status updated to processing (progress: 80.0%)
2025-07-19 10:33:33 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a
2025-07-19 10:33:33 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:33:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:33:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_276edc22.wav
2025-07-19 10:33:33 | INFO     | app.services.base_service:update_task_status:54 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a status updated to processing (progress: 90.0%)
2025-07-19 10:33:33 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a
2025-07-19 10:33:33 | INFO     | app.services.video_service:compose_video:98 - Composing video: storage\uploads\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s.mp4 + storage\outputs\tts_276edc22.wav
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:139 - Audio replacement successful
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:160 - Subtitle file generated: storage\outputs\subtitles_c6ab7abb.srt
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:161 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:174 - 开始写入视频文件: storage\outputs\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s_translated.mp4
2025-07-19 10:33:34 | ERROR    | app.services.video_service:compose_video:200 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:203 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:215 - FFprobe音频文件检查结果: True
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:234 - 使用新音频替换: storage\outputs\tts_276edc22.wav
2025-07-19 10:33:34 | INFO     | app.services.video_service:compose_video:246 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s.mp4 -i storage\outputs\tts_276edc22.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s_translated.mp4
2025-07-19 10:33:35 | INFO     | app.services.video_service:compose_video:258 - FFmpeg直接命令执行成功: storage\outputs\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s_translated.mp4
2025-07-19 10:33:35 | INFO     | app.services.video_service:compose_video:307 - Video composition completed: storage\outputs\90b54427-ae18-458a-8fca-879166ba1c4c_test_30s_translated.mp4
2025-07-19 10:33:35 | INFO     | app.services.base_service:update_task_status:54 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a status updated to completed (progress: 100.0%)
2025-07-19 10:33:35 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a
2025-07-19 10:33:35 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 8b50b8fb-6917-4e66-ba9d-607c45ff8a0a completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:53:14 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:53:14 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 10:53:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:53:26 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 10:53:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:53:26 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 10:53:26 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 10:53:26 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 10:53:26 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 10:54:07 | INFO     | app.services.base_service:create_task:35 - Created task c6baac45-7233-49bd-96f5-1b074605f911 of type tts_synthesis
2025-07-19 10:54:07 | INFO     | app.services.base_service:update_task_status:54 - Task c6baac45-7233-49bd-96f5-1b074605f911 status updated to processing (progress: 0.0%)
2025-07-19 10:54:07 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 10:54:07 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 10:54:07 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: zh-CN-XiaoxiaoNeural
2025-07-19 10:54:13 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:13 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:13 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:188 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=e2268f627be247fcb41f18c07adfd476')
2025-07-19 10:54:13 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:14 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:14 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:14 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:15 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:15 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:20 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:188 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=a07e71a8ae3c4cf2974408b50eeca732')
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:21 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:22 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:29 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:188 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=7a5caf4030c14532a343c16f7b5fe095')
2025-07-19 10:54:29 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=7a5caf4030c14532a343c16f7b5fe095')
2025-07-19 10:54:29 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 10:54:29 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 10:54:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:360 - Using local TTS service as fallback
2025-07-19 10:54:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:395 - Local TTS synthesis completed: storage\outputs\tts_a4aab861.wav
2025-07-19 10:54:29 | WARNING  | app.services.tts_service:process_tts:67 - TTS task c6baac45-7233-49bd-96f5-1b074605f911 completed with fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 10:54:29 | INFO     | app.services.base_service:update_task_status:54 - Task c6baac45-7233-49bd-96f5-1b074605f911 status updated to completed (progress: 100.0%)
2025-07-19 10:54:29 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:29 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:29 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:29 | INFO     | app.services.base_service:create_task:35 - Created task c8d94b8d-4511-4705-932d-2987dbf0e5eb of type tts_synthesis
2025-07-19 10:54:29 | INFO     | app.services.base_service:update_task_status:54 - Task c8d94b8d-4511-4705-932d-2987dbf0e5eb status updated to processing (progress: 0.0%)
2025-07-19 10:54:29 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: auto
2025-07-19 10:54:29 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 10:54:29 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: zh-CN-XiaoxiaoNeural
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:35 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:188 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f6821d6181e84319b2cca785acbee8fb')
2025-07-19 10:54:35 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:36 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:42 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:188 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f555618ad89b427fb40d1e649224b4e0')
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:43 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:44 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:44 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:44 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:50 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:188 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=6e0c6d79070e46219f0526cee1984f49')
2025-07-19 10:54:51 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts: 403, message='Invalid response status', url=URL('wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=6e0c6d79070e46219f0526cee1984f49')
2025-07-19 10:54:51 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 10:54:51 | INFO     | app.services.tts_service:_synthesize_with_local_tts:360 - Using local TTS service as fallback
2025-07-19 10:54:51 | INFO     | app.services.tts_service:_synthesize_with_local_tts:395 - Local TTS synthesis completed: storage\outputs\tts_d3d1261a.wav
2025-07-19 10:54:51 | WARNING  | app.services.tts_service:process_tts:67 - TTS task c8d94b8d-4511-4705-932d-2987dbf0e5eb completed with fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 10:54:51 | INFO     | app.services.base_service:update_task_status:54 - Task c8d94b8d-4511-4705-932d-2987dbf0e5eb status updated to completed (progress: 100.0%)
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:51 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:52 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:53 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:54 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:55 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:56 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:56 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:54:56 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 1 validation error for TaskInfo
task_type
  Input should be 'audio_extraction', 'speech_recognition', 'translation', 'subtitle_generation', 'tts_generation', 'video_composition' or 'full_pipeline' [type=enum, input_value='tts_synthesis', input_type=str]
2025-07-19 10:55:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:55:11 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 10:55:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:55:23 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 10:55:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:55:23 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 10:55:23 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 10:55:23 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 10:55:23 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:23 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:24 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:25 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:26 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:28 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:30 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 10:55:31 | ERROR    | app.api.routes:get_task_status:402 - Get task status failed: 
2025-07-19 11:03:15 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:03:15 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:03:24 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:03:24 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:03:24 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:03:24 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:03:24 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 11:03:24 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 11:03:24 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 11:07:03 | INFO     | app.services.base_service:create_task:35 - Created task b2dd954d-e4ef-4b7c-bdcb-7cb7e933df6d of type tts_generation
2025-07-19 11:07:03 | INFO     | app.services.base_service:update_task_status:54 - Task b2dd954d-e4ef-4b7c-bdcb-7cb7e933df6d status updated to processing (progress: 0.0%)
2025-07-19 11:07:03 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 11:07:03 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 11:07:03 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: zh-CN-YunxiNeural
2025-07-19 11:07:19 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:171 - Edge TTS synthesis completed: storage\outputs\tts_81e6df02.wav (duration: 5.09s)
2025-07-19 11:07:19 | INFO     | app.services.base_service:update_task_status:54 - Task b2dd954d-e4ef-4b7c-bdcb-7cb7e933df6d status updated to completed (progress: 100.0%)
2025-07-19 11:08:28 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s.mp4
2025-07-19 11:08:28 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 11:08:28 | INFO     | app.services.base_service:create_task:35 - Created task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 of type full_pipeline
2025-07-19 11:08:28 | INFO     | app.services.base_service:update_task_status:54 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 status updated to processing (progress: 0.0%)
2025-07-19 11:08:28 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 6499e7ac-a5f9-431d-b46f-c134bec0abd1
2025-07-19 11:08:28 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s.mp4 to storage\outputs\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s_audio.wav
2025-07-19 11:08:30 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s_audio.wav (duration: 30.06s)
2025-07-19 11:08:30 | INFO     | app.services.base_service:update_task_status:54 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 status updated to processing (progress: 20.0%)
2025-07-19 11:08:30 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 6499e7ac-a5f9-431d-b46f-c134bec0abd1
2025-07-19 11:08:30 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 11:08:32 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s_audio.wav
2025-07-19 11:08:40 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 11:08:40 | INFO     | app.services.base_service:update_task_status:54 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 status updated to processing (progress: 40.0%)
2025-07-19 11:08:40 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 6499e7ac-a5f9-431d-b46f-c134bec0abd1
2025-07-19 11:08:46 | INFO     | app.services.base_service:update_task_status:54 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 status updated to processing (progress: 60.0%)
2025-07-19 11:08:46 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 6499e7ac-a5f9-431d-b46f-c134bec0abd1
2025-07-19 11:09:01 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_91941de3.srt
2025-07-19 11:09:01 | INFO     | app.services.base_service:update_task_status:54 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 status updated to processing (progress: 80.0%)
2025-07-19 11:09:01 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 6499e7ac-a5f9-431d-b46f-c134bec0abd1
2025-07-19 11:09:01 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 11:09:01 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 11:09:01 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 11:09:01 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 11:09:03 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 11:09:05 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 11:09:05 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 11:09:05 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 11:09:05 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 11:09:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 11:09:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_234cccfc.wav
2025-07-19 11:09:05 | INFO     | app.services.base_service:update_task_status:54 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 status updated to processing (progress: 90.0%)
2025-07-19 11:09:05 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 6499e7ac-a5f9-431d-b46f-c134bec0abd1
2025-07-19 11:09:05 | INFO     | app.services.video_service:compose_video:98 - Composing video: storage\uploads\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s.mp4 + storage\outputs\tts_234cccfc.wav
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:139 - Audio replacement successful
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:160 - Subtitle file generated: storage\outputs\subtitles_91941de3.srt
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:161 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:174 - 开始写入视频文件: storage\outputs\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s_translated.mp4
2025-07-19 11:09:06 | ERROR    | app.services.video_service:compose_video:200 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:203 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:215 - FFprobe音频文件检查结果: True
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:234 - 使用新音频替换: storage\outputs\tts_234cccfc.wav
2025-07-19 11:09:06 | INFO     | app.services.video_service:compose_video:246 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s.mp4 -i storage\outputs\tts_234cccfc.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s_translated.mp4
2025-07-19 11:09:07 | INFO     | app.services.video_service:compose_video:258 - FFmpeg直接命令执行成功: storage\outputs\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s_translated.mp4
2025-07-19 11:09:07 | INFO     | app.services.video_service:compose_video:307 - Video composition completed: storage\outputs\5a4783fa-90d9-4be7-8208-29ebb18a17ef_test_30s_translated.mp4
2025-07-19 11:09:07 | INFO     | app.services.base_service:update_task_status:54 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 status updated to completed (progress: 100.0%)
2025-07-19 11:09:07 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 6499e7ac-a5f9-431d-b46f-c134bec0abd1
2025-07-19 11:09:07 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 6499e7ac-a5f9-431d-b46f-c134bec0abd1 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 11:21:19 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:21:19 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:21:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:21:29 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:21:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:21:29 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:21:29 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 11:21:29 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 11:21:29 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 11:51:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:51:36 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:51:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:51:46 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:51:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 11:51:46 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 11:51:46 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 11:51:46 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 11:51:46 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 11:58:54 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\ce3c84ed-33f1-4a63-a0e5-b3d58b464c15_test_30s.mp4
2025-07-19 11:58:54 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\ce3c84ed-33f1-4a63-a0e5-b3d58b464c15_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 11:58:54 | INFO     | app.services.base_service:create_task:35 - Created task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 of type full_pipeline
2025-07-19 11:58:54 | INFO     | app.services.base_service:update_task_status:54 - Task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 status updated to processing (progress: 0.0%)
2025-07-19 11:58:54 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993
2025-07-19 11:58:54 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 11:58:54 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 11:58:54 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 11:58:54 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 11:58:56 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 11:58:58 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 11:58:58 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 11:58:58 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 11:58:58 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 11:58:58 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 11:58:58 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_462d315e.wav
2025-07-19 11:58:58 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 11:58:58 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993
2025-07-19 11:58:58 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\ce3c84ed-33f1-4a63-a0e5-b3d58b464c15_test_30s.mp4 to storage\outputs\ce3c84ed-33f1-4a63-a0e5-b3d58b464c15_test_30s_audio.wav
2025-07-19 11:59:01 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\ce3c84ed-33f1-4a63-a0e5-b3d58b464c15_test_30s_audio.wav (duration: 30.06s)
2025-07-19 11:59:01 | INFO     | app.services.base_service:update_task_status:54 - Task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 status updated to processing (progress: 20.0%)
2025-07-19 11:59:01 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993
2025-07-19 11:59:01 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 11:59:02 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\ce3c84ed-33f1-4a63-a0e5-b3d58b464c15_test_30s_audio.wav
2025-07-19 11:59:10 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 11:59:10 | INFO     | app.services.base_service:update_task_status:54 - Task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 status updated to processing (progress: 40.0%)
2025-07-19 11:59:10 | INFO     | app.services.task_service:process_full_pipeline:95 - Step 3: Translation for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993
2025-07-19 11:59:16 | INFO     | app.services.base_service:update_task_status:54 - Task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 status updated to processing (progress: 60.0%)
2025-07-19 11:59:16 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 4: Generating subtitles for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993
2025-07-19 11:59:31 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_ca7a4072.srt
2025-07-19 11:59:31 | INFO     | app.services.base_service:update_task_status:54 - Task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 status updated to processing (progress: 80.0%)
2025-07-19 11:59:31 | INFO     | app.services.task_service:process_full_pipeline:135 - Step 5: TTS synthesis for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993
2025-07-19 11:59:31 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 1/3
2025-07-19 11:59:31 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 11:59:31 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 11:59:31 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 11:59:31 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 11:59:33 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 11:59:35 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 11:59:35 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 11:59:35 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 11:59:35 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 11:59:35 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 11:59:35 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_de530573.wav
2025-07-19 11:59:35 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 11:59:35 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 3 seconds...
2025-07-19 11:59:38 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 2/3
2025-07-19 11:59:38 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 11:59:38 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 11:59:38 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 11:59:38 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 11:59:40 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 11:59:42 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 11:59:42 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 11:59:42 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 11:59:42 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 11:59:42 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 11:59:42 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_1d6f03b2.wav
2025-07-19 11:59:42 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 11:59:42 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 6 seconds...
2025-07-19 11:59:48 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 3/3
2025-07-19 11:59:48 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 11:59:48 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 11:59:48 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 11:59:48 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 11:59:50 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 11:59:52 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 11:59:52 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 11:59:52 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 11:59:52 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 11:59:52 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 11:59:52 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_d04c0221.wav
2025-07-19 11:59:52 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 11:59:52 | WARNING  | app.services.task_service:process_full_pipeline:167 - Accepting TTS fallback result after all retries
2025-07-19 11:59:52 | INFO     | app.services.base_service:update_task_status:54 - Task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 status updated to processing (progress: 90.0%)
2025-07-19 11:59:52 | INFO     | app.services.task_service:process_full_pipeline:193 - Step 6: Video composition for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993
2025-07-19 11:59:52 | ERROR    | app.services.video_service:compose_video:388 - Video composition failed: cannot access local variable 'os' where it is not associated with a value
2025-07-19 11:59:52 | ERROR    | app.services.task_service:process_full_pipeline:240 - Full pipeline failed for task 3ea3dd14-d70e-402c-b4bd-64c1d928a993: cannot access local variable 'os' where it is not associated with a value
2025-07-19 11:59:52 | INFO     | app.services.base_service:update_task_status:54 - Task 3ea3dd14-d70e-402c-b4bd-64c1d928a993 status updated to failed (progress: 0.0%)
2025-07-19 12:08:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:08:31 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:08:41 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:08:41 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:08:41 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:08:41 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:08:41 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 12:08:41 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 12:08:41 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 12:09:01 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4
2025-07-19 12:09:01 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 12:09:01 | INFO     | app.services.base_service:create_task:35 - Created task 86ea07d4-c14e-42f5-9dd5-58489e63641f of type full_pipeline
2025-07-19 12:09:01 | INFO     | app.services.base_service:update_task_status:54 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f status updated to processing (progress: 0.0%)
2025-07-19 12:09:01 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:09:01 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:09:01 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:09:01 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:09:01 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:09:03 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:09:05 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:09:05 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:09:05 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:09:05 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:09:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:09:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_286dac14.wav
2025-07-19 12:09:05 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 12:09:05 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:09:05 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4 to storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_audio.wav
2025-07-19 12:09:07 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_audio.wav (duration: 30.06s)
2025-07-19 12:09:07 | INFO     | app.services.base_service:update_task_status:54 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f status updated to processing (progress: 20.0%)
2025-07-19 12:09:07 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:09:07 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 12:09:08 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_audio.wav
2025-07-19 12:09:17 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 12:09:17 | INFO     | app.services.base_service:update_task_status:54 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f status updated to processing (progress: 40.0%)
2025-07-19 12:09:17 | INFO     | app.services.task_service:process_full_pipeline:95 - Step 3: Translation for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:09:22 | INFO     | app.services.base_service:update_task_status:54 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f status updated to processing (progress: 60.0%)
2025-07-19 12:09:22 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 4: Generating subtitles for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:09:36 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_0c0c5724.srt
2025-07-19 12:09:36 | INFO     | app.services.base_service:update_task_status:54 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f status updated to processing (progress: 80.0%)
2025-07-19 12:09:36 | INFO     | app.services.task_service:process_full_pipeline:135 - Step 5: TTS synthesis for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:09:36 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 1/3
2025-07-19 12:09:36 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:09:36 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:09:36 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:09:36 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:09:38 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:09:40 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:09:40 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:09:40 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:09:40 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:09:40 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:09:41 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_c6f3574e.wav
2025-07-19 12:09:41 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:09:41 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 3 seconds...
2025-07-19 12:09:44 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 2/3
2025-07-19 12:09:44 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:09:44 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:09:44 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:09:44 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:09:46 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:09:48 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:09:48 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:09:48 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:09:48 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:09:48 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:09:48 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_ed3d32ab.wav
2025-07-19 12:09:48 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:09:48 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 6 seconds...
2025-07-19 12:09:54 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 3/3
2025-07-19 12:09:54 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:09:54 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:09:54 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:09:54 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:09:56 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:09:58 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:09:58 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:09:58 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:09:58 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:09:58 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:09:58 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_3ccce711.wav
2025-07-19 12:09:58 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:09:58 | WARNING  | app.services.task_service:process_full_pipeline:167 - Accepting TTS fallback result after all retries
2025-07-19 12:09:58 | INFO     | app.services.base_service:update_task_status:54 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f status updated to processing (progress: 90.0%)
2025-07-19 12:09:58 | INFO     | app.services.task_service:process_full_pipeline:193 - Step 6: Video composition for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:09:58 | INFO     | app.services.video_service:compose_video:104 - Composing video: storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4 + storage\outputs\tts_3ccce711.wav
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:145 - Audio replacement successful
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:166 - Subtitle file generated: storage\outputs\subtitles_0c0c5724.srt
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:167 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:180 - 开始写入视频文件: storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4
2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:226 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 12:09:59 | WARNING  | app.services.video_service:compose_video:230 - 检测到Windows服务环境stdout问题，直接使用FFmpeg命令
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:233 - MoviePy失败，立即使用FFmpeg直接命令
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:259 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4 -i storage\outputs\tts_3ccce711.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4
2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:273 - FFmpeg命令失败，返回码: 4294967294
2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:274 - FFmpeg错误输出: ffmpeg version 2025-07-01-git-11d1b71c31-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-liboapv --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-openal --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
  libavutil      60.  4.101 / 60.  4.101
  libavcodec     62.  4.103 / 62.  4.103
  libavformat    62.  1.101 / 62.  1.101
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
[in#0 @ 0000011defa6f940] Error opening input: No such file or directory
Error opening input file storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4.
Error opening input files: No such file or directory

2025-07-19 12:09:59 | ERROR    | app.services.video_service:compose_video:278 - FFmpeg备选方案也失败: Command '['ffmpeg', '-y', '-i', 'storage\\uploads\\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4', '-i', 'storage\\outputs\\tts_3ccce711.wav', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest', 'storage\\outputs\\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4']' returned non-zero exit status 4294967294.
2025-07-19 12:09:59 | WARNING  | app.services.video_service:compose_video:282 - 使用原始视频作为最终备选方案: storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:297 - FFprobe音频文件检查结果: True
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:316 - 使用新音频替换: storage\outputs\tts_3ccce711.wav
2025-07-19 12:09:59 | INFO     | app.services.video_service:compose_video:328 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s.mp4 -i storage\outputs\tts_3ccce711.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4
2025-07-19 12:10:00 | INFO     | app.services.video_service:compose_video:340 - FFmpeg直接命令执行成功: storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4
2025-07-19 12:10:00 | INFO     | app.services.video_service:compose_video:389 - Video composition completed: storage\outputs\b47137b6-4792-44ee-ba01-2cd2c15c69f3_test_30s_translated.mp4
2025-07-19 12:10:00 | INFO     | app.services.base_service:update_task_status:54 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f status updated to completed (progress: 100.0%)
2025-07-19 12:10:00 | INFO     | app.services.task_service:process_full_pipeline:233 - Full pipeline completed for task 86ea07d4-c14e-42f5-9dd5-58489e63641f
2025-07-19 12:10:00 | WARNING  | app.services.task_service:process_full_pipeline:237 - Task 86ea07d4-c14e-42f5-9dd5-58489e63641f completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 12:11:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:11:00 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:11:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:11:11 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:11:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:11:11 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:11:11 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 12:11:11 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 12:11:11 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 12:11:30 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4
2025-07-19 12:11:30 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 12:11:30 | INFO     | app.services.base_service:create_task:35 - Created task f244d35b-7699-4d48-8337-e339526dd883 of type full_pipeline
2025-07-19 12:11:30 | INFO     | app.services.base_service:update_task_status:54 - Task f244d35b-7699-4d48-8337-e339526dd883 status updated to processing (progress: 0.0%)
2025-07-19 12:11:30 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:11:30 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:11:30 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:11:30 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:11:30 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:11:32 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:11:34 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:11:34 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:11:34 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:11:34 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:11:34 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:11:34 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_74faa0f3.wav
2025-07-19 12:11:34 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 12:11:34 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:11:34 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4 to storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_audio.wav
2025-07-19 12:11:37 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_audio.wav (duration: 30.06s)
2025-07-19 12:11:37 | INFO     | app.services.base_service:update_task_status:54 - Task f244d35b-7699-4d48-8337-e339526dd883 status updated to processing (progress: 20.0%)
2025-07-19 12:11:37 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:11:37 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 12:11:38 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_audio.wav
2025-07-19 12:11:46 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 12:11:46 | INFO     | app.services.base_service:update_task_status:54 - Task f244d35b-7699-4d48-8337-e339526dd883 status updated to processing (progress: 40.0%)
2025-07-19 12:11:46 | INFO     | app.services.task_service:process_full_pipeline:95 - Step 3: Translation for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:11:52 | INFO     | app.services.base_service:update_task_status:54 - Task f244d35b-7699-4d48-8337-e339526dd883 status updated to processing (progress: 60.0%)
2025-07-19 12:11:52 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 4: Generating subtitles for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:12:06 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_7a05f954.srt
2025-07-19 12:12:06 | INFO     | app.services.base_service:update_task_status:54 - Task f244d35b-7699-4d48-8337-e339526dd883 status updated to processing (progress: 80.0%)
2025-07-19 12:12:06 | INFO     | app.services.task_service:process_full_pipeline:135 - Step 5: TTS synthesis for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:12:06 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 1/3
2025-07-19 12:12:06 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:12:06 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:12:06 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:12:06 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:12:08 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:12:10 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:12:10 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:12:10 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:12:10 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:12:10 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:12:10 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_a81f1ccf.wav
2025-07-19 12:12:10 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:12:10 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 3 seconds...
2025-07-19 12:12:13 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 2/3
2025-07-19 12:12:13 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:12:13 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:12:13 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:12:13 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:12:15 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:12:17 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:12:17 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:12:17 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:12:17 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:12:17 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:12:18 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_b747ea89.wav
2025-07-19 12:12:18 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:12:18 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 6 seconds...
2025-07-19 12:12:24 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 3/3
2025-07-19 12:12:24 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:12:24 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:12:24 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:12:24 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:12:26 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:12:28 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:12:28 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:12:28 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:12:28 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:12:28 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:12:28 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_8ebcf7c4.wav
2025-07-19 12:12:28 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:12:28 | WARNING  | app.services.task_service:process_full_pipeline:167 - Accepting TTS fallback result after all retries
2025-07-19 12:12:28 | INFO     | app.services.base_service:update_task_status:54 - Task f244d35b-7699-4d48-8337-e339526dd883 status updated to processing (progress: 90.0%)
2025-07-19 12:12:28 | INFO     | app.services.task_service:process_full_pipeline:193 - Step 6: Video composition for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:12:28 | INFO     | app.services.video_service:compose_video:104 - Composing video: storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4 + storage\outputs\tts_8ebcf7c4.wav
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:145 - Audio replacement successful
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:166 - Subtitle file generated: storage\outputs\subtitles_7a05f954.srt
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:167 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:180 - 开始写入视频文件: storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4
2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:226 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 12:12:29 | WARNING  | app.services.video_service:compose_video:230 - 检测到Windows服务环境stdout问题，直接使用FFmpeg命令
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:233 - MoviePy失败，立即使用FFmpeg直接命令
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:259 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4 -i storage\outputs\tts_8ebcf7c4.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4
2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:273 - FFmpeg命令失败，返回码: 4294967294
2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:274 - FFmpeg错误输出: ffmpeg version 2025-07-01-git-11d1b71c31-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-liboapv --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-openal --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
  libavutil      60.  4.101 / 60.  4.101
  libavcodec     62.  4.103 / 62.  4.103
  libavformat    62.  1.101 / 62.  1.101
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
[in#0 @ 00000231d67ff940] Error opening input: No such file or directory
Error opening input file storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4.
Error opening input files: No such file or directory

2025-07-19 12:12:29 | ERROR    | app.services.video_service:compose_video:278 - FFmpeg备选方案也失败: Command '['ffmpeg', '-y', '-i', 'storage\\uploads\\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4', '-i', 'storage\\outputs\\tts_8ebcf7c4.wav', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest', 'storage\\outputs\\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4']' returned non-zero exit status 4294967294.
2025-07-19 12:12:29 | WARNING  | app.services.video_service:compose_video:282 - 使用原始视频作为最终备选方案: storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:297 - FFprobe音频文件检查结果: True
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:316 - 使用新音频替换: storage\outputs\tts_8ebcf7c4.wav
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:328 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s.mp4 -i storage\outputs\tts_8ebcf7c4.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:340 - FFmpeg直接命令执行成功: storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4
2025-07-19 12:12:29 | INFO     | app.services.video_service:compose_video:389 - Video composition completed: storage\outputs\d8c2723c-0514-453d-85f9-8d43994360e8_test_30s_translated.mp4
2025-07-19 12:12:29 | INFO     | app.services.base_service:update_task_status:54 - Task f244d35b-7699-4d48-8337-e339526dd883 status updated to completed (progress: 100.0%)
2025-07-19 12:12:29 | INFO     | app.services.task_service:process_full_pipeline:233 - Full pipeline completed for task f244d35b-7699-4d48-8337-e339526dd883
2025-07-19 12:12:29 | WARNING  | app.services.task_service:process_full_pipeline:237 - Task f244d35b-7699-4d48-8337-e339526dd883 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 12:17:48 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:17:48 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:17:59 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:18:00 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:18:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:18:00 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:18:00 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 12:18:00 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 12:18:00 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 12:18:25 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s.mp4
2025-07-19 12:18:25 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 12:18:25 | INFO     | app.services.base_service:create_task:35 - Created task 0d02946d-9da8-41dc-9626-155dd51fe84b of type full_pipeline
2025-07-19 12:18:25 | INFO     | app.services.base_service:update_task_status:54 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b status updated to processing (progress: 0.0%)
2025-07-19 12:18:25 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:18:25 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:18:25 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:18:25 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:18:25 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:18:27 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:18:29 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:18:29 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:18:29 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:18:29 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:18:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:18:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_6a0c5e74.wav
2025-07-19 12:18:29 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 12:18:29 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:18:29 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s.mp4 to storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_audio.wav
2025-07-19 12:18:32 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_audio.wav (duration: 30.06s)
2025-07-19 12:18:32 | INFO     | app.services.base_service:update_task_status:54 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b status updated to processing (progress: 20.0%)
2025-07-19 12:18:32 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:18:32 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 12:18:34 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_audio.wav
2025-07-19 12:18:44 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 12:18:44 | INFO     | app.services.base_service:update_task_status:54 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b status updated to processing (progress: 40.0%)
2025-07-19 12:18:44 | INFO     | app.services.task_service:process_full_pipeline:95 - Step 3: Translation for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:18:50 | INFO     | app.services.base_service:update_task_status:54 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b status updated to processing (progress: 60.0%)
2025-07-19 12:18:50 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 4: Generating subtitles for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:19:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_690620a7.srt
2025-07-19 12:19:05 | INFO     | app.services.base_service:update_task_status:54 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b status updated to processing (progress: 80.0%)
2025-07-19 12:19:05 | INFO     | app.services.task_service:process_full_pipeline:135 - Step 5: TTS synthesis for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:19:05 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 1/3
2025-07-19 12:19:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:19:05 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:19:05 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:19:05 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:19:07 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:19:09 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:19:09 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:19:09 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:19:09 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:19:09 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:19:09 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_2877c958.wav
2025-07-19 12:19:09 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:19:09 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 3 seconds...
2025-07-19 12:19:12 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 2/3
2025-07-19 12:19:12 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:19:12 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:19:12 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:19:12 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:19:14 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:19:16 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:19:16 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:19:16 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:19:16 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:19:16 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:19:16 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_26c3060b.wav
2025-07-19 12:19:16 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:19:16 | INFO     | app.services.task_service:process_full_pipeline:162 - Retrying TTS in 6 seconds...
2025-07-19 12:19:22 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 3/3
2025-07-19 12:19:22 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:19:22 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:19:22 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:19:22 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:19:24 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:19:26 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:19:26 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:19:26 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:19:26 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:19:26 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:19:26 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_7660139c.wav
2025-07-19 12:19:26 | WARNING  | app.services.task_service:process_full_pipeline:157 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:19:26 | WARNING  | app.services.task_service:process_full_pipeline:167 - Accepting TTS fallback result after all retries
2025-07-19 12:19:26 | INFO     | app.services.base_service:update_task_status:54 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b status updated to processing (progress: 90.0%)
2025-07-19 12:19:26 | INFO     | app.services.task_service:process_full_pipeline:193 - Step 6: Video composition for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:19:26 | INFO     | app.services.video_service:compose_video:109 - Composing video: F:\project\ai\video\python-ai-service\storage\uploads\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s.mp4 + F:\project\ai\video\python-ai-service\storage\outputs\tts_7660139c.wav
2025-07-19 12:19:26 | INFO     | app.services.video_service:compose_video:110 - 输出路径: F:\project\ai\video\python-ai-service\storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:151 - Audio replacement successful
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:172 - Subtitle file generated: storage\outputs\subtitles_690620a7.srt
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:173 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:186 - 开始写入视频文件: storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:27 | ERROR    | app.services.video_service:compose_video:232 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 12:19:27 | WARNING  | app.services.video_service:compose_video:236 - 检测到Windows服务环境stdout问题，直接使用FFmpeg命令
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:239 - MoviePy失败，立即使用FFmpeg直接命令
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:269 - 使用音频替换模式
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:280 - 执行FFmpeg命令: ffmpeg -y -i F:\project\ai\video\python-ai-service\storage\uploads\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s.mp4 -i F:\project\ai\video\python-ai-service\storage\outputs\tts_7660139c.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest F:\project\ai\video\python-ai-service\storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:281 - 输入视频: F:\project\ai\video\python-ai-service\storage\uploads\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s.mp4
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:283 - 输入音频: F:\project\ai\video\python-ai-service\storage\outputs\tts_7660139c.wav
2025-07-19 12:19:27 | INFO     | app.services.video_service:compose_video:284 - 输出文件: F:\project\ai\video\python-ai-service\storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:28 | INFO     | app.services.video_service:compose_video:296 - FFmpeg命令执行成功: storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:28 | INFO     | app.services.video_service:compose_video:322 - FFprobe音频文件检查结果: True
2025-07-19 12:19:28 | INFO     | app.services.video_service:compose_video:341 - 使用新音频替换: storage\outputs\tts_7660139c.wav
2025-07-19 12:19:28 | INFO     | app.services.video_service:compose_video:353 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s.mp4 -i storage\outputs\tts_7660139c.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:28 | INFO     | app.services.video_service:compose_video:365 - FFmpeg直接命令执行成功: storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:29 | INFO     | app.services.video_service:compose_video:414 - Video composition completed: F:\project\ai\video\python-ai-service\storage\outputs\ca0e3aca-2b24-497c-915f-f7daf45e9171_test_30s_translated.mp4
2025-07-19 12:19:29 | INFO     | app.services.base_service:update_task_status:54 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b status updated to completed (progress: 100.0%)
2025-07-19 12:19:29 | INFO     | app.services.task_service:process_full_pipeline:233 - Full pipeline completed for task 0d02946d-9da8-41dc-9626-155dd51fe84b
2025-07-19 12:19:29 | WARNING  | app.services.task_service:process_full_pipeline:237 - Task 0d02946d-9da8-41dc-9626-155dd51fe84b completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 12:26:22 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:26:22 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:26:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:26:32 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:26:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:26:32 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:26:32 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 12:26:32 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 12:26:32 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 12:27:17 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s.mp4
2025-07-19 12:27:17 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 12:27:17 | INFO     | app.services.base_service:create_task:35 - Created task 21d3f806-279a-43fc-afac-770e2f3524a9 of type full_pipeline
2025-07-19 12:27:17 | INFO     | app.services.base_service:update_task_status:54 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 status updated to processing (progress: 0.0%)
2025-07-19 12:27:17 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:27:17 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:27:17 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:27:17 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:27:17 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:27:19 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:27:21 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:27:21 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:27:21 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:27:21 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:27:21 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:27:21 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_78537274.wav
2025-07-19 12:27:21 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 12:27:21 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:27:21 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s.mp4 to storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_audio.wav
2025-07-19 12:27:24 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_audio.wav (duration: 30.06s)
2025-07-19 12:27:24 | INFO     | app.services.base_service:update_task_status:54 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 status updated to processing (progress: 20.0%)
2025-07-19 12:27:24 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:27:24 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 12:27:26 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_audio.wav
2025-07-19 12:27:37 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 12:27:37 | INFO     | app.services.base_service:update_task_status:54 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 status updated to processing (progress: 40.0%)
2025-07-19 12:27:37 | INFO     | app.services.task_service:process_full_pipeline:95 - Step 3: Translation for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:27:42 | INFO     | app.services.base_service:update_task_status:54 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 status updated to processing (progress: 60.0%)
2025-07-19 12:27:42 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 4: Generating subtitles for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:27:57 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_4ad430aa.srt
2025-07-19 12:27:57 | INFO     | app.services.base_service:update_task_status:54 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 status updated to processing (progress: 80.0%)
2025-07-19 12:27:57 | INFO     | app.services.task_service:process_full_pipeline:135 - Step 5: TTS synthesis for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:27:57 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 1/3
2025-07-19 12:27:57 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser编辑器V4的速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser编辑器V4是...
2025-07-19 12:27:57 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 12:27:57 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 12:27:57 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:27:57 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:27:57 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:27:57 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:27:59 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:28:01 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:28:01 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:28:01 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:28:01 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:28:01 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:28:01 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_ac29d052.wav
2025-07-19 12:28:01 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_ac29d052.wav', 'duration': 34.199999999999996, 'text': '大家好，欢迎来到Faser编辑器V4的速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser编辑器V4是一个强大的工具，用于可视化构建HTML5游戏，让你专注于创作，而不是迷失在代码中。在本视频中，我将一步步指导你如何使用编辑器设置项目并创建一个简单的游戏。以下是今天的内容概要：什么是Faser编辑器？如何启动一个全新项目？我们将对世界进行一个高层次的概述。', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 12:28:01 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_ac29d052.wav
2025-07-19 12:28:01 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 34.199999999999996
2025-07-19 12:28:01 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 12:28:01 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 12:28:01 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:28:01 | INFO     | app.services.task_service:process_full_pipeline:178 - Retrying TTS in 3 seconds...
2025-07-19 12:28:04 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 2/3
2025-07-19 12:28:04 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser编辑器V4的速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser编辑器V4是...
2025-07-19 12:28:04 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 12:28:04 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 12:28:04 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:28:04 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:28:04 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:28:04 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:28:06 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:28:08 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:28:08 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:28:08 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:28:08 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:28:08 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:28:08 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_4a0d911f.wav
2025-07-19 12:28:08 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_4a0d911f.wav', 'duration': 34.199999999999996, 'text': '大家好，欢迎来到Faser编辑器V4的速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser编辑器V4是一个强大的工具，用于可视化构建HTML5游戏，让你专注于创作，而不是迷失在代码中。在本视频中，我将一步步指导你如何使用编辑器设置项目并创建一个简单的游戏。以下是今天的内容概要：什么是Faser编辑器？如何启动一个全新项目？我们将对世界进行一个高层次的概述。', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 12:28:08 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_4a0d911f.wav
2025-07-19 12:28:08 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 34.199999999999996
2025-07-19 12:28:08 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 12:28:08 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 12:28:08 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:28:08 | INFO     | app.services.task_service:process_full_pipeline:178 - Retrying TTS in 6 seconds...
2025-07-19 12:28:14 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 3/3
2025-07-19 12:28:14 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser编辑器V4的速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser编辑器V4是...
2025-07-19 12:28:14 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 12:28:14 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 12:28:14 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:28:14 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:28:14 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:28:14 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:28:16 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:28:18 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:28:18 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:28:18 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:28:18 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:28:18 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:28:19 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_173e9a35.wav
2025-07-19 12:28:19 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_173e9a35.wav', 'duration': 34.199999999999996, 'text': '大家好，欢迎来到Faser编辑器V4的速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser编辑器V4是一个强大的工具，用于可视化构建HTML5游戏，让你专注于创作，而不是迷失在代码中。在本视频中，我将一步步指导你如何使用编辑器设置项目并创建一个简单的游戏。以下是今天的内容概要：什么是Faser编辑器？如何启动一个全新项目？我们将对世界进行一个高层次的概述。', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 12:28:19 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_173e9a35.wav
2025-07-19 12:28:19 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 34.199999999999996
2025-07-19 12:28:19 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 12:28:19 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 12:28:19 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:28:19 | WARNING  | app.services.task_service:process_full_pipeline:183 - Accepting TTS fallback result after all retries
2025-07-19 12:28:19 | INFO     | app.services.base_service:update_task_status:54 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 status updated to processing (progress: 90.0%)
2025-07-19 12:28:19 | INFO     | app.services.task_service:process_full_pipeline:209 - Step 6: Video composition for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:109 - Composing video: F:\project\ai\video\python-ai-service\storage\uploads\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s.mp4 + F:\project\ai\video\python-ai-service\storage\outputs\tts_173e9a35.wav
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:110 - 输出路径: F:\project\ai\video\python-ai-service\storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:157 - Audio replacement successful
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:178 - Subtitle file generated: storage\outputs\subtitles_4ad430aa.srt
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:179 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:192 - 开始写入视频文件: storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:19 | ERROR    | app.services.video_service:compose_video:238 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 12:28:19 | WARNING  | app.services.video_service:compose_video:242 - 检测到Windows服务环境stdout问题，直接使用FFmpeg命令
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:245 - MoviePy失败，立即使用FFmpeg直接命令
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:275 - 使用音频替换模式
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:286 - 执行FFmpeg命令: ffmpeg -y -i F:\project\ai\video\python-ai-service\storage\uploads\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s.mp4 -i F:\project\ai\video\python-ai-service\storage\outputs\tts_173e9a35.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest F:\project\ai\video\python-ai-service\storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:287 - 输入视频: F:\project\ai\video\python-ai-service\storage\uploads\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s.mp4
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:289 - 输入音频: F:\project\ai\video\python-ai-service\storage\outputs\tts_173e9a35.wav
2025-07-19 12:28:19 | INFO     | app.services.video_service:compose_video:290 - 输出文件: F:\project\ai\video\python-ai-service\storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:20 | INFO     | app.services.video_service:compose_video:302 - FFmpeg命令执行成功: storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:20 | INFO     | app.services.video_service:compose_video:328 - FFprobe音频文件检查结果: True
2025-07-19 12:28:20 | INFO     | app.services.video_service:compose_video:347 - 使用新音频替换: storage\outputs\tts_173e9a35.wav
2025-07-19 12:28:20 | INFO     | app.services.video_service:compose_video:359 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s.mp4 -i storage\outputs\tts_173e9a35.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:21 | INFO     | app.services.video_service:compose_video:371 - FFmpeg直接命令执行成功: storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:21 | INFO     | app.services.video_service:compose_video:420 - Video composition completed: F:\project\ai\video\python-ai-service\storage\outputs\8dbd1ca8-5738-41f3-b3ae-b86045901f31_test_30s_translated.mp4
2025-07-19 12:28:21 | INFO     | app.services.base_service:update_task_status:54 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 status updated to completed (progress: 100.0%)
2025-07-19 12:28:21 | INFO     | app.services.task_service:process_full_pipeline:249 - Full pipeline completed for task 21d3f806-279a-43fc-afac-770e2f3524a9
2025-07-19 12:28:21 | WARNING  | app.services.task_service:process_full_pipeline:253 - Task 21d3f806-279a-43fc-afac-770e2f3524a9 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 12:43:50 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:43:51 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:44:03 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:44:03 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:44:03 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:44:03 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:44:03 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 12:44:03 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 12:44:03 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 12:44:11 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s.mp4
2025-07-19 12:44:11 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 12:44:11 | INFO     | app.services.base_service:create_task:35 - Created task 6a119aa9-9366-4477-81b1-088440e82446 of type full_pipeline
2025-07-19 12:44:11 | INFO     | app.services.base_service:update_task_status:54 - Task 6a119aa9-9366-4477-81b1-088440e82446 status updated to processing (progress: 0.0%)
2025-07-19 12:44:11 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:44:11 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:44:11 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:44:11 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:44:11 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:44:13 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:44:15 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:44:15 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:44:15 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:44:15 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:44:15 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:44:15 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_eb5ae283.wav
2025-07-19 12:44:15 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 12:44:15 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:44:15 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s.mp4 to storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_audio.wav
2025-07-19 12:44:19 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_audio.wav (duration: 30.06s)
2025-07-19 12:44:19 | INFO     | app.services.base_service:update_task_status:54 - Task 6a119aa9-9366-4477-81b1-088440e82446 status updated to processing (progress: 20.0%)
2025-07-19 12:44:19 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:44:19 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 12:44:20 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_audio.wav
2025-07-19 12:44:31 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 12:44:31 | INFO     | app.services.base_service:update_task_status:54 - Task 6a119aa9-9366-4477-81b1-088440e82446 status updated to processing (progress: 40.0%)
2025-07-19 12:44:31 | INFO     | app.services.task_service:process_full_pipeline:95 - Step 3: Translation for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:44:36 | INFO     | app.services.base_service:update_task_status:54 - Task 6a119aa9-9366-4477-81b1-088440e82446 status updated to processing (progress: 60.0%)
2025-07-19 12:44:36 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 4: Generating subtitles for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:44:51 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_57cb64a8.srt
2025-07-19 12:44:51 | INFO     | app.services.base_service:update_task_status:54 - Task 6a119aa9-9366-4477-81b1-088440e82446 status updated to processing (progress: 80.0%)
2025-07-19 12:44:51 | INFO     | app.services.task_service:process_full_pipeline:135 - Step 5: TTS synthesis for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:44:51 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 1/3
2025-07-19 12:44:51 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser编辑器V4的快速入门课程。今天我们将探讨如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那你来对地方了。Faser编辑器V4...
2025-07-19 12:44:51 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 12:44:51 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 12:44:51 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:44:51 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:44:51 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:44:51 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:44:53 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:44:55 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:44:55 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:44:55 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:44:55 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:44:55 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:44:55 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_12e773dd.wav
2025-07-19 12:44:55 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_12e773dd.wav', 'duration': 33.0, 'text': '大家好，欢迎来到Faser编辑器V4的快速入门课程。今天我们将探讨如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那你来对地方了。Faser编辑器V4是一款强大的可视化HTML5游戏开发工具，让你能专注于创作而非迷失在代码中。在本视频中，我将逐步引导你通过编辑器设置项目并创建一个简单游戏。以下是今天的内容概要：什么是Faser编辑器？如何启动一个全新项目？我们将对编辑器界面进行全局概览。', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 12:44:55 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_12e773dd.wav
2025-07-19 12:44:55 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 33.0
2025-07-19 12:44:55 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 12:44:55 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 12:44:55 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:44:55 | INFO     | app.services.task_service:process_full_pipeline:178 - Retrying TTS in 3 seconds...
2025-07-19 12:44:58 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 2/3
2025-07-19 12:44:58 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser编辑器V4的快速入门课程。今天我们将探讨如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那你来对地方了。Faser编辑器V4...
2025-07-19 12:44:58 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 12:44:58 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 12:44:58 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:44:58 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:44:58 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:44:58 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:45:00 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:45:02 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:45:02 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:45:02 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:45:02 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:45:02 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:45:03 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_a49190f6.wav
2025-07-19 12:45:03 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_a49190f6.wav', 'duration': 33.0, 'text': '大家好，欢迎来到Faser编辑器V4的快速入门课程。今天我们将探讨如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那你来对地方了。Faser编辑器V4是一款强大的可视化HTML5游戏开发工具，让你能专注于创作而非迷失在代码中。在本视频中，我将逐步引导你通过编辑器设置项目并创建一个简单游戏。以下是今天的内容概要：什么是Faser编辑器？如何启动一个全新项目？我们将对编辑器界面进行全局概览。', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 12:45:03 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_a49190f6.wav
2025-07-19 12:45:03 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 33.0
2025-07-19 12:45:03 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 12:45:03 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 12:45:03 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:45:03 | INFO     | app.services.task_service:process_full_pipeline:178 - Retrying TTS in 6 seconds...
2025-07-19 12:45:09 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 3/3
2025-07-19 12:45:09 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser编辑器V4的快速入门课程。今天我们将探讨如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那你来对地方了。Faser编辑器V4...
2025-07-19 12:45:09 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 12:45:09 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 12:45:09 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:45:09 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:45:09 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:45:09 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:45:11 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:45:13 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:45:13 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:45:13 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:45:13 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:45:13 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:45:13 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_59d9e6da.wav
2025-07-19 12:45:13 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_59d9e6da.wav', 'duration': 33.0, 'text': '大家好，欢迎来到Faser编辑器V4的快速入门课程。今天我们将探讨如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那你来对地方了。Faser编辑器V4是一款强大的可视化HTML5游戏开发工具，让你能专注于创作而非迷失在代码中。在本视频中，我将逐步引导你通过编辑器设置项目并创建一个简单游戏。以下是今天的内容概要：什么是Faser编辑器？如何启动一个全新项目？我们将对编辑器界面进行全局概览。', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 12:45:13 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_59d9e6da.wav
2025-07-19 12:45:13 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 33.0
2025-07-19 12:45:13 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 12:45:13 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 12:45:13 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 12:45:13 | WARNING  | app.services.task_service:process_full_pipeline:183 - Accepting TTS fallback result after all retries
2025-07-19 12:45:13 | INFO     | app.services.base_service:update_task_status:54 - Task 6a119aa9-9366-4477-81b1-088440e82446 status updated to processing (progress: 90.0%)
2025-07-19 12:45:13 | INFO     | app.services.task_service:process_full_pipeline:209 - Step 6: Video composition for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:45:13 | INFO     | app.services.video_service:compose_video:109 - Composing video: F:\project\ai\video\python-ai-service\storage\uploads\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s.mp4 + F:\project\ai\video\python-ai-service\storage\outputs\tts_59d9e6da.wav
2025-07-19 12:45:13 | INFO     | app.services.video_service:compose_video:110 - 输出路径: F:\project\ai\video\python-ai-service\storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_translated.mp4
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:157 - Audio replacement successful
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:178 - Subtitle file generated: storage\outputs\subtitles_57cb64a8.srt
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:179 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:192 - 开始写入视频文件: storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_translated.mp4
2025-07-19 12:45:14 | ERROR    | app.services.video_service:compose_video:238 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 12:45:14 | WARNING  | app.services.video_service:compose_video:242 - 检测到Windows服务环境stdout问题，直接使用FFmpeg命令
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:245 - MoviePy失败，立即使用FFmpeg直接命令
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:275 - 使用音频替换模式
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:286 - 执行FFmpeg命令: ffmpeg -y -i F:\project\ai\video\python-ai-service\storage\uploads\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s.mp4 -i F:\project\ai\video\python-ai-service\storage\outputs\tts_59d9e6da.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest F:\project\ai\video\python-ai-service\storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_translated.mp4
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:287 - 输入视频: F:\project\ai\video\python-ai-service\storage\uploads\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s.mp4
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:289 - 输入音频: F:\project\ai\video\python-ai-service\storage\outputs\tts_59d9e6da.wav
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:290 - 输出文件: F:\project\ai\video\python-ai-service\storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_translated.mp4
2025-07-19 12:45:14 | INFO     | app.services.video_service:compose_video:302 - FFmpeg命令执行成功: storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_translated.mp4
2025-07-19 12:45:15 | INFO     | app.services.video_service:compose_video:319 - FFmpeg视频合成完成，跳过重复处理
2025-07-19 12:45:15 | INFO     | app.services.video_service:compose_video:370 - Video composition completed: F:\project\ai\video\python-ai-service\storage\outputs\f93bb89b-d749-4519-8057-9fdf6d8e67ff_test_30s_translated.mp4
2025-07-19 12:45:15 | INFO     | app.services.base_service:update_task_status:54 - Task 6a119aa9-9366-4477-81b1-088440e82446 status updated to completed (progress: 100.0%)
2025-07-19 12:45:15 | INFO     | app.services.task_service:process_full_pipeline:249 - Full pipeline completed for task 6a119aa9-9366-4477-81b1-088440e82446
2025-07-19 12:45:15 | WARNING  | app.services.task_service:process_full_pipeline:253 - Task 6a119aa9-9366-4477-81b1-088440e82446 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 12:48:00 | INFO     | app.api.routes:upload_file:88 - 上传文件: input.mp4 -> storage\uploads\13f61cb3-f799-4ab6-a9dc-e2a60937eb82_input.mp4
2025-07-19 12:48:01 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\13f61cb3-f799-4ab6-a9dc-e2a60937eb82_input.mp4 (大小: 225912517 bytes)
2025-07-19 12:48:01 | INFO     | app.services.base_service:create_task:35 - Created task 801bb5c1-1234-4ad2-989c-a93c2c88644f of type full_pipeline
2025-07-19 12:48:01 | INFO     | app.services.base_service:update_task_status:54 - Task 801bb5c1-1234-4ad2-989c-a93c2c88644f status updated to processing (progress: 0.0%)
2025-07-19 12:48:01 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task 801bb5c1-1234-4ad2-989c-a93c2c88644f
2025-07-19 12:48:01 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:48:01 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:48:01 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:48:01 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:48:03 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:48:05 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:48:05 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:48:05 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:48:05 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:48:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:48:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_40bc72a4.wav
2025-07-19 12:48:05 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 12:48:05 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task 801bb5c1-1234-4ad2-989c-a93c2c88644f
2025-07-19 12:48:05 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\13f61cb3-f799-4ab6-a9dc-e2a60937eb82_input.mp4 to storage\outputs\13f61cb3-f799-4ab6-a9dc-e2a60937eb82_input_audio.wav
2025-07-19 12:48:35 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\13f61cb3-f799-4ab6-a9dc-e2a60937eb82_input_audio.wav (duration: 4242.68s)
2025-07-19 12:48:35 | INFO     | app.services.base_service:update_task_status:54 - Task 801bb5c1-1234-4ad2-989c-a93c2c88644f status updated to processing (progress: 20.0%)
2025-07-19 12:48:35 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task 801bb5c1-1234-4ad2-989c-a93c2c88644f
2025-07-19 12:48:35 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 12:48:35 | ERROR    | app.services.task_service:process_full_pipeline:256 - Full pipeline failed for task 801bb5c1-1234-4ad2-989c-a93c2c88644f: 'Whisper' object has no attribute 'name'
2025-07-19 12:48:35 | INFO     | app.services.base_service:update_task_status:54 - Task 801bb5c1-1234-4ad2-989c-a93c2c88644f status updated to failed (progress: 0.0%)
2025-07-19 12:50:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:50:46 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:50:58 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:50:58 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:50:58 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 12:50:58 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 12:50:58 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 12:50:58 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 12:50:58 | INFO     | main:lifespan:37 - 环境: 开发
2025-07-19 12:51:11 | INFO     | app.api.routes:upload_file:88 - 上传文件: input.mp4 -> storage\uploads\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input.mp4
2025-07-19 12:51:12 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input.mp4 (大小: 225912517 bytes)
2025-07-19 12:51:12 | INFO     | app.services.base_service:create_task:35 - Created task 0dabd902-4f1d-4338-848b-e419697798c6 of type full_pipeline
2025-07-19 12:51:12 | INFO     | app.services.base_service:update_task_status:54 - Task 0dabd902-4f1d-4338-848b-e419697798c6 status updated to processing (progress: 0.0%)
2025-07-19 12:51:12 | INFO     | app.services.task_service:process_full_pipeline:48 - Pre-flight check: Testing TTS service for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 12:51:12 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 12:51:12 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 12:51:12 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 12:51:12 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 12:51:14 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 12:51:16 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 12:51:16 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 12:51:16 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 12:51:16 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 12:51:16 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 12:51:16 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_5042314d.wav
2025-07-19 12:51:16 | WARNING  | app.services.task_service:process_full_pipeline:69 - TTS pre-flight check returned fallback: Used local TTS as fallback due to online services unavailability
2025-07-19 12:51:16 | INFO     | app.services.task_service:process_full_pipeline:76 - Step 1: Extracting audio for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 12:51:16 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input.mp4 to storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_audio.wav
2025-07-19 12:51:49 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_audio.wav (duration: 4242.68s)
2025-07-19 12:51:49 | INFO     | app.services.base_service:update_task_status:54 - Task 0dabd902-4f1d-4338-848b-e419697798c6 status updated to processing (progress: 20.0%)
2025-07-19 12:51:49 | INFO     | app.services.task_service:process_full_pipeline:86 - Step 2: Speech recognition for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 12:51:49 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 12:51:50 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_audio.wav
2025-07-19 13:07:43 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.11, Segments: 823
2025-07-19 13:07:43 | INFO     | app.services.base_service:update_task_status:54 - Task 0dabd902-4f1d-4338-848b-e419697798c6 status updated to processing (progress: 40.0%)
2025-07-19 13:07:43 | INFO     | app.services.task_service:process_full_pipeline:95 - Step 3: Translation for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 13:09:10 | INFO     | app.services.base_service:update_task_status:54 - Task 0dabd902-4f1d-4338-848b-e419697798c6 status updated to processing (progress: 60.0%)
2025-07-19 13:09:10 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 4: Generating subtitles for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 13:33:55 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_5511c09a.srt
2025-07-19 13:33:55 | INFO     | app.services.base_service:update_task_status:54 - Task 0dabd902-4f1d-4338-848b-e419697798c6 status updated to processing (progress: 80.0%)
2025-07-19 13:33:55 | INFO     | app.services.task_service:process_full_pipeline:135 - Step 5: TTS synthesis for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 13:33:55 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 1/3
2025-07-19 13:33:55 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser Editor V4速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser E...
2025-07-19 13:33:55 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 13:33:55 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 13:33:55 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 13:33:55 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 13:33:55 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 13:33:55 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 13:33:57 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 13:33:59 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 13:33:59 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 13:33:59 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 13:33:59 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 13:33:59 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 13:34:02 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_dc5f08d0.wav
2025-07-19 13:34:02 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_dc5f08d0.wav', 'duration': 604.9499999999999, 'text': '大家好，欢迎来到Faser Editor V4速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser Editor V4是一个强大的工具，可以可视化地构建HTML5游戏，让你专注于创作，而不是迷失在代码中。在本视频中，我将逐步指导你如何使用编辑器设置项目并创建一个简单的游戏。以下是今天的内容： **Faser Editor是什么？** - 如何快速设置一个新项目 - 对UI中各个视图的工作台进行概览 - 编辑器如何根据你在GUI中的操作自动生成代码 - 如何在本地运行和测试游戏 - 添加键盘输入并根据这些事件移动游戏对象 - 使用Arcade物理系统和碰撞 - 处理对象重叠 - 使用预制件（Prefabs）创建可复用的游戏组件 - 控制对象图层和渲染顺序 - 添加新资源并为游戏创建新动画 **准备工作：** 确保你已安装Faser Editor V4。我会在描述中附上文档链接，以便你获取详细的设置说明。在开始之前，请注意：本教程使用Faser Editor V4。如果你对JavaScript和Faser 3框架有一些经验会更有帮助，但即使你是完全的新手，也能轻松跟上。本视频不会涵盖安装过程，但别担心，我已经在描述中链接了相关文档和资源。 **项目目标：** 在这个速成课程中，我们将创建一个非常基础的Faser 3游戏。游戏的目标是帮助我们学习使用Faser Editor创建Faser 3游戏的基础知识。游戏中，我们将创建一个简单的标题场景，玩家可以点击进入下一个场景（学习场景过渡），然后学习如何移动玩家（恐龙）并与方块碰撞，最后拾取食物物品。我们还会学习如何添加不同变体的食物，加载精灵表（sprite sheets）并创建基本动画。虽然这是一个非常基础的游戏，但它能让我们掌握使用Faser Editor创建Faser 3游戏的基础。 **Faser Editor简介：** Faser Editor是一个游戏开发工具，旨在帮助你更快地构建Faser 3游戏。它可以协助填充游戏场景、管理资源，甚至生成代码。更正式地说，Faser Editor是一个强大的可视化开发工具，用于使用Faser游戏引擎创建2D游戏。凭借其直观的界面和丰富的功能集，它可以让不同技能水平的开发者快速轻松地为桌面和移动平台创建高质量的游戏。无论你是初学者还是经验丰富的开发者，Faser Editor都能提升你的Faser 3游戏开发体验。 **主要功能：** 1. **场景编辑器**：一个可视化工具，允许你通过放置图像和其他类型的游戏对象来创建Faser场景或关卡。你可以将各种Faser游戏对象类型拖放到场景中，比如图像、文本等。场景预览是直接用Faser渲染的，因此你可以确保所见即所得。 2. **物理编辑器**：这个工具允许你轻松创建和修改Arcade物理游戏对象。你可以直观地修改游戏对象的物理体，而无需通过代码手动完成。你仍然可以通过GUI添加碰撞和重叠检查。 3. **动画编辑器**：这个工具允许你从纹理帧手动或自动创建精灵动画，无需再为各种动画动态生成JSON文件。通过GUI，你可以修改帧率、延迟、重复时间比例等配置。 4. **资源包编辑器**：当你向项目添加新资源时，Faser Editor允许你将它们导入到资源包文件中，然后可以从一个中心位置加载所有游戏资源。资源包编辑器允许你查看这些文件，并查看包含的游戏资源类型，还可以修改JSON配置。 5. **预制件系统（Prefabs）**：预制件是Faser Editor中的一个关键概念，它允许你创建可复用的游戏对象，控制其外观、属性和设置，并存储在一个中心位置。然后，你可以通过使用该预制件创建该对象的多个实例。 **创建项目：** 打开Faser Editor后，点击“New Project”创建一个全新的Faser Editor项目。在下一个屏幕上，你可以为项目选择一个起始模板，这将为你的Faser 3游戏项目搭建基础。内置的三个项目模板是离线安装的一部分，而更高级的框架示例需要从在线下载。我们将使用基本的JavaScript模板。选择模板后，系统会提示你选择保存项目的位置。你需要创建一个新文件夹，我将其命名为“Hello World”。创建项目文件夹后，Faser Editor将设置项目模板并搭建所有本地运行游戏所需的项目文件。完成后，你将看到欢迎页面。从这里，我们可以选择“Open and Scene”从项目模板中包含的两个场景中选择一个。让我们打开“level”场景，现在编辑器将更新为显示Faser场景及其中的游戏对象。 **工作台概览：** 在Faser Editor项目的工作台或IDE中，你会看到许多不同的视图和菜单选项： - **大纲视图（Outline View）**：位于左侧，显示活动编辑器的所有内容，以层级结构呈现。例如，当我们打开一个场景时，我们会看到场景中包含的各种游戏对象。如果我们添加更多游戏对象，它们会出现在大纲中。 - **检查器面板（Inspector Panel）**：位于右侧，允许你查看当前在编辑器中选择的对象的属性。例如，如果选择图像游戏对象，面板会更新显示该游戏对象的所有属性，你可以修改变量名、位置等。 - **文件视图（Files View）**：位于屏幕底部，显示所有项目文件的列表。你可以执行基本操作，如添加新文件、删除、重命名或移动文件。 - **块视图（Blocks View）**：显示可用于在当前活动编辑器中构建对象的元素。例如，当场景编辑器打开时，它会显示可以添加到场景中的各种内置Faser 3游戏对象类型，以及已加载的图像或其他资源。 - **主菜单**：位于右上角，允许你打开不同的项目、新项目窗口等。你还可以更改场景的默认布局、选择主题颜色等。 - **支持聊天按钮**：用于联系Faser Studio团队。 - **快捷按钮**：位于左上角，包括创建新内容的快捷方式、播放按钮（用于在浏览器中运行项目）、快速打开场景的对话框，以及在Visual Studio Code中打开项目的按钮。 **场景编辑器工具：** - **平移工具（Translate Tool）**：允许你选择游戏对象并在场景中拖动它，更新其X和Y位置。 - **缩放工具（Scale Tool）**：允许你拖动并缩放游戏对象的X和Y坐标。 - **旋转工具（Rotate Tool）**：允许你选择游戏对象并旋转它，修改其角度。 - **原点工具（Origin Tool）**：允许你修改游戏对象的原点。默认情况下，大多数游戏对象的原点位于中心（X和Y均为0.5）。原点是Faser放置游戏对象的起点。 - **区域选择工具（Select All Objects Within a Region Tool）**：允许你拖动一个区域来选择多个游戏对象。 - **平移工具（Pan Tool）**：允许你通过点击和拖动场景来导航视口。 **代码生成：** 当我们在场景中移动对象时，Faser Editor会动态地为我们的Faser项目生成代码。要查看此代码，我们需要打开与“level”场景关联的.js文件。在GUI中创建新文件时，通常会有两个文件：一个是.scene文件（用于在GUI中呈现），另一个是.js或.ts文件（根据我们的操作生成的代码）。生成的代码分为两部分：编译器生成的代码和用户可以安全添加代码的区域。用户代码应放在“start user code”和“end user code”块之间，以避免被覆盖。 **本地运行和测试游戏：** 要在本地运行和测试游戏，你需要运行开发服务器。默认情况下，Faser Editor提供了一个内置的HTTP服务器。使用基本的JavaScript项目模板时，点击左上角的播放按钮将启动开发服务器并在浏览器中打开游戏。如果使用其他项目模板（如基于Webpack、Parcel或Vite），则需要手动运行开发服务器。 **输入类型：** Faser框架支持多种输入类型，包括键盘、鼠标、游戏手柄和移动设备的触摸输入。我们将首先看看点击事件。点击事件是玩家与Faser场景或游戏对象交互的一种机制。默认情况下，我们的Faser模板支持点击事件。在场景编辑器中选择游戏对象时，可以定义其点击区域（hit area），即玩家可以点击的区域。我们可以监听点击事件和其他事件类型。 **键盘事件：** 接下来，我们将添加对键盘事件的支持。我们将监听箭头键并移动游戏对象。从内置块中，我们可以添加键盘键到场景中。选择键码（如“UP”），然后在代码中监听按键按下事件。我们将在场景的“update”方法中检查按键状态，并更新游戏对象的位置或速度。 **物理系统：** Faser Editor支持Faser的Arcade物理系统，允许我们检查两个游戏对象之间的碰撞或重叠。要启用物理系统，我们需要更新游戏配置以指定使用的物理系统（如Arcade Physics）。然后，我们需要为游戏对象添加物理体（physics body），以便进行碰撞检查。可以通过将游戏对象类型更改为“Arcade Image”或使用内置的“Arcade Image”或“Arcade Sprite”类型来添加物理体。 **碰撞和重叠：** 使用物理系统检查碰撞时，我们需要添加碰撞器（collider）来指定要检查碰撞的游戏对象或组。我们还可以提供回调函数，在碰撞发生时执行。重叠（overlap）是一种允许游戏对象穿过另一个对象但仍能收到通知的机制，适用于拾取物品或触发区域。 **预制件（Prefabs）：**', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 13:34:02 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_dc5f08d0.wav
2025-07-19 13:34:02 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 604.9499999999999
2025-07-19 13:34:02 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 13:34:02 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 13:34:02 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 13:34:02 | INFO     | app.services.task_service:process_full_pipeline:178 - Retrying TTS in 3 seconds...
2025-07-19 13:34:05 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 2/3
2025-07-19 13:34:05 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser Editor V4速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser E...
2025-07-19 13:34:05 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 13:34:05 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 13:34:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 13:34:05 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 13:34:05 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 13:34:05 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 13:34:07 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 13:34:09 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 13:34:09 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 13:34:09 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 13:34:09 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 13:34:09 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 13:34:11 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_e93fb164.wav
2025-07-19 13:34:11 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_e93fb164.wav', 'duration': 604.9499999999999, 'text': '大家好，欢迎来到Faser Editor V4速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser Editor V4是一个强大的工具，可以可视化地构建HTML5游戏，让你专注于创作，而不是迷失在代码中。在本视频中，我将逐步指导你如何使用编辑器设置项目并创建一个简单的游戏。以下是今天的内容： **Faser Editor是什么？** - 如何快速设置一个新项目 - 对UI中各个视图的工作台进行概览 - 编辑器如何根据你在GUI中的操作自动生成代码 - 如何在本地运行和测试游戏 - 添加键盘输入并根据这些事件移动游戏对象 - 使用Arcade物理系统和碰撞 - 处理对象重叠 - 使用预制件（Prefabs）创建可复用的游戏组件 - 控制对象图层和渲染顺序 - 添加新资源并为游戏创建新动画 **准备工作：** 确保你已安装Faser Editor V4。我会在描述中附上文档链接，以便你获取详细的设置说明。在开始之前，请注意：本教程使用Faser Editor V4。如果你对JavaScript和Faser 3框架有一些经验会更有帮助，但即使你是完全的新手，也能轻松跟上。本视频不会涵盖安装过程，但别担心，我已经在描述中链接了相关文档和资源。 **项目目标：** 在这个速成课程中，我们将创建一个非常基础的Faser 3游戏。游戏的目标是帮助我们学习使用Faser Editor创建Faser 3游戏的基础知识。游戏中，我们将创建一个简单的标题场景，玩家可以点击进入下一个场景（学习场景过渡），然后学习如何移动玩家（恐龙）并与方块碰撞，最后拾取食物物品。我们还会学习如何添加不同变体的食物，加载精灵表（sprite sheets）并创建基本动画。虽然这是一个非常基础的游戏，但它能让我们掌握使用Faser Editor创建Faser 3游戏的基础。 **Faser Editor简介：** Faser Editor是一个游戏开发工具，旨在帮助你更快地构建Faser 3游戏。它可以协助填充游戏场景、管理资源，甚至生成代码。更正式地说，Faser Editor是一个强大的可视化开发工具，用于使用Faser游戏引擎创建2D游戏。凭借其直观的界面和丰富的功能集，它可以让不同技能水平的开发者快速轻松地为桌面和移动平台创建高质量的游戏。无论你是初学者还是经验丰富的开发者，Faser Editor都能提升你的Faser 3游戏开发体验。 **主要功能：** 1. **场景编辑器**：一个可视化工具，允许你通过放置图像和其他类型的游戏对象来创建Faser场景或关卡。你可以将各种Faser游戏对象类型拖放到场景中，比如图像、文本等。场景预览是直接用Faser渲染的，因此你可以确保所见即所得。 2. **物理编辑器**：这个工具允许你轻松创建和修改Arcade物理游戏对象。你可以直观地修改游戏对象的物理体，而无需通过代码手动完成。你仍然可以通过GUI添加碰撞和重叠检查。 3. **动画编辑器**：这个工具允许你从纹理帧手动或自动创建精灵动画，无需再为各种动画动态生成JSON文件。通过GUI，你可以修改帧率、延迟、重复时间比例等配置。 4. **资源包编辑器**：当你向项目添加新资源时，Faser Editor允许你将它们导入到资源包文件中，然后可以从一个中心位置加载所有游戏资源。资源包编辑器允许你查看这些文件，并查看包含的游戏资源类型，还可以修改JSON配置。 5. **预制件系统（Prefabs）**：预制件是Faser Editor中的一个关键概念，它允许你创建可复用的游戏对象，控制其外观、属性和设置，并存储在一个中心位置。然后，你可以通过使用该预制件创建该对象的多个实例。 **创建项目：** 打开Faser Editor后，点击“New Project”创建一个全新的Faser Editor项目。在下一个屏幕上，你可以为项目选择一个起始模板，这将为你的Faser 3游戏项目搭建基础。内置的三个项目模板是离线安装的一部分，而更高级的框架示例需要从在线下载。我们将使用基本的JavaScript模板。选择模板后，系统会提示你选择保存项目的位置。你需要创建一个新文件夹，我将其命名为“Hello World”。创建项目文件夹后，Faser Editor将设置项目模板并搭建所有本地运行游戏所需的项目文件。完成后，你将看到欢迎页面。从这里，我们可以选择“Open and Scene”从项目模板中包含的两个场景中选择一个。让我们打开“level”场景，现在编辑器将更新为显示Faser场景及其中的游戏对象。 **工作台概览：** 在Faser Editor项目的工作台或IDE中，你会看到许多不同的视图和菜单选项： - **大纲视图（Outline View）**：位于左侧，显示活动编辑器的所有内容，以层级结构呈现。例如，当我们打开一个场景时，我们会看到场景中包含的各种游戏对象。如果我们添加更多游戏对象，它们会出现在大纲中。 - **检查器面板（Inspector Panel）**：位于右侧，允许你查看当前在编辑器中选择的对象的属性。例如，如果选择图像游戏对象，面板会更新显示该游戏对象的所有属性，你可以修改变量名、位置等。 - **文件视图（Files View）**：位于屏幕底部，显示所有项目文件的列表。你可以执行基本操作，如添加新文件、删除、重命名或移动文件。 - **块视图（Blocks View）**：显示可用于在当前活动编辑器中构建对象的元素。例如，当场景编辑器打开时，它会显示可以添加到场景中的各种内置Faser 3游戏对象类型，以及已加载的图像或其他资源。 - **主菜单**：位于右上角，允许你打开不同的项目、新项目窗口等。你还可以更改场景的默认布局、选择主题颜色等。 - **支持聊天按钮**：用于联系Faser Studio团队。 - **快捷按钮**：位于左上角，包括创建新内容的快捷方式、播放按钮（用于在浏览器中运行项目）、快速打开场景的对话框，以及在Visual Studio Code中打开项目的按钮。 **场景编辑器工具：** - **平移工具（Translate Tool）**：允许你选择游戏对象并在场景中拖动它，更新其X和Y位置。 - **缩放工具（Scale Tool）**：允许你拖动并缩放游戏对象的X和Y坐标。 - **旋转工具（Rotate Tool）**：允许你选择游戏对象并旋转它，修改其角度。 - **原点工具（Origin Tool）**：允许你修改游戏对象的原点。默认情况下，大多数游戏对象的原点位于中心（X和Y均为0.5）。原点是Faser放置游戏对象的起点。 - **区域选择工具（Select All Objects Within a Region Tool）**：允许你拖动一个区域来选择多个游戏对象。 - **平移工具（Pan Tool）**：允许你通过点击和拖动场景来导航视口。 **代码生成：** 当我们在场景中移动对象时，Faser Editor会动态地为我们的Faser项目生成代码。要查看此代码，我们需要打开与“level”场景关联的.js文件。在GUI中创建新文件时，通常会有两个文件：一个是.scene文件（用于在GUI中呈现），另一个是.js或.ts文件（根据我们的操作生成的代码）。生成的代码分为两部分：编译器生成的代码和用户可以安全添加代码的区域。用户代码应放在“start user code”和“end user code”块之间，以避免被覆盖。 **本地运行和测试游戏：** 要在本地运行和测试游戏，你需要运行开发服务器。默认情况下，Faser Editor提供了一个内置的HTTP服务器。使用基本的JavaScript项目模板时，点击左上角的播放按钮将启动开发服务器并在浏览器中打开游戏。如果使用其他项目模板（如基于Webpack、Parcel或Vite），则需要手动运行开发服务器。 **输入类型：** Faser框架支持多种输入类型，包括键盘、鼠标、游戏手柄和移动设备的触摸输入。我们将首先看看点击事件。点击事件是玩家与Faser场景或游戏对象交互的一种机制。默认情况下，我们的Faser模板支持点击事件。在场景编辑器中选择游戏对象时，可以定义其点击区域（hit area），即玩家可以点击的区域。我们可以监听点击事件和其他事件类型。 **键盘事件：** 接下来，我们将添加对键盘事件的支持。我们将监听箭头键并移动游戏对象。从内置块中，我们可以添加键盘键到场景中。选择键码（如“UP”），然后在代码中监听按键按下事件。我们将在场景的“update”方法中检查按键状态，并更新游戏对象的位置或速度。 **物理系统：** Faser Editor支持Faser的Arcade物理系统，允许我们检查两个游戏对象之间的碰撞或重叠。要启用物理系统，我们需要更新游戏配置以指定使用的物理系统（如Arcade Physics）。然后，我们需要为游戏对象添加物理体（physics body），以便进行碰撞检查。可以通过将游戏对象类型更改为“Arcade Image”或使用内置的“Arcade Image”或“Arcade Sprite”类型来添加物理体。 **碰撞和重叠：** 使用物理系统检查碰撞时，我们需要添加碰撞器（collider）来指定要检查碰撞的游戏对象或组。我们还可以提供回调函数，在碰撞发生时执行。重叠（overlap）是一种允许游戏对象穿过另一个对象但仍能收到通知的机制，适用于拾取物品或触发区域。 **预制件（Prefabs）：**', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 13:34:11 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_e93fb164.wav
2025-07-19 13:34:11 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 604.9499999999999
2025-07-19 13:34:11 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 13:34:11 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 13:34:11 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 13:34:11 | INFO     | app.services.task_service:process_full_pipeline:178 - Retrying TTS in 6 seconds...
2025-07-19 13:34:17 | INFO     | app.services.task_service:process_full_pipeline:143 - TTS attempt 3/3
2025-07-19 13:34:17 | INFO     | app.services.task_service:process_full_pipeline:147 - TTS输入文本: 大家好，欢迎来到Faser Editor V4速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser E...
2025-07-19 13:34:17 | INFO     | app.services.task_service:process_full_pipeline:148 - TTS语音: local
2025-07-19 13:34:17 | INFO     | app.services.task_service:process_full_pipeline:149 - TTS服务: edge-tts
2025-07-19 13:34:17 | INFO     | app.services.tts_service:synthesize_speech:79 - Starting TTS synthesis with service: edge-tts
2025-07-19 13:34:17 | INFO     | app.services.tts_service:synthesize_speech:89 - Attempting Edge TTS synthesis...
2025-07-19 13:34:17 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:153 - Synthesizing speech with Edge TTS, voice: local
2025-07-19 13:34:17 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 1/3 failed: Invalid voice 'local'.
2025-07-19 13:34:19 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 2/3 failed: Invalid voice 'local'.
2025-07-19 13:34:21 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:183 - Edge TTS attempt 3/3 failed: Invalid voice 'local'.
2025-07-19 13:34:21 | WARNING  | app.services.tts_service:synthesize_speech:92 - Edge TTS failed: Edge TTS failed after 3 attempts. This may be due to network issues or service limitations.
2025-07-19 13:34:21 | INFO     | app.services.tts_service:synthesize_speech:95 - Falling back to local TTS...
2025-07-19 13:34:21 | INFO     | app.services.tts_service:synthesize_speech:100 - Attempting local TTS synthesis...
2025-07-19 13:34:21 | INFO     | app.services.tts_service:_synthesize_with_local_tts:364 - Using local TTS service as fallback
2025-07-19 13:34:24 | INFO     | app.services.tts_service:_synthesize_with_local_tts:399 - Local TTS synthesis completed: storage\outputs\tts_1a40d59e.wav
2025-07-19 13:34:24 | INFO     | app.services.task_service:process_full_pipeline:160 - TTS结果: {'audio_file_path': 'storage\\outputs\\tts_1a40d59e.wav', 'duration': 604.9499999999999, 'text': '大家好，欢迎来到Faser Editor V4速成课程。今天我们将探索如何快速轻松地构建Faser 3游戏，而无需手动编写大量代码。如果你是Faser或游戏开发的新手，那么你来对地方了。Faser Editor V4是一个强大的工具，可以可视化地构建HTML5游戏，让你专注于创作，而不是迷失在代码中。在本视频中，我将逐步指导你如何使用编辑器设置项目并创建一个简单的游戏。以下是今天的内容： **Faser Editor是什么？** - 如何快速设置一个新项目 - 对UI中各个视图的工作台进行概览 - 编辑器如何根据你在GUI中的操作自动生成代码 - 如何在本地运行和测试游戏 - 添加键盘输入并根据这些事件移动游戏对象 - 使用Arcade物理系统和碰撞 - 处理对象重叠 - 使用预制件（Prefabs）创建可复用的游戏组件 - 控制对象图层和渲染顺序 - 添加新资源并为游戏创建新动画 **准备工作：** 确保你已安装Faser Editor V4。我会在描述中附上文档链接，以便你获取详细的设置说明。在开始之前，请注意：本教程使用Faser Editor V4。如果你对JavaScript和Faser 3框架有一些经验会更有帮助，但即使你是完全的新手，也能轻松跟上。本视频不会涵盖安装过程，但别担心，我已经在描述中链接了相关文档和资源。 **项目目标：** 在这个速成课程中，我们将创建一个非常基础的Faser 3游戏。游戏的目标是帮助我们学习使用Faser Editor创建Faser 3游戏的基础知识。游戏中，我们将创建一个简单的标题场景，玩家可以点击进入下一个场景（学习场景过渡），然后学习如何移动玩家（恐龙）并与方块碰撞，最后拾取食物物品。我们还会学习如何添加不同变体的食物，加载精灵表（sprite sheets）并创建基本动画。虽然这是一个非常基础的游戏，但它能让我们掌握使用Faser Editor创建Faser 3游戏的基础。 **Faser Editor简介：** Faser Editor是一个游戏开发工具，旨在帮助你更快地构建Faser 3游戏。它可以协助填充游戏场景、管理资源，甚至生成代码。更正式地说，Faser Editor是一个强大的可视化开发工具，用于使用Faser游戏引擎创建2D游戏。凭借其直观的界面和丰富的功能集，它可以让不同技能水平的开发者快速轻松地为桌面和移动平台创建高质量的游戏。无论你是初学者还是经验丰富的开发者，Faser Editor都能提升你的Faser 3游戏开发体验。 **主要功能：** 1. **场景编辑器**：一个可视化工具，允许你通过放置图像和其他类型的游戏对象来创建Faser场景或关卡。你可以将各种Faser游戏对象类型拖放到场景中，比如图像、文本等。场景预览是直接用Faser渲染的，因此你可以确保所见即所得。 2. **物理编辑器**：这个工具允许你轻松创建和修改Arcade物理游戏对象。你可以直观地修改游戏对象的物理体，而无需通过代码手动完成。你仍然可以通过GUI添加碰撞和重叠检查。 3. **动画编辑器**：这个工具允许你从纹理帧手动或自动创建精灵动画，无需再为各种动画动态生成JSON文件。通过GUI，你可以修改帧率、延迟、重复时间比例等配置。 4. **资源包编辑器**：当你向项目添加新资源时，Faser Editor允许你将它们导入到资源包文件中，然后可以从一个中心位置加载所有游戏资源。资源包编辑器允许你查看这些文件，并查看包含的游戏资源类型，还可以修改JSON配置。 5. **预制件系统（Prefabs）**：预制件是Faser Editor中的一个关键概念，它允许你创建可复用的游戏对象，控制其外观、属性和设置，并存储在一个中心位置。然后，你可以通过使用该预制件创建该对象的多个实例。 **创建项目：** 打开Faser Editor后，点击“New Project”创建一个全新的Faser Editor项目。在下一个屏幕上，你可以为项目选择一个起始模板，这将为你的Faser 3游戏项目搭建基础。内置的三个项目模板是离线安装的一部分，而更高级的框架示例需要从在线下载。我们将使用基本的JavaScript模板。选择模板后，系统会提示你选择保存项目的位置。你需要创建一个新文件夹，我将其命名为“Hello World”。创建项目文件夹后，Faser Editor将设置项目模板并搭建所有本地运行游戏所需的项目文件。完成后，你将看到欢迎页面。从这里，我们可以选择“Open and Scene”从项目模板中包含的两个场景中选择一个。让我们打开“level”场景，现在编辑器将更新为显示Faser场景及其中的游戏对象。 **工作台概览：** 在Faser Editor项目的工作台或IDE中，你会看到许多不同的视图和菜单选项： - **大纲视图（Outline View）**：位于左侧，显示活动编辑器的所有内容，以层级结构呈现。例如，当我们打开一个场景时，我们会看到场景中包含的各种游戏对象。如果我们添加更多游戏对象，它们会出现在大纲中。 - **检查器面板（Inspector Panel）**：位于右侧，允许你查看当前在编辑器中选择的对象的属性。例如，如果选择图像游戏对象，面板会更新显示该游戏对象的所有属性，你可以修改变量名、位置等。 - **文件视图（Files View）**：位于屏幕底部，显示所有项目文件的列表。你可以执行基本操作，如添加新文件、删除、重命名或移动文件。 - **块视图（Blocks View）**：显示可用于在当前活动编辑器中构建对象的元素。例如，当场景编辑器打开时，它会显示可以添加到场景中的各种内置Faser 3游戏对象类型，以及已加载的图像或其他资源。 - **主菜单**：位于右上角，允许你打开不同的项目、新项目窗口等。你还可以更改场景的默认布局、选择主题颜色等。 - **支持聊天按钮**：用于联系Faser Studio团队。 - **快捷按钮**：位于左上角，包括创建新内容的快捷方式、播放按钮（用于在浏览器中运行项目）、快速打开场景的对话框，以及在Visual Studio Code中打开项目的按钮。 **场景编辑器工具：** - **平移工具（Translate Tool）**：允许你选择游戏对象并在场景中拖动它，更新其X和Y位置。 - **缩放工具（Scale Tool）**：允许你拖动并缩放游戏对象的X和Y坐标。 - **旋转工具（Rotate Tool）**：允许你选择游戏对象并旋转它，修改其角度。 - **原点工具（Origin Tool）**：允许你修改游戏对象的原点。默认情况下，大多数游戏对象的原点位于中心（X和Y均为0.5）。原点是Faser放置游戏对象的起点。 - **区域选择工具（Select All Objects Within a Region Tool）**：允许你拖动一个区域来选择多个游戏对象。 - **平移工具（Pan Tool）**：允许你通过点击和拖动场景来导航视口。 **代码生成：** 当我们在场景中移动对象时，Faser Editor会动态地为我们的Faser项目生成代码。要查看此代码，我们需要打开与“level”场景关联的.js文件。在GUI中创建新文件时，通常会有两个文件：一个是.scene文件（用于在GUI中呈现），另一个是.js或.ts文件（根据我们的操作生成的代码）。生成的代码分为两部分：编译器生成的代码和用户可以安全添加代码的区域。用户代码应放在“start user code”和“end user code”块之间，以避免被覆盖。 **本地运行和测试游戏：** 要在本地运行和测试游戏，你需要运行开发服务器。默认情况下，Faser Editor提供了一个内置的HTTP服务器。使用基本的JavaScript项目模板时，点击左上角的播放按钮将启动开发服务器并在浏览器中打开游戏。如果使用其他项目模板（如基于Webpack、Parcel或Vite），则需要手动运行开发服务器。 **输入类型：** Faser框架支持多种输入类型，包括键盘、鼠标、游戏手柄和移动设备的触摸输入。我们将首先看看点击事件。点击事件是玩家与Faser场景或游戏对象交互的一种机制。默认情况下，我们的Faser模板支持点击事件。在场景编辑器中选择游戏对象时，可以定义其点击区域（hit area），即玩家可以点击的区域。我们可以监听点击事件和其他事件类型。 **键盘事件：** 接下来，我们将添加对键盘事件的支持。我们将监听箭头键并移动游戏对象。从内置块中，我们可以添加键盘键到场景中。选择键码（如“UP”），然后在代码中监听按键按下事件。我们将在场景的“update”方法中检查按键状态，并更新游戏对象的位置或速度。 **物理系统：** Faser Editor支持Faser的Arcade物理系统，允许我们检查两个游戏对象之间的碰撞或重叠。要启用物理系统，我们需要更新游戏配置以指定使用的物理系统（如Arcade Physics）。然后，我们需要为游戏对象添加物理体（physics body），以便进行碰撞检查。可以通过将游戏对象类型更改为“Arcade Image”或使用内置的“Arcade Image”或“Arcade Sprite”类型来添加物理体。 **碰撞和重叠：** 使用物理系统检查碰撞时，我们需要添加碰撞器（collider）来指定要检查碰撞的游戏对象或组。我们还可以提供回调函数，在碰撞发生时执行。重叠（overlap）是一种允许游戏对象穿过另一个对象但仍能收到通知的机制，适用于拾取物品或触发区域。 **预制件（Prefabs）：**', 'fallback': True, 'message': 'Used local TTS as fallback due to online services unavailability'}
2025-07-19 13:34:24 | INFO     | app.services.task_service:process_full_pipeline:162 - TTS音频文件: storage\outputs\tts_1a40d59e.wav
2025-07-19 13:34:24 | INFO     | app.services.task_service:process_full_pipeline:163 - TTS时长: 604.9499999999999
2025-07-19 13:34:24 | INFO     | app.services.task_service:process_full_pipeline:164 - TTS回退: True
2025-07-19 13:34:24 | WARNING  | app.services.task_service:process_full_pipeline:166 - TTS回退消息: Used local TTS as fallback due to online services unavailability
2025-07-19 13:34:24 | WARNING  | app.services.task_service:process_full_pipeline:173 - TTS returned fallback result: Used local TTS as fallback due to online services unavailability
2025-07-19 13:34:24 | WARNING  | app.services.task_service:process_full_pipeline:183 - Accepting TTS fallback result after all retries
2025-07-19 13:34:24 | INFO     | app.services.base_service:update_task_status:54 - Task 0dabd902-4f1d-4338-848b-e419697798c6 status updated to processing (progress: 90.0%)
2025-07-19 13:34:24 | INFO     | app.services.task_service:process_full_pipeline:209 - Step 6: Video composition for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 13:34:24 | INFO     | app.services.video_service:compose_video:109 - Composing video: F:\project\ai\video\python-ai-service\storage\uploads\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input.mp4 + F:\project\ai\video\python-ai-service\storage\outputs\tts_1a40d59e.wav
2025-07-19 13:34:24 | INFO     | app.services.video_service:compose_video:110 - 输出路径: F:\project\ai\video\python-ai-service\storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_translated.mp4
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:157 - Audio replacement successful
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:178 - Subtitle file generated: storage\outputs\subtitles_5511c09a.srt
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:179 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:192 - 开始写入视频文件: storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_translated.mp4
2025-07-19 13:34:25 | ERROR    | app.services.video_service:compose_video:238 - write_videofile failed: 'NoneType' object has no attribute 'get_frame'
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:245 - MoviePy失败，立即使用FFmpeg直接命令
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:275 - 使用音频替换模式
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:286 - 执行FFmpeg命令: ffmpeg -y -i F:\project\ai\video\python-ai-service\storage\uploads\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input.mp4 -i F:\project\ai\video\python-ai-service\storage\outputs\tts_1a40d59e.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest F:\project\ai\video\python-ai-service\storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_translated.mp4
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:287 - 输入视频: F:\project\ai\video\python-ai-service\storage\uploads\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input.mp4
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:289 - 输入音频: F:\project\ai\video\python-ai-service\storage\outputs\tts_1a40d59e.wav
2025-07-19 13:34:25 | INFO     | app.services.video_service:compose_video:290 - 输出文件: F:\project\ai\video\python-ai-service\storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_translated.mp4
2025-07-19 13:34:34 | INFO     | app.services.video_service:compose_video:302 - FFmpeg命令执行成功: storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_translated.mp4
2025-07-19 13:34:34 | INFO     | app.services.video_service:compose_video:319 - FFmpeg视频合成完成，跳过重复处理
2025-07-19 13:34:34 | INFO     | app.services.video_service:compose_video:370 - Video composition completed: F:\project\ai\video\python-ai-service\storage\outputs\fb83e2a7-84b2-449c-98cb-c81e4ab133cf_input_translated.mp4
2025-07-19 13:34:34 | INFO     | app.services.base_service:update_task_status:54 - Task 0dabd902-4f1d-4338-848b-e419697798c6 status updated to completed (progress: 100.0%)
2025-07-19 13:34:34 | INFO     | app.services.task_service:process_full_pipeline:249 - Full pipeline completed for task 0dabd902-4f1d-4338-848b-e419697798c6
2025-07-19 13:34:34 | WARNING  | app.services.task_service:process_full_pipeline:253 - Task 0dabd902-4f1d-4338-848b-e419697798c6 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 15:21:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 15:21:34 | INFO     | __main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 15:21:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 15:21:45 | INFO     | __mp_main__:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 15:21:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 15:21:45 | INFO     | main:<module>:90 - 静态文件服务已启用: /static -> F:\project\ai\video\python-ai-service\storage
2025-07-19 15:21:45 | INFO     | main:lifespan:35 - 正在启动视频翻译AI服务
2025-07-19 15:21:45 | INFO     | main:lifespan:36 - 版本: 1.0.0
2025-07-19 15:21:45 | INFO     | main:lifespan:37 - 环境: 开发
