2025-07-13 08:41:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-13 08:41:05 | INFO     | main:lifespan:25 - Starting Video Translation AI Service
2025-07-13 08:41:05 | INFO     | main:lifespan:26 - Version: 1.0.0
2025-07-13 08:41:05 | INFO     | main:lifespan:27 - Environment: Development
2025-07-14 20:37:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:37:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:37:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:37:54 | INFO     | main:lifespan:25 - Starting Video Translation AI Service
2025-07-14 20:37:54 | INFO     | main:lifespan:26 - Version: 1.0.0
2025-07-14 20:37:54 | INFO     | main:lifespan:27 - Environment: Development
2025-07-14 20:40:12 | INFO     | main:lifespan:33 - Shutting down Video Translation AI Service
2025-07-14 20:40:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:40:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 20:40:18 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 20:40:18 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 20:40:18 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:09:22 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:12:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:12:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:12:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:12:49 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:12:49 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:12:49 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:15:53 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:16:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:16:06 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:16:06 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:16:06 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:16:06 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:16:06 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:16:16 | INFO     | app.services.base_service:create_task:32 - Created task bef3eca5-42ba-4103-9ebe-b36c96598714 of type full_pipeline
2025-07-14 21:16:16 | INFO     | app.services.task_service:process_full_pipeline:63 - Step 1: Extracting audio for task bef3eca5-42ba-4103-9ebe-b36c96598714
2025-07-14 21:16:16 | ERROR    | app.services.audio_service:extract_audio:108 - Audio extraction failed: Video file not found: /tmp/test_30s.mp4
2025-07-14 21:16:16 | ERROR    | app.services.task_service:process_full_pipeline:158 - Full pipeline failed for task bef3eca5-42ba-4103-9ebe-b36c96598714: Video file not found: /tmp/test_30s.mp4
2025-07-14 21:17:11 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:17:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:16 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:17:16 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:17:16 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:17:18 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:17:24 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:24 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:17:24 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:17:24 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:17:24 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:18:33 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:18:39 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:18:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:18:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:18:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:18:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:18:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:19:06 | INFO     | app.api.routes:upload_file:85 - 上传文件: test_30s.mp4 -> storage\uploads\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s.mp4
2025-07-14 21:19:06 | INFO     | app.api.routes:upload_file:92 - 文件上传成功: storage\uploads\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 21:19:06 | INFO     | app.services.base_service:create_task:32 - Created task cddc6cf0-e747-4a56-933a-4fe5a9ca2a01 of type audio_extraction
2025-07-14 21:19:06 | INFO     | app.services.base_service:update_task_status:40 - Task cddc6cf0-e747-4a56-933a-4fe5a9ca2a01 status updated to processing (progress: 0.0%)
2025-07-14 21:19:06 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s.mp4 to storage\outputs\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav
2025-07-14 21:19:20 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav (duration: 30.06s)
2025-07-14 21:19:20 | INFO     | app.services.base_service:update_task_status:40 - Task cddc6cf0-e747-4a56-933a-4fe5a9ca2a01 status updated to completed (progress: 100.0%)
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:25 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:25 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:26 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:27 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:27 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:29 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:30 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:30 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:48 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:48 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:48 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:50 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:50 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:50 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:51 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:51 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:56 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:56 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:56 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:58 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:58 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:58 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:59 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:19:59 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:02 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:02 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:06 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:07 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:10 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:10 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:11 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:12 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:20:26 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:20:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:32 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:20:32 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:20:32 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:20:39 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:20:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:20:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:20:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:20:48 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:20:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:20:53 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:20:53 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:20:53 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:20:59 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:05 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:05 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:05 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:10 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:15 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:15 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:15 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:15 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:15 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:15 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 'TaskService' object has no attribute 'tasks'
2025-07-14 21:21:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 'TaskService' object has no attribute 'tasks'
2025-07-14 21:21:19 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 'TaskService' object has no attribute 'tasks'
2025-07-14 21:21:20 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:26 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:26 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:26 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:29 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:34 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:34 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:34 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:21:51 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:21:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:21:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:21:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:21:57 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:07 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:13 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:16 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:21 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:21 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:21 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:21 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:21 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:21 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:23 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:29 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:29 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:29 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:31 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:36 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:36 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:36 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:39 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:45 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:45 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:45 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:45 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:46 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:47 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:22:52 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:52 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:22:52 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:22:52 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:22:52 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:52 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:53 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:54 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:22:55 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:23:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:00 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:00 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:23:00 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:23:00 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:00 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:01 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:23:08 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:08 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:08 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:23:08 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:23:08 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:08 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:09 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:10 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:23:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:23:16 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:23:16 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:23:16 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:17 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:18 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:20 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:22 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:23 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:24 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:25 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:26 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:26 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:28 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:29 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:29 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:30 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:31 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:32 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:34 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:36 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:38 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:39 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:39 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:40 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:41 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:42 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:23:44 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:24:13 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:24:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:24:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:25:12 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:25:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:25:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:12 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:26:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:26:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:26:55 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:26:55 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:26:55 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:26:55 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:27:13 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:27:14 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:27:16 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:03 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:04 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:05 | ERROR    | app.api.routes:get_task_status:324 - Get task status failed: 
2025-07-14 21:28:19 | INFO     | app.api.routes:upload_file:85 - 上传文件: test_30s.mp4 -> storage\uploads\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s.mp4
2025-07-14 21:28:19 | INFO     | app.api.routes:upload_file:92 - 文件上传成功: storage\uploads\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 21:28:19 | INFO     | app.services.base_service:create_task:35 - Created task b0d82d58-2266-4ce6-ad34-e1f53160ced3 of type audio_extraction
2025-07-14 21:28:19 | INFO     | app.services.base_service:update_task_status:54 - Task b0d82d58-2266-4ce6-ad34-e1f53160ced3 status updated to processing (progress: 0.0%)
2025-07-14 21:28:19 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s.mp4 to storage\outputs\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s_audio.wav
2025-07-14 21:28:22 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\ce9e528a-2a0c-4db2-a561-7905caf71fa4_test_30s_audio.wav (duration: 30.06s)
2025-07-14 21:28:22 | INFO     | app.services.base_service:update_task_status:54 - Task b0d82d58-2266-4ce6-ad34-e1f53160ced3 status updated to completed (progress: 100.0%)
2025-07-14 21:29:28 | ERROR    | app.api.routes:upload_file:102 - 文件上传失败: 
2025-07-14 21:30:09 | INFO     | app.services.base_service:create_task:35 - Created task 7355bd93-ae52-40e8-8d0d-b5a836f9c015 of type translation
2025-07-14 21:30:09 | INFO     | app.services.base_service:update_task_status:54 - Task 7355bd93-ae52-40e8-8d0d-b5a836f9c015 status updated to processing (progress: 0.0%)
2025-07-14 21:30:09 | ERROR    | app.services.translation_service:_translate_with_openai:109 - OpenAI translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 21:30:09 | ERROR    | app.services.translation_service:translate_text:64 - Translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 21:30:09 | ERROR    | app.services.translation_service:process_translation:51 - Translation failed for task 7355bd93-ae52-40e8-8d0d-b5a836f9c015: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 21:30:09 | INFO     | app.services.base_service:update_task_status:54 - Task 7355bd93-ae52-40e8-8d0d-b5a836f9c015 status updated to failed (progress: 0.0%)
2025-07-14 21:34:31 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:34:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:38 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:34:38 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:34:38 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:34:42 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:34:47 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:47 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:34:47 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:34:47 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:34:47 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:53:28 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:53:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:53:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:53:34 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:53:34 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:53:34 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:56:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 21:56:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:56:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:56:16 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:56:16 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:56:16 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:56:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 21:57:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 21:57:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 21:57:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 21:58:10 | ERROR    | app.api.routes:upload_file:102 - 文件上传失败: 
2025-07-14 22:00:00 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:00:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:00:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:00:05 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:00:05 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:00:05 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:03:08 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:03:13 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:03:13 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:03:13 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:03:13 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:03:13 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:03:31 | INFO     | app.api.routes:upload_file:88 - 上传文件: 1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav -> storage\uploads\c22be5bc-aa9b-4e71-a1b4-650c6374424c_1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav
2025-07-14 22:03:31 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c22be5bc-aa9b-4e71-a1b4-650c6374424c_1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav (大小: 961964 bytes)
2025-07-14 22:03:31 | INFO     | app.services.base_service:create_task:35 - Created task 8d0d60b4-e5be-45f3-b3a2-5f0b9625c4ce of type speech_recognition
2025-07-14 22:03:31 | INFO     | app.services.base_service:update_task_status:54 - Task 8d0d60b4-e5be-45f3-b3a2-5f0b9625c4ce status updated to processing (progress: 0.0%)
2025-07-14 22:03:31 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:03:50 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\uploads\c22be5bc-aa9b-4e71-a1b4-650c6374424c_1c347d49-d00a-40c7-b2f2-487bfdd47921_test_30s_audio.wav
2025-07-14 22:03:57 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:03:57 | INFO     | app.services.base_service:update_task_status:54 - Task 8d0d60b4-e5be-45f3-b3a2-5f0b9625c4ce status updated to completed (progress: 100.0%)
2025-07-14 22:04:26 | INFO     | app.services.base_service:create_task:35 - Created task 168e8fbc-c75b-41f6-ba03-491679f71a43 of type translation
2025-07-14 22:04:26 | INFO     | app.services.base_service:update_task_status:54 - Task 168e8fbc-c75b-41f6-ba03-491679f71a43 status updated to processing (progress: 0.0%)
2025-07-14 22:04:26 | ERROR    | app.services.translation_service:_translate_with_openai:109 - OpenAI translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 22:04:26 | ERROR    | app.services.translation_service:translate_text:64 - Translation failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 22:04:26 | ERROR    | app.services.translation_service:process_translation:51 - Translation failed for task 168e8fbc-c75b-41f6-ba03-491679f71a43: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-07-14 22:04:26 | INFO     | app.services.base_service:update_task_status:54 - Task 168e8fbc-c75b-41f6-ba03-491679f71a43 status updated to failed (progress: 0.0%)
2025-07-14 22:05:48 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:05:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:05:53 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:05:53 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:05:53 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:05:53 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:05:57 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:06:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:02 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:06:02 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:06:02 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:06:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:06:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:06:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:06:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:27:21 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:27:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:27:29 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:27:29 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:27:29 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:27:29 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:28:02 | INFO     | app.services.base_service:create_task:35 - Created task 6ab4adb7-7827-4a29-8114-419401479991 of type translation
2025-07-14 22:28:02 | INFO     | app.services.base_service:update_task_status:54 - Task 6ab4adb7-7827-4a29-8114-419401479991 status updated to processing (progress: 0.0%)
2025-07-14 22:28:04 | INFO     | app.services.base_service:update_task_status:54 - Task 6ab4adb7-7827-4a29-8114-419401479991 status updated to completed (progress: 100.0%)
2025-07-14 22:28:22 | INFO     | app.services.base_service:create_task:35 - Created task 3479a310-c0c6-4d51-990c-238e258abec7 of type translation
2025-07-14 22:28:22 | INFO     | app.services.base_service:update_task_status:54 - Task 3479a310-c0c6-4d51-990c-238e258abec7 status updated to processing (progress: 0.0%)
2025-07-14 22:28:24 | INFO     | app.services.base_service:update_task_status:54 - Task 3479a310-c0c6-4d51-990c-238e258abec7 status updated to completed (progress: 100.0%)
2025-07-14 22:28:46 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s.mp4
2025-07-14 22:28:46 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 22:28:46 | INFO     | app.services.base_service:create_task:35 - Created task e0c28da2-bc8a-4ac0-bd72-69d1a33800de of type full_pipeline
2025-07-14 22:28:46 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 0.0%)
2025-07-14 22:28:46 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:28:46 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s.mp4 to storage\outputs\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s_audio.wav
2025-07-14 22:28:49 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s_audio.wav (duration: 30.06s)
2025-07-14 22:28:49 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 20.0%)
2025-07-14 22:28:49 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:28:49 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:28:50 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\b16b7e92-ad14-443c-96a0-ec7f69361426_test_30s_audio.wav
2025-07-14 22:28:57 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:28:57 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 40.0%)
2025-07-14 22:28:57 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:29:02 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 60.0%)
2025-07-14 22:29:02 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:30:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_d914f092.srt
2025-07-14 22:30:05 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to processing (progress: 80.0%)
2025-07-14 22:30:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de
2025-07-14 22:30:05 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:78 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-14 22:30:10 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:99 - Edge TTS synthesis failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f60f12a32f3848bfaa126f8ecb6cbfec'
2025-07-14 22:30:10 | ERROR    | app.services.tts_service:synthesize_speech:68 - TTS synthesis failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f60f12a32f3848bfaa126f8ecb6cbfec'
2025-07-14 22:30:10 | ERROR    | app.services.task_service:process_full_pipeline:143 - Full pipeline failed for task e0c28da2-bc8a-4ac0-bd72-69d1a33800de: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=f60f12a32f3848bfaa126f8ecb6cbfec'
2025-07-14 22:30:10 | INFO     | app.services.base_service:update_task_status:54 - Task e0c28da2-bc8a-4ac0-bd72-69d1a33800de status updated to failed (progress: 0.0%)
2025-07-14 22:37:14 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:37:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:23 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:37:23 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:37:23 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:37:27 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:37:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:36 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:36 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:37:36 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:37:36 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:37:49 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:37:59 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:59 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:37:59 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:37:59 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:37:59 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:38:03 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:38:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:38:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:38:11 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:38:11 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:38:11 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:47:29 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:47:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:47:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:47:45 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:47:45 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:47:45 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:47:45 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:47:52 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4
2025-07-14 22:47:52 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 22:47:52 | INFO     | app.services.base_service:create_task:35 - Created task ed28fc8f-98be-4fee-9612-3500abd05b78 of type full_pipeline
2025-07-14 22:47:52 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 0.0%)
2025-07-14 22:47:52 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:47:52 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4 to storage\outputs\90204720-f2d2-429f-b904-ebc739420cdf_test_30s_audio.wav
2025-07-14 22:47:54 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\90204720-f2d2-429f-b904-ebc739420cdf_test_30s_audio.wav (duration: 30.06s)
2025-07-14 22:47:54 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 20.0%)
2025-07-14 22:47:54 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:47:54 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:47:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\90204720-f2d2-429f-b904-ebc739420cdf_test_30s_audio.wav
2025-07-14 22:48:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:48:02 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 40.0%)
2025-07-14 22:48:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:08 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 60.0%)
2025-07-14 22:48:08 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:26 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_5a6c980d.srt
2025-07-14 22:48:26 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 80.0%)
2025-07-14 22:48:26 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:26 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:88 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-14 22:48:32 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=11cd5cb82b5f44a4806973b45af059d4'
2025-07-14 22:48:32 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 2 seconds...
2025-07-14 22:48:40 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=2724d0adcacd411ba250d2cc20df142e'
2025-07-14 22:48:40 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 4 seconds...
2025-07-14 22:48:49 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=d997290446b24c97a102190515a6f3c3'
2025-07-14 22:48:49 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:136 - Edge TTS service unavailable, creating text fallback
2025-07-14 22:48:49 | INFO     | app.services.tts_service:_create_text_fallback:155 - Created text fallback: storage\outputs\tts_58ca81c0.txt
2025-07-14 22:48:49 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to processing (progress: 90.0%)
2025-07-14 22:48:49 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task ed28fc8f-98be-4fee-9612-3500abd05b78
2025-07-14 22:48:49 | INFO     | app.services.video_service:compose_video:76 - Composing video: storage\uploads\90204720-f2d2-429f-b904-ebc739420cdf_test_30s.mp4 + storage\outputs\tts_58ca81c0.txt
2025-07-14 22:48:50 | ERROR    | app.services.video_service:compose_video:127 - Video composition failed: MoviePy error: failed to read the duration of file storage\outputs\tts_58ca81c0.txt.
Here are the file infos returned by ffmpeg:

ffmpeg version 7.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers

  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)

  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband

  libavutil      59. 39.100 / 59. 39.100

  libavcodec     61. 19.100 / 61. 19.100

  libavformat    61.  7.100 / 61.  7.100

  libavdevice    61.  3.100 / 61.  3.100

  libavfilter    10.  4.100 / 10.  4.100

  libswscale      8.  3.100 /  8.  3.100

  libswresample   5.  3.100 /  5.  3.100

  libpostproc    58.  3.100 / 58.  3.100

[in#0 @ 000002351c277280] Error opening input: Invalid data found when processing input

Error opening input file storage\outputs\tts_58ca81c0.txt.

Error opening input files: Invalid data found when processing input


2025-07-14 22:48:50 | ERROR    | app.services.task_service:process_full_pipeline:143 - Full pipeline failed for task ed28fc8f-98be-4fee-9612-3500abd05b78: MoviePy error: failed to read the duration of file storage\outputs\tts_58ca81c0.txt.
Here are the file infos returned by ffmpeg:

ffmpeg version 7.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers

  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)

  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband

  libavutil      59. 39.100 / 59. 39.100

  libavcodec     61. 19.100 / 61. 19.100

  libavformat    61.  7.100 / 61.  7.100

  libavdevice    61.  3.100 / 61.  3.100

  libavfilter    10.  4.100 / 10.  4.100

  libswscale      8.  3.100 /  8.  3.100

  libswresample   5.  3.100 /  5.  3.100

  libpostproc    58.  3.100 / 58.  3.100

[in#0 @ 000002351c277280] Error opening input: Invalid data found when processing input

Error opening input file storage\outputs\tts_58ca81c0.txt.

Error opening input files: Invalid data found when processing input


2025-07-14 22:48:50 | INFO     | app.services.base_service:update_task_status:54 - Task ed28fc8f-98be-4fee-9612-3500abd05b78 status updated to failed (progress: 0.0%)
2025-07-14 22:50:24 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:50:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:50:32 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:50:32 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:50:32 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:50:32 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:50:53 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-14 22:52:50 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:52:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:52:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-14 22:52:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-14 22:52:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-14 22:52:57 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-14 22:53:10 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4
2025-07-14 22:53:10 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4 (大小: 1952084 bytes)
2025-07-14 22:53:10 | INFO     | app.services.base_service:create_task:35 - Created task a853327d-7409-4037-9f14-22c225b57039 of type full_pipeline
2025-07-14 22:53:10 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 0.0%)
2025-07-14 22:53:10 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:53:10 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4 to storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_audio.wav
2025-07-14 22:53:12 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_audio.wav (duration: 30.06s)
2025-07-14 22:53:12 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 20.0%)
2025-07-14 22:53:12 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:53:12 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-14 22:53:13 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_audio.wav
2025-07-14 22:53:20 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-14 22:53:20 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 40.0%)
2025-07-14 22:53:20 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:53:26 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 60.0%)
2025-07-14 22:53:26 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:56:45 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_9be3b58f.srt
2025-07-14 22:56:45 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 80.0%)
2025-07-14 22:56:45 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:56:45 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:88 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-14 22:56:50 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=44e4f1ae845645608e5ca295082998a7'
2025-07-14 22:56:50 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 2 seconds...
2025-07-14 22:56:54 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=a8a577a22d6340039f104431e05b0cbd'
2025-07-14 22:56:54 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 4 seconds...
2025-07-14 22:56:59 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=0fab5d71d9444f63b8203f6aa3ac29d0'
2025-07-14 22:56:59 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:136 - Edge TTS service unavailable, creating text fallback
2025-07-14 22:56:59 | INFO     | app.services.tts_service:_create_text_fallback:155 - Created text fallback: storage\outputs\tts_20592537.txt
2025-07-14 22:56:59 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to processing (progress: 90.0%)
2025-07-14 22:56:59 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:56:59 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\b954d987-65eb-490e-ac39-d1236d83641e_test_30s.mp4 + storage\outputs\tts_20592537.txt
2025-07-14 22:56:59 | WARNING  | app.services.video_service:compose_video:86 - TTS fallback detected, creating video without audio replacement
2025-07-14 22:56:59 | WARNING  | app.services.video_service:compose_video:118 - Failed to add text overlay: MoviePy Error: creation of None failed because of the following error:

[WinError 2] 系统找不到指定的文件。.

.This error can be due to the fact that ImageMagick is not installed on your computer, or (for Windows users) that you didn't specify the path to the ImageMagick binary in file conf.py, or that the path you specified is incorrect
2025-07-14 22:56:59 | INFO     | app.services.video_service:compose_video:131 - Adding subtitles: storage\outputs\subtitles_9be3b58f.srt
2025-07-14 22:56:59 | WARNING  | app.services.video_service:compose_video:136 - Failed to add subtitles: module 'moviepy.editor' has no attribute 'SubtitlesClip'
2025-07-14 22:57:14 | INFO     | app.services.video_service:compose_video:168 - Video composition completed: storage\outputs\b954d987-65eb-490e-ac39-d1236d83641e_test_30s_translated.mp4
2025-07-14 22:57:14 | INFO     | app.services.base_service:update_task_status:54 - Task a853327d-7409-4037-9f14-22c225b57039 status updated to completed (progress: 100.0%)
2025-07-14 22:57:14 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task a853327d-7409-4037-9f14-22c225b57039
2025-07-14 22:57:14 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task a853327d-7409-4037-9f14-22c225b57039 completed with fallbacks: TTS service was unavailable - text content saved instead of audio; Video created with original audio due to TTS unavailability
2025-07-15 20:10:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:10:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:10:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:10:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:10:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:10:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:11:22 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4
2025-07-15 20:11:22 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:11:22 | INFO     | app.services.base_service:create_task:35 - Created task 7fe79985-040f-4ffd-ab8b-de1fde856f7f of type full_pipeline
2025-07-15 20:11:22 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 0.0%)
2025-07-15 20:11:22 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:11:22 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4 to storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_audio.wav
2025-07-15 20:11:26 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:11:26 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 20.0%)
2025-07-15 20:11:26 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:11:26 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:11:27 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_audio.wav
2025-07-15 20:11:33 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:11:33 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 40.0%)
2025-07-15 20:11:33 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:11:39 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 60.0%)
2025-07-15 20:11:39 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:02 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_bb3f672e.srt
2025-07-15 20:12:02 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 80.0%)
2025-07-15 20:12:02 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:02 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:88 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:12:08 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=733bfedb9e8f43dfae18b04f74f70e15'
2025-07-15 20:12:08 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:12:16 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=bab290597fe5441cb7fa82877c05905e'
2025-07-15 20:12:16 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:131 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:12:25 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:126 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=5e219da5801e4bc3878e9fc122f4481b'
2025-07-15 20:12:25 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:136 - Edge TTS service unavailable, creating text fallback
2025-07-15 20:12:25 | INFO     | app.services.tts_service:_create_text_fallback:155 - Created text fallback: storage\outputs\tts_da91097b.txt
2025-07-15 20:12:25 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to processing (progress: 90.0%)
2025-07-15 20:12:25 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:25 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s.mp4 + storage\outputs\tts_da91097b.txt
2025-07-15 20:12:26 | WARNING  | app.services.video_service:compose_video:86 - TTS fallback detected, creating video without audio replacement
2025-07-15 20:12:26 | WARNING  | app.services.video_service:compose_video:118 - Failed to add text overlay: MoviePy Error: creation of None failed because of the following error:

[WinError 2] 系统找不到指定的文件。.

.This error can be due to the fact that ImageMagick is not installed on your computer, or (for Windows users) that you didn't specify the path to the ImageMagick binary in file conf.py, or that the path you specified is incorrect
2025-07-15 20:12:26 | INFO     | app.services.video_service:compose_video:131 - Adding subtitles: storage\outputs\subtitles_bb3f672e.srt
2025-07-15 20:12:26 | WARNING  | app.services.video_service:compose_video:136 - Failed to add subtitles: module 'moviepy.editor' has no attribute 'SubtitlesClip'
2025-07-15 20:12:41 | INFO     | app.services.video_service:compose_video:168 - Video composition completed: storage\outputs\93e4c6b4-5599-401d-bfaa-e2b93804ad31_test_30s_translated.mp4
2025-07-15 20:12:41 | INFO     | app.services.base_service:update_task_status:54 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f status updated to completed (progress: 100.0%)
2025-07-15 20:12:41 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 7fe79985-040f-4ffd-ab8b-de1fde856f7f
2025-07-15 20:12:41 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 7fe79985-040f-4ffd-ab8b-de1fde856f7f completed with fallbacks: TTS service was unavailable - text content saved instead of audio; Video created with original audio due to TTS unavailability
2025-07-15 20:16:34 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:16:42 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:42 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:42 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:16:42 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:16:42 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:16:48 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:16:56 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:56 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:16:56 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:16:56 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:16:56 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:17:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:17:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:09 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:17:09 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:17:09 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:17:17 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:17:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:17:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:17:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:17:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:18:26 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:18:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:35 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:18:35 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:18:35 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:18:49 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:18:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:18:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:18:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:18:57 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:21:32 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:21:40 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:40 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:40 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:21:40 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:21:40 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:21:43 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:21:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:21:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:21:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:21:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:21:57 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:22:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:04 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:04 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:22:04 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:22:04 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:22:10 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:22:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:17 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:22:17 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:22:17 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:22:37 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:22:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:54 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:22:54 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:22:54 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:22:54 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:22:57 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4
2025-07-15 20:22:57 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:22:57 | INFO     | app.services.base_service:create_task:35 - Created task 47d57faf-4394-457a-95fa-b77b89888e1a of type full_pipeline
2025-07-15 20:22:57 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 0.0%)
2025-07-15 20:22:57 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:22:57 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4 to storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_audio.wav
2025-07-15 20:22:59 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:22:59 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 20.0%)
2025-07-15 20:22:59 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:22:59 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:23:00 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_audio.wav
2025-07-15 20:23:06 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:23:06 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 40.0%)
2025-07-15 20:23:06 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:13 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 60.0%)
2025-07-15 20:23:13 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:33 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_1fc2d981.srt
2025-07-15 20:23:33 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 80.0%)
2025-07-15 20:23:33 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:33 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:94 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:23:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=bd646e4c435d4efc8d494e90ce1ce71d'
2025-07-15 20:23:39 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:23:47 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=b3a7620097054883af04cb4c8571f563'
2025-07-15 20:23:47 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:23:57 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=ce8e317bb8e244b48f0fe3f591492060'
2025-07-15 20:23:57 | ERROR    | app.services.tts_service:_synthesize_with_edge_tts:142 - Edge TTS service unavailable, creating text fallback
2025-07-15 20:23:57 | INFO     | app.services.tts_service:_create_text_fallback:161 - Created text fallback: storage\outputs\tts_128461c0.txt
2025-07-15 20:23:57 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to processing (progress: 90.0%)
2025-07-15 20:23:57 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:23:57 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\59233011-1983-4458-aaab-e84ff51f3b47_test_30s.mp4 + storage\outputs\tts_128461c0.txt
2025-07-15 20:23:58 | WARNING  | app.services.video_service:compose_video:86 - TTS fallback detected, creating video without audio replacement
2025-07-15 20:23:58 | INFO     | app.services.video_service:compose_video:96 - TTS fallback detected - text content available in storage\outputs\tts_128461c0.txt
2025-07-15 20:23:58 | INFO     | app.services.video_service:compose_video:109 - Subtitle file generated: storage\outputs\subtitles_1fc2d981.srt
2025-07-15 20:23:58 | INFO     | app.services.video_service:compose_video:110 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 20:24:12 | INFO     | app.services.video_service:compose_video:142 - Video composition completed: storage\outputs\59233011-1983-4458-aaab-e84ff51f3b47_test_30s_translated.mp4
2025-07-15 20:24:12 | INFO     | app.services.base_service:update_task_status:54 - Task 47d57faf-4394-457a-95fa-b77b89888e1a status updated to completed (progress: 100.0%)
2025-07-15 20:24:12 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 47d57faf-4394-457a-95fa-b77b89888e1a
2025-07-15 20:24:12 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 47d57faf-4394-457a-95fa-b77b89888e1a completed with fallbacks: TTS service was unavailable - text content saved instead of audio; Video created with original audio due to TTS unavailability
2025-07-15 20:25:21 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:25:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:25:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:25:30 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:25:30 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:25:30 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:28:14 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:28:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:28:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:28:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:28:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:28:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:29:04 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:29:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:29:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:29:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:29:25 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:29:37 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:37 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:29:37 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:29:37 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:29:37 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:30:52 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:31:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:31:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:31:09 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:31:09 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:31:09 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:31:09 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:31:24 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4
2025-07-15 20:31:24 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:31:24 | INFO     | app.services.base_service:create_task:35 - Created task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 of type full_pipeline
2025-07-15 20:31:24 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 0.0%)
2025-07-15 20:31:24 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:31:24 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4 to storage\outputs\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s_audio.wav
2025-07-15 20:31:26 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:31:26 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 20.0%)
2025-07-15 20:31:26 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:31:26 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:31:27 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s_audio.wav
2025-07-15 20:31:33 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:31:33 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 40.0%)
2025-07-15 20:31:33 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:31:41 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 60.0%)
2025-07-15 20:31:41 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:32:04 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_b070580d.srt
2025-07-15 20:32:04 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 80.0%)
2025-07-15 20:32:04 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:32:04 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:94 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:32:09 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=dff30961603948058733d3b3b8ef8a2f'
2025-07-15 20:32:09 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:32:17 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=1e303e50e9d54311a06f244a5c347be5'
2025-07-15 20:32:17 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:32:26 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=81bcbeda903c4d5baaef5658aecfdeb2'
2025-07-15 20:32:26 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:142 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 20:32:26 | INFO     | app.services.tts_service:_synthesize_with_gtts:185 - Synthesizing speech with gTTS
2025-07-15 20:32:51 | INFO     | app.services.tts_service:_synthesize_with_gtts:219 - gTTS synthesis completed: storage\outputs\tts_050059b1.mp3
2025-07-15 20:32:51 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to processing (progress: 90.0%)
2025-07-15 20:32:51 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6
2025-07-15 20:32:51 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\a9657e00-aaf2-463e-9ae3-12822b0a0c85_test_30s.mp4 + storage\outputs\tts_050059b1.mp3
2025-07-15 20:32:51 | INFO     | app.services.video_service:compose_video:109 - Subtitle file generated: storage\outputs\subtitles_b070580d.srt
2025-07-15 20:32:51 | INFO     | app.services.video_service:compose_video:110 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 20:32:51 | ERROR    | app.services.video_service:compose_video:146 - Video composition failed: 'NoneType' object has no attribute 'get_frame'
2025-07-15 20:32:51 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6: 'NoneType' object has no attribute 'get_frame'
2025-07-15 20:32:51 | INFO     | app.services.base_service:update_task_status:54 - Task 74cdfa59-6eec-4d4f-8a6e-5e58db36b0e6 status updated to failed (progress: 0.0%)
2025-07-15 20:34:41 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:34:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:34:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:34:49 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:34:49 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:34:49 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:34:54 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:35:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:02 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:02 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:35:02 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:35:02 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:35:12 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:35:20 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:20 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:35:20 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:35:20 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:35:20 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:39:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:39:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:39:19 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:39:19 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:39:19 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:39:19 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:39:19 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:42:24 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:42:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:42:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:42:34 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:42:34 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:42:34 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:44:07 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:44:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:44:17 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:44:17 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:44:17 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:44:17 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:47:39 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4
2025-07-15 20:47:39 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 20:47:39 | INFO     | app.services.base_service:create_task:35 - Created task f7a436b1-a897-416a-b674-f22c205feaae of type full_pipeline
2025-07-15 20:47:39 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 0.0%)
2025-07-15 20:47:39 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:47:39 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4 to storage\outputs\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s_audio.wav
2025-07-15 20:47:41 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s_audio.wav (duration: 30.06s)
2025-07-15 20:47:41 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 20.0%)
2025-07-15 20:47:41 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:47:41 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 20:47:42 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s_audio.wav
2025-07-15 20:47:49 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 20:47:49 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 40.0%)
2025-07-15 20:47:49 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:47:57 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 60.0%)
2025-07-15 20:47:57 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:48:20 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_132ade3e.srt
2025-07-15 20:48:20 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 80.0%)
2025-07-15 20:48:20 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:48:20 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:94 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 20:48:25 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=95a4f93a88e341bd9816ae0fe56ec835'
2025-07-15 20:48:25 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 2 seconds...
2025-07-15 20:48:33 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=0adddeef473f4aeab4906f320aeebb7a'
2025-07-15 20:48:33 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:137 - Retrying Edge TTS in 4 seconds...
2025-07-15 20:48:43 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:132 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=93465d550e8741df85350fc32efc8259'
2025-07-15 20:48:43 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:142 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 20:48:43 | INFO     | app.services.tts_service:_synthesize_with_gtts:185 - Synthesizing speech with gTTS
2025-07-15 20:49:02 | INFO     | app.services.tts_service:_synthesize_with_gtts:237 - gTTS synthesis completed: storage\outputs\tts_b4845af4.mp3
2025-07-15 20:49:02 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to processing (progress: 90.0%)
2025-07-15 20:49:02 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task f7a436b1-a897-416a-b674-f22c205feaae
2025-07-15 20:49:02 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\3242b1f0-b9ad-4bdb-9a02-a924ab88802c_test_30s.mp4 + storage\outputs\tts_b4845af4.mp3
2025-07-15 20:49:03 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_132ade3e.srt
2025-07-15 20:49:03 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 20:49:03 | ERROR    | app.services.video_service:compose_video:161 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 20:49:03 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task f7a436b1-a897-416a-b674-f22c205feaae: 'NoneType' object has no attribute 'stdout'
2025-07-15 20:49:03 | INFO     | app.services.base_service:update_task_status:54 - Task f7a436b1-a897-416a-b674-f22c205feaae status updated to failed (progress: 0.0%)
2025-07-15 20:56:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:56:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:12 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:12 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:56:12 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:56:12 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:56:27 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:56:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:38 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:38 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:56:38 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:56:38 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:56:41 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:56:50 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:50 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:56:50 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:56:50 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:56:50 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 20:57:43 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 20:57:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:57:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 20:57:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 20:57:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 20:57:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:08:06 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:08:23 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:08:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:08:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:08:31 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:08:31 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:08:31 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:08:36 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4
2025-07-15 21:08:36 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:08:36 | INFO     | app.services.base_service:create_task:35 - Created task 47164a92-6e81-4a5c-acaf-372876c629d0 of type full_pipeline
2025-07-15 21:08:36 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 0.0%)
2025-07-15 21:08:36 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:08:36 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4 to storage\outputs\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s_audio.wav
2025-07-15 21:08:39 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:08:39 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 20.0%)
2025-07-15 21:08:39 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:08:39 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:08:40 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s_audio.wav
2025-07-15 21:08:46 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:08:46 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 40.0%)
2025-07-15 21:08:46 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:08:54 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 60.0%)
2025-07-15 21:08:54 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:09:16 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_13a89206.srt
2025-07-15 21:09:16 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 80.0%)
2025-07-15 21:09:16 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:09:16 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:112 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 21:09:22 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=220ee9a619114687bcb99b7522ed8220'
2025-07-15 21:09:22 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 2 seconds...
2025-07-15 21:09:30 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=2cdddee181174a0fb80e8d36cffd82b6'
2025-07-15 21:09:30 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 4 seconds...
2025-07-15 21:09:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=7c77674cde2a498f954581c8613ee08d'
2025-07-15 21:09:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:160 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 21:09:39 | INFO     | app.services.tts_service:_synthesize_with_gtts:203 - Synthesizing speech with gTTS
2025-07-15 21:10:03 | INFO     | app.services.tts_service:_synthesize_with_gtts:255 - gTTS synthesis completed: storage\outputs\tts_4812dfc9.mp3
2025-07-15 21:10:03 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to processing (progress: 90.0%)
2025-07-15 21:10:03 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 47164a92-6e81-4a5c-acaf-372876c629d0
2025-07-15 21:10:03 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\32a26db0-3428-4fd5-8656-eb21a6e0486e_test_30s.mp4 + storage\outputs\tts_4812dfc9.mp3
2025-07-15 21:10:04 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_13a89206.srt
2025-07-15 21:10:04 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:10:04 | ERROR    | app.services.video_service:compose_video:161 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:10:04 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 47164a92-6e81-4a5c-acaf-372876c629d0: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:10:04 | INFO     | app.services.base_service:update_task_status:54 - Task 47164a92-6e81-4a5c-acaf-372876c629d0 status updated to failed (progress: 0.0%)
2025-07-15 21:12:28 | INFO     | app.services.base_service:create_task:35 - Created task 9c43e902-e50b-4deb-a210-774399a97b40 of type translation
2025-07-15 21:12:28 | INFO     | app.services.base_service:update_task_status:54 - Task 9c43e902-e50b-4deb-a210-774399a97b40 status updated to processing (progress: 0.0%)
2025-07-15 21:12:33 | INFO     | app.services.base_service:update_task_status:54 - Task 9c43e902-e50b-4deb-a210-774399a97b40 status updated to completed (progress: 100.0%)
2025-07-15 21:12:59 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:13:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:07 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:13:07 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:13:07 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:13:17 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:13:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:13:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:13:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:13:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:19:18 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:19:28 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:19:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:19:35 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:19:35 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:19:35 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:19:35 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:19:43 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4
2025-07-15 21:19:43 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:19:43 | INFO     | app.services.base_service:create_task:35 - Created task 8819e12d-13ab-4623-a522-50b58781924d of type full_pipeline
2025-07-15 21:19:43 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 0.0%)
2025-07-15 21:19:43 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:19:43 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4 to storage\outputs\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s_audio.wav
2025-07-15 21:19:45 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:19:45 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 20.0%)
2025-07-15 21:19:45 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:19:45 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:19:46 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s_audio.wav
2025-07-15 21:19:53 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:19:53 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 40.0%)
2025-07-15 21:19:53 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:20:01 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 60.0%)
2025-07-15 21:20:01 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:20:25 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_61a8c264.srt
2025-07-15 21:20:25 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 80.0%)
2025-07-15 21:20:25 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:20:25 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:112 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 21:20:31 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=9e7b38b6499d4e35b82cf013a18e4421'
2025-07-15 21:20:31 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 2 seconds...
2025-07-15 21:20:39 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=7cc03d26bfcb4d038b6fab826c3d085e'
2025-07-15 21:20:39 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 4 seconds...
2025-07-15 21:20:48 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=4a7dd817243b46c0a0790aa7fb05070b'
2025-07-15 21:20:48 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:160 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 21:20:48 | INFO     | app.services.tts_service:_synthesize_with_gtts:203 - Synthesizing speech with gTTS
2025-07-15 21:21:13 | INFO     | app.services.tts_service:_synthesize_with_gtts:255 - gTTS synthesis completed: storage\outputs\tts_9c055230.mp3
2025-07-15 21:21:13 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to processing (progress: 90.0%)
2025-07-15 21:21:13 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 8819e12d-13ab-4623-a522-50b58781924d
2025-07-15 21:21:13 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\5b9ec2a9-940b-41d2-bc95-672b0b0630ad_test_30s.mp4 + storage\outputs\tts_9c055230.mp3
2025-07-15 21:21:14 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_61a8c264.srt
2025-07-15 21:21:14 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:21:14 | ERROR    | app.services.video_service:compose_video:140 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | ERROR    | app.services.video_service:compose_video:153 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | ERROR    | app.services.video_service:compose_video:186 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 8819e12d-13ab-4623-a522-50b58781924d: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:21:14 | INFO     | app.services.base_service:update_task_status:54 - Task 8819e12d-13ab-4623-a522-50b58781924d status updated to failed (progress: 0.0%)
2025-07-15 21:26:37 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:26:37 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:26:37 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:26:37 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:26:37 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:28:49 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:28:49 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:28:49 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:28:49 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:29:41 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4
2025-07-15 21:29:41 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:29:41 | INFO     | app.services.base_service:create_task:35 - Created task 73842441-d76c-4200-bd81-90f62b4940db of type full_pipeline
2025-07-15 21:29:41 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 0.0%)
2025-07-15 21:29:41 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:29:41 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4 to storage\outputs\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s_audio.wav
2025-07-15 21:29:44 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:29:44 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 20.0%)
2025-07-15 21:29:44 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:29:44 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:29:45 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s_audio.wav
2025-07-15 21:29:52 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:29:52 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 40.0%)
2025-07-15 21:29:52 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:30:01 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 60.0%)
2025-07-15 21:30:01 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:30:29 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_22f992e1.srt
2025-07-15 21:30:29 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 80.0%)
2025-07-15 21:30:29 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:30:29 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:112 - Synthesizing speech with voice zh-CN-XiaoxiaoNeural
2025-07-15 21:30:34 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 1/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=6aff3451e5494f7ebb3befe024c1969b'
2025-07-15 21:30:34 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 2 seconds...
2025-07-15 21:30:42 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 2/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=79e257eab97d4b7497bfff52470f6d00'
2025-07-15 21:30:42 | INFO     | app.services.tts_service:_synthesize_with_edge_tts:155 - Retrying Edge TTS in 4 seconds...
2025-07-15 21:30:52 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:150 - Edge TTS attempt 3/3 failed: 403, message='Invalid response status', url='wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4&ConnectionId=18e9a2d564cb4ac09e06e20bf059920b'
2025-07-15 21:30:52 | WARNING  | app.services.tts_service:_synthesize_with_edge_tts:160 - Edge TTS service unavailable, falling back to gTTS
2025-07-15 21:30:52 | INFO     | app.services.tts_service:_synthesize_with_gtts:203 - Synthesizing speech with gTTS
2025-07-15 21:31:17 | INFO     | app.services.tts_service:_synthesize_with_gtts:255 - gTTS synthesis completed: storage\outputs\tts_9f2d504c.mp3
2025-07-15 21:31:17 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to processing (progress: 90.0%)
2025-07-15 21:31:17 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 73842441-d76c-4200-bd81-90f62b4940db
2025-07-15 21:31:17 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\c5cb7acf-ed43-4817-aea0-2a57b411fa34_test_30s.mp4 + storage\outputs\tts_9f2d504c.mp3
2025-07-15 21:31:18 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_22f992e1.srt
2025-07-15 21:31:18 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:31:18 | ERROR    | app.services.video_service:compose_video:140 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | ERROR    | app.services.video_service:compose_video:153 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | ERROR    | app.services.video_service:compose_video:186 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 73842441-d76c-4200-bd81-90f62b4940db: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:31:18 | INFO     | app.services.base_service:update_task_status:54 - Task 73842441-d76c-4200-bd81-90f62b4940db status updated to failed (progress: 0.0%)
2025-07-15 21:41:30 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:41:39 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:41:39 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:41:39 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:41:39 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:41:46 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4
2025-07-15 21:41:46 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:41:46 | INFO     | app.services.base_service:create_task:35 - Created task 83e023b4-0496-4934-8f3f-14dcc1b6434d of type full_pipeline
2025-07-15 21:41:46 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 0.0%)
2025-07-15 21:41:46 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:41:46 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4 to storage\outputs\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s_audio.wav
2025-07-15 21:41:48 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:41:48 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 20.0%)
2025-07-15 21:41:48 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:41:48 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:41:49 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s_audio.wav
2025-07-15 21:41:56 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:41:56 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 40.0%)
2025-07-15 21:41:56 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:04 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 60.0%)
2025-07-15 21:42:04 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:28 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_e1519e44.srt
2025-07-15 21:42:28 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 80.0%)
2025-07-15 21:42:28 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:28 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 21:42:28 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 21:42:28 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_e04fa480.wav
2025-07-15 21:42:28 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to processing (progress: 90.0%)
2025-07-15 21:42:28 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 83e023b4-0496-4934-8f3f-14dcc1b6434d
2025-07-15 21:42:28 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\dafa3014-9b90-4e42-9baf-a07123a7e62a_test_30s.mp4 + storage\outputs\tts_e04fa480.wav
2025-07-15 21:42:29 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_e1519e44.srt
2025-07-15 21:42:29 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:42:29 | ERROR    | app.services.video_service:compose_video:140 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | ERROR    | app.services.video_service:compose_video:153 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | ERROR    | app.services.video_service:compose_video:186 - Video composition failed: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 83e023b4-0496-4934-8f3f-14dcc1b6434d: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:42:29 | INFO     | app.services.base_service:update_task_status:54 - Task 83e023b4-0496-4934-8f3f-14dcc1b6434d status updated to failed (progress: 0.0%)
2025-07-15 21:51:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:51:30 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:51:30 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:51:30 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:52:22 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4
2025-07-15 21:52:22 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:52:22 | INFO     | app.services.base_service:create_task:35 - Created task c9a625e1-be77-4c57-bc42-095c725f7f74 of type full_pipeline
2025-07-15 21:52:22 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 0.0%)
2025-07-15 21:52:22 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:52:22 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4 to storage\outputs\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s_audio.wav
2025-07-15 21:52:25 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:52:25 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 20.0%)
2025-07-15 21:52:25 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:52:25 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:52:26 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s_audio.wav
2025-07-15 21:52:32 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:52:32 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 40.0%)
2025-07-15 21:52:32 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:52:42 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 60.0%)
2025-07-15 21:52:42 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:53:06 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_71414528.srt
2025-07-15 21:53:06 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 80.0%)
2025-07-15 21:53:06 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:53:06 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 21:53:06 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 21:53:06 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_b22d8096.wav
2025-07-15 21:53:06 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to processing (progress: 90.0%)
2025-07-15 21:53:06 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task c9a625e1-be77-4c57-bc42-095c725f7f74
2025-07-15 21:53:06 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\bc95d12a-7ed6-4464-9045-66c9e6ffca94_test_30s.mp4 + storage\outputs\tts_b22d8096.wav
2025-07-15 21:53:07 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_71414528.srt
2025-07-15 21:53:07 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:146 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:157 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:164 - All video writing methods failed: name 'video_file_path' is not defined
2025-07-15 21:53:07 | ERROR    | app.services.video_service:compose_video:196 - Video composition failed: name 'video_file_path' is not defined
2025-07-15 21:53:07 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task c9a625e1-be77-4c57-bc42-095c725f7f74: name 'video_file_path' is not defined
2025-07-15 21:53:07 | INFO     | app.services.base_service:update_task_status:54 - Task c9a625e1-be77-4c57-bc42-095c725f7f74 status updated to failed (progress: 0.0%)
2025-07-15 21:56:34 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 21:56:42 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 21:56:42 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 21:56:42 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 21:56:42 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 21:56:48 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4
2025-07-15 21:56:48 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 21:56:48 | INFO     | app.services.base_service:create_task:35 - Created task fed354db-2512-498f-b5fc-28c0b5847aef of type full_pipeline
2025-07-15 21:56:48 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 0.0%)
2025-07-15 21:56:48 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:56:48 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4 to storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_audio.wav
2025-07-15 21:56:50 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_audio.wav (duration: 30.06s)
2025-07-15 21:56:50 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 20.0%)
2025-07-15 21:56:50 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:56:50 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 21:56:51 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_audio.wav
2025-07-15 21:56:58 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 21:56:58 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 40.0%)
2025-07-15 21:56:58 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:05 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 60.0%)
2025-07-15 21:57:05 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:29 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_f5c75c4f.srt
2025-07-15 21:57:29 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 80.0%)
2025-07-15 21:57:29 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:29 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 21:57:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 21:57:29 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_d1b4077d.wav
2025-07-15 21:57:29 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to processing (progress: 90.0%)
2025-07-15 21:57:29 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task fed354db-2512-498f-b5fc-28c0b5847aef
2025-07-15 21:57:29 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s.mp4 + storage\outputs\tts_d1b4077d.wav
2025-07-15 21:57:30 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_f5c75c4f.srt
2025-07-15 21:57:30 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 21:57:30 | INFO     | app.services.video_service:compose_video:138 - 开始写入视频文件: storage\outputs\e3ea21e3-df23-4cfe-aa53-090bebca9fb8_test_30s_translated.mp4
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:153 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:164 - Video writing failed completely: 'NoneType' object has no attribute 'stdout'
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:171 - All video writing methods failed: name 'video_file_path' is not defined
2025-07-15 21:57:30 | ERROR    | app.services.video_service:compose_video:203 - Video composition failed: name 'video_file_path' is not defined
2025-07-15 21:57:30 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task fed354db-2512-498f-b5fc-28c0b5847aef: name 'video_file_path' is not defined
2025-07-15 21:57:30 | INFO     | app.services.base_service:update_task_status:54 - Task fed354db-2512-498f-b5fc-28c0b5847aef status updated to failed (progress: 0.0%)
2025-07-15 22:03:05 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:03:05 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:03:05 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:03:05 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:03:49 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4
2025-07-15 22:03:49 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:03:49 | INFO     | app.services.base_service:create_task:35 - Created task c622dda5-a6cd-4bf4-a92a-130f3647cce5 of type full_pipeline
2025-07-15 22:03:49 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 0.0%)
2025-07-15 22:03:49 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:03:49 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4 to storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_audio.wav
2025-07-15 22:03:51 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:03:51 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 20.0%)
2025-07-15 22:03:51 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:03:51 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:03:52 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_audio.wav
2025-07-15 22:03:59 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:03:59 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 40.0%)
2025-07-15 22:03:59 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:07 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 60.0%)
2025-07-15 22:04:07 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:30 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_2bc38c10.srt
2025-07-15 22:04:30 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 80.0%)
2025-07-15 22:04:30 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:30 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:04:30 | INFO     | app.services.tts_service:_synthesize_with_local_tts:284 - Using local TTS service as fallback
2025-07-15 22:04:30 | INFO     | app.services.tts_service:_synthesize_with_local_tts:319 - Local TTS synthesis completed: storage\outputs\tts_443eee48.wav
2025-07-15 22:04:30 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to processing (progress: 90.0%)
2025-07-15 22:04:30 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s.mp4 + storage\outputs\tts_443eee48.wav
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_2bc38c10.srt
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:04:30 | INFO     | app.services.video_service:compose_video:138 - 开始写入视频文件: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_translated.mp4
2025-07-15 22:04:31 | ERROR    | app.services.video_service:compose_video:149 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:04:31 | WARNING  | app.services.video_service:compose_video:154 - Used original video as fallback: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_translated.mp4
2025-07-15 22:04:31 | INFO     | app.services.video_service:compose_video:190 - Video composition completed: storage\outputs\6cc8a785-e647-4eb6-8fd6-00aa16d8fd08_test_30s_translated.mp4
2025-07-15 22:04:31 | INFO     | app.services.base_service:update_task_status:54 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 status updated to completed (progress: 100.0%)
2025-07-15 22:04:31 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task c622dda5-a6cd-4bf4-a92a-130f3647cce5
2025-07-15 22:04:31 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task c622dda5-a6cd-4bf4-a92a-130f3647cce5 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:09:21 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:09:31 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:09:31 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:09:31 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:09:31 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:09:52 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4
2025-07-15 22:09:52 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:09:52 | INFO     | app.services.base_service:create_task:35 - Created task eaa8dae3-5601-407d-b49d-265e034c4ba5 of type full_pipeline
2025-07-15 22:09:52 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 0.0%)
2025-07-15 22:09:52 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:09:52 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4 to storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_audio.wav
2025-07-15 22:09:55 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:09:55 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 20.0%)
2025-07-15 22:09:55 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:09:55 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:09:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_audio.wav
2025-07-15 22:10:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:10:02 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 40.0%)
2025-07-15 22:10:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:09 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 60.0%)
2025-07-15 22:10:09 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:33 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_0ed3fd3a.srt
2025-07-15 22:10:33 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 80.0%)
2025-07-15 22:10:33 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:33 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:10:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:10:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_cf99d3c5.wav
2025-07-15 22:10:33 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to processing (progress: 90.0%)
2025-07-15 22:10:33 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:33 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s.mp4 + storage\outputs\tts_cf99d3c5.wav
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_0ed3fd3a.srt
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:138 - 开始写入视频文件: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_translated.mp4
2025-07-15 22:10:34 | ERROR    | app.services.video_service:compose_video:149 - Failed to write video file: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:10:34 | WARNING  | app.services.video_service:compose_video:154 - Used original video as fallback: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_translated.mp4
2025-07-15 22:10:34 | INFO     | app.services.video_service:compose_video:190 - Video composition completed: storage\outputs\da4a2b9b-d21e-40eb-b56c-91453e1c8b56_test_30s_translated.mp4
2025-07-15 22:10:34 | INFO     | app.services.base_service:update_task_status:54 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 status updated to completed (progress: 100.0%)
2025-07-15 22:10:34 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task eaa8dae3-5601-407d-b49d-265e034c4ba5
2025-07-15 22:10:34 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task eaa8dae3-5601-407d-b49d-265e034c4ba5 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:14:25 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:14:25 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:14:25 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:14:25 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:20:06 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:20:06 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:20:06 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:20:06 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:20:06 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:20:24 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4
2025-07-15 22:20:24 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:20:24 | INFO     | app.services.base_service:create_task:35 - Created task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad of type full_pipeline
2025-07-15 22:20:24 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 0.0%)
2025-07-15 22:20:24 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:20:24 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4 to storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_audio.wav
2025-07-15 22:20:26 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:20:26 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 20.0%)
2025-07-15 22:20:26 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:20:26 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:20:27 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_audio.wav
2025-07-15 22:20:34 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:20:34 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 40.0%)
2025-07-15 22:20:34 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:20:42 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 60.0%)
2025-07-15 22:20:42 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_1274953b.srt
2025-07-15 22:21:05 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 80.0%)
2025-07-15 22:21:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:21:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:21:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_846a7587.wav
2025-07-15 22:21:05 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to processing (progress: 90.0%)
2025-07-15 22:21:05 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:05 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s.mp4 + storage\outputs\tts_846a7587.wav
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_1274953b.srt
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:134 - 开始写入视频文件: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_translated.mp4
2025-07-15 22:21:06 | ERROR    | app.services.video_service:compose_video:148 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:21:06 | WARNING  | app.services.video_service:compose_video:153 - 使用原始视频作为后备: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_translated.mp4
2025-07-15 22:21:06 | INFO     | app.services.video_service:compose_video:181 - Video composition completed: storage\outputs\162ca2d5-0d36-43ca-a58c-0453ec5d1b33_test_30s_translated.mp4
2025-07-15 22:21:06 | INFO     | app.services.base_service:update_task_status:54 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad status updated to completed (progress: 100.0%)
2025-07-15 22:21:06 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad
2025-07-15 22:21:06 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 3cfea90c-62cd-405a-b6a9-c59ff05a98ad completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:23:15 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:23:15 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:23:15 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:23:15 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:23:31 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s.mp4
2025-07-15 22:23:31 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:23:31 | INFO     | app.services.base_service:create_task:35 - Created task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e of type full_pipeline
2025-07-15 22:23:31 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 0.0%)
2025-07-15 22:23:31 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:23:31 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s.mp4 to storage\outputs\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s_audio.wav
2025-07-15 22:23:34 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:23:34 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 20.0%)
2025-07-15 22:23:34 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:23:34 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:23:35 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\c3e6be1e-334b-4560-a655-f3f123c14c39_test_30s_audio.wav
2025-07-15 22:23:41 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:23:41 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 40.0%)
2025-07-15 22:23:41 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:23:48 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 60.0%)
2025-07-15 22:23:48 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:24:11 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_18baf175.srt
2025-07-15 22:24:11 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 80.0%)
2025-07-15 22:24:11 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:24:11 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:24:11 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:24:11 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_185b469b.wav
2025-07-15 22:24:11 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to processing (progress: 90.0%)
2025-07-15 22:24:11 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e
2025-07-15 22:24:11 | ERROR    | app.services.video_service:compose_video:193 - Video composition failed: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:24:11 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:24:11 | INFO     | app.services.base_service:update_task_status:54 - Task e6ea7689-299e-4507-bb8a-b6b8e97f3f8e status updated to failed (progress: 0.0%)
2025-07-15 22:29:20 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:29:20 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:29:20 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:29:20 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:29:51 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4
2025-07-15 22:29:51 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:29:51 | INFO     | app.services.base_service:create_task:35 - Created task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a of type full_pipeline
2025-07-15 22:29:51 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 0.0%)
2025-07-15 22:29:51 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:29:51 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4 to storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_audio.wav
2025-07-15 22:29:54 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:29:54 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 20.0%)
2025-07-15 22:29:54 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:29:54 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:29:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_audio.wav
2025-07-15 22:30:01 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:30:01 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 40.0%)
2025-07-15 22:30:01 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:09 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 60.0%)
2025-07-15 22:30:09 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:34 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_0eaf17f6.srt
2025-07-15 22:30:34 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 80.0%)
2025-07-15 22:30:34 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:34 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:30:34 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:30:34 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_a56f15a8.wav
2025-07-15 22:30:34 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to processing (progress: 90.0%)
2025-07-15 22:30:34 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:34 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s.mp4 + storage\outputs\tts_a56f15a8.wav
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_0eaf17f6.srt
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:134 - 开始写入视频文件: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_translated.mp4
2025-07-15 22:30:35 | ERROR    | app.services.video_service:compose_video:149 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:30:35 | WARNING  | app.services.video_service:compose_video:154 - 使用原始视频作为后备: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_translated.mp4
2025-07-15 22:30:35 | INFO     | app.services.video_service:compose_video:182 - Video composition completed: storage\outputs\ad168e24-5252-4a60-b2ae-87b269b3ba8c_test_30s_translated.mp4
2025-07-15 22:30:35 | INFO     | app.services.base_service:update_task_status:54 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a status updated to completed (progress: 100.0%)
2025-07-15 22:30:35 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a
2025-07-15 22:30:35 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 9e3deadb-3287-47db-8ab7-d1549d8b2f1a completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:34:51 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:34:51 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:34:51 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:34:51 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:35:20 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s.mp4
2025-07-15 22:35:20 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:35:20 | INFO     | app.services.base_service:create_task:35 - Created task 7de0cfbf-ecfd-4837-b765-3419233e962f of type full_pipeline
2025-07-15 22:35:20 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 0.0%)
2025-07-15 22:35:20 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:35:20 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s.mp4 to storage\outputs\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s_audio.wav
2025-07-15 22:35:22 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:35:22 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 20.0%)
2025-07-15 22:35:22 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:35:22 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:35:23 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\41b5fc47-58a2-4479-9a18-196fe1d227ac_test_30s_audio.wav
2025-07-15 22:35:29 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:35:29 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 40.0%)
2025-07-15 22:35:29 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:35:38 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 60.0%)
2025-07-15 22:35:38 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:36:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_bf8900f1.srt
2025-07-15 22:36:05 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 80.0%)
2025-07-15 22:36:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:36:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:36:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:36:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_30dd706b.wav
2025-07-15 22:36:05 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to processing (progress: 90.0%)
2025-07-15 22:36:05 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 7de0cfbf-ecfd-4837-b765-3419233e962f
2025-07-15 22:36:05 | ERROR    | app.services.video_service:compose_video:202 - Video composition failed: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:36:05 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 7de0cfbf-ecfd-4837-b765-3419233e962f: cannot access local variable 'os' where it is not associated with a value
2025-07-15 22:36:05 | INFO     | app.services.base_service:update_task_status:54 - Task 7de0cfbf-ecfd-4837-b765-3419233e962f status updated to failed (progress: 0.0%)
2025-07-15 22:39:02 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:39:11 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:39:11 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:39:11 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:39:11 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:39:39 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4
2025-07-15 22:39:39 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:39:39 | INFO     | app.services.base_service:create_task:35 - Created task 9c710363-3630-4eee-b349-fd967bafe3a3 of type full_pipeline
2025-07-15 22:39:39 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 0.0%)
2025-07-15 22:39:39 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:39:39 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4 to storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_audio.wav
2025-07-15 22:39:42 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:39:42 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 20.0%)
2025-07-15 22:39:42 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:39:42 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:39:43 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_audio.wav
2025-07-15 22:39:49 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:39:49 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 40.0%)
2025-07-15 22:39:49 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:39:57 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 60.0%)
2025-07-15 22:39:57 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:24 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_d412e056.srt
2025-07-15 22:40:24 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 80.0%)
2025-07-15 22:40:24 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:24 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:40:24 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:40:24 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_7c27ae4f.wav
2025-07-15 22:40:24 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to processing (progress: 90.0%)
2025-07-15 22:40:24 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:79 - Composing video: storage\uploads\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s.mp4 + storage\outputs\tts_7c27ae4f.wav
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:124 - Subtitle file generated: storage\outputs\subtitles_d412e056.srt
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:125 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:40:24 | INFO     | app.services.video_service:compose_video:134 - 开始写入视频文件: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_translated.mp4
2025-07-15 22:40:24 | ERROR    | app.services.video_service:compose_video:164 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:40:24 | WARNING  | app.services.video_service:compose_video:169 - 使用原始视频作为后备: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_translated.mp4
2025-07-15 22:40:25 | INFO     | app.services.video_service:compose_video:197 - Video composition completed: storage\outputs\d9b67a64-d9c9-4ed8-9a0b-b9712e3c575e_test_30s_translated.mp4
2025-07-15 22:40:25 | INFO     | app.services.base_service:update_task_status:54 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 status updated to completed (progress: 100.0%)
2025-07-15 22:40:25 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 9c710363-3630-4eee-b349-fd967bafe3a3
2025-07-15 22:40:25 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 9c710363-3630-4eee-b349-fd967bafe3a3 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:47:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:47:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:47:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:47:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:48:21 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4
2025-07-15 22:48:21 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:48:21 | INFO     | app.services.base_service:create_task:35 - Created task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c of type full_pipeline
2025-07-15 22:48:21 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 0.0%)
2025-07-15 22:48:21 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:48:21 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4 to storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_audio.wav
2025-07-15 22:48:23 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:48:23 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 20.0%)
2025-07-15 22:48:23 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:48:23 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:48:24 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_audio.wav
2025-07-15 22:48:31 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:48:31 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 40.0%)
2025-07-15 22:48:31 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:48:39 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 60.0%)
2025-07-15 22:48:39 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:05 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_d4ea8ebb.srt
2025-07-15 22:49:05 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 80.0%)
2025-07-15 22:49:05 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:05 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:49:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:49:05 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_74556077.wav
2025-07-15 22:49:05 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to processing (progress: 90.0%)
2025-07-15 22:49:05 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:05 | INFO     | app.services.video_service:compose_video:84 - Composing video: storage\uploads\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s.mp4 + storage\outputs\tts_74556077.wav
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:129 - Subtitle file generated: storage\outputs\subtitles_d4ea8ebb.srt
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:130 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:139 - 开始写入视频文件: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_translated.mp4
2025-07-15 22:49:06 | ERROR    | app.services.video_service:compose_video:166 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:49:06 | WARNING  | app.services.video_service:compose_video:170 - 使用原始视频作为后备: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_translated.mp4
2025-07-15 22:49:06 | INFO     | app.services.video_service:compose_video:198 - Video composition completed: storage\outputs\03a85d6d-6219-4866-9477-7ad531f084b9_test_30s_translated.mp4
2025-07-15 22:49:06 | INFO     | app.services.base_service:update_task_status:54 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c status updated to completed (progress: 100.0%)
2025-07-15 22:49:06 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c
2025-07-15 22:49:06 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task c5c2a4ca-de7b-4a18-b466-23fd6a7a035c completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-15 22:51:21 | INFO     | main:lifespan:39 - 正在关闭视频翻译AI服务
2025-07-15 22:51:30 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-15 22:51:30 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-15 22:51:30 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-15 22:51:30 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-15 22:51:51 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4
2025-07-15 22:51:51 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4 (大小: 1952084 bytes)
2025-07-15 22:51:51 | INFO     | app.services.base_service:create_task:35 - Created task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e of type full_pipeline
2025-07-15 22:51:51 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 0.0%)
2025-07-15 22:51:51 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:51:51 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4 to storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_audio.wav
2025-07-15 22:51:54 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_audio.wav (duration: 30.06s)
2025-07-15 22:51:54 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 20.0%)
2025-07-15 22:51:54 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:51:54 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-15 22:51:55 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_audio.wav
2025-07-15 22:52:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-15 22:52:02 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 40.0%)
2025-07-15 22:52:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:09 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 60.0%)
2025-07-15 22:52:09 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:33 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_acb16bc7.srt
2025-07-15 22:52:33 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 80.0%)
2025-07-15 22:52:33 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:33 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-15 22:52:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-15 22:52:33 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_4fd4ddbd.wav
2025-07-15 22:52:33 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to processing (progress: 90.0%)
2025-07-15 22:52:33 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:33 | INFO     | app.services.video_service:compose_video:84 - Composing video: storage\uploads\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s.mp4 + storage\outputs\tts_4fd4ddbd.wav
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:129 - Subtitle file generated: storage\outputs\subtitles_acb16bc7.srt
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:130 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:139 - 开始写入视频文件: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_translated.mp4
2025-07-15 22:52:34 | ERROR    | app.services.video_service:compose_video:166 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-15 22:52:34 | WARNING  | app.services.video_service:compose_video:170 - 使用原始视频作为后备: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_translated.mp4
2025-07-15 22:52:34 | INFO     | app.services.video_service:compose_video:204 - Video composition completed: storage\outputs\c9de224b-2f18-43ef-8331-e9290c5cfd64_test_30s_translated.mp4
2025-07-15 22:52:34 | INFO     | app.services.base_service:update_task_status:54 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e status updated to completed (progress: 100.0%)
2025-07-15 22:52:34 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e
2025-07-15 22:52:34 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 6d3d4eb1-5700-4900-ab8e-4800a8a0f18e completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:00:56 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:01:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:01:07 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:01:07 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:01:07 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:01:07 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:03:38 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4
2025-07-19 10:03:38 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:03:38 | INFO     | app.services.base_service:create_task:35 - Created task 42ddf68e-5fff-422f-9704-a01f55ef0f3b of type full_pipeline
2025-07-19 10:03:38 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 0.0%)
2025-07-19 10:03:38 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:03:38 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4 to storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_audio.wav
2025-07-19 10:03:49 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:03:49 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 20.0%)
2025-07-19 10:03:49 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:03:49 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:03:51 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_audio.wav
2025-07-19 10:03:59 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:03:59 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 40.0%)
2025-07-19 10:03:59 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:03:59 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s.mp4
2025-07-19 10:03:59 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:03:59 | INFO     | app.services.base_service:create_task:35 - Created task 8c5f8045-d104-4816-b270-97ee8591fce8 of type full_pipeline
2025-07-19 10:03:59 | INFO     | app.services.base_service:update_task_status:54 - Task 8c5f8045-d104-4816-b270-97ee8591fce8 status updated to processing (progress: 0.0%)
2025-07-19 10:03:59 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 8c5f8045-d104-4816-b270-97ee8591fce8
2025-07-19 10:03:59 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s.mp4 to storage\outputs\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s_audio.wav
2025-07-19 10:04:00 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\cc244cd1-e600-4e10-af33-e9949ee3e3b4_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:04:00 | INFO     | app.services.base_service:update_task_status:54 - Task 8c5f8045-d104-4816-b270-97ee8591fce8 status updated to processing (progress: 20.0%)
2025-07-19 10:04:00 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 8c5f8045-d104-4816-b270-97ee8591fce8
2025-07-19 10:04:00 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:04:00 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 8c5f8045-d104-4816-b270-97ee8591fce8: 'Whisper' object has no attribute 'name'
2025-07-19 10:04:00 | INFO     | app.services.base_service:update_task_status:54 - Task 8c5f8045-d104-4816-b270-97ee8591fce8 status updated to failed (progress: 0.0%)
2025-07-19 10:04:06 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 60.0%)
2025-07-19 10:04:06 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:20 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_cf5a170b.srt
2025-07-19 10:04:20 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 80.0%)
2025-07-19 10:04:20 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:20 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:04:20 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:04:21 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_b36f9c25.wav
2025-07-19 10:04:21 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to processing (progress: 90.0%)
2025-07-19 10:04:21 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:84 - Composing video: storage\uploads\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s.mp4 + storage\outputs\tts_b36f9c25.wav
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:129 - Subtitle file generated: storage\outputs\subtitles_cf5a170b.srt
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:130 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:04:21 | INFO     | app.services.video_service:compose_video:139 - 开始写入视频文件: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_translated.mp4
2025-07-19 10:04:21 | ERROR    | app.services.video_service:compose_video:166 - 写入视频文件失败: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:04:21 | WARNING  | app.services.video_service:compose_video:170 - 使用原始视频作为后备: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_translated.mp4
2025-07-19 10:04:22 | INFO     | app.services.video_service:compose_video:204 - Video composition completed: storage\outputs\a17a7001-9ea3-4889-ad34-a3e465a5b1a9_test_30s_translated.mp4
2025-07-19 10:04:22 | INFO     | app.services.base_service:update_task_status:54 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b status updated to completed (progress: 100.0%)
2025-07-19 10:04:22 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 42ddf68e-5fff-422f-9704-a01f55ef0f3b
2025-07-19 10:04:22 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 42ddf68e-5fff-422f-9704-a01f55ef0f3b completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:10:16 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:10:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:10:26 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:10:26 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:10:26 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:10:26 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:10:42 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4
2025-07-19 10:10:42 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:10:42 | INFO     | app.services.base_service:create_task:35 - Created task f8199595-4230-4d7e-9aed-d73fce1b13da of type full_pipeline
2025-07-19 10:10:42 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 0.0%)
2025-07-19 10:10:42 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:10:42 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4 to storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_audio.wav
2025-07-19 10:10:44 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:10:44 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 20.0%)
2025-07-19 10:10:44 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:10:44 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:10:45 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_audio.wav
2025-07-19 10:10:53 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:10:53 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 40.0%)
2025-07-19 10:10:53 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:10:59 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 60.0%)
2025-07-19 10:10:59 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:13 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_91bdc60d.srt
2025-07-19 10:11:13 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 80.0%)
2025-07-19 10:11:13 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:13 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:11:13 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:11:13 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_a8d68ceb.wav
2025-07-19 10:11:13 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to processing (progress: 90.0%)
2025-07-19 10:11:13 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:13 | INFO     | app.services.video_service:compose_video:83 - Composing video: storage\uploads\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s.mp4 + storage\outputs\tts_a8d68ceb.wav
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:124 - Audio replacement successful
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:140 - Subtitle file generated: storage\outputs\subtitles_91bdc60d.srt
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:141 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:154 - 开始写入视频文件: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_translated.mp4
2025-07-19 10:11:14 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:11:14 | INFO     | app.services.video_service:compose_video:183 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 10:11:20 | INFO     | app.services.video_service:compose_video:208 - FFmpeg直接命令执行成功: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_translated.mp4
2025-07-19 10:11:20 | INFO     | app.services.video_service:compose_video:255 - Video composition completed: storage\outputs\0bfb1d93-3264-4954-b96f-6b25c1805dee_test_30s_translated.mp4
2025-07-19 10:11:20 | INFO     | app.services.base_service:update_task_status:54 - Task f8199595-4230-4d7e-9aed-d73fce1b13da status updated to completed (progress: 100.0%)
2025-07-19 10:11:20 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task f8199595-4230-4d7e-9aed-d73fce1b13da
2025-07-19 10:11:20 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task f8199595-4230-4d7e-9aed-d73fce1b13da completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:16:37 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\90de387d-c2b2-4096-92f1-b0671528d432_test_30s.mp4
2025-07-19 10:16:37 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\90de387d-c2b2-4096-92f1-b0671528d432_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:16:37 | INFO     | app.services.base_service:create_task:35 - Created task 164f34ef-97c2-46a4-8098-ad8b5428e144 of type full_pipeline
2025-07-19 10:16:37 | INFO     | app.services.base_service:update_task_status:54 - Task 164f34ef-97c2-46a4-8098-ad8b5428e144 status updated to processing (progress: 0.0%)
2025-07-19 10:16:37 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 164f34ef-97c2-46a4-8098-ad8b5428e144
2025-07-19 10:16:37 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\90de387d-c2b2-4096-92f1-b0671528d432_test_30s.mp4 to storage\outputs\90de387d-c2b2-4096-92f1-b0671528d432_test_30s_audio.wav
2025-07-19 10:16:38 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\90de387d-c2b2-4096-92f1-b0671528d432_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:16:38 | INFO     | app.services.base_service:update_task_status:54 - Task 164f34ef-97c2-46a4-8098-ad8b5428e144 status updated to processing (progress: 20.0%)
2025-07-19 10:16:38 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 164f34ef-97c2-46a4-8098-ad8b5428e144
2025-07-19 10:16:38 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:16:38 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task 164f34ef-97c2-46a4-8098-ad8b5428e144: 'Whisper' object has no attribute 'name'
2025-07-19 10:16:38 | INFO     | app.services.base_service:update_task_status:54 - Task 164f34ef-97c2-46a4-8098-ad8b5428e144 status updated to failed (progress: 0.0%)
2025-07-19 10:17:10 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\129181da-4f09-4282-b207-70f6774d168a_test_30s.mp4
2025-07-19 10:17:10 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\129181da-4f09-4282-b207-70f6774d168a_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:17:10 | INFO     | app.services.base_service:create_task:35 - Created task ca0723b8-2755-43b8-8efa-858d909f1318 of type full_pipeline
2025-07-19 10:17:10 | INFO     | app.services.base_service:update_task_status:54 - Task ca0723b8-2755-43b8-8efa-858d909f1318 status updated to processing (progress: 0.0%)
2025-07-19 10:17:10 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task ca0723b8-2755-43b8-8efa-858d909f1318
2025-07-19 10:17:10 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\129181da-4f09-4282-b207-70f6774d168a_test_30s.mp4 to storage\outputs\129181da-4f09-4282-b207-70f6774d168a_test_30s_audio.wav
2025-07-19 10:17:11 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\129181da-4f09-4282-b207-70f6774d168a_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:17:11 | INFO     | app.services.base_service:update_task_status:54 - Task ca0723b8-2755-43b8-8efa-858d909f1318 status updated to processing (progress: 20.0%)
2025-07-19 10:17:11 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task ca0723b8-2755-43b8-8efa-858d909f1318
2025-07-19 10:17:11 | ERROR    | app.services.speech_service:recognize_speech:108 - Speech recognition failed: 'Whisper' object has no attribute 'name'
2025-07-19 10:17:11 | ERROR    | app.services.task_service:process_full_pipeline:165 - Full pipeline failed for task ca0723b8-2755-43b8-8efa-858d909f1318: 'Whisper' object has no attribute 'name'
2025-07-19 10:17:11 | INFO     | app.services.base_service:update_task_status:54 - Task ca0723b8-2755-43b8-8efa-858d909f1318 status updated to failed (progress: 0.0%)
2025-07-19 10:18:33 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:18:43 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:18:43 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:18:43 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:18:43 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:18:43 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:18:52 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4
2025-07-19 10:18:52 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:18:52 | INFO     | app.services.base_service:create_task:35 - Created task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 of type full_pipeline
2025-07-19 10:18:52 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 0.0%)
2025-07-19 10:18:52 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:18:52 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4 to storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_audio.wav
2025-07-19 10:18:55 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:18:55 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 20.0%)
2025-07-19 10:18:55 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:18:55 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:18:56 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_audio.wav
2025-07-19 10:19:02 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:19:02 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 40.0%)
2025-07-19 10:19:02 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:08 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 60.0%)
2025-07-19 10:19:08 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:23 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_99ffe62e.srt
2025-07-19 10:19:23 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 80.0%)
2025-07-19 10:19:23 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:23 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:19:23 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:19:23 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_14dd905f.wav
2025-07-19 10:19:23 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to processing (progress: 90.0%)
2025-07-19 10:19:23 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:23 | INFO     | app.services.video_service:compose_video:83 - Composing video: storage\uploads\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s.mp4 + storage\outputs\tts_14dd905f.wav
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:124 - Audio replacement successful
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:140 - Subtitle file generated: storage\outputs\subtitles_99ffe62e.srt
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:141 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:154 - 开始写入视频文件: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_translated.mp4
2025-07-19 10:19:24 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:19:24 | INFO     | app.services.video_service:compose_video:183 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 10:19:29 | INFO     | app.services.video_service:compose_video:208 - FFmpeg直接命令执行成功: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_translated.mp4
2025-07-19 10:19:30 | INFO     | app.services.video_service:compose_video:255 - Video composition completed: storage\outputs\2cd0720f-ecf1-4186-a61b-69d582b70690_test_30s_translated.mp4
2025-07-19 10:19:30 | INFO     | app.services.base_service:update_task_status:54 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 status updated to completed (progress: 100.0%)
2025-07-19 10:19:30 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task 6ecf3e7f-97ce-40b2-83c6-da5168241be3
2025-07-19 10:19:30 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task 6ecf3e7f-97ce-40b2-83c6-da5168241be3 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:23:34 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:23:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:23:44 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:23:44 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:23:44 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:23:44 | INFO     | main:lifespan:34 - 环境: 开发
2025-07-19 10:23:49 | INFO     | app.api.routes:upload_file:88 - 上传文件: test_30s.mp4 -> storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4
2025-07-19 10:23:49 | INFO     | app.api.routes:upload_file:95 - 文件上传成功: storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 (大小: 1952084 bytes)
2025-07-19 10:23:49 | INFO     | app.services.base_service:create_task:35 - Created task d09e07d2-59a9-4130-851f-23fc59ad7c24 of type full_pipeline
2025-07-19 10:23:49 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 0.0%)
2025-07-19 10:23:49 | INFO     | app.services.task_service:process_full_pipeline:48 - Step 1: Extracting audio for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:23:49 | INFO     | app.services.audio_service:extract_audio:76 - Extracting audio from storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 to storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_audio.wav
2025-07-19 10:23:51 | INFO     | app.services.audio_service:extract_audio:104 - Audio extracted successfully: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_audio.wav (duration: 30.06s)
2025-07-19 10:23:51 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 20.0%)
2025-07-19 10:23:51 | INFO     | app.services.task_service:process_full_pipeline:58 - Step 2: Speech recognition for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:23:51 | INFO     | app.services.speech_service:_load_model:27 - Loading Whisper model: base
2025-07-19 10:23:52 | INFO     | app.services.speech_service:recognize_speech:74 - Recognizing speech from storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_audio.wav
2025-07-19 10:23:59 | INFO     | app.services.speech_service:recognize_speech:102 - Speech recognition completed. Language: en, Confidence: -0.23, Segments: 9
2025-07-19 10:23:59 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 40.0%)
2025-07-19 10:23:59 | INFO     | app.services.task_service:process_full_pipeline:67 - Step 3: Translation for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:05 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 60.0%)
2025-07-19 10:24:05 | INFO     | app.services.task_service:process_full_pipeline:79 - Step 4: Generating subtitles for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:20 | INFO     | app.services.subtitle_service:_generate_srt_file:126 - SRT file generated: storage\outputs\subtitles_cd1385a6.srt
2025-07-19 10:24:20 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 80.0%)
2025-07-19 10:24:20 | INFO     | app.services.task_service:process_full_pipeline:107 - Step 5: TTS synthesis for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:20 | INFO     | app.services.tts_service:synthesize_speech:79 - Using Local TTS directly (bypassing online services due to persistent issues)
2025-07-19 10:24:20 | INFO     | app.services.tts_service:_synthesize_with_local_tts:314 - Using local TTS service as fallback
2025-07-19 10:24:20 | INFO     | app.services.tts_service:_synthesize_with_local_tts:349 - Local TTS synthesis completed: storage\outputs\tts_bea4150b.wav
2025-07-19 10:24:20 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to processing (progress: 90.0%)
2025-07-19 10:24:20 | INFO     | app.services.task_service:process_full_pipeline:118 - Step 6: Video composition for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:20 | INFO     | app.services.video_service:compose_video:83 - Composing video: storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 + storage\outputs\tts_bea4150b.wav
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:124 - Audio replacement successful
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:140 - Subtitle file generated: storage\outputs\subtitles_cd1385a6.srt
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:141 - Note: Subtitle overlay skipped to avoid ImageMagick dependency. SRT file is available for download.
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:154 - 开始写入视频文件: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:21 | ERROR    | app.services.video_service:compose_video:180 - write_videofile failed: 'NoneType' object has no attribute 'stdout'
2025-07-19 10:24:21 | INFO     | app.services.video_service:compose_video:183 - 尝试使用FFmpeg直接命令作为备选方案
2025-07-19 10:24:22 | INFO     | app.services.video_service:compose_video:193 - 音频文件检查结果: True
2025-07-19 10:24:22 | INFO     | app.services.video_service:compose_video:211 - 使用新音频替换: storage\outputs\tts_bea4150b.wav
2025-07-19 10:24:22 | INFO     | app.services.video_service:compose_video:223 - 执行FFmpeg命令: ffmpeg -y -i storage\uploads\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s.mp4 -i storage\outputs\tts_bea4150b.wav -c:v copy -c:a aac -map 0:v:0 -map 1:a:0 -shortest -avoid_negative_ts make_zero storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:23 | INFO     | app.services.video_service:compose_video:235 - FFmpeg直接命令执行成功: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:23 | INFO     | app.services.video_service:compose_video:284 - Video composition completed: storage\outputs\482d2aac-97be-4089-bcf9-53d478724ebe_test_30s_translated.mp4
2025-07-19 10:24:23 | INFO     | app.services.base_service:update_task_status:54 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 status updated to completed (progress: 100.0%)
2025-07-19 10:24:23 | INFO     | app.services.task_service:process_full_pipeline:158 - Full pipeline completed for task d09e07d2-59a9-4130-851f-23fc59ad7c24
2025-07-19 10:24:23 | WARNING  | app.services.task_service:process_full_pipeline:162 - Task d09e07d2-59a9-4130-851f-23fc59ad7c24 completed with fallbacks: TTS service was unavailable - text content saved instead of audio
2025-07-19 10:31:46 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:31:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:31:57 | INFO     | app.core.logging:setup_logging:51 - Logging configured successfully
2025-07-19 10:31:57 | INFO     | main:lifespan:32 - 正在启动视频翻译AI服务
2025-07-19 10:31:57 | INFO     | main:lifespan:33 - 版本: 1.0.0
2025-07-19 10:31:57 | INFO     | main:lifespan:34 - 环境: 开发
