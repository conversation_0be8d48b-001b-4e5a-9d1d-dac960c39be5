import os

# 检查音频文件大小
audio_path = r'VideoLingo20250607_2(1)\VideoLingo20250607\translation-service-python\out\temp_audio.wav'

if os.path.exists(audio_path):
    file_size = os.path.getsize(audio_path)
    print(f'🎵 音频文件: {audio_path}')
    print(f'📈 文件大小: {file_size/1024/1024:.1f} MB')
    
    # 估算时长 (假设标准WAV格式)
    # 标准16位44.1kHz立体声: 约172KB/秒
    # 标准16位44.1kHz单声道: 约86KB/秒
    estimated_duration_stereo = file_size / (172 * 1024)
    estimated_duration_mono = file_size / (86 * 1024)
    
    print(f'📊 估算时长 (立体声): {estimated_duration_stereo:.1f}秒 ({estimated_duration_stereo/60:.1f}分钟)')
    print(f'📊 估算时长 (单声道): {estimated_duration_mono:.1f}秒 ({estimated_duration_mono/60:.1f}分钟)')
    
    # 检查原视频文件大小作为对比
    video_path = 'input.mp4'
    if os.path.exists(video_path):
        video_size = os.path.getsize(video_path)
        print(f'📹 原视频大小: {video_size/1024/1024:.1f} MB')
        print(f'📊 音频占比: {(file_size/video_size)*100:.1f}%')
else:
    print(f'❌ 音频文件不存在: {audio_path}') 