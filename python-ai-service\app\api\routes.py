"""
API routes for video translation AI service
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File
from typing import List, Optional
from datetime import datetime
import os
from pathlib import Path

from app.models.schemas import (
    AudioExtractionRequest, AudioExtractionResponse,
    SpeechRecognitionRequest, SpeechRecognitionResponse,
    TranslationRequest, TranslationResponse,
    SubtitleGenerationRequest, SubtitleGenerationResponse,
    TTSRequest, TTSResponse,
    VideoCompositionRequest, VideoCompositionResponse,
    FullPipelineRequest, TaskInfo, TaskListResponse,
    HealthCheckResponse, ErrorResponse
)
from app.services.audio_service import AudioService
from app.services.speech_service import SpeechService
from app.services.translation_service import TranslationService
from app.services.subtitle_service import SubtitleService
from app.services.tts_service import TTSService
from app.services.video_service import VideoService
from app.services.task_service import TaskService
from app.core.logging import get_logger
from app.core.config import settings

# Initialize logger
logger = get_logger(__name__)

# Create API router
api_router = APIRouter()

# Initialize services
audio_service = AudioService()
speech_service = SpeechService()
translation_service = TranslationService()
subtitle_service = SubtitleService()
tts_service = TTSService()
video_service = VideoService()
task_service = TaskService()


@api_router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    return HealthCheckResponse(
        status="healthy",
        timestamp=datetime.now(),
        version=settings.app_version,
        dependencies={
            "audio_service": "healthy",
            "speech_service": "healthy",
            "translation_service": "healthy",
            "tts_service": "healthy",
            "video_service": "healthy",
        }
    )


@api_router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload video file"""
    try:
        # 检查文件类型 - 支持视频和音频文件
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
        audio_extensions = ['.wav', '.mp3', '.m4a', '.aac', '.flac', '.ogg']
        allowed_extensions = video_extensions + audio_extensions
        
        file_extension = os.path.splitext(file.filename)[1].lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(status_code=400, detail=f"不支持的文件格式: {file_extension}，支持的格式: {', '.join(allowed_extensions)}")
        
        # 确保上传目录存在
        upload_dir = Path(settings.uploads_path)
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成唯一文件名
        import uuid
        unique_filename = f"{uuid.uuid4()}_{file.filename}"
        file_path = upload_dir / unique_filename
        
        # 保存文件
        logger.info(f"上传文件: {file.filename} -> {file_path}")
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        file_size = len(content)
        logger.info(f"文件上传成功: {file_path} (大小: {file_size} bytes)")
        
        return {
            "message": "文件上传成功",
            "filename": file.filename,
            "file_path": str(file_path),
            "file_size": file_size,
            "unique_filename": unique_filename
        }
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/audio/extract", response_model=AudioExtractionResponse)
async def extract_audio(
    request: AudioExtractionRequest,
    background_tasks: BackgroundTasks
):
    """Extract audio from video"""
    try:
        task_id = await audio_service.extract_audio_async(
            request.video_file_path,
            request.output_format,
            request.sample_rate,
            request.channels
        )
        
        # Add background task to process
        background_tasks.add_task(
            audio_service.process_audio_extraction,
            task_id,
            request
        )
        
        return AudioExtractionResponse(
            task_id=task_id,
            audio_file_path="",  # Will be filled after processing
            duration=0.0,
            sample_rate=request.sample_rate,
            channels=request.channels
        )
    except Exception as e:
        logger.error(f"Audio extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/speech/recognize", response_model=SpeechRecognitionResponse)
async def recognize_speech(
    request: SpeechRecognitionRequest,
    background_tasks: BackgroundTasks
):
    """Recognize speech from audio"""
    try:
        task_id = await speech_service.recognize_speech_async(
            request.audio_file_path,
            request.language,
            request.model
        )
        
        background_tasks.add_task(
            speech_service.process_speech_recognition,
            task_id,
            request
        )
        
        return SpeechRecognitionResponse(
            task_id=task_id,
            text="",  # Will be filled after processing
            language=request.language,
            confidence=0.0,
            segments=[]
        )
    except Exception as e:
        logger.error(f"Speech recognition failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/translation/translate", response_model=TranslationResponse)
async def translate_text(
    request: TranslationRequest,
    background_tasks: BackgroundTasks
):
    """Translate text"""
    try:
        task_id = await translation_service.translate_text_async(
            request.text,
            request.source_language,
            request.target_language,
            request.service
        )
        
        background_tasks.add_task(
            translation_service.process_translation,
            task_id,
            request
        )
        
        return TranslationResponse(
            task_id=task_id,
            translated_text="",  # Will be filled after processing
            source_language=request.source_language,
            target_language=request.target_language,
            confidence=0.0
        )
    except Exception as e:
        logger.error(f"Translation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/subtitles/generate", response_model=SubtitleGenerationResponse)
async def generate_subtitles(
    request: SubtitleGenerationRequest,
    background_tasks: BackgroundTasks
):
    """Generate subtitles from segments"""
    try:
        task_id = await subtitle_service.generate_subtitles_async(
            request.segments,
            request.max_chars_per_line,
            request.max_lines
        )
        
        background_tasks.add_task(
            subtitle_service.process_subtitle_generation,
            task_id,
            request
        )
        
        return SubtitleGenerationResponse(
            task_id=task_id,
            subtitles=[],  # Will be filled after processing
            subtitle_file_path=""
        )
    except Exception as e:
        logger.error(f"Subtitle generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/tts/synthesize", response_model=TTSResponse)
async def synthesize_speech(
    request: TTSRequest,
    background_tasks: BackgroundTasks
):
    """Synthesize speech from text"""
    try:
        task_id = await tts_service.synthesize_speech_async(
            request.text,
            request.voice,
            request.speed,
            request.pitch,
            request.service
        )
        
        background_tasks.add_task(
            tts_service.process_tts,
            task_id,
            request
        )
        
        return TTSResponse(
            task_id=task_id,
            audio_file_path="",  # Will be filled after processing
            duration=0.0,
            text=request.text
        )
    except Exception as e:
        logger.error(f"TTS synthesis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/tts/voices")
async def get_available_voices():
    """Get available TTS voices"""
    try:
        # Get voices from Edge TTS if available
        try:
            edge_voices = await tts_service.get_available_voices()
            if edge_voices:
                return {
                    "service": "edge-tts",
                    "voices": edge_voices,
                    "fallback_service": "gtts",
                    "fallback_languages": ["zh", "en", "ja", "ko", "fr", "de", "es", "ru"]
                }
        except Exception as e:
            logger.warning(f"Edge TTS voices unavailable: {e}")
        
        # Return gTTS supported languages as fallback
        return {
            "service": "gtts",
            "voices": {
                "zh": [{"name": "Chinese", "code": "zh-CN-XiaoxiaoNeural", "language": "zh"}],
                "en": [{"name": "English", "code": "en-US-AriaNeural", "language": "en"}],
                "ja": [{"name": "Japanese", "code": "ja-JP-Standard", "language": "ja"}],
                "ko": [{"name": "Korean", "code": "ko-KR-Standard", "language": "ko"}],
                "fr": [{"name": "French", "code": "fr-FR-Standard", "language": "fr"}],
                "de": [{"name": "German", "code": "de-DE-Standard", "language": "de"}],
                "es": [{"name": "Spanish", "code": "es-ES-Standard", "language": "es"}],
                "ru": [{"name": "Russian", "code": "ru-RU-Standard", "language": "ru"}]
            },
            "fallback_service": "text",
            "note": "gTTS fallback mode - voices mapped to language codes"
        }
        
    except Exception as e:
        logger.error(f"Failed to get voices: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/video/compose", response_model=VideoCompositionResponse)
async def compose_video(
    request: VideoCompositionRequest,
    background_tasks: BackgroundTasks
):
    """Compose video with new audio and subtitles"""
    try:
        task_id = await video_service.compose_video_async(
            request.video_file_path,
            request.audio_file_path,
            request.subtitle_file_path,
            request.output_format
        )
        
        background_tasks.add_task(
            video_service.process_video_composition,
            task_id,
            request
        )
        
        return VideoCompositionResponse(
            task_id=task_id,
            video_file_path="",  # Will be filled after processing
            duration=0.0,
            file_size=0
        )
    except Exception as e:
        logger.error(f"Video composition failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/pipeline/full")
async def full_pipeline(
    request: FullPipelineRequest,
    background_tasks: BackgroundTasks
):
    """Run full video translation pipeline"""
    try:
        task_id = await task_service.create_full_pipeline_task(request)
        
        background_tasks.add_task(
            task_service.process_full_pipeline,
            task_id,
            request
        )
        
        return {"task_id": task_id, "status": "started"}
    except Exception as e:
        logger.error(f"Full pipeline failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/tasks/{task_id}", response_model=TaskInfo)
async def get_task_status(task_id: str):
    """Get task status"""
    try:
        task = await task_service.get_task_status(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        return task
    except Exception as e:
        logger.error(f"Get task status failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/tasks", response_model=TaskListResponse)
async def list_tasks(
    page: int = 1,
    per_page: int = 10,
    status: Optional[str] = None,
    task_type: Optional[str] = None
):
    """List tasks with pagination"""
    try:
        tasks = await task_service.list_tasks(page, per_page, status, task_type)
        return tasks
    except Exception as e:
        logger.error(f"List tasks failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """Cancel a task"""
    try:
        success = await task_service.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
        return {"message": "Task cancelled successfully"}
    except Exception as e:
        logger.error(f"Cancel task failed: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 