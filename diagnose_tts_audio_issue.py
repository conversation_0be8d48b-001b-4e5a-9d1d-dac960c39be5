#!/usr/bin/env python3
"""
诊断TTS音频问题
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def check_audio_files():
    """检查音频文件"""
    try:
        print("🎵 检查音频文件...")
        
        # 检查存储目录
        storage_dirs = [
            "python-ai-service/storage/uploads",
            "python-ai-service/storage/outputs"
        ]
        
        for dir_path in storage_dirs:
            abs_dir = os.path.abspath(dir_path)
            if not os.path.exists(abs_dir):
                print(f"❌ 目录不存在: {abs_dir}")
                continue
                
            print(f"\n📁 检查目录: {abs_dir}")
            
            # 查找音频文件
            audio_extensions = ['.wav', '.mp3', '.m4a', '.aac']
            audio_files = []
            
            for ext in audio_extensions:
                audio_files.extend(list(Path(abs_dir).glob(f"*{ext}")))
            
            if not audio_files:
                print("  没有找到音频文件")
                continue
            
            for audio_file in audio_files:
                file_size = audio_file.stat().st_size
                print(f"  📄 {audio_file.name}")
                print(f"    大小: {file_size} 字节")
                print(f"    路径: {audio_file}")
                
                # 检查是否是有效的音频文件
                try:
                    if audio_file.suffix.lower() == '.wav':
                        import wave
                        with wave.open(str(audio_file), 'rb') as wav_file:
                            frames = wav_file.getnframes()
                            rate = wav_file.getframerate()
                            duration = frames / rate
                            print(f"    时长: {duration:.2f}秒")
                            print(f"    采样率: {rate}Hz")
                            print(f"    帧数: {frames}")
                            
                            if frames == 0:
                                print("    ⚠️ 警告: 音频文件为空")
                            else:
                                print("    ✅ 音频文件有效")
                except Exception as e:
                    print(f"    ❌ 音频文件无效: {e}")
                
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查音频文件失败: {e}")
        return False

async def check_tts_files():
    """检查TTS生成的文件"""
    try:
        print("🎤 检查TTS生成的文件...")
        
        outputs_dir = Path("python-ai-service/storage/outputs")
        if not outputs_dir.exists():
            print("❌ 输出目录不存在")
            return False
        
        # 查找TTS文件
        tts_files = list(outputs_dir.glob("tts_*"))
        
        if not tts_files:
            print("❌ 没有找到TTS文件")
            return False
        
        for tts_file in tts_files:
            print(f"\n📄 TTS文件: {tts_file.name}")
            print(f"  路径: {tts_file}")
            print(f"  大小: {tts_file.stat().st_size} 字节")
            print(f"  扩展名: {tts_file.suffix}")
            
            if tts_file.suffix == '.txt':
                print("  类型: 文本回退文件")
                try:
                    with open(tts_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"  内容预览: {content[:100]}...")
                except Exception as e:
                    print(f"  读取失败: {e}")
            elif tts_file.suffix in ['.wav', '.mp3', '.m4a']:
                print("  类型: 音频文件")
                try:
                    if tts_file.suffix == '.wav':
                        import wave
                        with wave.open(str(tts_file), 'rb') as wav_file:
                            duration = wav_file.getnframes() / wav_file.getframerate()
                            print(f"  时长: {duration:.2f}秒")
                            print(f"  ✅ 有效的音频文件")
                except Exception as e:
                    print(f"  ❌ 音频文件无效: {e}")
            else:
                print("  类型: 未知")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查TTS文件失败: {e}")
        return False

async def check_video_files():
    """检查视频文件"""
    try:
        print("🎬 检查视频文件...")
        
        # 检查上传和输出目录中的视频文件
        dirs_to_check = [
            ("上传目录", "python-ai-service/storage/uploads"),
            ("输出目录", "python-ai-service/storage/outputs")
        ]
        
        for dir_name, dir_path in dirs_to_check:
            abs_dir = os.path.abspath(dir_path)
            if not os.path.exists(abs_dir):
                print(f"❌ {dir_name}不存在: {abs_dir}")
                continue
            
            print(f"\n📁 {dir_name}: {abs_dir}")
            
            # 查找视频文件
            video_files = list(Path(abs_dir).glob("*.mp4"))
            
            if not video_files:
                print("  没有找到视频文件")
                continue
            
            for video_file in video_files:
                file_size = video_file.stat().st_size
                print(f"  🎥 {video_file.name}")
                print(f"    大小: {file_size} 字节")
                
                # 检查视频是否有音频轨道
                try:
                    import subprocess
                    
                    # 使用ffprobe检查音频轨道
                    cmd = [
                        'ffprobe', '-v', 'quiet', '-show_streams', 
                        '-select_streams', 'a', str(video_file)
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                    
                    if result.returncode == 0 and 'codec_type=audio' in result.stdout:
                        print("    ✅ 包含音频轨道")
                        
                        # 获取音频信息
                        audio_info = result.stdout
                        if 'duration=' in audio_info:
                            duration_line = [line for line in audio_info.split('\n') if 'duration=' in line]
                            if duration_line:
                                duration = duration_line[0].split('=')[1]
                                print(f"    音频时长: {duration}秒")
                    else:
                        print("    ❌ 没有音频轨道")
                        
                except Exception as e:
                    print(f"    ⚠️ 无法检查音频轨道: {e}")
                
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查视频文件失败: {e}")
        return False

async def test_tts_directly():
    """直接测试TTS功能"""
    try:
        print("🧪 直接测试TTS功能...")
        
        from app.services.tts_service import TTSService
        
        tts_service = TTSService()
        print("✅ TTS服务初始化成功")
        
        # 测试简单的TTS合成
        test_text = "这是一个测试，验证TTS功能是否正常工作。"
        voice = "zh-CN-XiaoxiaoNeural"
        
        print(f"测试文本: {test_text}")
        print(f"使用语音: {voice}")
        
        result = await tts_service.synthesize_speech(
            test_text,
            voice,
            1.0,
            0,
            "edge-tts"
        )
        
        print(f"\nTTS结果:")
        print(f"  音频文件: {result['audio_file_path']}")
        print(f"  时长: {result['duration']:.2f}秒")
        print(f"  是否回退: {result.get('fallback', False)}")
        
        if result.get('fallback'):
            print(f"  ⚠️ 使用了回退方案: {result.get('message', 'Unknown')}")
            return False
        else:
            # 检查生成的文件
            audio_file = Path(result['audio_file_path'])
            if audio_file.exists() and audio_file.stat().st_size > 0:
                print(f"  ✅ TTS文件生成成功: {audio_file.stat().st_size} 字节")
                return True
            else:
                print(f"  ❌ TTS文件生成失败或为空")
                return False
        
    except Exception as e:
        print(f"❌ 直接TTS测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🔍 开始诊断TTS音频问题...")
    
    # 1. 检查音频文件
    audio_ok = await check_audio_files()
    
    # 2. 检查TTS文件
    tts_ok = await check_tts_files()
    
    # 3. 检查视频文件
    video_ok = await check_video_files()
    
    # 4. 直接测试TTS
    direct_tts_ok = await test_tts_directly()
    
    print(f"\n📊 诊断结果总结:")
    print(f"  音频文件检查: {'✅ 正常' if audio_ok else '❌ 异常'}")
    print(f"  TTS文件检查: {'✅ 正常' if tts_ok else '❌ 异常'}")
    print(f"  视频文件检查: {'✅ 正常' if video_ok else '❌ 异常'}")
    print(f"  直接TTS测试: {'✅ 成功' if direct_tts_ok else '❌ 失败'}")
    
    print(f"\n🎯 问题分析:")
    
    if not direct_tts_ok:
        print("❌ TTS服务本身有问题")
        print("  - 检查Edge TTS网络连接")
        print("  - 运行TTS诊断功能")
        print("  - 检查TTS服务配置")
    elif not tts_ok:
        print("❌ 完整流程中TTS没有正确生成音频")
        print("  - 检查完整流程的TTS调用")
        print("  - 查看完整流程的日志")
        print("  - 检查TTS重试机制")
    elif not video_ok:
        print("❌ 视频合成时没有正确使用TTS音频")
        print("  - 检查视频合成的音频输入")
        print("  - 确认使用了正确的音频文件")
        print("  - 检查FFmpeg命令")
    else:
        print("✅ 所有组件看起来都正常")
        print("  - 可能是时序问题")
        print("  - 检查完整流程的执行顺序")

if __name__ == "__main__":
    asyncio.run(main())
