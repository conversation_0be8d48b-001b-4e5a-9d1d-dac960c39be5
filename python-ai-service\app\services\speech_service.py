"""
Speech recognition service for video translation AI service
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Tuple
import whisper
import torch

from .base_service import BaseService
from app.models.schemas import SpeechRecognitionRequest


class SpeechService(BaseService):
    """Speech recognition service using Whisper"""
    
    def __init__(self):
        super().__init__()
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.logger.info(f"Using device: {self.device}")
    
    def _load_model(self, model_name: str = "base"):
        """Load Whisper model"""
        if self.model is None or self.model.name != model_name:
            self.logger.info(f"Loading Whisper model: {model_name}")
            self.model = whisper.load_model(model_name, device=self.device)
    
    async def recognize_speech_async(self, audio_path: str, language: str = "auto", 
                                   model: str = "base") -> str:
        """Create async task for speech recognition"""
        task_id = await self.create_task("speech_recognition", {
            "audio_path": audio_path,
            "language": language,
            "model": model
        })
        return task_id
    
    async def process_speech_recognition(self, task_id: str, request: SpeechRecognitionRequest):
        """Process speech recognition task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Recognize speech
            result = await self.recognize_speech(
                request.audio_file_path,
                request.language,
                request.model
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "text": result["text"],
                "language": result["language"],
                "confidence": result["confidence"],
                "segments": result["segments"]
            })
            
        except Exception as e:
            self.logger.error(f"Speech recognition failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def recognize_speech(self, audio_path: str, language: str = "auto", 
                             model: str = "base") -> Dict[str, Any]:
        """Recognize speech from audio file"""
        try:
            # Validate input file
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")
            
            # Load model
            self._load_model(model)
            
            self.logger.info(f"Recognizing speech from {audio_path}")
            
            # Perform speech recognition
            if language == "auto":
                result = self.model.transcribe(audio_path)
            else:
                result = self.model.transcribe(audio_path, language=language)
            
            # Process segments
            segments = []
            for segment in result["segments"]:
                segments.append({
                    "start": segment["start"],
                    "end": segment["end"],
                    "text": segment["text"].strip(),
                    "confidence": segment.get("avg_logprob", 0.0)
                })
            
            # Calculate overall confidence
            confidence = sum(seg.get("confidence", 0.0) for seg in segments) / len(segments) if segments else 0.0
            
            result_data = {
                "text": result["text"].strip(),
                "language": result["language"],
                "confidence": confidence,
                "segments": segments
            }
            
            self.logger.info(f"Speech recognition completed. Language: {result['language']}, "
                           f"Confidence: {confidence:.2f}, Segments: {len(segments)}")
            
            return result_data
            
        except Exception as e:
            self.logger.error(f"Speech recognition failed: {e}")
            raise
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process speech recognition task"""
        pass
    
    def get_available_models(self) -> List[str]:
        """Get available Whisper models"""
        return ["tiny", "base", "small", "medium", "large", "large-v2", "large-v3"]
    
    def get_supported_languages(self) -> List[str]:
        """Get supported languages"""
        return list(whisper.tokenizer.LANGUAGES.keys()) 