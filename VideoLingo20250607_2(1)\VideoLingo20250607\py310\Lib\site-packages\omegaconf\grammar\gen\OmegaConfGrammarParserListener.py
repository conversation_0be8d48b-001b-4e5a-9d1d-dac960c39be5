# Generated from /tmp/build-via-sdist-fm63w174/omegaconf-2.3.0/omegaconf/grammar/OmegaConfGrammarParser.g4 by ANTLR 4.9.3
from antlr4 import *
if __name__ is not None and "." in __name__:
    from .OmegaConfGrammarParser import OmegaConfGrammarParser
else:
    from OmegaConfGrammarParser import OmegaConfGrammarParser

# This class defines a complete listener for a parse tree produced by OmegaConfGrammarParser.
class OmegaConfGrammarParserListener(ParseTreeListener):

    # Enter a parse tree produced by OmegaConfGrammarParser#configValue.
    def enterConfigValue(self, ctx:OmegaConfGrammarParser.ConfigValueContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#configValue.
    def exitConfigValue(self, ctx:OmegaConfGrammarParser.ConfigValueContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#singleElement.
    def enterSingleElement(self, ctx:OmegaConfGrammarParser.SingleElementContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#singleElement.
    def exitSingleElement(self, ctx:OmegaConfGrammarParser.SingleElementContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#text.
    def enterText(self, ctx:OmegaConfGrammarParser.TextContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#text.
    def exitText(self, ctx:OmegaConfGrammarParser.TextContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#element.
    def enterElement(self, ctx:OmegaConfGrammarParser.ElementContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#element.
    def exitElement(self, ctx:OmegaConfGrammarParser.ElementContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#listContainer.
    def enterListContainer(self, ctx:OmegaConfGrammarParser.ListContainerContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#listContainer.
    def exitListContainer(self, ctx:OmegaConfGrammarParser.ListContainerContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#dictContainer.
    def enterDictContainer(self, ctx:OmegaConfGrammarParser.DictContainerContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#dictContainer.
    def exitDictContainer(self, ctx:OmegaConfGrammarParser.DictContainerContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#dictKeyValuePair.
    def enterDictKeyValuePair(self, ctx:OmegaConfGrammarParser.DictKeyValuePairContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#dictKeyValuePair.
    def exitDictKeyValuePair(self, ctx:OmegaConfGrammarParser.DictKeyValuePairContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#sequence.
    def enterSequence(self, ctx:OmegaConfGrammarParser.SequenceContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#sequence.
    def exitSequence(self, ctx:OmegaConfGrammarParser.SequenceContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#interpolation.
    def enterInterpolation(self, ctx:OmegaConfGrammarParser.InterpolationContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#interpolation.
    def exitInterpolation(self, ctx:OmegaConfGrammarParser.InterpolationContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#interpolationNode.
    def enterInterpolationNode(self, ctx:OmegaConfGrammarParser.InterpolationNodeContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#interpolationNode.
    def exitInterpolationNode(self, ctx:OmegaConfGrammarParser.InterpolationNodeContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#interpolationResolver.
    def enterInterpolationResolver(self, ctx:OmegaConfGrammarParser.InterpolationResolverContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#interpolationResolver.
    def exitInterpolationResolver(self, ctx:OmegaConfGrammarParser.InterpolationResolverContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#configKey.
    def enterConfigKey(self, ctx:OmegaConfGrammarParser.ConfigKeyContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#configKey.
    def exitConfigKey(self, ctx:OmegaConfGrammarParser.ConfigKeyContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#resolverName.
    def enterResolverName(self, ctx:OmegaConfGrammarParser.ResolverNameContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#resolverName.
    def exitResolverName(self, ctx:OmegaConfGrammarParser.ResolverNameContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#quotedValue.
    def enterQuotedValue(self, ctx:OmegaConfGrammarParser.QuotedValueContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#quotedValue.
    def exitQuotedValue(self, ctx:OmegaConfGrammarParser.QuotedValueContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#primitive.
    def enterPrimitive(self, ctx:OmegaConfGrammarParser.PrimitiveContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#primitive.
    def exitPrimitive(self, ctx:OmegaConfGrammarParser.PrimitiveContext):
        pass


    # Enter a parse tree produced by OmegaConfGrammarParser#dictKey.
    def enterDictKey(self, ctx:OmegaConfGrammarParser.DictKeyContext):
        pass

    # Exit a parse tree produced by OmegaConfGrammarParser#dictKey.
    def exitDictKey(self, ctx:OmegaConfGrammarParser.DictKeyContext):
        pass



del OmegaConfGrammarParser