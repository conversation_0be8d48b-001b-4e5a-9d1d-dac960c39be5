#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕分割工具
将长字幕按句子分割成更短、更易读的片段
"""

import re
import os

def parse_srt_time(time_str):
    """解析SRT时间格式到秒数"""
    time_parts = time_str.replace(',', '.').split(':')
    hours = int(time_parts[0])
    minutes = int(time_parts[1])
    seconds = float(time_parts[2])
    return hours * 3600 + minutes * 60 + seconds

def seconds_to_srt_time(seconds):
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def split_text_by_sentences(text, max_chars=50):
    """按句子分割文本，控制每段长度"""
    # 中文句子分割
    sentences = re.split(r'[。！？；]', text)
    
    segments = []
    current_segment = ""
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # 如果当前段落加上新句子不超过限制，就合并
        if len(current_segment + sentence) <= max_chars:
            if current_segment:
                current_segment += sentence + "。"
            else:
                current_segment = sentence + "。"
        else:
            # 如果当前段落不为空，先保存
            if current_segment:
                segments.append(current_segment.strip())
            
            # 如果单个句子太长，需要进一步分割
            if len(sentence) > max_chars:
                # 按逗号分割
                parts = re.split(r'[，,]', sentence)
                temp_part = ""
                for part in parts:
                    if len(temp_part + part) <= max_chars:
                        temp_part += part + "，"
                    else:
                        if temp_part:
                            segments.append(temp_part.strip())
                        temp_part = part + "，"
                if temp_part:
                    current_segment = temp_part.strip() + "。"
                else:
                    current_segment = ""
            else:
                current_segment = sentence + "。"
    
    # 添加最后一个段落
    if current_segment:
        segments.append(current_segment.strip())
    
    return segments

def split_subtitles(input_srt, output_srt, max_chars=40):
    """分割字幕文件"""
    print(f"🔄 分割字幕文件: {input_srt}")
    print(f"📝 最大字符数: {max_chars}")
    
    with open(input_srt, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 解析原始字幕
    blocks = content.split('\n\n')
    original_subs = []
    
    for block in blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            time_line = lines[1]
            text = '\n'.join(lines[2:])
            
            # 解析时间
            start_str, end_str = time_line.split(' --> ')
            start_time = parse_srt_time(start_str)
            end_time = parse_srt_time(end_str)
            
            original_subs.append({
                'index': index,
                'start': start_time,
                'end': end_time,
                'text': text,
                'duration': end_time - start_time
            })
    
    # 分割字幕
    new_subs = []
    new_index = 1
    
    for sub in original_subs:
        # 分割文本
        text_segments = split_text_by_sentences(sub['text'], max_chars)
        
        if len(text_segments) <= 1:
            # 不需要分割
            new_subs.append({
                'index': new_index,
                'start': sub['start'],
                'end': sub['end'],
                'text': sub['text']
            })
            new_index += 1
        else:
            # 需要分割，按时间平均分配
            segment_duration = sub['duration'] / len(text_segments)
            
            for i, segment_text in enumerate(text_segments):
                segment_start = sub['start'] + i * segment_duration
                segment_end = sub['start'] + (i + 1) * segment_duration
                
                new_subs.append({
                    'index': new_index,
                    'start': segment_start,
                    'end': segment_end,
                    'text': segment_text
                })
                new_index += 1
    
    # 写入新的SRT文件
    with open(output_srt, 'w', encoding='utf-8') as f:
        for sub in new_subs:
            f.write(f"{sub['index']}\n")
            f.write(f"{seconds_to_srt_time(sub['start'])} --> {seconds_to_srt_time(sub['end'])}\n")
            f.write(f"{sub['text']}\n\n")
    
    print(f"✅ 字幕分割完成!")
    print(f"📊 原始字幕: {len(original_subs)} 段")
    print(f"📊 分割后: {len(new_subs)} 段")
    print(f"💾 输出文件: {output_srt}")
    
    return len(new_subs)

def main():
    # 分割中文字幕
    chinese_input = "out/output_chinese.srt"  # 修改为相对路径
    chinese_output = "out/output_chinese_split.srt"  # 输出到out目录
    
    if os.path.exists(chinese_input):
        split_subtitles(chinese_input, chinese_output, max_chars=20)
    else:
        print(f"❌ 文件不存在: {chinese_input}")

if __name__ == "__main__":
    main() 