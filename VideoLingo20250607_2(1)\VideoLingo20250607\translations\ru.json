{"a. Download or Upload Video": "a. Скачать или загрузить видео", "Delete and Reselect": "Удалить и выбрать заново", "Enter YouTube link:": "Введите ссылку YouTube:", "Resolution": "Разрешение", "Download Video": "Скачать видео", "Or upload video": "Или загрузить видео", "Youtube Settings": "Настройки Youtube", "Cookies Path": "Путь к файлу Cookies", "LLM Configuration": "Настройка LLM", "API_KEY": "API-к<PERSON><PERSON><PERSON>", "BASE_URL": "Базовый URL", "MODEL": "Модель", "Openai format, will add /v1/chat/completions automatically": "Формат OpenAI, /v1/chat/completions добавится автоматически", "click to check API validity": "нажмите для проверки API", "API Key is valid": "API-ключ действителен", "API Key is invalid": "API-ключ недействителен", "Recog Lang": "Язык распознавания", "Subtitles Settings": "Настройки субтитров", "Target Lang": "Целевой язык", "Input any language in natural language, as long as llm can understand": "Введите любой язык на естественном языке, главное чтобы LLM мог понять", "Vocal separation enhance": "Улучшение отделения голоса", "Burn-in Subtitles": "Встроить субтитры", "Whether to burn subtitles into the video, will increase processing time": "Встраивать ли субтитры в видео, это увеличит время обработки", "Video Resolution": "Разрешение видео", "Recommended for videos with loud background noise, but will increase processing time": "Рекомендуется для видео с громким фоновым шумом, но увеличит время обработки", "Dubbing Settings": "Настройки дубляжа", "TTS Method": "Метод TTS", "SiliconFlow API Key": "API-ключ SiliconFlow", "Mode Selection": "Выбор режима", "Preset": "Пресет", "Refer_stable": "Стабильная ссылка", "Refer_dynamic": "Динамическая ссылка", "OpenAI Voice": "Голос OpenAI", "Fish TTS Character": "Персонаж Fish TTS", "Azure Voice": "Голос Azure", "Please refer to Github homepage for GPT_SoVITS configuration": "Обратитесь к домашней странице Github для настройки GPT_SoVITS", "SoVITS Character": "Персонаж SoVITS", "Refer Mode": "Режим ссылки", "Mode 1: Use provided reference audio only": "Режим 1: Использовать только предоставленное эталонное аудио", "Mode 2: Use first audio from video as reference": "Режим 2: Использовать первое аудио из видео как эталон", "Mode 3: Use each audio from video as reference": "Режим 3: Использовать каждое аудио из видео как эталон", "Configure reference audio mode for GPT-SoVITS": "Настройка режима эталонного аудио для GPT-SoVITS", "Edge TTS Voice": "Голос Edge TTS", "=====NOTE=====": "Содержимое st.py ниже", "=====NOTE2=====": "Ниже содержится в install.py", "🚀 Starting Installation": "🚀 Начало установки", "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)": "Нужно ли автоматически настроить зеркала PyPI? (Рекомендуется при проблемах с доступом к pypi.org)", "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...": "🎮 Обнаружен GPU NVIDIA, установка CUDA версии PyTorch...", "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 Обнар<PERSON><PERSON><PERSON>на MacOS, установка CPU версии PyTorch... Примечание: транскрипция whisperX может быть медленной.", "💻 No NVIDIA GPU detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "💻 GPU NVIDIA не обнаружен, установка CPU версии PyTorch... Примечание: транскрипция whisperX может быть медленной.", "❌ Failed to install requirements:": "❌ Не удалось установить зависимости:", "✅ FFmpeg is already installed": "✅ FFmpeg уже установлен", "❌ FFmpeg not found\n\n": "❌ FFmpeg не найден\n\n", "🛠️ Install using:": "🛠️ Установить используя:", "💡 Note:": "💡 Примечание:", "🔄 After installing FFmpeg, please run this installer again:": "🔄 После установки FFmpeg, пожалуйста, запустите установщик снова:", "Install Chocolatey first (https://chocolatey.org/)": "Сначала установите Chocolatey (https://chocolatey.org/)", "Install Homebrew first (https://brew.sh/)": "Сначала установите Homebrew (https://brew.sh/)", "Use your distribution's package manager": "Используйте менеджер пакетов вашего дистрибутива", "FFmpeg is required. Please install it and run the installer again.": "Требуется FFmpeg. Пожалуйста, установите его и запустите установщик снова.", "Installation completed": "Установка завершена", "Now I will run this command to start the application:": "Сейчас я запущу эту команду для запуска приложения:", "Note: First startup may take up to 1 minute": "Примечание: Первый запуск может занять до 1 минуты", "If the application fails to start:": "Если приложение не запускается:", "Check your network connection": "Проверьте подключение к сети", "Re-run the installer: [bold]python install.py[/bold]": "Перезапустите установщик: [bold]python install.py[/bold]", "Installing requirements using `pip install -r requirements.txt`": "Установка зависимостей с помощью `pip install -r requirements.txt`", "b. Translate and Generate Subtitles": "b. Перевести и создать субтитры", "This stage includes the following steps:": "Этот этап включает следующие шаги:", "WhisperX word-level transcription": "Пословная транскрипция WhisperX", "Sentence segmentation using NLP and LLM": "Сегментация предложений с помощью NLP и LLM", "Summarization and multi-step translation": "Обобщение и многоэтапный перевод", "Cutting and aligning long subtitles": "Разделение и выравнивание длинных субтитров", "Generating timeline and subtitles": "Создание таймлайна и субтитров", "Merging subtitles into the video": "Объединение субтитров с видео", "Start Processing Subtitles": "Начать обработку субтитров", "Download All Srt Files": "Скачать все Srt файлы", "Archive to 'history'": "Архивировать в 'history'", "Using Whisper for transcription...": "Используется Whisper для транскрипции...", "Splitting long sentences...": "Разделение длинных предложений...", "Summarizing and translating...": "Обобщение и перевод...", "Processing and aligning subtitles...": "Обработка и выравнивание субтитров...", "Merging subtitles to video...": "Объединение субтитров с видео...", "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...": "⚠️ ПАУЗА_ПЕРЕД_ПЕРЕВОДОМ. Перейдите в `output/log/terminology.json` для редактирования терминологии. Затем нажмите ENTER для продолжения...", "Subtitle processing complete! 🎉": "Обработка субтитров завершена! 🎉", "c. Dubbing": "c. <PERSON><PERSON>", "Generate audio tasks and chunks": "Создание аудио задач и фрагментов", "Extract reference audio": "Извлечение эталонного аудио", "Generate and merge audio files": "Создание и объединение аудио файлов", "Merge final audio into video": "Объединение финального аудио с видео", "Start Audio Processing": "Начать обработку аудио", "Audio processing is complete! You can check the audio files in the `output` folder.": "Обработка аудио завершена! Вы можете проверить аудио файлы в папке `output`.", "Delete dubbing files": "Удалить файлы дубляжа", "Generate audio tasks": "Создать аудио задачи", "Extract refer audio": "Извлечь эталонное аудио", "Generate all audio": "Создать все аудио", "Merge full audio": "Объединить полное аудио", "Merge dubbing to the video": "Объединить дубляж с видео", "Audio processing complete! 🎇": "Обработка аудио завершена! 🎇", "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!": "Здравствуйте, добро пожаловать в VideoLingo. Если у вас возникнут вопросы, вы можете получить мгновенные ответы с помощью нашего бесплатного QA-агента <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">здесь</a>! Вы также можете бесплатно попробовать наш SaaS-сайт <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a>!", "WhisperX Runtime": "Среда выполнения WhisperX", "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key": "Локальная среда требует GPU >8ГБ, облачная среда требует API-ключ 302ai, elevenlabs среда требует API-ключ ElevenLabs", "WhisperX 302ai API": "API 302ai WhisperX", "Detected NVIDIA GPU(s)": "Об<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(ы) GPU NVIDIA", "No NVIDIA GPU detected": "GPU NVIDIA не обнаружен", "No NVIDIA GPU detected or NVIDIA drivers not properly installed": "GPU NVIDIA не обнаружен или драйверы NVIDIA установлены неправильно", "LLM JSON Format Support": "Поддержка формата JSON для LLM", "Enable if your LLM supports JSON mode output": "Включите, если ваш LLM поддерживает вывод в формате JSON"}