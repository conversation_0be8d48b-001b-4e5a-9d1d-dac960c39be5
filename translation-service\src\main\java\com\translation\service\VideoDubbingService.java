package com.translation.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;

@Service
public class VideoDubbingService {

    private static final Logger logger = LoggerFactory.getLogger(VideoDubbingService.class);

    @Autowired
    private TranslationManager translationManager;

    @Autowired
    private BaiduTTSService baiduTTSService;

    @Autowired
    private EdgeTTSService edgeTTSService;

    @Autowired
    private AudioVideoMerger audioVideoMerger;

    // 句子分割的正则表达式
    private static final Pattern SENTENCE_PATTERN = Pattern.compile(
            "[.!?。！？]+\\s*|[\\n\\r]+",
            Pattern.MULTILINE
    );

    /**
     * 视频配音请求
     */
    public static class DubbingRequest {
        private String videoPath;
        private String text;
        private String sourceLanguage;
        private String targetLanguage;
        private String ttsProvider; // "baidu" 或 "edge"
        private String voice;
        private String outputPath;
        private boolean preserveOriginalAudio;

        // Getters and Setters
        public String getVideoPath() { return videoPath; }
        public void setVideoPath(String videoPath) { this.videoPath = videoPath; }
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public String getSourceLanguage() { return sourceLanguage; }
        public void setSourceLanguage(String sourceLanguage) { this.sourceLanguage = sourceLanguage; }
        public String getTargetLanguage() { return targetLanguage; }
        public void setTargetLanguage(String targetLanguage) { this.targetLanguage = targetLanguage; }
        public String getTtsProvider() { return ttsProvider; }
        public void setTtsProvider(String ttsProvider) { this.ttsProvider = ttsProvider; }
        public String getVoice() { return voice; }
        public void setVoice(String voice) { this.voice = voice; }
        public String getOutputPath() { return outputPath; }
        public void setOutputPath(String outputPath) { this.outputPath = outputPath; }
        public boolean isPreserveOriginalAudio() { return preserveOriginalAudio; }
        public void setPreserveOriginalAudio(boolean preserveOriginalAudio) { this.preserveOriginalAudio = preserveOriginalAudio; }
    }

    /**
     * 视频配音响应
     */
    public static class DubbingResponse {
        private boolean success;
        private String message;
        private String outputVideoPath;
        private String translatedText;
        private List<String> audioSegments;
        private double totalDuration;

        public DubbingResponse(boolean success, String message) {
            this.success = success;
            this.message = message;
            this.audioSegments = new ArrayList<>();
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getOutputVideoPath() { return outputVideoPath; }
        public void setOutputVideoPath(String outputVideoPath) { this.outputVideoPath = outputVideoPath; }
        public String getTranslatedText() { return translatedText; }
        public void setTranslatedText(String translatedText) { this.translatedText = translatedText; }
        public List<String> getAudioSegments() { return audioSegments; }
        public void setAudioSegments(List<String> audioSegments) { this.audioSegments = audioSegments; }
        public double getTotalDuration() { return totalDuration; }
        public void setTotalDuration(double totalDuration) { this.totalDuration = totalDuration; }
    }

    /**
     * 执行视频配音
     *
     * @param request 配音请求
     * @return 配音响应
     */
    public DubbingResponse processVideoDubbing(DubbingRequest request) {
        try {
            logger.info("开始处理视频配音: {}", request.getVideoPath());

            // 1. 验证输入
            if (!validateRequest(request)) {
                return new DubbingResponse(false, "请求参数验证失败");
            }

            // 2. 翻译文本
            String translatedText = translateText(request.getText(), request.getSourceLanguage(), request.getTargetLanguage());
            if (translatedText == null) {
                return new DubbingResponse(false, "文本翻译失败");
            }

            // 3. 分割文本为句子
            List<String> sentences = splitTextIntoSentences(translatedText);
            logger.info("文本分割为 {} 个句子", sentences.size());

            // 4. 为每个句子生成语音
            List<String> audioFiles = new ArrayList<>();
            for (int i = 0; i < sentences.size(); i++) {
                String sentence = sentences.get(i).trim();
                if (sentence.isEmpty()) continue;

                String audioPath = generateAudioForSentence(sentence, request, i);
                if (audioPath != null) {
                    audioFiles.add(audioPath);
                }
            }

            if (audioFiles.isEmpty()) {
                return new DubbingResponse(false, "没有生成任何音频文件");
            }

            // 5. 合并音频文件
            String mergedAudioPath = mergeAudioFiles(audioFiles, request.getOutputPath());
            if (mergedAudioPath == null) {
                return new DubbingResponse(false, "音频合并失败");
            }

            // 6. 与视频合并
            String finalVideoPath = mergeWithVideo(request.getVideoPath(), mergedAudioPath, request.getOutputPath());
            if (finalVideoPath == null) {
                return new DubbingResponse(false, "视频合并失败");
            }

            // 7. 清理临时文件
            cleanupTempFiles(audioFiles, mergedAudioPath);

            // 8. 构建响应
            DubbingResponse response = new DubbingResponse(true, "视频配音完成");
            response.setOutputVideoPath(finalVideoPath);
            response.setTranslatedText(translatedText);
            response.setAudioSegments(audioFiles);

            try {
                response.setTotalDuration(audioVideoMerger.getAudioDuration(mergedAudioPath));
            } catch (Exception e) {
                logger.warn("获取音频时长失败: {}", e.getMessage());
            }

            logger.info("视频配音完成: {}", finalVideoPath);
            return response;

        } catch (Exception e) {
            logger.error("视频配音处理失败", e);
            return new DubbingResponse(false, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证请求参数
     */
    private boolean validateRequest(DubbingRequest request) {
        if (request.getVideoPath() == null || request.getVideoPath().trim().isEmpty()) {
            logger.error("视频路径不能为空");
            return false;
        }

        File videoFile = new File(request.getVideoPath());
        if (!videoFile.exists()) {
            logger.error("视频文件不存在: {}", request.getVideoPath());
            return false;
        }

        if (request.getText() == null || request.getText().trim().isEmpty()) {
            logger.error("文本内容不能为空");
            return false;
        }

        if (request.getTargetLanguage() == null || request.getTargetLanguage().trim().isEmpty()) {
            logger.error("目标语言不能为空");
            return false;
        }

        if (request.getOutputPath() == null || request.getOutputPath().trim().isEmpty()) {
            logger.error("输出路径不能为空");
            return false;
        }

        return true;
    }

    /**
     * 翻译文本
     */
    private String translateText(String text, String sourceLanguage, String targetLanguage) {
        try {
            if (sourceLanguage != null && sourceLanguage.equals(targetLanguage)) {
                return text; // 源语言和目标语言相同，不需要翻译
            }

            return translationManager.translateText(text, sourceLanguage, targetLanguage);
        } catch (Exception e) {
            logger.error("翻译失败", e);
            return null;
        }
    }

    /**
     * 将文本分割为句子
     */
    private List<String> splitTextIntoSentences(String text) {
        List<String> sentences = new ArrayList<>();
        String[] parts = SENTENCE_PATTERN.split(text);
        
        for (String part : parts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                // 限制每个句子的长度，避免TTS处理过长文本
                if (trimmed.length() > 200) {
                    // 进一步分割长句子
                    List<String> subSentences = splitLongSentence(trimmed);
                    sentences.addAll(subSentences);
                } else {
                    sentences.add(trimmed);
                }
            }
        }
        
        return sentences;
    }

    /**
     * 分割过长的句子
     */
    private List<String> splitLongSentence(String sentence) {
        List<String> parts = new ArrayList<>();
        String[] words = sentence.split("[，,；;]");
        
        StringBuilder current = new StringBuilder();
        for (String word : words) {
            if (current.length() + word.length() > 200) {
                if (current.length() > 0) {
                    parts.add(current.toString().trim());
                    current = new StringBuilder();
                }
            }
            if (current.length() > 0) {
                current.append("，");
            }
            current.append(word);
        }
        
        if (current.length() > 0) {
            parts.add(current.toString().trim());
        }
        
        return parts;
    }

    /**
     * 为单个句子生成音频
     */
    private String generateAudioForSentence(String sentence, DubbingRequest request, int index) {
        try {
            String tempDir = createTempDirectory();
            String audioFileName = String.format("audio_segment_%03d.mp3", index);
            String audioPath = Paths.get(tempDir, audioFileName).toString();

            boolean success = false;
            String ttsProvider = request.getTtsProvider();
            
            if ("edge".equalsIgnoreCase(ttsProvider)) {
                success = generateWithEdgeTTS(sentence, request, audioPath);
            } else if ("baidu".equalsIgnoreCase(ttsProvider)) {
                success = generateWithBaiduTTS(sentence, request, audioPath);
            } else {
                // 默认尝试Edge TTS，失败后尝试百度TTS
                success = generateWithEdgeTTS(sentence, request, audioPath);
                if (!success) {
                    logger.warn("Edge TTS失败，尝试百度TTS");
                    success = generateWithBaiduTTS(sentence, request, audioPath);
                }
            }

            return success ? audioPath : null;

        } catch (Exception e) {
            logger.error("生成音频失败: {}", sentence, e);
            return null;
        }
    }

    /**
     * 使用Edge TTS生成音频
     */
    private boolean generateWithEdgeTTS(String text, DubbingRequest request, String outputPath) {
        try {
            if (!edgeTTSService.isServiceAvailable()) {
                logger.warn("Edge TTS服务不可用");
                return false;
            }

            String voice = request.getVoice();
            if (voice == null || voice.trim().isEmpty()) {
                Map<String, String> recommendedVoice = edgeTTSService.getRecommendedVoice(request.getTargetLanguage());
                voice = recommendedVoice.get("voice");
            }

            File audioFile = edgeTTSService.synthesizeText(text, voice, "+0%", outputPath);
            return audioFile != null && audioFile.exists();

        } catch (Exception e) {
            logger.error("Edge TTS生成失败", e);
            return false;
        }
    }

    /**
     * 使用百度TTS生成音频
     */
    private boolean generateWithBaiduTTS(String text, DubbingRequest request, String outputPath) {
        try {
            if (!baiduTTSService.isServiceAvailable()) {
                logger.warn("百度TTS服务不可用");
                return false;
            }

            File audioFile = baiduTTSService.synthesizeText(text, request.getTargetLanguage(), outputPath);
            return audioFile != null && audioFile.exists();

        } catch (Exception e) {
            logger.error("百度TTS生成失败", e);
            return false;
        }
    }

    /**
     * 合并音频文件
     */
    private String mergeAudioFiles(List<String> audioFiles, String outputBasePath) {
        try {
            String mergedAudioPath = outputBasePath.replace(".mp4", "_merged_audio.mp3");
            File mergedFile = audioVideoMerger.mergeAudioFiles(audioFiles, mergedAudioPath);
            return mergedFile != null ? mergedFile.getAbsolutePath() : null;

        } catch (Exception e) {
            logger.error("合并音频文件失败", e);
            return null;
        }
    }

    /**
     * 与视频合并
     */
    private String mergeWithVideo(String videoPath, String audioPath, String outputPath) {
        try {
            File finalVideo = audioVideoMerger.mergeAudioWithVideo(videoPath, audioPath, outputPath);
            return finalVideo != null ? finalVideo.getAbsolutePath() : null;

        } catch (Exception e) {
            logger.error("视频合并失败", e);
            return null;
        }
    }

    /**
     * 创建临时目录
     */
    private String createTempDirectory() throws IOException {
        Path tempDir = Files.createTempDirectory("video_dubbing_");
        return tempDir.toString();
    }

    /**
     * 清理临时文件 - 暂时注释掉以保留文件用于调试
     */
    private void cleanupTempFiles(List<String> audioFiles, String mergedAudioPath) {
        // 清理音频片段文件 - 暂时注释掉
        // for (String audioFile : audioFiles) {
        //     try {
        //         Files.deleteIfExists(Paths.get(audioFile));
        //     } catch (IOException e) {
        //         logger.warn("删除临时音频文件失败: {}", audioFile, e);
        //     }
        // }
        logger.info("保留音频片段文件用于调试: {}", audioFiles);

        // 清理合并的音频文件 - 暂时注释掉
        // if (mergedAudioPath != null) {
        //     try {
        //         Files.deleteIfExists(Paths.get(mergedAudioPath));
        //     } catch (IOException e) {
        //         logger.warn("删除合并音频文件失败: {}", mergedAudioPath, e);
        //     }
        // }
        if (mergedAudioPath != null) {
            logger.info("保留合并音频文件用于调试: {}", mergedAudioPath);
        }
    }

    /**
     * 获取支持的TTS提供商
     */
    public List<String> getSupportedTTSProviders() {
        List<String> providers = new ArrayList<>();
        
        if (edgeTTSService.isServiceAvailable()) {
            providers.add("edge");
        }
        
        if (baiduTTSService.isServiceAvailable()) {
            providers.add("baidu");
        }
        
        return providers;
    }

    /**
     * 获取可用的语音列表
     */
    public Map<String, Object> getAvailableVoices(String provider) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if ("edge".equalsIgnoreCase(provider)) {
                if (edgeTTSService.isServiceAvailable()) {
                    result.put("voices", edgeTTSService.getAvailableVoices());
                    result.put("success", true);
                } else {
                    result.put("success", false);
                    result.put("message", "Edge TTS服务不可用");
                }
            } else if ("baidu".equalsIgnoreCase(provider)) {
                if (baiduTTSService.isServiceAvailable()) {
                    // 百度TTS的语音列表是固定的
                    List<Map<String, String>> baiduVoices = new ArrayList<>();
                    Map<String, String> voice1 = new HashMap<>();
                    voice1.put("name", "度小美");
                    voice1.put("voice", "0");
                    voice1.put("language", "zh");
                    baiduVoices.add(voice1);
                    
                    Map<String, String> voice2 = new HashMap<>();
                    voice2.put("name", "度小宇");
                    voice2.put("voice", "1");
                    voice2.put("language", "zh");
                    baiduVoices.add(voice2);
                    
                    result.put("voices", baiduVoices);
                    result.put("success", true);
                } else {
                    result.put("success", false);
                    result.put("message", "百度TTS服务不可用");
                }
            } else {
                result.put("success", false);
                result.put("message", "不支持的TTS提供商: " + provider);
            }
        } catch (Exception e) {
            logger.error("获取语音列表失败", e);
            result.put("success", false);
            result.put("message", "获取语音列表失败: " + e.getMessage());
        }
        
        return result;
    }
}