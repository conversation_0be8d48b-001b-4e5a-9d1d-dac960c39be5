#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的中文配音视频合成器
"""

import os
import re
from moviepy import VideoFileClip, AudioFileClip
from pydub import AudioSegment

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def create_simple_chinese_video():
    """创建简化的中文配音视频"""
    
    # 文件路径
    video_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/input/test_30s.mp4"
    srt_path = "output_chinese_split.srt"
    audio_dir = "out/audio"
    output_path = "chinese_video_simple.mp4"
    
    print("🎬 开始创建简化的中文配音视频...")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    try:
        # 读取字幕文件
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 分割字幕块
        subtitle_blocks = re.split(r'\n\s*\n', content)
        subtitles = []
        
        for block in subtitle_blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 解析时间
                start_time_str, end_time_str = time_line.split(' --> ')
                start_time = parse_srt_time(start_time_str)
                end_time = parse_srt_time(end_time_str)
                
                subtitles.append((index, start_time, end_time, text.strip()))
        
        print(f"📝 读取到 {len(subtitles)} 条字幕")
        
        # 加载原视频
        print("📹 加载原视频...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        print(f"📊 原视频时长: {original_duration:.2f}s")
        
        # 创建静音背景音轨
        print("🎵 创建合成音频...")
        composite_audio = AudioSegment.silent(duration=int(original_duration * 1000))
        
        # 处理每个字幕的音频
        for i, (index, start_time, end_time, text) in enumerate(subtitles):
            audio_file = os.path.join(audio_dir, f"subtitle_{index:03d}.wav")
            
            if os.path.exists(audio_file):
                try:
                    # 加载TTS音频
                    tts_audio = AudioSegment.from_wav(audio_file)
                    tts_duration = len(tts_audio) / 1000.0
                    
                    # 计算字幕时间段长度
                    subtitle_duration = end_time - start_time
                    
                    print(f"🔄 [{i+1}/{len(subtitles)}] 处理: {text[:30]}... (时长: {tts_duration:.2f}s)")
                    
                    # 如果TTS音频比字幕时间段长，需要加速
                    if tts_duration > subtitle_duration:
                        speed_factor = tts_duration / subtitle_duration
                        tts_audio = tts_audio.speedup(playback_speed=speed_factor)
                        print(f"  🔄 音频加速 {speed_factor:.2f}x")
                    
                    # 将TTS音频插入到指定时间位置
                    start_ms = int(start_time * 1000)
                    
                    # 确保不超出总时长
                    if start_ms + len(tts_audio) > len(composite_audio):
                        tts_audio = tts_audio[:len(composite_audio) - start_ms]
                    
                    # 叠加音频
                    composite_audio = composite_audio.overlay(tts_audio, position=start_ms)
                    
                except Exception as e:
                    print(f"❌ 处理音频失败 {audio_file}: {str(e)}")
                    continue
            else:
                print(f"⚠️  音频文件不存在: {audio_file}")
        
        # 将AudioSegment转换为临时文件
        temp_audio_path = "temp_simple_audio.wav"
        print("💾 导出合成音频...")
        composite_audio.export(temp_audio_path, format="wav")
        
        # 加载合成音频
        print("🎧 加载合成音频到MoviePy...")
        new_audio = AudioFileClip(temp_audio_path)
        
        # 设置视频的新音轨
        print("🎬 合成最终视频...")
        final_video = video.set_audio(new_audio)
        
        # 输出视频
        print(f"💾 保存视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            verbose=False,
            logger=None
        )
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        
        # 释放资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"🎉 中文配音视频创建成功！")
        print(f"📁 输出文件: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建视频失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_simple_chinese_video()
    if success:
        print("✅ 任务完成！")
    else:
        print("❌ 任务失败！") 