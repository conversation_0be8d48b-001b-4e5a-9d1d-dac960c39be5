#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频质量优化器
解决声音忽高忽低、不清晰的问题
"""

import os
import re
import time
from moviepy import VideoFileClip, AudioFileClip
from pydub import AudioSegment
from pydub.effects import normalize, compress_dynamic_range
from pydub.utils import db_to_float

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                # 尝试解析第一行为数字索引
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 检查时间行格式
                if ' --> ' in time_line:
                    # 解析时间
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
            except ValueError:
                continue
    
    return subtitles

def optimize_audio_segment(audio_segment: AudioSegment, target_db: float = -20.0) -> AudioSegment:
    """
    优化单个音频片段的质量
    
    Args:
        audio_segment: 输入音频片段
        target_db: 目标音量（dB）
        
    Returns:
        优化后的音频片段
    """
    try:
        # 1. 标准化音量到目标dB
        normalized_audio = normalize(audio_segment, headroom=abs(target_db))
        
        # 2. 动态范围压缩 - 减少音量差异
        compressed_audio = compress_dynamic_range(
            normalized_audio,
            threshold=-20.0,    # 压缩阈值
            ratio=4.0,          # 压缩比例
            attack=5.0,         # 攻击时间(ms)
            release=50.0        # 释放时间(ms)
        )
        
        # 3. 音频增强 - 提高清晰度
        # 高频增强
        enhanced_audio = compressed_audio.high_pass_filter(100)  # 去除低频噪音
        enhanced_audio = enhanced_audio.low_pass_filter(8000)    # 去除高频噪音
        
        # 4. 最终音量调整
        final_audio = enhanced_audio.apply_gain(target_db - enhanced_audio.dBFS)
        
        return final_audio
        
    except Exception as e:
        print(f"⚠️  音频优化失败，使用原始音频: {str(e)}")
        return audio_segment

def create_optimized_video():
    """创建音频质量优化的视频"""
    
    # 文件路径
    video_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/input.mp4"
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese_split.srt"
    audio_dir = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/audio"
    output_path = "chinese_video_optimized.mp4"
    
    print("🎬 开始创建音频质量优化的视频...")
    print("🔧 优化功能：")
    print("   • 音频标准化 - 统一音量")
    print("   • 动态范围压缩 - 减少音量差异")
    print("   • 音频增强 - 提高清晰度")
    print("   • 噪音过滤 - 去除杂音")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(srt_path):
        print(f"❌ 字幕文件不存在: {srt_path}")
        return False
        
    if not os.path.exists(audio_dir):
        print(f"❌ 音频目录不存在: {audio_dir}")
        return False
    
    try:
        # 获取所有音频文件并排序
        audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
        audio_files.sort()
        print(f"🎵 找到 {len(audio_files)} 个音频文件")
        
        # 读取分片字幕文件
        subtitles = read_srt_file(srt_path)
        print(f"📝 读取到 {len(subtitles)} 条分片字幕")
        
        # 加载视频
        print("📹 加载原视频...")
        video = VideoFileClip(video_path)
        original_duration = video.duration
        print(f"📊 原视频时长: {original_duration/60:.1f}分钟 ({original_duration:.2f}秒)")
        
        # 创建静音背景音轨
        print("🎵 创建合成音频...")
        composite_audio = AudioSegment.silent(duration=int(original_duration * 1000))
        
        # 处理每个字幕的音频 - 一对一映射
        successful_audio_count = 0
        skipped_count = 0
        max_process = min(len(subtitles), len(audio_files))
        
        print(f"🔄 开始优化并合成 {max_process} 个音频片段...")
        
        # 设置目标音量（dB）
        target_db = -18.0  # 统一的目标音量
        
        for i in range(max_process):
            index, start_time_sub, end_time_sub, text = subtitles[i]
            audio_file = os.path.join(audio_dir, audio_files[i])
            
            try:
                # 加载TTS音频（MP3格式但扩展名为wav）
                try:
                    tts_audio = AudioSegment.from_wav(audio_file)
                except Exception:
                    tts_audio = AudioSegment.from_mp3(audio_file)
                
                # 优化音频质量
                optimized_audio = optimize_audio_segment(tts_audio, target_db)
                
                tts_duration = len(optimized_audio) / 1000.0
                subtitle_duration = end_time_sub - start_time_sub
                
                if (i + 1) % 100 == 0:  # 每100个显示进度
                    print(f"🔄 [{i+1}/{max_process}] 处理进度: {((i+1)/max_process*100):.1f}%")
                    print(f"    片段{index}({subtitle_duration:.2f}s) -> {audio_files[i]}({tts_duration:.2f}s)")
                    print(f"    音量: {optimized_audio.dBFS:.1f}dB -> {target_db:.1f}dB")
                
                # 轻微的时长调整（仅在差异很大时）
                if abs(tts_duration - subtitle_duration) > subtitle_duration * 0.4:
                    if tts_duration > subtitle_duration:
                        speed_factor = min(tts_duration / subtitle_duration, 1.8)
                        optimized_audio = optimized_audio.speedup(playback_speed=speed_factor)
                    elif tts_duration < subtitle_duration * 0.5:
                        speed_factor = max(tts_duration / (subtitle_duration * 0.8), 0.7)
                        optimized_audio = optimized_audio.speedup(playback_speed=speed_factor)
                
                # 添加淡入淡出效果，减少突变
                fade_duration = min(50, len(optimized_audio) // 4)  # 最多50ms淡入淡出
                optimized_audio = optimized_audio.fade_in(fade_duration).fade_out(fade_duration)
                
                # 将优化后的音频插入到指定时间位置
                start_ms = int(start_time_sub * 1000)
                
                # 确保不超出总时长
                if start_ms + len(optimized_audio) > len(composite_audio):
                    optimized_audio = optimized_audio[:len(composite_audio) - start_ms]
                
                # 叠加音频
                composite_audio = composite_audio.overlay(optimized_audio, position=start_ms)
                successful_audio_count += 1
                
            except Exception as e:
                print(f"❌ 处理音频失败 {audio_files[i]}: {str(e)}")
                skipped_count += 1
                continue
        
        print(f"✅ 成功处理: {successful_audio_count}/{max_process} 个音频文件")
        print(f"⚠️  跳过文件: {skipped_count} 个")
        
        # 对整体音频进行最终优化
        print("🎵 对整体音频进行最终优化...")
        final_composite_audio = optimize_audio_segment(composite_audio, target_db)
        
        # 将AudioSegment转换为临时文件
        temp_audio_path = "temp_optimized_audio.wav"
        print("💾 导出优化后的合成音频...")
        final_composite_audio.export(temp_audio_path, format="wav")
        print(f"✅ 合成音频导出完成，大小: {os.path.getsize(temp_audio_path)/(1024*1024):.2f} MB")
        
        # 加载合成音频
        print("🎧 加载优化后的音频到MoviePy...")
        new_audio = AudioFileClip(temp_audio_path)
        
        # 设置视频的新音轨
        print("🎬 合成最终优化视频...")
        final_video = video.with_audio(new_audio)
        
        # 输出视频
        print(f"💾 保存优化视频到: {output_path}")
        start_time = time.time()
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            bitrate='2500k',        # 提高视频质量
            audio_bitrate='160k'    # 提高音频质量
        )
        
        processing_time = time.time() - start_time
        print(f"⏱️  视频处理完成，耗时: {processing_time/60:.1f}分钟")
        
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
            print("🗑️  临时文件已清理")
        
        # 释放资源
        video.close()
        new_audio.close()
        final_video.close()
        
        print(f"🎉 音频质量优化视频创建成功！")
        print(f"📁 输出文件: {output_path}")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"📊 输出文件大小: {file_size:.2f} MB")
            print("🔧 优化效果:")
            print("   ✅ 音量标准化 - 统一音量水平")
            print("   ✅ 动态范围压缩 - 减少音量差异")
            print("   ✅ 音频增强 - 提高清晰度")
            print("   ✅ 淡入淡出 - 减少突变")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 音频质量优化器")
    print("🔧 专门解决声音忽高忽低、不清晰的问题")
    
    success = create_optimized_video()
    
    if success:
        print("✅ 任务完成！")
        print("🎧 请测试新视频的音频质量是否有改善")
        print("📝 优化内容：")
        print("   • 音量标准化：统一所有音频片段的音量")
        print("   • 动态范围压缩：减少音量差异")
        print("   • 音频增强：提高清晰度")
        print("   • 噪音过滤：去除杂音")
        print("   • 淡入淡出：减少音频突变")
    else:
        print("❌ 任务失败！")

if __name__ == "__main__":
    main() 