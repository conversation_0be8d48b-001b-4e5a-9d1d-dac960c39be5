#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import psutil
import json
from datetime import datetime

def monitor_progress():
    """监控处理进展"""
    print("📊 开始监控音频处理进展...")
    
    start_time = time.time()
    out_dir = "out"
    
    # 检查主音频文件
    audio_file = os.path.join(out_dir, "temp_audio.wav")
    if os.path.exists(audio_file):
        audio_size = os.path.getsize(audio_file) / (1024*1024)
        print(f"🎵 主音频文件: {audio_size:.1f}MB")
    
    print("\n⏰ 实时监控中... (按Ctrl+C停止)")
    print("=" * 50)
    
    last_file_count = 0
    
    try:
        while True:
            current_time = datetime.now().strftime("%H:%M:%S")
            elapsed = time.time() - start_time
            
            # 检查输出文件
            files = []
            if os.path.exists(out_dir):
                for f in os.listdir(out_dir):
                    if f.endswith(('.srt', '.json')):
                        files.append(f)
            
            # 检查Python进程
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                try:
                    if proc.info['name'] == 'python.exe':
                        memory_mb = proc.info['memory_info'].rss / (1024*1024)
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'memory': memory_mb,
                            'cpu': proc.info['cpu_percent']
                        })
                except:
                    continue
            
            # 清屏并显示状态
            os.system('cls' if os.name == 'nt' else 'clear')
            print(f"🕒 监控时间: {current_time} | 运行时长: {elapsed/60:.1f}分钟")
            print("=" * 60)
            
            print("🐍 Python进程状态:")
            if python_processes:
                for proc in python_processes:
                    status = "🔥 处理中" if proc['memory'] > 500 else "💤 待机"
                    print(f"  PID {proc['pid']}: {proc['memory']:.0f}MB 内存, CPU: {proc['cpu']:.1f}% {status}")
            else:
                print("  ❌ 没有发现Python进程")
            
            print(f"\n📁 输出文件 ({len(files)}个):")
            if files:
                for f in files:
                    file_path = os.path.join(out_dir, f)
                    size = os.path.getsize(file_path)
                    mtime = os.path.getmtime(file_path)
                    time_str = datetime.fromtimestamp(mtime).strftime("%H:%M:%S")
                    print(f"  📄 {f} ({size}B, {time_str})")
            else:
                print("  ⏳ 暂无输出文件...")
            
            # 检查是否有新文件生成
            if len(files) > last_file_count:
                print(f"\n🎉 发现新文件! 共{len(files)}个文件")
                last_file_count = len(files)
            
            print(f"\n💡 提示: 预计总处理时间 25-35分钟 (60分钟视频)")
            print("=" * 60)
            
            time.sleep(30)  # 每30秒更新一次
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 监控已停止")

if __name__ == "__main__":
    monitor_progress() 