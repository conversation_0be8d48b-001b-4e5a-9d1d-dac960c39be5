# 🔧 语法错误修复指南

## 🚨 **问题描述**

在添加字幕烧录功能时，出现了语法错误：
```
SyntaxError: expected 'except' or 'finally' block
```

## 🔍 **问题根源**

在 `video_service.py` 文件中，`compose_video` 方法的 try-except 块结构不完整，导致语法错误。

## ✅ **修复方案**

### 方案1：完整重写 compose_video 方法

由于当前的 `compose_video` 方法结构复杂且有语法问题，建议完全重写这个方法：

```python
async def compose_video(self, video_path: str, audio_path: str,
                      subtitle_path: Optional[str] = None, output_format: str = "mp4") -> Dict[str, Any]:
    """Compose video with new audio and optional subtitles"""
    
    # 在函数开始就定义这些变量，避免作用域问题
    is_tts_fallback = False
    audio_file_exists = False
    
    try:
        # 转换为绝对路径
        abs_video_path = os.path.abspath(video_path)
        abs_audio_path = os.path.abspath(audio_path)
        
        # 生成输出路径
        output_path = self.output_dir / f"video_{self._generate_unique_id()}.{output_format}"
        abs_output_path = os.path.abspath(str(output_path))
        
        # 验证输入文件
        if not os.path.exists(abs_video_path):
            raise FileNotFoundError(f"Video file not found: {abs_video_path}")
        if not os.path.exists(abs_audio_path):
            raise FileNotFoundError(f"Audio file not found: {abs_audio_path}")
        
        # 检查是否是TTS回退（文本文件）
        try:
            with open(abs_audio_path, 'r', encoding='utf-8') as f:
                content = f.read(100)  # 读取前100个字符
                if content.strip():  # 如果能读取到文本内容
                    is_tts_fallback = True
                    self.logger.warning("检测到TTS回退文件（文本），将只复制原视频")
        except UnicodeDecodeError:
            # 不是文本文件，是正常的音频文件
            audio_file_exists = True
        except Exception as e:
            self.logger.warning(f"检查音频文件时出错: {e}")
            audio_file_exists = False
        
        # 使用FFmpeg进行视频合成
        if audio_file_exists and not is_tts_fallback:
            # 有有效音频文件，替换音频
            ffmpeg_cmd = [
                'ffmpeg', '-y',  # 覆盖输出文件
                '-i', abs_video_path,  # 输入视频
                '-i', abs_audio_path,  # 输入音频
                '-c:v', 'copy',        # 复制视频流
                '-c:a', 'aac',         # 音频编码为AAC
                '-map', '0:v:0',       # 映射视频流
                '-map', '1:a:0',       # 映射音频流
                '-shortest',           # 以最短流为准
                abs_output_path        # 输出文件
            ]
            self.logger.info("使用音频替换模式")
        else:
            # 没有有效音频文件或TTS回退，只复制视频
            ffmpeg_cmd = [
                'ffmpeg', '-y',
                '-i', abs_video_path,  # 输入视频
                '-c', 'copy',          # 复制所有流
                abs_output_path        # 输出文件
            ]
            self.logger.info("使用视频复制模式")
        
        self.logger.info(f"执行FFmpeg命令: {' '.join(ffmpeg_cmd)}")
        
        # 执行FFmpeg命令
        result = subprocess.run(
            ffmpeg_cmd,
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            self.logger.info(f"FFmpeg命令执行成功: {abs_output_path}")
        else:
            self.logger.error(f"FFmpeg命令失败，返回码: {result.returncode}")
            self.logger.error(f"FFmpeg错误输出: {result.stderr}")
            raise subprocess.CalledProcessError(result.returncode, ffmpeg_cmd, result.stderr)
        
        # 获取文件信息
        file_size = os.path.getsize(abs_output_path)
        duration = self._get_video_duration(abs_output_path)
        
        result = {
            "video_file_path": abs_output_path,
            "duration": duration,
            "file_size": file_size
        }
        
        # 添加回退信息
        if is_tts_fallback:
            result["tts_fallback"] = True
            result["message"] = "Video created with original audio due to TTS service unavailability"
        
        return result
        
    except Exception as e:
        self.logger.error(f"Video composition failed: {e}")
        raise
```

### 方案2：快速修复现有代码

如果不想重写整个方法，可以快速修复语法错误：

1. **备份当前文件**：
   ```bash
   cp app/services/video_service.py app/services/video_service.py.backup
   ```

2. **替换 compose_video 方法**：
   - 找到 `async def compose_video` 开始的行
   - 替换整个方法为上面的简化版本

3. **保留其他方法**：
   - 保留 `compose_video_to_path`
   - 保留 `compose_video_with_subtitles`
   - 保留所有辅助方法

## 🚀 **立即修复步骤**

### 1. 创建简化的 video_service.py

```python
# 在 app/services/ 目录下创建新的 video_service_simple.py
# 包含所有必要的方法但结构更简单
```

### 2. 替换导入

```python
# 在需要的地方，临时使用简化版本
from app.services.video_service_simple import VideoService
```

### 3. 测试修复

```bash
# 测试语法
python -m py_compile app/services/video_service.py

# 测试导入
python -c "from app.services.video_service import VideoService; print('导入成功')"

# 测试服务启动
python main.py
```

## 📋 **检查清单**

- [ ] 备份原始文件
- [ ] 修复语法错误
- [ ] 测试Python语法
- [ ] 测试模块导入
- [ ] 测试服务启动
- [ ] 验证功能正常

## 🎯 **预期结果**

修复后应该能够：

1. ✅ Python语法检查通过
2. ✅ 模块导入成功
3. ✅ 服务正常启动
4. ✅ 视频合成功能正常
5. ✅ 字幕烧录功能可用

## 🔧 **如果仍有问题**

如果修复后仍有问题，可以：

1. **回滚到备份版本**
2. **逐步添加新功能**
3. **使用更简单的实现**
4. **分离字幕功能到独立服务**

修复语法错误是当前的首要任务，功能完善可以后续进行。
