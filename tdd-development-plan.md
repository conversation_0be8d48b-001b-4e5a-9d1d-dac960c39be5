# TDD开发计划 - 视频翻译系统

## 📋 TDD开发策略

### TDD基本原则
1. **Red** - 先写测试，测试失败（红色）
2. **Green** - 写最简单的代码让测试通过（绿色）
3. **Refactor** - 重构代码，保持测试通过

### 项目TDD实施策略
```
测试优先 → 最小实现 → 重构优化 → 集成测试 → 部署验证
```

## 🎯 测试层次设计

### 1. 单元测试 (Unit Tests)
**覆盖率目标**: 90%以上
**测试范围**: 单个方法、类的功能

#### Spring Boot 单元测试
```java
// 示例：任务服务单元测试
@ExtendWith(MockitoExtension.class)
class TaskServiceTest {
    
    @Mock
    private TaskRepository taskRepository;
    
    @Mock
    private PythonAIClient pythonAIClient;
    
    @InjectMocks
    private TaskService taskService;
    
    @Test
    @DisplayName("创建任务应该返回任务ID")
    void createTask_ShouldReturnTaskId() {
        // Given
        CreateTaskRequest request = new CreateTaskRequest("test.mp4", "en", "zh");
        Task expectedTask = Task.builder()
            .id(1L)
            .fileName("test.mp4")
            .status(TaskStatus.PENDING)
            .build();
        
        when(taskRepository.save(any(Task.class))).thenReturn(expectedTask);
        
        // When
        Long taskId = taskService.createTask(request);
        
        // Then
        assertThat(taskId).isEqualTo(1L);
        verify(taskRepository).save(any(Task.class));
    }
}
```

#### Python 单元测试
```python
# 示例：音频提取服务单元测试
import pytest
from unittest.mock import Mock, patch
from services.audio_extractor import AudioExtractor

class TestAudioExtractor:
    
    def setup_method(self):
        self.audio_extractor = AudioExtractor()
    
    @patch('subprocess.run')
    def test_extract_audio_success(self, mock_subprocess):
        # Given
        mock_subprocess.return_value.returncode = 0
        video_path = "/path/to/video.mp4"
        expected_audio_path = "/path/to/audio.wav"
        
        # When
        result = self.audio_extractor.extract_audio(video_path)
        
        # Then
        assert result.success is True
        assert result.audio_path == expected_audio_path
        mock_subprocess.assert_called_once()
```

### 2. 集成测试 (Integration Tests)
**测试范围**: 服务间交互、数据库操作、外部API调用

#### Spring Boot 集成测试
```java
@SpringBootTest
@Testcontainers
class TaskIntegrationTest {
    
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("test_db")
            .withUsername("test")
            .withPassword("test");
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7.0")
            .withExposedPorts(6379);
    
    @Autowired
    private TaskService taskService;
    
    @Test
    @DisplayName("完整的任务创建和处理流程")
    void completeTaskFlow() {
        // Given
        CreateTaskRequest request = new CreateTaskRequest("test.mp4", "en", "zh");
        
        // When
        Long taskId = taskService.createTask(request);
        Task task = taskService.getTask(taskId);
        
        // Then
        assertThat(task.getStatus()).isEqualTo(TaskStatus.PENDING);
        assertThat(task.getFileName()).isEqualTo("test.mp4");
    }
}
```

#### Python 集成测试
```python
# 示例：完整的视频处理流程测试
import pytest
from fastapi.testclient import TestClient
from main import app

class TestVideoProcessingFlow:
    
    def setup_method(self):
        self.client = TestClient(app)
    
    @pytest.mark.integration
    def test_complete_video_processing(self):
        # Given
        task_data = {
            "task_id": 1,
            "video_path": "/test/video.mp4",
            "source_language": "en",
            "target_language": "zh"
        }
        
        # When
        response = self.client.post("/ai/video/process", json=task_data)
        
        # Then
        assert response.status_code == 200
        assert response.json()["status"] == "processing"
```

### 3. 端到端测试 (E2E Tests)
**测试范围**: 完整的用户流程

```javascript
// 示例：前端E2E测试
describe('视频翻译完整流程', () => {
  it('用户可以上传视频并获得翻译结果', async () => {
    // Given
    await page.goto('/upload');
    
    // When
    await page.setInputFiles('input[type="file"]', 'test-video.mp4');
    await page.selectOption('select[name="targetLanguage"]', 'zh');
    await page.click('button[type="submit"]');
    
    // Then
    await expect(page.locator('.task-status')).toContainText('处理中');
    await expect(page.locator('.progress-bar')).toBeVisible();
  });
});
```

## 🚀 TDD开发流程

### 阶段1: 核心服务TDD开发 (5-7天)

#### Day 1-2: Spring Boot 核心服务
```
1. 写测试：用户管理服务测试
   - 用户注册测试
   - 用户登录测试
   - 用户信息获取测试

2. 实现功能：让测试通过
   - UserService 基本实现
   - UserController 基本实现
   - UserRepository 基本实现

3. 重构优化：
   - 提取公共方法
   - 优化代码结构
   - 添加异常处理
```

#### Day 3-4: 任务管理服务
```
1. 写测试：任务管理服务测试
   - 创建任务测试
   - 获取任务列表测试
   - 更新任务状态测试
   - 删除任务测试

2. 实现功能：
   - TaskService 实现
   - TaskController 实现
   - TaskRepository 实现

3. 重构优化：
   - 状态机模式
   - 任务状态验证
   - 错误处理机制
```

#### Day 5-7: Python AI服务
```
1. 写测试：AI处理服务测试
   - 音频提取测试
   - 语音识别测试
   - 翻译服务测试
   - TTS生成测试

2. 实现功能：
   - AudioExtractor 实现
   - SpeechRecognizer 实现
   - Translator 实现
   - TTSGenerator 实现

3. 重构优化：
   - 服务抽象化
   - 配置管理
   - 错误重试机制
```

### 阶段2: 服务集成TDD开发 (3-4天)

#### Day 1-2: 服务间通信
```
1. 写测试：通信机制测试
   - HTTP调用测试
   - 消息队列测试
   - 回调机制测试

2. 实现功能：
   - RestTemplate 配置
   - RabbitMQ 配置
   - 回调处理器实现

3. 重构优化：
   - 连接池配置
   - 重试机制
   - 熔断器模式
```

#### Day 3-4: 完整流程集成
```
1. 写测试：端到端流程测试
   - 完整视频处理流程
   - 错误处理流程
   - 并发处理测试

2. 实现功能：
   - 流程编排服务
   - 状态同步机制
   - 错误恢复机制

3. 重构优化：
   - 性能优化
   - 内存管理
   - 日志完善
```

### 阶段3: 前端TDD开发 (3-4天)

#### Day 1-2: 组件测试
```
1. 写测试：Vue组件测试
   - 文件上传组件测试
   - 任务列表组件测试
   - 进度显示组件测试

2. 实现功能：
   - 组件基本实现
   - 状态管理
   - 事件处理

3. 重构优化：
   - 组件抽象化
   - 状态管理优化
   - 样式优化
```

#### Day 3-4: 页面集成测试
```
1. 写测试：页面集成测试
   - 上传页面测试
   - 任务管理页面测试
   - 结果展示页面测试

2. 实现功能：
   - 页面路由
   - API集成
   - 状态同步

3. 重构优化：
   - 用户体验优化
   - 错误处理
   - 加载状态管理
```

## 🔧 测试工具和框架

### Spring Boot 测试栈
```xml
<dependencies>
    <!-- 单元测试 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- 集成测试 -->
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers</artifactId>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mysql</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- Mock测试 -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- 断言库 -->
    <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

### Python 测试栈
```txt
# 单元测试
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Mock测试
pytest-mock==3.11.1
responses==0.23.3

# 集成测试
httpx==0.24.1
testcontainers==3.7.1

# 断言库
assertpy==1.1
```

### Vue.js 测试栈
```json
{
  "devDependencies": {
    "@vue/test-utils": "^2.4.0",
    "vitest": "^0.34.0",
    "jsdom": "^22.1.0",
    "playwright": "^1.37.0",
    "@playwright/test": "^1.37.0"
  }
}
```

## 📊 测试覆盖率目标

### 代码覆盖率目标
- **Spring Boot**: 90%以上
- **Python AI服务**: 85%以上
- **Vue.js 前端**: 80%以上

### 测试类型分布
- **单元测试**: 70%
- **集成测试**: 20%
- **端到端测试**: 10%

## 🔄 持续集成配置

### GitHub Actions 配置
```yaml
name: TDD CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Run Spring Boot Tests
      run: |
        cd backend
        ./mvnw test
        ./mvnw jacoco:report
    
    - name: Run Python Tests
      run: |
        cd python-ai-service
        pip install -r requirements.txt
        pytest --cov=app --cov-report=xml
    
    - name: Run Vue.js Tests
      run: |
        cd frontend
        npm install
        npm run test:unit
        npm run test:e2e
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
```

## 📝 TDD最佳实践

### 1. 测试命名规范
```java
// 格式：methodName_condition_expectedResult
@Test
void createTask_withValidInput_shouldReturnTaskId() { }

@Test
void createTask_withInvalidInput_shouldThrowException() { }
```

### 2. 测试数据管理
```java
// 使用Builder模式创建测试数据
public class TaskTestDataBuilder {
    public static Task.TaskBuilder defaultTask() {
        return Task.builder()
            .fileName("test.mp4")
            .sourceLanguage("en")
            .targetLanguage("zh")
            .status(TaskStatus.PENDING);
    }
}
```

### 3. Mock使用原则
- 只Mock外部依赖
- 不要Mock被测试的类
- 使用真实对象进行集成测试

### 4. 测试独立性
- 每个测试方法独立运行
- 不依赖其他测试的结果
- 使用@DirtiesContext清理上下文

## 🎯 TDD开发检查清单

### 开发前检查
- [ ] 是否先写了测试？
- [ ] 测试是否覆盖了所有场景？
- [ ] 测试是否简单易懂？

### 开发中检查
- [ ] 测试是否失败了（Red）？
- [ ] 代码是否让测试通过（Green）？
- [ ] 是否进行了重构（Refactor）？

### 开发后检查
- [ ] 代码覆盖率是否达标？
- [ ] 所有测试是否通过？
- [ ] 是否添加了集成测试？

## 🚀 下一步行动

1. **确认TDD策略** - 您是否同意这个TDD开发计划？
2. **选择起始点** - 从哪个服务开始TDD开发？
3. **设置环境** - 配置测试环境和CI/CD
4. **团队培训** - 确保团队掌握TDD方法

---

*此文档将指导整个项目的TDD开发过程，确保高质量的代码交付。* 