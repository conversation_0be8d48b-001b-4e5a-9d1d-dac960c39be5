# 🎬 字幕烧录功能实现

## 📋 **功能概述**

参照VideoLingo项目，为您的视频翻译系统添加了完整的字幕烧录功能，支持将字幕永久嵌入到视频中。

### ✨ **新增功能**

1. **字幕烧录 (Hardsubs)**：将字幕永久嵌入视频
2. **Netflix风格样式**：专业级字幕外观
3. **双语字幕支持**：原文+翻译同时显示
4. **自定义样式**：可调整字体、颜色、位置等
5. **完整集成**：无缝集成到翻译流程

---

## 🛠️ **技术实现**

### 1. **SubtitleService增强**

#### 新增方法：
```python
# 字幕烧录到视频
async def burn_subtitles_to_video(video_path, subtitle_path, output_path, subtitle_style)

# 创建双语字幕
async def create_dual_subtitles(original_segments, translated_segments, output_path)

# 构建字幕滤镜
def _build_subtitle_filter(subtitle_path, style)
```

#### 字幕样式配置：
```python
default_style = {
    'font_name': 'Arial',
    'font_size': 24,
    'primary_color': '&Hffffff',  # 白色文字
    'outline_color': '&H000000',  # 黑色描边
    'back_color': '&H80000000',   # 半透明背景
    'outline': 2,                 # 描边宽度
    'shadow': 1,                  # 阴影
    'alignment': 2,               # 底部居中
    'margin_v': 30                # 底部边距
}
```

### 2. **VideoService增强**

#### 新增方法：
```python
# 带字幕的视频合成
async def compose_video_with_subtitles(video_path, audio_path, subtitle_path, output_path, subtitle_style)

# 获取视频时长
def _get_video_duration(video_path)
```

#### FFmpeg命令示例：
```bash
# 音频替换 + 字幕烧录
ffmpeg -y \
  -i video.mp4 \
  -i audio.wav \
  -vf "subtitles='subtitle.srt':force_style='FontName=Arial,FontSize=24,PrimaryColour=&Hffffff'" \
  -c:a aac \
  -c:v libx264 \
  -preset medium \
  -crf 23 \
  -map 0:v:0 \
  -map 1:a:0 \
  -shortest \
  output.mp4
```

### 3. **API接口扩展**

#### 请求参数：
```python
class FullPipelineRequest(BaseModel):
    video_file_path: str
    source_language: str = "en"
    target_language: str = "zh-CN"
    include_subtitles: bool = True
    burn_subtitles: bool = False  # 新增：是否烧录字幕
    tts_voice: str = "zh-CN-XiaoxiaoNeural"
```

---

## 🎯 **使用方法**

### 1. **API调用示例**

#### 生成软字幕（SRT文件）：
```json
{
    "video_file_path": "input.mp4",
    "source_language": "en",
    "target_language": "zh-CN",
    "include_subtitles": true,
    "burn_subtitles": false,
    "tts_voice": "zh-CN-XiaoxiaoNeural"
}
```

#### 生成硬字幕（烧录到视频）：
```json
{
    "video_file_path": "input.mp4",
    "source_language": "en",
    "target_language": "zh-CN",
    "include_subtitles": true,
    "burn_subtitles": true,
    "tts_voice": "zh-CN-XiaoxiaoNeural"
}
```

### 2. **目录结构**

```
20250719_1430_video/
├── source/
│   └── original.mp4
├── subtitles/
│   ├── original.srt        # 原文字幕
│   ├── translated.srt      # 翻译字幕
│   └── dual.srt           # 双语字幕（可选）
├── output/
│   ├── final_video.mp4    # 带软字幕的视频
│   └── burned_video.mp4   # 带硬字幕的视频（如果启用）
└── metadata.json
```

### 3. **字幕样式自定义**

```python
# 自定义字幕样式
custom_style = {
    'font_name': 'Microsoft YaHei',  # 中文字体
    'font_size': 28,                 # 更大字号
    'primary_color': '&H00ffff',     # 黄色文字
    'outline_color': '&H000000',     # 黑色描边
    'outline': 3,                    # 更粗描边
    'margin_v': 50                   # 更大底部边距
}

# 应用自定义样式
await subtitle_service.burn_subtitles_to_video(
    video_path="input.mp4",
    subtitle_path="subtitle.srt",
    output_path="output.mp4",
    subtitle_style=custom_style
)
```

---

## 🎨 **字幕样式选项**

### Netflix风格（默认）
- **字体**：Arial, 24px
- **颜色**：白色文字，黑色描边
- **位置**：底部居中
- **背景**：半透明黑色

### 自定义选项
| 参数 | 说明 | 示例值 |
|------|------|--------|
| `font_name` | 字体名称 | `'Arial'`, `'Microsoft YaHei'` |
| `font_size` | 字体大小 | `24`, `28`, `32` |
| `primary_color` | 文字颜色 | `'&Hffffff'` (白色), `'&H00ffff'` (黄色) |
| `outline_color` | 描边颜色 | `'&H000000'` (黑色) |
| `back_color` | 背景颜色 | `'&H80000000'` (半透明黑色) |
| `outline` | 描边宽度 | `1`, `2`, `3` |
| `shadow` | 阴影 | `0`, `1`, `2` |
| `alignment` | 对齐方式 | `1` (左下), `2` (中下), `3` (右下) |
| `margin_v` | 垂直边距 | `20`, `30`, `50` |

---

## 🔧 **技术细节**

### 1. **FFmpeg字幕滤镜**

#### subtitles滤镜：
```bash
# 基本用法
-vf "subtitles=subtitle.srt"

# 带样式
-vf "subtitles=subtitle.srt:force_style='FontName=Arial,FontSize=24'"

# 复杂样式
-vf "subtitles=subtitle.srt:force_style='FontName=Arial,FontSize=24,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2'"
```

#### 路径处理：
```python
# Windows路径转换
escaped_path = subtitle_path.replace('\\', '/').replace(':', '\\:')
```

### 2. **性能考虑**

#### 编码设置：
- **视频编码**：libx264
- **预设**：medium (平衡速度和质量)
- **质量**：CRF 23 (高质量)
- **音频编码**：AAC (复制或重新编码)

#### 处理时间：
- **软字幕**：几乎无额外时间
- **硬字幕**：需要重新编码视频，时间增加2-3倍

### 3. **错误处理**

```python
try:
    # 字幕烧录
    result = await burn_subtitles_to_video(...)
except FileNotFoundError:
    # 文件不存在
    logger.error("字幕或视频文件不存在")
except subprocess.CalledProcessError:
    # FFmpeg执行失败
    logger.error("字幕烧录失败")
except Exception as e:
    # 其他错误
    logger.error(f"未知错误: {e}")
```

---

## 🚀 **测试验证**

### 1. **运行测试脚本**
```bash
python test_subtitle_burn.py
```

### 2. **预期输出**
```
✅ SubtitleService: 通过
✅ VideoService: 通过
✅ TaskService: 通过
✅ 请求模型: 通过
✅ FFmpeg支持: 通过
```

### 3. **完整流程测试**
1. 重启AI服务
2. 上传视频文件
3. 设置 `burn_subtitles: true`
4. 运行完整翻译
5. 检查生成的视频是否包含烧录字幕

---

## 📊 **对比分析**

### 软字幕 vs 硬字幕

| 特性 | 软字幕 (SRT) | 硬字幕 (烧录) |
|------|-------------|-------------|
| **文件大小** | 小 | 大 |
| **兼容性** | 需播放器支持 | 通用兼容 |
| **可编辑性** | 可编辑 | 不可编辑 |
| **样式控制** | 播放器决定 | 完全控制 |
| **处理时间** | 快 | 慢 |
| **质量** | 依赖播放器 | 一致 |

### 使用建议

- **软字幕**：适合需要编辑或多语言切换
- **硬字幕**：适合分发、社交媒体、确保样式一致

---

## 🎉 **功能完成**

字幕烧录功能已完全集成到您的视频翻译系统中，提供了：

1. ✅ **专业级字幕样式**
2. ✅ **灵活的配置选项**
3. ✅ **完整的API集成**
4. ✅ **结构化目录管理**
5. ✅ **全面的错误处理**

现在您的系统可以生成Netflix级别的专业字幕视频了！🚀
