"""
视频翻译AI服务
FastAPI 应用入口点
"""

import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

# 从应用核心配置中导入设置
from app.core.config import settings
# 从应用核心日志模块中导入日志设置和获取logger的方法
from app.core.logging import setup_logging, get_logger
# 从应用API路由中导入API路由器
from app.api.routes import api_router


# 设置日志系统
setup_logging()
# 获取当前模块的logger实例
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用程序生命周期管理器
    在应用启动和关闭时执行相关逻辑
    """
    logger.info("正在启动视频翻译AI服务")
    logger.info(f"版本: {settings.app_version}")
    logger.info(f"环境: {'开发' if settings.debug else '生产'}")

    # 应用启动逻辑可以在这里添加
    yield  # 应用在此处运行
    # 应用关闭逻辑可以在这里添加
    logger.info("正在关闭视频翻译AI服务")


# 创建 FastAPI 应用程序实例
app = FastAPI(
    title=settings.app_name,  # 应用名称
    version=settings.app_version,  # 应用版本
    description="一个AI驱动的视频翻译服务，支持语音识别、翻译和语音合成",  # 应用描述
    docs_url="/docs" if settings.debug else None,  # 仅在调试模式下启用Swagger UI文档
    redoc_url="/redoc" if settings.debug else None,  # 仅在调试模式下启用ReDoc文档
    lifespan=lifespan,  # 设置应用程序生命周期管理器
)

# 添加 CORS 中间件，处理跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,  # 允许的来源
    allow_credentials=True,  # 允许凭据（如cookie）
    allow_methods=settings.cors_methods,  # 允许的HTTP方法
    allow_headers=settings.cors_headers,  # 允许的HTTP头
)


# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理程序，捕获所有未处理的异常
    """
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",  # 内部服务器错误
            "message": "发生了一个意外错误",  # 错误消息
            "details": str(exc) if settings.debug else None,  # 在调试模式下显示详细错误信息
        }
    )


# 包含 API 路由
# 所有API路由都将以 "/api/v1" 为前缀
app.include_router(api_router, prefix="/api/v1")


# 健康检查端点
@app.get("/health")
async def health_check():
    """
    健康检查端点，用于检查服务是否正常运行
    """
    return {
        "status": "healthy",  # 服务状态
        "service": settings.app_name,  # 服务名称
        "version": settings.app_version,  # 服务版本
        "timestamp": "2024-01-01T00:00:00Z",  # 占位符，将被替换为实际时间戳
    }


# 根端点
@app.get("/")
async def root():
    """
    根端点，提供服务的基本信息
    """
    return {
        "message": f"欢迎使用 {settings.app_name}",  # 欢迎消息
        "version": settings.app_version,  # 服务版本
        "docs": "/docs" if settings.debug else "生产环境下文档不可用",  # 文档链接
    }


# 当作为主程序运行时，启动 uvicorn 服务器
if __name__ == "__main__":
    uvicorn.run(
        "main:app",  # 指定应用
        host=settings.host,  # 监听的主机地址
        port=settings.port,  # 监听的端口
        reload=settings.debug,  # 在调试模式下启用热重载
        workers=1 if settings.debug else settings.workers,  # 工作进程数
        log_level=settings.log_level.lower(),  # 日志级别
    ) 