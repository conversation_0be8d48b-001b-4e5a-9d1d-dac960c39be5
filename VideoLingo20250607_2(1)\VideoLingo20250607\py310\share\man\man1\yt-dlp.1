'\" t
.\" Automatically generated by Pandoc 3.1.3
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "yt-dlp" "1" "" "" ""
.hy
.SH NAME
.PP
yt-dlp - A feature-rich command-line audio/video downloader
.SH SYNOPSIS
.PP
\f[B]yt-dlp\f[R] [OPTIONS] URL [URL...]
.SH DESCRIPTION
.PP
yt-dlp is a feature-rich command-line audio/video downloader with
support for thousands of sites.
The project is a fork of
youtube-dl (https://github.com/ytdl-org/youtube-dl) based on the now
inactive youtube-dlc (https://github.com/blackjack4494/yt-dlc).
.SH OPTIONS
.SS General Options:
.TP
-h, --help
Print this help text and exit
.TP
--version
Print program version and exit
.TP
-U, --update
Update this program to the latest version
.TP
--no-update
Do not check for updates (default)
.TP
--update-to \f[I][CHANNEL]\[at][TAG]\f[R]
Upgrade/downgrade to a specific version.
CHANNEL can be a repository as well.
CHANNEL and TAG default to \[dq]stable\[dq] and \[dq]latest\[dq]
respectively if omitted; See \[dq]UPDATE\[dq] for details.
Supported channels: stable, nightly, master
.TP
-i, --ignore-errors
Ignore download and postprocessing errors.
The download will be considered successful even if the postprocessing
fails
.TP
--no-abort-on-error
Continue with next video on download errors; e.g.
to skip unavailable videos in a playlist (default)
.TP
--abort-on-error
Abort downloading of further videos if an error occurs (Alias:
--no-ignore-errors)
.TP
--dump-user-agent
Display the current user-agent and exit
.TP
--list-extractors
List all supported extractors and exit
.TP
--extractor-descriptions
Output descriptions of all supported extractors and exit
.TP
--use-extractors \f[I]NAMES\f[R]
Extractor names to use separated by commas.
You can also use regexes, \[dq]all\[dq], \[dq]default\[dq] and
\[dq]end\[dq] (end URL matching); e.g.
--ies \[dq]holodex.*,end,youtube\[dq].
Prefix the name with a \[dq]-\[dq] to exclude it, e.g.
--ies default,-generic.
Use --list-extractors for a list of extractor names.
(Alias: --ies)
.TP
--default-search \f[I]PREFIX\f[R]
Use this prefix for unqualified URLs.
E.g.
\[dq]gvsearch2:python\[dq] downloads two videos from google videos for
the search term \[dq]python\[dq].
Use the value \[dq]auto\[dq] to let yt-dlp guess (\[dq]auto_warning\[dq]
to emit a warning when guessing).
\[dq]error\[dq] just throws an error.
The default value \[dq]fixup_error\[dq] repairs broken URLs, but emits
an error if this is not possible instead of searching
.TP
--ignore-config
Don\[aq]t load any more configuration files except those given to
--config-locations.
For backward compatibility, if this option is found inside the system
configuration file, the user configuration is not loaded.
(Alias: --no-config)
.TP
--no-config-locations
Do not load any custom configuration files (default).
When given inside a configuration file, ignore all previous
--config-locations defined in the current file
.TP
--config-locations \f[I]PATH\f[R]
Location of the main configuration file; either the path to the config
or its containing directory (\[dq]-\[dq] for stdin).
Can be used multiple times and inside other configuration files
.TP
--plugin-dirs \f[I]PATH\f[R]
Path to an additional directory to search for plugins.
This option can be used multiple times to add multiple directories.
Use \[dq]default\[dq] to search the default plugin directories (default)
.TP
--no-plugin-dirs
Clear plugin directories to search, including defaults and those
provided by previous --plugin-dirs
.TP
--flat-playlist
Do not extract a playlist\[aq]s URL result entries; some entry metadata
may be missing and downloading may be bypassed
.TP
--no-flat-playlist
Fully extract the videos of a playlist (default)
.TP
--live-from-start
Download livestreams from the start.
Currently experimental and only supported for YouTube and Twitch
.TP
--no-live-from-start
Download livestreams from the current time (default)
.TP
--wait-for-video \f[I]MIN[-MAX]\f[R]
Wait for scheduled streams to become available.
Pass the minimum number of seconds (or range) to wait between retries
.TP
--no-wait-for-video
Do not wait for scheduled streams (default)
.TP
--mark-watched
Mark videos watched (even with --simulate)
.TP
--no-mark-watched
Do not mark videos watched (default)
.TP
--color \f[I][STREAM:]POLICY\f[R]
Whether to emit color codes in output, optionally prefixed by the STREAM
(stdout or stderr) to apply the setting to.
Can be one of \[dq]always\[dq], \[dq]auto\[dq] (default),
\[dq]never\[dq], or \[dq]no_color\[dq] (use non color terminal
sequences).
Use \[dq]auto-tty\[dq] or \[dq]no_color-tty\[dq] to decide based on
terminal support only.
Can be used multiple times
.TP
--compat-options \f[I]OPTS\f[R]
Options that can help keep compatibility with youtube-dl or youtube-dlc
configurations by reverting some of the changes made in yt-dlp.
See \[dq]Differences in default behavior\[dq] for details
.TP
--alias \f[I]ALIASES OPTIONS\f[R]
Create aliases for an option string.
Unless an alias starts with a dash \[dq]-\[dq], it is prefixed with
\[dq]--\[dq].
Arguments are parsed according to the Python string formatting
mini-language.
E.g.
--alias get-audio,-X \[dq]-S aext:{0},abr -x --audio-format {0}\[dq]
creates options \[dq]--get-audio\[dq] and \[dq]-X\[dq] that takes an
argument (ARG0) and expands to \[dq]-S aext:ARG0,abr -x --audio-format
ARG0\[dq].
All defined aliases are listed in the --help output.
Alias options can trigger more aliases; so be careful to avoid defining
recursive options.
As a safety measure, each alias may be triggered a maximum of 100 times.
This option can be used multiple times
.TP
-t, --preset-alias \f[I]PRESET\f[R]
Applies a predefined set of options.
e.g.
--preset-alias mp3.
The following presets are available: mp3, aac, mp4, mkv, sleep.
See the \[dq]Preset Aliases\[dq] section at the end for more info.
This option can be used multiple times
.SS Network Options:
.TP
--proxy \f[I]URL\f[R]
Use the specified HTTP/HTTPS/SOCKS proxy.
To enable SOCKS proxy, specify a proper scheme, e.g.
socks5://user:pass\[at]127.0.0.1:1080/.
Pass in an empty string (--proxy \[dq]\[dq]) for direct connection
.TP
--socket-timeout \f[I]SECONDS\f[R]
Time to wait before giving up, in seconds
.TP
--source-address \f[I]IP\f[R]
Client-side IP address to bind to
.TP
--impersonate \f[I]CLIENT[:OS]\f[R]
Client to impersonate for requests.
E.g.
chrome, chrome-110, chrome:windows-10.
Pass --impersonate=\[dq]\[dq] to impersonate any client.
Note that forcing impersonation for all requests may have a detrimental
impact on download speed and stability
.TP
--list-impersonate-targets
List available clients to impersonate.
.TP
-4, --force-ipv4
Make all connections via IPv4
.TP
-6, --force-ipv6
Make all connections via IPv6
.TP
--enable-file-urls
Enable file:// URLs.
This is disabled by default for security reasons.
.SS Geo-restriction:
.TP
--geo-verification-proxy \f[I]URL\f[R]
Use this proxy to verify the IP address for some geo-restricted sites.
The default proxy specified by --proxy (or none, if the option is not
present) is used for the actual downloading
.TP
--xff \f[I]VALUE\f[R]
How to fake X-Forwarded-For HTTP header to try bypassing geographic
restriction.
One of \[dq]default\[dq] (only when known to be useful),
\[dq]never\[dq], an IP block in CIDR notation, or a two-letter ISO
3166-2 country code
.SS Video Selection:
.TP
-I, --playlist-items \f[I]ITEM_SPEC\f[R]
Comma separated playlist_index of the items to download.
You can specify a range using \[dq][START]:[STOP][:STEP]\[dq].
For backward compatibility, START-STOP is also supported.
Use negative indices to count from the right and negative STEP to
download in reverse order.
E.g.
\[dq]-I 1:3,7,-5::2\[dq] used on a playlist of size 15 will download the
items at index 1,2,3,7,11,13,15
.TP
--min-filesize \f[I]SIZE\f[R]
Abort download if filesize is smaller than SIZE, e.g.
50k or 44.6M
.TP
--max-filesize \f[I]SIZE\f[R]
Abort download if filesize is larger than SIZE, e.g.
50k or 44.6M
.TP
--date \f[I]DATE\f[R]
Download only videos uploaded on this date.
The date can be \[dq]YYYYMMDD\[dq] or in the format
[now|today|yesterday][-N[day|week|month|year]].
E.g.
\[dq]--date today-2weeks\[dq] downloads only videos uploaded on the same
day two weeks ago
.TP
--datebefore \f[I]DATE\f[R]
Download only videos uploaded on or before this date.
The date formats accepted are the same as --date
.TP
--dateafter \f[I]DATE\f[R]
Download only videos uploaded on or after this date.
The date formats accepted are the same as --date
.TP
--match-filters \f[I]FILTER\f[R]
Generic video filter.
Any \[dq]OUTPUT TEMPLATE\[dq] field can be compared with a number or a
string using the operators defined in \[dq]Filtering Formats\[dq].
You can also simply specify a field to match if the field is present,
use \[dq]!field\[dq] to check if the field is not present, and
\[dq]&\[dq] to check multiple conditions.
Use a \[dq]\[dq] to escape \[dq]&\[dq] or quotes if needed.
If used multiple times, the filter matches if at least one of the
conditions is met.
E.g.
--match-filters !is_live --match-filters \[dq]like_count>?100 &
description\[ti]=\[aq](?i)& dogs\[dq] matches only videos that are not
live OR those that have a like count more than 100 (or the like field is
not available) and also has a description that contains the phrase
\[dq]cats & dogs\[dq] (caseless).
Use \[dq]--match-filters -\[dq] to interactively ask whether to download
each video
.TP
--no-match-filters
Do not use any --match-filters (default)
.TP
--break-match-filters \f[I]FILTER\f[R]
Same as \[dq]--match-filters\[dq] but stops the download process when a
video is rejected
.TP
--no-break-match-filters
Do not use any --break-match-filters (default)
.TP
--no-playlist
Download only the video, if the URL refers to a video and a playlist
.TP
--yes-playlist
Download the playlist, if the URL refers to a video and a playlist
.TP
--age-limit \f[I]YEARS\f[R]
Download only videos suitable for the given age
.TP
--download-archive \f[I]FILE\f[R]
Download only videos not listed in the archive file.
Record the IDs of all downloaded videos in it
.TP
--no-download-archive
Do not use archive file (default)
.TP
--max-downloads \f[I]NUMBER\f[R]
Abort after downloading NUMBER files
.TP
--break-on-existing
Stop the download process when encountering a file that is in the
archive supplied with the --download-archive option
.TP
--no-break-on-existing
Do not stop the download process when encountering a file that is in the
archive (default)
.TP
--break-per-input
Alters --max-downloads, --break-on-existing, --break-match-filters, and
autonumber to reset per input URL
.TP
--no-break-per-input
--break-on-existing and similar options terminates the entire download
queue
.TP
--skip-playlist-after-errors \f[I]N\f[R]
Number of allowed failures until the rest of the playlist is skipped
.SS Download Options:
.TP
-N, --concurrent-fragments \f[I]N\f[R]
Number of fragments of a dash/hlsnative video that should be downloaded
concurrently (default is 1)
.TP
-r, --limit-rate \f[I]RATE\f[R]
Maximum download rate in bytes per second, e.g.
50K or 4.2M
.TP
--throttled-rate \f[I]RATE\f[R]
Minimum download rate in bytes per second below which throttling is
assumed and the video data is re-extracted, e.g.
100K
.TP
-R, --retries \f[I]RETRIES\f[R]
Number of retries (default is 10), or \[dq]infinite\[dq]
.TP
--file-access-retries \f[I]RETRIES\f[R]
Number of times to retry on file access error (default is 3), or
\[dq]infinite\[dq]
.TP
--fragment-retries \f[I]RETRIES\f[R]
Number of retries for a fragment (default is 10), or \[dq]infinite\[dq]
(DASH, hlsnative and ISM)
.TP
--retry-sleep \f[I][TYPE:]EXPR\f[R]
Time to sleep between retries in seconds (optionally) prefixed by the
type of retry (http (default), fragment, file_access, extractor) to
apply the sleep to.
EXPR can be a number, linear=START[:END[:STEP=1]] or
exp=START[:END[:BASE=2]].
This option can be used multiple times to set the sleep for the
different retry types, e.g.
--retry-sleep linear=1::2 --retry-sleep fragment:exp=1:20
.TP
--skip-unavailable-fragments
Skip unavailable fragments for DASH, hlsnative and ISM downloads
(default) (Alias: --no-abort-on-unavailable-fragments)
.TP
--abort-on-unavailable-fragments
Abort download if a fragment is unavailable (Alias:
--no-skip-unavailable-fragments)
.TP
--keep-fragments
Keep downloaded fragments on disk after downloading is finished
.TP
--no-keep-fragments
Delete downloaded fragments after downloading is finished (default)
.TP
--buffer-size \f[I]SIZE\f[R]
Size of download buffer, e.g.
1024 or 16K (default is 1024)
.TP
--resize-buffer
The buffer size is automatically resized from an initial value of
--buffer-size (default)
.TP
--no-resize-buffer
Do not automatically adjust the buffer size
.TP
--http-chunk-size \f[I]SIZE\f[R]
Size of a chunk for chunk-based HTTP downloading, e.g.
10485760 or 10M (default is disabled).
May be useful for bypassing bandwidth throttling imposed by a webserver
(experimental)
.TP
--playlist-random
Download playlist videos in random order
.TP
--lazy-playlist
Process entries in the playlist as they are received.
This disables n_entries, --playlist-random and --playlist-reverse
.TP
--no-lazy-playlist
Process videos in the playlist only after the entire playlist is parsed
(default)
.TP
--xattr-set-filesize
Set file xattribute ytdl.filesize with expected file size
.TP
--hls-use-mpegts
Use the mpegts container for HLS videos; allowing some players to play
the video while downloading, and reducing the chance of file corruption
if download is interrupted.
This is enabled by default for live streams
.TP
--no-hls-use-mpegts
Do not use the mpegts container for HLS videos.
This is default when not downloading live streams
.TP
--download-sections \f[I]REGEX\f[R]
Download only chapters that match the regular expression.
A \[dq]*\[dq] prefix denotes time-range instead of chapter.
Negative timestamps are calculated from the end.
\[dq]*from-url\[dq] can be used to download between the
\[dq]start_time\[dq] and \[dq]end_time\[dq] extracted from the URL.
Needs ffmpeg.
This option can be used multiple times to download multiple sections,
e.g.
--download-sections \[dq]*10:15-inf\[dq] --download-sections
\[dq]intro\[dq]
.TP
--downloader \f[I][PROTO:]NAME\f[R]
Name or path of the external downloader to use (optionally) prefixed by
the protocols (http, ftp, m3u8, dash, rstp, rtmp, mms) to use it for.
Currently supports native, aria2c, avconv, axel, curl, ffmpeg, httpie,
wget.
You can use this option multiple times to set different downloaders for
different protocols.
E.g.
--downloader aria2c --downloader \[dq]dash,m3u8:native\[dq] will use
aria2c for http/ftp downloads, and the native downloader for dash/m3u8
downloads (Alias: --external-downloader)
.TP
--downloader-args \f[I]NAME:ARGS\f[R]
Give these arguments to the external downloader.
Specify the downloader name and the arguments separated by a colon
\[dq]:\[dq].
For ffmpeg, arguments can be passed to different positions using the
same syntax as --postprocessor-args.
You can use this option multiple times to give different arguments to
different downloaders (Alias: --external-downloader-args)
.SS Filesystem Options:
.TP
-a, --batch-file \f[I]FILE\f[R]
File containing URLs to download (\[dq]-\[dq] for stdin), one URL per
line.
Lines starting with \[dq]#\[dq], \[dq];\[dq] or \[dq]]\[dq] are
considered as comments and ignored
.TP
--no-batch-file
Do not read URLs from batch file (default)
.TP
-P, --paths \f[I][TYPES:]PATH\f[R]
The paths where the files should be downloaded.
Specify the type of file and the path separated by a colon \[dq]:\[dq].
All the same TYPES as --output are supported.
Additionally, you can also provide \[dq]home\[dq] (default) and
\[dq]temp\[dq] paths.
All intermediary files are first downloaded to the temp path and then
the final files are moved over to the home path after download is
finished.
This option is ignored if --output is an absolute path
.TP
-o, --output \f[I][TYPES:]TEMPLATE\f[R]
Output filename template; see \[dq]OUTPUT TEMPLATE\[dq] for details
.TP
--output-na-placeholder \f[I]TEXT\f[R]
Placeholder for unavailable fields in --output (default: \[dq]NA\[dq])
.TP
--restrict-filenames
Restrict filenames to only ASCII characters, and avoid \[dq]&\[dq] and
spaces in filenames
.TP
--no-restrict-filenames
Allow Unicode characters, \[dq]&\[dq] and spaces in filenames (default)
.TP
--windows-filenames
Force filenames to be Windows-compatible
.TP
--no-windows-filenames
Sanitize filenames only minimally
.TP
--trim-filenames \f[I]LENGTH\f[R]
Limit the filename length (excluding extension) to the specified number
of characters
.TP
-w, --no-overwrites
Do not overwrite any files
.TP
--force-overwrites
Overwrite all video and metadata files.
This option includes --no-continue
.TP
--no-force-overwrites
Do not overwrite the video, but overwrite related files (default)
.TP
-c, --continue
Resume partially downloaded files/fragments (default)
.TP
--no-continue
Do not resume partially downloaded fragments.
If the file is not fragmented, restart download of the entire file
.TP
--part
Use .part files instead of writing directly into output file (default)
.TP
--no-part
Do not use .part files - write directly into output file
.TP
--mtime
Use the Last-modified header to set the file modification time (default)
.TP
--no-mtime
Do not use the Last-modified header to set the file modification time
.TP
--write-description
Write video description to a .description file
.TP
--no-write-description
Do not write video description (default)
.TP
--write-info-json
Write video metadata to a .info.json file (this may contain personal
information)
.TP
--no-write-info-json
Do not write video metadata (default)
.TP
--write-playlist-metafiles
Write playlist metadata in addition to the video metadata when using
--write-info-json, --write-description etc.
(default)
.TP
--no-write-playlist-metafiles
Do not write playlist metadata when using --write-info-json,
--write-description etc.
.TP
--clean-info-json
Remove some internal metadata such as filenames from the infojson
(default)
.TP
--no-clean-info-json
Write all fields to the infojson
.TP
--write-comments
Retrieve video comments to be placed in the infojson.
The comments are fetched even without this option if the extraction is
known to be quick (Alias: --get-comments)
.TP
--no-write-comments
Do not retrieve video comments unless the extraction is known to be
quick (Alias: --no-get-comments)
.TP
--load-info-json \f[I]FILE\f[R]
JSON file containing the video information (created with the
\[dq]--write-info-json\[dq] option)
.TP
--cookies \f[I]FILE\f[R]
Netscape formatted file to read cookies from and dump cookie jar in
.TP
--no-cookies
Do not read/dump cookies from/to file (default)
.TP
--cookies-from-browser \f[I]BROWSER[+KEYRING][:PROFILE][::CONTAINER]\f[R]
The name of the browser to load cookies from.
Currently supported browsers are: brave, chrome, chromium, edge,
firefox, opera, safari, vivaldi, whale.
Optionally, the KEYRING used for decrypting Chromium cookies on Linux,
the name/path of the PROFILE to load cookies from, and the CONTAINER
name (if Firefox) (\[dq]none\[dq] for no container) can be given with
their respective separators.
By default, all containers of the most recently accessed profile are
used.
Currently supported keyrings are: basictext, gnomekeyring, kwallet,
kwallet5, kwallet6
.TP
--no-cookies-from-browser
Do not load cookies from browser (default)
.TP
--cache-dir \f[I]DIR\f[R]
Location in the filesystem where yt-dlp can store some downloaded
information (such as client ids and signatures) permanently.
By default ${XDG_CACHE_HOME}/yt-dlp
.TP
--no-cache-dir
Disable filesystem caching
.TP
--rm-cache-dir
Delete all filesystem cache files
.SS Thumbnail Options:
.TP
--write-thumbnail
Write thumbnail image to disk
.TP
--no-write-thumbnail
Do not write thumbnail image to disk (default)
.TP
--write-all-thumbnails
Write all thumbnail image formats to disk
.TP
--list-thumbnails
List available thumbnails of each video.
Simulate unless --no-simulate is used
.SS Internet Shortcut Options:
.TP
--write-link
Write an internet shortcut file, depending on the current platform
(.url, .webloc or .desktop).
The URL may be cached by the OS
.TP
--write-url-link
Write a .url Windows internet shortcut.
The OS caches the URL based on the file path
.TP
--write-webloc-link
Write a .webloc macOS internet shortcut
.TP
--write-desktop-link
Write a .desktop Linux internet shortcut
.SS Verbosity and Simulation Options:
.TP
-q, --quiet
Activate quiet mode.
If used with --verbose, print the log to stderr
.TP
--no-quiet
Deactivate quiet mode.
(Default)
.TP
--no-warnings
Ignore warnings
.TP
-s, --simulate
Do not download the video and do not write anything to disk
.TP
--no-simulate
Download the video even if printing/listing options are used
.TP
--ignore-no-formats-error
Ignore \[dq]No video formats\[dq] error.
Useful for extracting metadata even if the videos are not actually
available for download (experimental)
.TP
--no-ignore-no-formats-error
Throw error when no downloadable video formats are found (default)
.TP
--skip-download
Do not download the video but write all related files (Alias:
--no-download)
.TP
-O, --print \f[I][WHEN:]TEMPLATE\f[R]
Field name or output template to print to screen, optionally prefixed
with when to print it, separated by a \[dq]:\[dq].
Supported values of \[dq]WHEN\[dq] are the same as that of
--use-postprocessor (default: video).
Implies --quiet.
Implies --simulate unless --no-simulate or later stages of WHEN are
used.
This option can be used multiple times
.TP
--print-to-file \f[I][WHEN:]TEMPLATE FILE\f[R]
Append given template to the file.
The values of WHEN and TEMPLATE are the same as that of --print.
FILE uses the same syntax as the output template.
This option can be used multiple times
.TP
-j, --dump-json
Quiet, but print JSON information for each video.
Simulate unless --no-simulate is used.
See \[dq]OUTPUT TEMPLATE\[dq] for a description of available keys
.TP
-J, --dump-single-json
Quiet, but print JSON information for each URL or infojson passed.
Simulate unless --no-simulate is used.
If the URL refers to a playlist, the whole playlist information is
dumped in a single line
.TP
--force-write-archive
Force download archive entries to be written as far as no errors occur,
even if -s or another simulation option is used (Alias:
--force-download-archive)
.TP
--newline
Output progress bar as new lines
.TP
--no-progress
Do not print progress bar
.TP
--progress
Show progress bar, even if in quiet mode
.TP
--console-title
Display progress in console titlebar
.TP
--progress-template \f[I][TYPES:]TEMPLATE\f[R]
Template for progress outputs, optionally prefixed with one of
\[dq]download:\[dq] (default), \[dq]download-title:\[dq] (the console
title), \[dq]postprocess:\[dq], or \[dq]postprocess-title:\[dq].
The video\[aq]s fields are accessible under the \[dq]info\[dq] key and
the progress attributes are accessible under \[dq]progress\[dq] key.
E.g.
--console-title --progress-template
\[dq]download-title:%(info.id)s-%(progress.eta)s\[dq]
.TP
--progress-delta \f[I]SECONDS\f[R]
Time between progress output (default: 0)
.TP
-v, --verbose
Print various debugging information
.TP
--dump-pages
Print downloaded pages encoded using base64 to debug problems (very
verbose)
.TP
--write-pages
Write downloaded intermediary pages to files in the current directory to
debug problems
.TP
--print-traffic
Display sent and read HTTP traffic
.SS Workarounds:
.TP
--encoding \f[I]ENCODING\f[R]
Force the specified encoding (experimental)
.TP
--legacy-server-connect
Explicitly allow HTTPS connection to servers that do not support RFC
5746 secure renegotiation
.TP
--no-check-certificates
Suppress HTTPS certificate validation
.TP
--prefer-insecure
Use an unencrypted connection to retrieve information about the video
(Currently supported only for YouTube)
.TP
--add-headers \f[I]FIELD:VALUE\f[R]
Specify a custom HTTP header and its value, separated by a colon
\[dq]:\[dq].
You can use this option multiple times
.TP
--bidi-workaround
Work around terminals that lack bidirectional text support.
Requires bidiv or fribidi executable in PATH
.TP
--sleep-requests \f[I]SECONDS\f[R]
Number of seconds to sleep between requests during data extraction
.TP
--sleep-interval \f[I]SECONDS\f[R]
Number of seconds to sleep before each download.
This is the minimum time to sleep when used along with
--max-sleep-interval (Alias: --min-sleep-interval)
.TP
--max-sleep-interval \f[I]SECONDS\f[R]
Maximum number of seconds to sleep.
Can only be used along with --min-sleep-interval
.TP
--sleep-subtitles \f[I]SECONDS\f[R]
Number of seconds to sleep before each subtitle download
.SS Video Format Options:
.TP
-f, --format \f[I]FORMAT\f[R]
Video format code, see \[dq]FORMAT SELECTION\[dq] for more details
.TP
-S, --format-sort \f[I]SORTORDER\f[R]
Sort the formats by the fields given, see \[dq]Sorting Formats\[dq] for
more details
.TP
--format-sort-force
Force user specified sort order to have precedence over all fields, see
\[dq]Sorting Formats\[dq] for more details (Alias: --S-force)
.TP
--no-format-sort-force
Some fields have precedence over the user specified sort order (default)
.TP
--video-multistreams
Allow multiple video streams to be merged into a single file
.TP
--no-video-multistreams
Only one video stream is downloaded for each output file (default)
.TP
--audio-multistreams
Allow multiple audio streams to be merged into a single file
.TP
--no-audio-multistreams
Only one audio stream is downloaded for each output file (default)
.TP
--prefer-free-formats
Prefer video formats with free containers over non-free ones of the same
quality.
Use with \[dq]-S ext\[dq] to strictly prefer free containers
irrespective of quality
.TP
--no-prefer-free-formats
Don\[aq]t give any special preference to free containers (default)
.TP
--check-formats
Make sure formats are selected only from those that are actually
downloadable
.TP
--check-all-formats
Check all formats for whether they are actually downloadable
.TP
--no-check-formats
Do not check that the formats are actually downloadable
.TP
-F, --list-formats
List available formats of each video.
Simulate unless --no-simulate is used
.TP
--merge-output-format \f[I]FORMAT\f[R]
Containers that may be used when merging formats, separated by
\[dq]/\[dq], e.g.
\[dq]mp4/mkv\[dq].
Ignored if no merge is required.
(currently supported: avi, flv, mkv, mov, mp4, webm)
.SS Subtitle Options:
.TP
--write-subs
Write subtitle file
.TP
--no-write-subs
Do not write subtitle file (default)
.TP
--write-auto-subs
Write automatically generated subtitle file (Alias:
--write-automatic-subs)
.TP
--no-write-auto-subs
Do not write auto-generated subtitles (default) (Alias:
--no-write-automatic-subs)
.TP
--list-subs
List available subtitles of each video.
Simulate unless --no-simulate is used
.TP
--sub-format \f[I]FORMAT\f[R]
Subtitle format; accepts formats preference separated by \[dq]/\[dq],
e.g.
\[dq]srt\[dq] or \[dq]ass/srt/best\[dq]
.TP
--sub-langs \f[I]LANGS\f[R]
Languages of the subtitles to download (can be regex) or \[dq]all\[dq]
separated by commas, e.g.
--sub-langs \[dq]en.*,ja\[dq] (where \[dq]en.*\[dq] is a regex pattern
that matches \[dq]en\[dq] followed by 0 or more of any character).
You can prefix the language code with a \[dq]-\[dq] to exclude it from
the requested languages, e.g.
--sub- langs all,-live_chat.
Use --list-subs for a list of available language tags
.SS Authentication Options:
.TP
-u, --username \f[I]USERNAME\f[R]
Login with this account ID
.TP
-p, --password \f[I]PASSWORD\f[R]
Account password.
If this option is left out, yt-dlp will ask interactively
.TP
-2, --twofactor \f[I]TWOFACTOR\f[R]
Two-factor authentication code
.TP
-n, --netrc
Use .netrc authentication data
.TP
--netrc-location \f[I]PATH\f[R]
Location of .netrc authentication data; either the path or its
containing directory.
Defaults to \[ti]/.netrc
.TP
--netrc-cmd \f[I]NETRC_CMD\f[R]
Command to execute to get the credentials for an extractor.
.TP
--video-password \f[I]PASSWORD\f[R]
Video-specific password
.TP
--ap-mso \f[I]MSO\f[R]
Adobe Pass multiple-system operator (TV provider) identifier, use
--ap-list-mso for a list of available MSOs
.TP
--ap-username \f[I]USERNAME\f[R]
Multiple-system operator account login
.TP
--ap-password \f[I]PASSWORD\f[R]
Multiple-system operator account password.
If this option is left out, yt-dlp will ask interactively
.TP
--ap-list-mso
List all supported multiple-system operators
.TP
--client-certificate \f[I]CERTFILE\f[R]
Path to client certificate file in PEM format.
May include the private key
.TP
--client-certificate-key \f[I]KEYFILE\f[R]
Path to private key file for client certificate
.TP
--client-certificate-password \f[I]PASSWORD\f[R]
Password for client certificate private key, if encrypted.
If not provided, and the key is encrypted, yt-dlp will ask interactively
.SS Post-Processing Options:
.TP
-x, --extract-audio
Convert video files to audio-only files (requires ffmpeg and ffprobe)
.TP
--audio-format \f[I]FORMAT\f[R]
Format to convert the audio to when -x is used.
(currently supported: best (default), aac, alac, flac, m4a, mp3, opus,
vorbis, wav).
You can specify multiple rules using similar syntax as --remux-video
.TP
--audio-quality \f[I]QUALITY\f[R]
Specify ffmpeg audio quality to use when converting the audio with -x.
Insert a value between 0 (best) and 10 (worst) for VBR or a specific
bitrate like 128K (default 5)
.TP
--remux-video \f[I]FORMAT\f[R]
Remux the video into another container if necessary (currently
supported: avi, flv, gif, mkv, mov, mp4, webm, aac, aiff, alac, flac,
m4a, mka, mp3, ogg, opus, vorbis, wav).
If the target container does not support the video/audio codec, remuxing
will fail.
You can specify multiple rules; e.g.
\[dq]aac>m4a/mov>mp4/mkv\[dq] will remux aac to m4a, mov to mp4 and
anything else to mkv
.TP
--recode-video \f[I]FORMAT\f[R]
Re-encode the video into another format if necessary.
The syntax and supported formats are the same as --remux-video
.TP
--postprocessor-args \f[I]NAME:ARGS\f[R]
Give these arguments to the postprocessors.
Specify the postprocessor/executable name and the arguments separated by
a colon \[dq]:\[dq] to give the argument to the specified
postprocessor/executable.
Supported PP are: Merger, ModifyChapters, SplitChapters, ExtractAudio,
VideoRemuxer, VideoConvertor, Metadata, EmbedSubtitle, EmbedThumbnail,
SubtitlesConvertor, ThumbnailsConvertor, FixupStretched, FixupM4a,
FixupM3u8, FixupTimestamp and FixupDuration.
The supported executables are: AtomicParsley, FFmpeg and FFprobe.
You can also specify \[dq]PP+EXE:ARGS\[dq] to give the arguments to the
specified executable only when being used by the specified
postprocessor.
Additionally, for ffmpeg/ffprobe, \[dq]_i\[dq]/\[dq]_o\[dq] can be
appended to the prefix optionally followed by a number to pass the
argument before the specified input/output file, e.g.
--ppa \[dq]Merger+ffmpeg_i1:-v quiet\[dq].
You can use this option multiple times to give different arguments to
different postprocessors.
(Alias: --ppa)
.TP
-k, --keep-video
Keep the intermediate video file on disk after post-processing
.TP
--no-keep-video
Delete the intermediate video file after post-processing (default)
.TP
--post-overwrites
Overwrite post-processed files (default)
.TP
--no-post-overwrites
Do not overwrite post-processed files
.TP
--embed-subs
Embed subtitles in the video (only for mp4, webm and mkv videos)
.TP
--no-embed-subs
Do not embed subtitles (default)
.TP
--embed-thumbnail
Embed thumbnail in the video as cover art
.TP
--no-embed-thumbnail
Do not embed thumbnail (default)
.TP
--embed-metadata
Embed metadata to the video file.
Also embeds chapters/infojson if present unless
--no-embed-chapters/--no-embed-info-json are used (Alias:
--add-metadata)
.TP
--no-embed-metadata
Do not add metadata to file (default) (Alias: --no-add-metadata)
.TP
--embed-chapters
Add chapter markers to the video file (Alias: --add-chapters)
.TP
--no-embed-chapters
Do not add chapter markers (default) (Alias: --no-add-chapters)
.TP
--embed-info-json
Embed the infojson as an attachment to mkv/mka video files
.TP
--no-embed-info-json
Do not embed the infojson as an attachment to the video file
.TP
--parse-metadata \f[I][WHEN:]FROM:TO\f[R]
Parse additional metadata like title/artist from other fields; see
\[dq]MODIFYING METADATA\[dq] for details.
Supported values of \[dq]WHEN\[dq] are the same as that of
--use-postprocessor (default: pre_process)
.TP
--replace-in-metadata \f[I][WHEN:]FIELDS REGEX REPLACE\f[R]
Replace text in a metadata field using the given regex.
This option can be used multiple times.
Supported values of \[dq]WHEN\[dq] are the same as that of
--use-postprocessor (default: pre_process)
.TP
--xattrs
Write metadata to the video file\[aq]s xattrs (using Dublin Core and XDG
standards)
.TP
--concat-playlist \f[I]POLICY\f[R]
Concatenate videos in a playlist.
One of \[dq]never\[dq], \[dq]always\[dq], or \[dq]multi_video\[dq]
(default; only when the videos form a single show).
All the video files must have the same codecs and number of streams to
be concatenable.
The \[dq]pl_video:\[dq] prefix can be used with \[dq]--paths\[dq] and
\[dq]--output\[dq] to set the output filename for the concatenated
files.
See \[dq]OUTPUT TEMPLATE\[dq] for details
.TP
--fixup \f[I]POLICY\f[R]
Automatically correct known faults of the file.
One of never (do nothing), warn (only emit a warning), detect_or_warn
(the default; fix the file if we can, warn otherwise), force (try fixing
even if the file already exists)
.TP
--ffmpeg-location \f[I]PATH\f[R]
Location of the ffmpeg binary; either the path to the binary or its
containing directory
.TP
--exec \f[I][WHEN:]CMD\f[R]
Execute a command, optionally prefixed with when to execute it,
separated by a \[dq]:\[dq].
Supported values of \[dq]WHEN\[dq] are the same as that of
--use-postprocessor (default: after_move).
The same syntax as the output template can be used to pass any field as
arguments to the command.
If no fields are passed, %(filepath,_filename|)q is appended to the end
of the command.
This option can be used multiple times
.TP
--no-exec
Remove any previously defined --exec
.TP
--convert-subs \f[I]FORMAT\f[R]
Convert the subtitles to another format (currently supported: ass, lrc,
srt, vtt).
Use \[dq]--convert-subs none\[dq] to disable conversion (default)
(Alias: --convert- subtitles)
.TP
--convert-thumbnails \f[I]FORMAT\f[R]
Convert the thumbnails to another format (currently supported: jpg, png,
webp).
You can specify multiple rules using similar syntax as
\[dq]--remux-video\[dq].
Use \[dq]--convert- thumbnails none\[dq] to disable conversion (default)
.TP
--split-chapters
Split video into multiple files based on internal chapters.
The \[dq]chapter:\[dq] prefix can be used with \[dq]--paths\[dq] and
\[dq]--output\[dq] to set the output filename for the split files.
See \[dq]OUTPUT TEMPLATE\[dq] for details
.TP
--no-split-chapters
Do not split video based on chapters (default)
.TP
--remove-chapters \f[I]REGEX\f[R]
Remove chapters whose title matches the given regular expression.
The syntax is the same as --download-sections.
This option can be used multiple times
.TP
--no-remove-chapters
Do not remove any chapters from the file (default)
.TP
--force-keyframes-at-cuts
Force keyframes at cuts when downloading/splitting/removing sections.
This is slow due to needing a re-encode, but the resulting video may
have fewer artifacts around the cuts
.TP
--no-force-keyframes-at-cuts
Do not force keyframes around the chapters when cutting/splitting
(default)
.TP
--use-postprocessor \f[I]NAME[:ARGS]\f[R]
The (case-sensitive) name of plugin postprocessors to be enabled, and
(optionally) arguments to be passed to it, separated by a colon
\[dq]:\[dq].
ARGS are a semicolon \[dq];\[dq] delimited list of NAME=VALUE.
The \[dq]when\[dq] argument determines when the postprocessor is
invoked.
It can be one of \[dq]pre_process\[dq] (after video extraction),
\[dq]after_filter\[dq] (after video passes filter), \[dq]video\[dq]
(after --format; before --print/--output), \[dq]before_dl\[dq] (before
each video download), \[dq]post_process\[dq] (after each video download;
default), \[dq]after_move\[dq] (after moving the video file to its final
location), \[dq]after_video\[dq] (after downloading and processing all
formats of a video), or \[dq]playlist\[dq] (at end of playlist).
This option can be used multiple times to add different postprocessors
.SS SponsorBlock Options:
.PP
Make chapter entries for, or remove various segments (sponsor,
introductions, etc.)
from downloaded YouTube videos using the SponsorBlock
API (https://sponsor.ajay.app)
.TP
--sponsorblock-mark \f[I]CATS\f[R]
SponsorBlock categories to create chapters for, separated by commas.
Available categories are sponsor, intro, outro, selfpromo, preview,
filler, interaction, music_offtopic, poi_highlight, chapter, all and
default (=all).
You can prefix the category with a \[dq]-\[dq] to exclude it.
See [1] for descriptions of the categories.
E.g.
--sponsorblock-mark all,-preview [1]
https://wiki.sponsor.ajay.app/w/Segment_Categories
.TP
--sponsorblock-remove \f[I]CATS\f[R]
SponsorBlock categories to be removed from the video file, separated by
commas.
If a category is present in both mark and remove, remove takes
precedence.
The syntax and available categories are the same as for
--sponsorblock-mark except that \[dq]default\[dq] refers to
\[dq]all,-filler\[dq] and poi_highlight, chapter are not available
.TP
--sponsorblock-chapter-title \f[I]TEMPLATE\f[R]
An output template for the title of the SponsorBlock chapters created by
--sponsorblock-mark.
The only available fields are start_time, end_time, category,
categories, name, category_names.
Defaults to \[dq][SponsorBlock]: %(category_names)l\[dq]
.TP
--no-sponsorblock
Disable both --sponsorblock-mark and --sponsorblock-remove
.TP
--sponsorblock-api \f[I]URL\f[R]
SponsorBlock API location, defaults to https://sponsor.ajay.app
.SS Extractor Options:
.TP
--extractor-retries \f[I]RETRIES\f[R]
Number of retries for known extractor errors (default is 3), or
\[dq]infinite\[dq]
.TP
--allow-dynamic-mpd
Process dynamic DASH manifests (default) (Alias:
--no-ignore-dynamic-mpd)
.TP
--ignore-dynamic-mpd
Do not process dynamic DASH manifests (Alias: --no-allow-dynamic-mpd)
.TP
--hls-split-discontinuity
Split HLS playlists to different formats at discontinuities such as ad
breaks
.TP
--no-hls-split-discontinuity
Do not split HLS playlists into different formats at discontinuities
such as ad breaks (default)
.TP
--extractor-args \f[I]IE_KEY:ARGS\f[R]
Pass ARGS arguments to the IE_KEY extractor.
See \[dq]EXTRACTOR ARGUMENTS\[dq] for details.
You can use this option multiple times to give arguments for different
extractors
.SS Preset Aliases:
.PP
Predefined aliases for convenience and ease of use.
Note that future versions of yt-dlp may add or adjust presets, but the
existing preset names will not be changed or removed
.TP
-t \f[I]mp3\f[R]
-f \[aq]ba[acodec\[ha]=mp3]/ba/b\[aq] -x --audio-format mp3
.TP
-t \f[I]aac\f[R]
-f \[aq]ba[acodec\[ha]=aac]/ba[acodec\[ha]=mp4a.40.]/ba/b\[aq] -x
--audio-format aac
.TP
-t \f[I]mp4\f[R]
--merge-output-format mp4 --remux-video mp4 -S
vcodec:h264,lang,quality,res,fps,hdr:12,a codec:aac
.TP
-t \f[I]mkv\f[R]
--merge-output-format mkv --remux-video mkv
.TP
-t \f[I]sleep\f[R]
--sleep-subtitles 5 --sleep-requests 0.75 --sleep-interval 10
--max-sleep-interval 20
.SH CONFIGURATION
.PP
You can configure yt-dlp by placing any supported command line option in
a configuration file.
The configuration is loaded from the following locations:
.IP "1." 3
\f[B]Main Configuration\f[R]:
.RS 4
.IP \[bu] 2
The file given to \f[V]--config-location\f[R]
.RE
.IP "2." 3
\f[B]Portable Configuration\f[R]: (Recommended for portable
installations)
.RS 4
.IP \[bu] 2
If using a binary, \f[V]yt-dlp.conf\f[R] in the same directory as the
binary
.IP \[bu] 2
If running from source-code, \f[V]yt-dlp.conf\f[R] in the parent
directory of \f[V]yt_dlp\f[R]
.RE
.IP "3." 3
\f[B]Home Configuration\f[R]:
.RS 4
.IP \[bu] 2
\f[V]yt-dlp.conf\f[R] in the home path given to \f[V]-P\f[R]
.IP \[bu] 2
If \f[V]-P\f[R] is not given, the current directory is searched
.RE
.IP "4." 3
\f[B]User Configuration\f[R]:
.RS 4
.IP \[bu] 2
\f[V]${XDG_CONFIG_HOME}/yt-dlp.conf\f[R]
.IP \[bu] 2
\f[V]${XDG_CONFIG_HOME}/yt-dlp/config\f[R] (recommended on Linux/macOS)
.IP \[bu] 2
\f[V]${XDG_CONFIG_HOME}/yt-dlp/config.txt\f[R]
.IP \[bu] 2
\f[V]${APPDATA}/yt-dlp.conf\f[R]
.IP \[bu] 2
\f[V]${APPDATA}/yt-dlp/config\f[R] (recommended on Windows)
.IP \[bu] 2
\f[V]${APPDATA}/yt-dlp/config.txt\f[R]
.IP \[bu] 2
\f[V]\[ti]/yt-dlp.conf\f[R]
.IP \[bu] 2
\f[V]\[ti]/yt-dlp.conf.txt\f[R]
.IP \[bu] 2
\f[V]\[ti]/.yt-dlp/config\f[R]
.IP \[bu] 2
\f[V]\[ti]/.yt-dlp/config.txt\f[R]
See also: Notes about environment variables
.RE
.IP "5." 3
\f[B]System Configuration\f[R]:
.RS 4
.IP \[bu] 2
\f[V]/etc/yt-dlp.conf\f[R]
.IP \[bu] 2
\f[V]/etc/yt-dlp/config\f[R]
.IP \[bu] 2
\f[V]/etc/yt-dlp/config.txt\f[R]
.RE
.PP
E.g.
with the following configuration file, yt-dlp will always extract the
audio, not copy the mtime, use a proxy and save all videos under
\f[V]YouTube\f[R] directory in your home directory:
.IP
.nf
\f[C]
# Lines starting with # are comments

# Always extract audio
-x

# Do not copy the mtime
--no-mtime

# Use this proxy
--proxy 127.0.0.1:3128

# Save all videos under YouTube directory in your home directory
-o \[ti]/YouTube/%(title)s.%(ext)s
\f[R]
.fi
.PP
\f[B]Note\f[R]: Options in a configuration file are just the same
options aka switches used in regular command line calls; thus there
\f[B]must be no whitespace\f[R] after \f[V]-\f[R] or \f[V]--\f[R], e.g.
\f[V]-o\f[R] or \f[V]--proxy\f[R] but not \f[V]- o\f[R] or
\f[V]-- proxy\f[R].
They must also be quoted when necessary, as if it were a UNIX shell.
.PP
You can use \f[V]--ignore-config\f[R] if you want to disable all
configuration files for a particular yt-dlp run.
If \f[V]--ignore-config\f[R] is found inside any configuration file, no
further configuration will be loaded.
For example, having the option in the portable configuration file
prevents loading of home, user, and system configurations.
Additionally, (for backward compatibility) if \f[V]--ignore-config\f[R]
is found inside the system configuration file, the user configuration is
not loaded.
.SS Configuration file encoding
.PP
The configuration files are decoded according to the UTF BOM if present,
and in the encoding from system locale otherwise.
.PP
If you want your file to be decoded differently, add
\f[V]# coding: ENCODING\f[R] to the beginning of the file (e.g.
\f[V]# coding: shift-jis\f[R]).
There must be no characters before that, even spaces or BOM.
.SS Authentication with netrc
.PP
You may also want to configure automatic credentials storage for
extractors that support authentication (by providing login and password
with \f[V]--username\f[R] and \f[V]--password\f[R]) in order not to pass
credentials as command line arguments on every yt-dlp execution and
prevent tracking plain text passwords in the shell command history.
You can achieve this using a \f[V].netrc\f[R]
file (https://stackoverflow.com/tags/.netrc/info) on a per-extractor
basis.
For that, you will need to create a \f[V].netrc\f[R] file in
\f[V]--netrc-location\f[R] and restrict permissions to read/write by
only you:
.IP
.nf
\f[C]
touch ${HOME}/.netrc
chmod a-rwx,u+rw ${HOME}/.netrc
\f[R]
.fi
.PP
After that, you can add credentials for an extractor in the following
format, where \f[I]extractor\f[R] is the name of the extractor in
lowercase:
.IP
.nf
\f[C]
machine <extractor> login <username> password <password>
\f[R]
.fi
.PP
E.g.
.IP
.nf
\f[C]
machine youtube login myaccount\[at]gmail.com password my_youtube_password
machine twitch login my_twitch_account_name password my_twitch_password
\f[R]
.fi
.PP
To activate authentication with the \f[V].netrc\f[R] file you should
pass \f[V]--netrc\f[R] to yt-dlp or place it in the configuration file.
.PP
The default location of the .netrc file is \f[V]\[ti]\f[R] (see below).
.PP
As an alternative to using the \f[V].netrc\f[R] file, which has the
disadvantage of keeping your passwords in a plain text file, you can
configure a custom shell command to provide the credentials for an
extractor.
This is done by providing the \f[V]--netrc-cmd\f[R] parameter, it shall
output the credentials in the netrc format and return \f[V]0\f[R] on
success, other values will be treated as an error.
\f[V]{}\f[R] in the command will be replaced by the name of the
extractor to make it possible to select the credentials for the right
extractor.
.PP
E.g.
To use an encrypted \f[V].netrc\f[R] file stored as
\f[V].authinfo.gpg\f[R]
.IP
.nf
\f[C]
yt-dlp --netrc-cmd \[aq]gpg --decrypt \[ti]/.authinfo.gpg\[aq] \[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]
\f[R]
.fi
.SS Notes about environment variables
.IP \[bu] 2
Environment variables are normally specified as
\f[V]${VARIABLE}\f[R]/\f[V]$VARIABLE\f[R] on UNIX and
\f[V]%VARIABLE%\f[R] on Windows; but is always shown as
\f[V]${VARIABLE}\f[R] in this documentation
.IP \[bu] 2
yt-dlp also allows using UNIX-style variables on Windows for path-like
options; e.g.
\f[V]--output\f[R], \f[V]--config-location\f[R]
.IP \[bu] 2
If unset, \f[V]${XDG_CONFIG_HOME}\f[R] defaults to
\f[V]\[ti]/.config\f[R] and \f[V]${XDG_CACHE_HOME}\f[R] to
\f[V]\[ti]/.cache\f[R]
.IP \[bu] 2
On Windows, \f[V]\[ti]\f[R] points to \f[V]${HOME}\f[R] if present; or,
\f[V]${USERPROFILE}\f[R] or \f[V]${HOMEDRIVE}${HOMEPATH}\f[R] otherwise
.IP \[bu] 2
On Windows, \f[V]${USERPROFILE}\f[R] generally points to
\f[V]C:\[rs]Users\[rs]<user name>\f[R] and \f[V]${APPDATA}\f[R] to
\f[V]${USERPROFILE}\[rs]AppData\[rs]Roaming\f[R]
.SH OUTPUT TEMPLATE
.PP
The \f[V]-o\f[R] option is used to indicate a template for the output
file names while \f[V]-P\f[R] option is used to specify the path each
type of file should be saved to.
.PP
The simplest usage of \f[V]-o\f[R] is not to set any template arguments
when downloading a single file, like in
\f[V]yt-dlp -o funny_video.flv \[dq]https://some/video\[dq]\f[R]
(hard-coding file extension like this is \f[I]not\f[R] recommended and
could break some post-processing).
.PP
It may however also contain special sequences that will be replaced when
downloading each video.
The special sequences may be formatted according to Python string
formatting
operations (https://docs.python.org/3/library/stdtypes.html#printf-style-string-formatting),
e.g.
\f[V]%(NAME)s\f[R] or \f[V]%(NAME)05d\f[R].
To clarify, that is a percent symbol followed by a name in parentheses,
followed by formatting operations.
.PP
The field names themselves (the part inside the parenthesis) can also
have some special formatting:
.IP "1." 3
\f[B]Object traversal\f[R]: The dictionaries and lists available in
metadata can be traversed by using a dot \f[V].\f[R] separator; e.g.
\f[V]%(tags.0)s\f[R], \f[V]%(subtitles.en.-1.ext)s\f[R].
You can do Python slicing with colon \f[V]:\f[R]; E.g.
\f[V]%(id.3:7)s\f[R], \f[V]%(id.6:2:-1)s\f[R],
\f[V]%(formats.:.format_id)s\f[R].
Curly braces \f[V]{}\f[R] can be used to build dictionaries with only
specific keys; e.g.
\f[V]%(formats.:.{format_id,height})#j\f[R].
An empty field name \f[V]%()s\f[R] refers to the entire infodict; e.g.
\f[V]%(.{id,title})s\f[R].
Note that all the fields that become available using this method are not
listed below.
Use \f[V]-j\f[R] to see such fields
.IP "2." 3
\f[B]Arithmetic\f[R]: Simple arithmetic can be done on numeric fields
using \f[V]+\f[R], \f[V]-\f[R] and \f[V]*\f[R].
E.g.
\f[V]%(playlist_index+10)03d\f[R],
\f[V]%(n_entries+1-playlist_index)d\f[R]
.IP "3." 3
\f[B]Date/time Formatting\f[R]: Date/time fields can be formatted
according to strftime
formatting (https://docs.python.org/3/library/datetime.html#strftime-and-strptime-format-codes)
by specifying it separated from the field name using a \f[V]>\f[R].
E.g.
\f[V]%(duration>%H-%M-%S)s\f[R], \f[V]%(upload_date>%Y-%m-%d)s\f[R],
\f[V]%(epoch-3600>%H-%M-%S)s\f[R]
.IP "4." 3
\f[B]Alternatives\f[R]: Alternate fields can be specified separated with
a \f[V],\f[R].
E.g.
\f[V]%(release_date>%Y,upload_date>%Y|Unknown)s\f[R]
.IP "5." 3
\f[B]Replacement\f[R]: A replacement value can be specified using a
\f[V]&\f[R] separator according to the \f[V]str.format\f[R]
mini-language (https://docs.python.org/3/library/string.html#format-specification-mini-language).
If the field is \f[I]not\f[R] empty, this replacement value will be used
instead of the actual field content.
This is done after alternate fields are considered; thus the replacement
is used if \f[I]any\f[R] of the alternative fields is \f[I]not\f[R]
empty.
E.g.
\f[V]%(chapters&has chapters|no chapters)s\f[R],
\f[V]%(title&TITLE={:>20}|NO TITLE)s\f[R]
.IP "6." 3
\f[B]Default\f[R]: A literal default value can be specified for when the
field is empty using a \f[V]|\f[R] separator.
This overrides \f[V]--output-na-placeholder\f[R].
E.g.
\f[V]%(uploader|Unknown)s\f[R]
.IP "7." 3
\f[B]More Conversions\f[R]: In addition to the normal format types
\f[V]diouxXeEfFgGcrs\f[R], yt-dlp additionally supports converting to
\f[V]B\f[R] = \f[B]B\f[R]ytes, \f[V]j\f[R] = \f[B]j\f[R]son (flag
\f[V]#\f[R] for pretty-printing, \f[V]+\f[R] for Unicode), \f[V]h\f[R] =
HTML escaping, \f[V]l\f[R] = a comma separated \f[B]l\f[R]ist (flag
\f[V]#\f[R] for \f[V]\[rs]n\f[R] newline-separated), \f[V]q\f[R] = a
string \f[B]q\f[R]uoted for the terminal (flag \f[V]#\f[R] to split a
list into different arguments), \f[V]D\f[R] = add \f[B]D\f[R]ecimal
suffixes (e.g.
10M) (flag \f[V]#\f[R] to use 1024 as factor), and \f[V]S\f[R] =
\f[B]S\f[R]anitize as filename (flag \f[V]#\f[R] for restricted)
.IP "8." 3
\f[B]Unicode normalization\f[R]: The format type \f[V]U\f[R] can be used
for NFC Unicode
normalization (https://docs.python.org/3/library/unicodedata.html#unicodedata.normalize).
The alternate form flag (\f[V]#\f[R]) changes the normalization to NFD
and the conversion flag \f[V]+\f[R] can be used for NFKC/NFKD
compatibility equivalence normalization.
E.g.
\f[V]%(title)+.100U\f[R] is NFKC
.PP
To summarize, the general syntax for a field is:
.IP
.nf
\f[C]
%(name[.keys][addition][>strf][,alternate][&replacement][|default])[flags][width][.precision][length]type
\f[R]
.fi
.PP
Additionally, you can set different output templates for the various
metadata files separately from the general output template by specifying
the type of file followed by the template separated by a colon
\f[V]:\f[R].
The different file types supported are \f[V]subtitle\f[R],
\f[V]thumbnail\f[R], \f[V]description\f[R], \f[V]annotation\f[R]
(deprecated), \f[V]infojson\f[R], \f[V]link\f[R],
\f[V]pl_thumbnail\f[R], \f[V]pl_description\f[R], \f[V]pl_infojson\f[R],
\f[V]chapter\f[R], \f[V]pl_video\f[R].
E.g.
\f[V]-o \[dq]%(title)s.%(ext)s\[dq] -o \[dq]thumbnail:%(title)s\[rs]%(title)s.%(ext)s\[dq]\f[R]
will put the thumbnails in a folder with the same name as the video.
If any of the templates is empty, that type of file will not be written.
E.g.
\f[V]--write-thumbnail -o \[dq]thumbnail:\[dq]\f[R] will write
thumbnails only for playlists and not for video.
.PP
.PP
\f[B]Note\f[R]: Due to post-processing (i.e.
merging etc.), the actual output filename might differ.
Use \f[V]--print after_move:filepath\f[R] to get the name after all
post-processing is complete.
.PP
The available fields are:
.IP \[bu] 2
\f[V]id\f[R] (string): Video identifier
.IP \[bu] 2
\f[V]title\f[R] (string): Video title
.IP \[bu] 2
\f[V]fulltitle\f[R] (string): Video title ignoring live timestamp and
generic title
.IP \[bu] 2
\f[V]ext\f[R] (string): Video filename extension
.IP \[bu] 2
\f[V]alt_title\f[R] (string): A secondary title of the video
.IP \[bu] 2
\f[V]description\f[R] (string): The description of the video
.IP \[bu] 2
\f[V]display_id\f[R] (string): An alternative identifier for the video
.IP \[bu] 2
\f[V]uploader\f[R] (string): Full name of the video uploader
.IP \[bu] 2
\f[V]uploader_id\f[R] (string): Nickname or id of the video uploader
.IP \[bu] 2
\f[V]uploader_url\f[R] (string): URL to the video uploader\[aq]s profile
.IP \[bu] 2
\f[V]license\f[R] (string): License name the video is licensed under
.IP \[bu] 2
\f[V]creators\f[R] (list): The creators of the video
.IP \[bu] 2
\f[V]creator\f[R] (string): The creators of the video; comma-separated
.IP \[bu] 2
\f[V]timestamp\f[R] (numeric): UNIX timestamp of the moment the video
became available
.IP \[bu] 2
\f[V]upload_date\f[R] (string): Video upload date in UTC (YYYYMMDD)
.IP \[bu] 2
\f[V]release_timestamp\f[R] (numeric): UNIX timestamp of the moment the
video was released
.IP \[bu] 2
\f[V]release_date\f[R] (string): The date (YYYYMMDD) when the video was
released in UTC
.IP \[bu] 2
\f[V]release_year\f[R] (numeric): Year (YYYY) when the video or album
was released
.IP \[bu] 2
\f[V]modified_timestamp\f[R] (numeric): UNIX timestamp of the moment the
video was last modified
.IP \[bu] 2
\f[V]modified_date\f[R] (string): The date (YYYYMMDD) when the video was
last modified in UTC
.IP \[bu] 2
\f[V]channel\f[R] (string): Full name of the channel the video is
uploaded on
.IP \[bu] 2
\f[V]channel_id\f[R] (string): Id of the channel
.IP \[bu] 2
\f[V]channel_url\f[R] (string): URL of the channel
.IP \[bu] 2
\f[V]channel_follower_count\f[R] (numeric): Number of followers of the
channel
.IP \[bu] 2
\f[V]channel_is_verified\f[R] (boolean): Whether the channel is verified
on the platform
.IP \[bu] 2
\f[V]location\f[R] (string): Physical location where the video was
filmed
.IP \[bu] 2
\f[V]duration\f[R] (numeric): Length of the video in seconds
.IP \[bu] 2
\f[V]duration_string\f[R] (string): Length of the video (HH:mm:ss)
.IP \[bu] 2
\f[V]view_count\f[R] (numeric): How many users have watched the video on
the platform
.IP \[bu] 2
\f[V]concurrent_view_count\f[R] (numeric): How many users are currently
watching the video on the platform.
.IP \[bu] 2
\f[V]like_count\f[R] (numeric): Number of positive ratings of the video
.IP \[bu] 2
\f[V]dislike_count\f[R] (numeric): Number of negative ratings of the
video
.IP \[bu] 2
\f[V]repost_count\f[R] (numeric): Number of reposts of the video
.IP \[bu] 2
\f[V]average_rating\f[R] (numeric): Average rating given by users, the
scale used depends on the webpage
.IP \[bu] 2
\f[V]comment_count\f[R] (numeric): Number of comments on the video (For
some extractors, comments are only downloaded at the end, and so this
field cannot be used)
.IP \[bu] 2
\f[V]age_limit\f[R] (numeric): Age restriction for the video (years)
.IP \[bu] 2
\f[V]live_status\f[R] (string): One of \[dq]not_live\[dq],
\[dq]is_live\[dq], \[dq]is_upcoming\[dq], \[dq]was_live\[dq],
\[dq]post_live\[dq] (was live, but VOD is not yet processed)
.IP \[bu] 2
\f[V]is_live\f[R] (boolean): Whether this video is a live stream or a
fixed-length video
.IP \[bu] 2
\f[V]was_live\f[R] (boolean): Whether this video was originally a live
stream
.IP \[bu] 2
\f[V]playable_in_embed\f[R] (string): Whether this video is allowed to
play in embedded players on other sites
.IP \[bu] 2
\f[V]availability\f[R] (string): Whether the video is \[dq]private\[dq],
\[dq]premium_only\[dq], \[dq]subscriber_only\[dq], \[dq]needs_auth\[dq],
\[dq]unlisted\[dq] or \[dq]public\[dq]
.IP \[bu] 2
\f[V]media_type\f[R] (string): The type of media as classified by the
site, e.g.
\[dq]episode\[dq], \[dq]clip\[dq], \[dq]trailer\[dq]
.IP \[bu] 2
\f[V]start_time\f[R] (numeric): Time in seconds where the reproduction
should start, as specified in the URL
.IP \[bu] 2
\f[V]end_time\f[R] (numeric): Time in seconds where the reproduction
should end, as specified in the URL
.IP \[bu] 2
\f[V]extractor\f[R] (string): Name of the extractor
.IP \[bu] 2
\f[V]extractor_key\f[R] (string): Key name of the extractor
.IP \[bu] 2
\f[V]epoch\f[R] (numeric): Unix epoch of when the information extraction
was completed
.IP \[bu] 2
\f[V]autonumber\f[R] (numeric): Number that will be increased with each
download, starting at \f[V]--autonumber-start\f[R], padded with leading
zeros to 5 digits
.IP \[bu] 2
\f[V]video_autonumber\f[R] (numeric): Number that will be increased with
each video
.IP \[bu] 2
\f[V]n_entries\f[R] (numeric): Total number of extracted items in the
playlist
.IP \[bu] 2
\f[V]playlist_id\f[R] (string): Identifier of the playlist that contains
the video
.IP \[bu] 2
\f[V]playlist_title\f[R] (string): Name of the playlist that contains
the video
.IP \[bu] 2
\f[V]playlist\f[R] (string): \f[V]playlist_title\f[R] if available or
else \f[V]playlist_id\f[R]
.IP \[bu] 2
\f[V]playlist_count\f[R] (numeric): Total number of items in the
playlist.
May not be known if entire playlist is not extracted
.IP \[bu] 2
\f[V]playlist_index\f[R] (numeric): Index of the video in the playlist
padded with leading zeros according the final index
.IP \[bu] 2
\f[V]playlist_autonumber\f[R] (numeric): Position of the video in the
playlist download queue padded with leading zeros according to the total
length of the playlist
.IP \[bu] 2
\f[V]playlist_uploader\f[R] (string): Full name of the playlist uploader
.IP \[bu] 2
\f[V]playlist_uploader_id\f[R] (string): Nickname or id of the playlist
uploader
.IP \[bu] 2
\f[V]playlist_channel\f[R] (string): Display name of the channel that
uploaded the playlist
.IP \[bu] 2
\f[V]playlist_channel_id\f[R] (string): Identifier of the channel that
uploaded the playlist
.IP \[bu] 2
\f[V]playlist_webpage_url\f[R] (string): URL of the playlist webpage
.IP \[bu] 2
\f[V]webpage_url\f[R] (string): A URL to the video webpage which, if
given to yt-dlp, should yield the same result again
.IP \[bu] 2
\f[V]webpage_url_basename\f[R] (string): The basename of the webpage URL
.IP \[bu] 2
\f[V]webpage_url_domain\f[R] (string): The domain of the webpage URL
.IP \[bu] 2
\f[V]original_url\f[R] (string): The URL given by the user (or the same
as \f[V]webpage_url\f[R] for playlist entries)
.IP \[bu] 2
\f[V]categories\f[R] (list): List of categories the video belongs to
.IP \[bu] 2
\f[V]tags\f[R] (list): List of tags assigned to the video
.IP \[bu] 2
\f[V]cast\f[R] (list): List of cast members
.PP
All the fields in Filtering Formats can also be used
.PP
Available for the video that belongs to some logical chapter or section:
.IP \[bu] 2
\f[V]chapter\f[R] (string): Name or title of the chapter the video
belongs to
.IP \[bu] 2
\f[V]chapter_number\f[R] (numeric): Number of the chapter the video
belongs to
.IP \[bu] 2
\f[V]chapter_id\f[R] (string): Id of the chapter the video belongs to
.PP
Available for the video that is an episode of some series or program:
.IP \[bu] 2
\f[V]series\f[R] (string): Title of the series or program the video
episode belongs to
.IP \[bu] 2
\f[V]series_id\f[R] (string): Id of the series or program the video
episode belongs to
.IP \[bu] 2
\f[V]season\f[R] (string): Title of the season the video episode belongs
to
.IP \[bu] 2
\f[V]season_number\f[R] (numeric): Number of the season the video
episode belongs to
.IP \[bu] 2
\f[V]season_id\f[R] (string): Id of the season the video episode belongs
to
.IP \[bu] 2
\f[V]episode\f[R] (string): Title of the video episode
.IP \[bu] 2
\f[V]episode_number\f[R] (numeric): Number of the video episode within a
season
.IP \[bu] 2
\f[V]episode_id\f[R] (string): Id of the video episode
.PP
Available for the media that is a track or a part of a music album:
.IP \[bu] 2
\f[V]track\f[R] (string): Title of the track
.IP \[bu] 2
\f[V]track_number\f[R] (numeric): Number of the track within an album or
a disc
.IP \[bu] 2
\f[V]track_id\f[R] (string): Id of the track
.IP \[bu] 2
\f[V]artists\f[R] (list): Artist(s) of the track
.IP \[bu] 2
\f[V]artist\f[R] (string): Artist(s) of the track; comma-separated
.IP \[bu] 2
\f[V]genres\f[R] (list): Genre(s) of the track
.IP \[bu] 2
\f[V]genre\f[R] (string): Genre(s) of the track; comma-separated
.IP \[bu] 2
\f[V]composers\f[R] (list): Composer(s) of the piece
.IP \[bu] 2
\f[V]composer\f[R] (string): Composer(s) of the piece; comma-separated
.IP \[bu] 2
\f[V]album\f[R] (string): Title of the album the track belongs to
.IP \[bu] 2
\f[V]album_type\f[R] (string): Type of the album
.IP \[bu] 2
\f[V]album_artists\f[R] (list): All artists appeared on the album
.IP \[bu] 2
\f[V]album_artist\f[R] (string): All artists appeared on the album;
comma-separated
.IP \[bu] 2
\f[V]disc_number\f[R] (numeric): Number of the disc or other physical
medium the track belongs to
.PP
Available only when using \f[V]--download-sections\f[R] and for
\f[V]chapter:\f[R] prefix when using \f[V]--split-chapters\f[R] for
videos with internal chapters:
.IP \[bu] 2
\f[V]section_title\f[R] (string): Title of the chapter
.IP \[bu] 2
\f[V]section_number\f[R] (numeric): Number of the chapter within the
file
.IP \[bu] 2
\f[V]section_start\f[R] (numeric): Start time of the chapter in seconds
.IP \[bu] 2
\f[V]section_end\f[R] (numeric): End time of the chapter in seconds
.PP
Available only when used in \f[V]--print\f[R]:
.IP \[bu] 2
\f[V]urls\f[R] (string): The URLs of all requested formats, one in each
line
.IP \[bu] 2
\f[V]filename\f[R] (string): Name of the video file.
Note that the actual filename may differ
.IP \[bu] 2
\f[V]formats_table\f[R] (table): The video format table as printed by
\f[V]--list-formats\f[R]
.IP \[bu] 2
\f[V]thumbnails_table\f[R] (table): The thumbnail format table as
printed by \f[V]--list-thumbnails\f[R]
.IP \[bu] 2
\f[V]subtitles_table\f[R] (table): The subtitle format table as printed
by \f[V]--list-subs\f[R]
.IP \[bu] 2
\f[V]automatic_captions_table\f[R] (table): The automatic subtitle
format table as printed by \f[V]--list-subs\f[R]
.PP
Available only after the video is downloaded
(\f[V]post_process\f[R]/\f[V]after_move\f[R]):
.IP \[bu] 2
\f[V]filepath\f[R]: Actual path of downloaded video file
.PP
Available only in \f[V]--sponsorblock-chapter-title\f[R]:
.IP \[bu] 2
\f[V]start_time\f[R] (numeric): Start time of the chapter in seconds
.IP \[bu] 2
\f[V]end_time\f[R] (numeric): End time of the chapter in seconds
.IP \[bu] 2
\f[V]categories\f[R] (list): The SponsorBlock
categories (https://wiki.sponsor.ajay.app/w/Types#Category) the chapter
belongs to
.IP \[bu] 2
\f[V]category\f[R] (string): The smallest SponsorBlock category the
chapter belongs to
.IP \[bu] 2
\f[V]category_names\f[R] (list): Friendly names of the categories
.IP \[bu] 2
\f[V]name\f[R] (string): Friendly name of the smallest category
.IP \[bu] 2
\f[V]type\f[R] (string): The SponsorBlock action
type (https://wiki.sponsor.ajay.app/w/Types#Action_Type) of the chapter
.PP
Each aforementioned sequence when referenced in an output template will
be replaced by the actual value corresponding to the sequence name.
E.g.
for \f[V]-o %(title)s-%(id)s.%(ext)s\f[R] and an mp4 video with title
\f[V]yt-dlp test video\f[R] and id \f[V]BaW_jenozKc\f[R], this will
result in a \f[V]yt-dlp test video-BaW_jenozKc.mp4\f[R] file created in
the current directory.
.PP
\f[B]Note\f[R]: Some of the sequences are not guaranteed to be present,
since they depend on the metadata obtained by a particular extractor.
Such sequences will be replaced with placeholder value provided with
\f[V]--output-na-placeholder\f[R] (\f[V]NA\f[R] by default).
.PP
\f[B]Tip\f[R]: Look at the \f[V]-j\f[R] output to identify which fields
are available for the particular URL
.PP
For numeric sequences, you can use numeric related
formatting (https://docs.python.org/3/library/stdtypes.html#printf-style-string-formatting);
e.g.
\f[V]%(view_count)05d\f[R] will result in a string with view count
padded with zeros up to 5 characters, like in \f[V]00042\f[R].
.PP
Output templates can also contain arbitrary hierarchical path, e.g.
\f[V]-o \[dq]%(playlist)s/%(playlist_index)s - %(title)s.%(ext)s\[dq]\f[R]
which will result in downloading each video in a directory corresponding
to this path template.
Any missing directory will be automatically created for you.
.PP
To use percent literals in an output template use \f[V]%%\f[R].
To output to stdout use \f[V]-o -\f[R].
.PP
The current default template is \f[V]%(title)s [%(id)s].%(ext)s\f[R].
.PP
In some cases, you don\[aq]t want special characters such as 中, spaces,
or &, such as when transferring the downloaded filename to a Windows
system or the filename through an 8bit-unsafe channel.
In these cases, add the \f[V]--restrict-filenames\f[R] flag to get a
shorter title.
.SS Output template examples
.IP
.nf
\f[C]
$ yt-dlp --print filename -o \[dq]test video.%(ext)s\[dq] BaW_jenozKc
test video.webm    # Literal name with correct extension

$ yt-dlp --print filename -o \[dq]%(title)s.%(ext)s\[dq] BaW_jenozKc
youtube-dl test video \[aq]\[aq]_ä↭𝕐.webm    # All kinds of weird characters

$ yt-dlp --print filename -o \[dq]%(title)s.%(ext)s\[dq] BaW_jenozKc --restrict-filenames
youtube-dl_test_video_.webm    # Restricted file name

# Download YouTube playlist videos in separate directory indexed by video order in a playlist
$ yt-dlp -o \[dq]%(playlist)s/%(playlist_index)s - %(title)s.%(ext)s\[dq] \[dq]https://www.youtube.com/playlist?list=PLwiyx1dc3P2JR9N8gQaQN_BCvlSlap7re\[dq]

# Download YouTube playlist videos in separate directories according to their uploaded year
$ yt-dlp -o \[dq]%(upload_date>%Y)s/%(title)s.%(ext)s\[dq] \[dq]https://www.youtube.com/playlist?list=PLwiyx1dc3P2JR9N8gQaQN_BCvlSlap7re\[dq]

# Prefix playlist index with \[dq] - \[dq] separator, but only if it is available
$ yt-dlp -o \[dq]%(playlist_index&{} - |)s%(title)s.%(ext)s\[dq] BaW_jenozKc \[dq]https://www.youtube.com/user/TheLinuxFoundation/playlists\[dq]

# Download all playlists of YouTube channel/user keeping each playlist in separate directory:
$ yt-dlp -o \[dq]%(uploader)s/%(playlist)s/%(playlist_index)s - %(title)s.%(ext)s\[dq] \[dq]https://www.youtube.com/user/TheLinuxFoundation/playlists\[dq]

# Download Udemy course keeping each chapter in separate directory under MyVideos directory in your home
$ yt-dlp -u user -p password -P \[dq]\[ti]/MyVideos\[dq] -o \[dq]%(playlist)s/%(chapter_number)s - %(chapter)s/%(title)s.%(ext)s\[dq] \[dq]https://www.udemy.com/java-tutorial\[dq]

# Download entire series season keeping each series and each season in separate directory under C:/MyVideos
$ yt-dlp -P \[dq]C:/MyVideos\[dq] -o \[dq]%(series)s/%(season_number)s - %(season)s/%(episode_number)s - %(episode)s.%(ext)s\[dq] \[dq]https://videomore.ru/kino_v_detalayah/5_sezon/367617\[dq]

# Download video as \[dq]C:\[rs]MyVideos\[rs]uploader\[rs]title.ext\[dq], subtitles as \[dq]C:\[rs]MyVideos\[rs]subs\[rs]uploader\[rs]title.ext\[dq]
# and put all temporary files in \[dq]C:\[rs]MyVideos\[rs]tmp\[dq]
$ yt-dlp -P \[dq]C:/MyVideos\[dq] -P \[dq]temp:tmp\[dq] -P \[dq]subtitle:subs\[dq] -o \[dq]%(uploader)s/%(title)s.%(ext)s\[dq] BaW_jenozKc --write-subs

# Download video as \[dq]C:\[rs]MyVideos\[rs]uploader\[rs]title.ext\[dq] and subtitles as \[dq]C:\[rs]MyVideos\[rs]uploader\[rs]subs\[rs]title.ext\[dq]
$ yt-dlp -P \[dq]C:/MyVideos\[dq] -o \[dq]%(uploader)s/%(title)s.%(ext)s\[dq] -o \[dq]subtitle:%(uploader)s/subs/%(title)s.%(ext)s\[dq] BaW_jenozKc --write-subs

# Stream the video being downloaded to stdout
$ yt-dlp -o - BaW_jenozKc
\f[R]
.fi
.SH FORMAT SELECTION
.PP
By default, yt-dlp tries to download the best available quality if you
\f[B]don\[aq]t\f[R] pass any options.
This is generally equivalent to using
\f[V]-f bestvideo*+bestaudio/best\f[R].
However, if multiple audiostreams is enabled
(\f[V]--audio-multistreams\f[R]), the default format changes to
\f[V]-f bestvideo+bestaudio/best\f[R].
Similarly, if ffmpeg is unavailable, or if you use yt-dlp to stream to
\f[V]stdout\f[R] (\f[V]-o -\f[R]), the default becomes
\f[V]-f best/bestvideo+bestaudio\f[R].
.PP
\f[B]Deprecation warning\f[R]: Latest versions of yt-dlp can stream
multiple formats to the stdout simultaneously using ffmpeg.
So, in future versions, the default for this will be set to
\f[V]-f bv*+ba/b\f[R] similar to normal downloads.
If you want to preserve the \f[V]-f b/bv+ba\f[R] setting, it is
recommended to explicitly specify it in the configuration options.
.PP
The general syntax for format selection is \f[V]-f FORMAT\f[R] (or
\f[V]--format FORMAT\f[R]) where \f[V]FORMAT\f[R] is a \f[I]selector
expression\f[R], i.e.
an expression that describes format or formats you would like to
download.
.PP
The simplest case is requesting a specific format; e.g.
with \f[V]-f 22\f[R] you can download the format with format code equal
to 22.
You can get the list of available format codes for particular video
using \f[V]--list-formats\f[R] or \f[V]-F\f[R].
Note that these format codes are extractor specific.
.PP
You can also use a file extension (currently \f[V]3gp\f[R],
\f[V]aac\f[R], \f[V]flv\f[R], \f[V]m4a\f[R], \f[V]mp3\f[R],
\f[V]mp4\f[R], \f[V]ogg\f[R], \f[V]wav\f[R], \f[V]webm\f[R] are
supported) to download the best quality format of a particular file
extension served as a single file, e.g.
\f[V]-f webm\f[R] will download the best quality format with the
\f[V]webm\f[R] extension served as a single file.
.PP
You can use \f[V]-f -\f[R] to interactively provide the format selector
\f[I]for each video\f[R]
.PP
You can also use special names to select particular edge case formats:
.IP \[bu] 2
\f[V]all\f[R]: Select \f[B]all formats\f[R] separately
.IP \[bu] 2
\f[V]mergeall\f[R]: Select and \f[B]merge all formats\f[R] (Must be used
with \f[V]--audio-multistreams\f[R], \f[V]--video-multistreams\f[R] or
both)
.IP \[bu] 2
\f[V]b*\f[R], \f[V]best*\f[R]: Select the best quality format that
\f[B]contains either\f[R] a video or an audio or both (i.e.;
\f[V]vcodec!=none or acodec!=none\f[R])
.IP \[bu] 2
\f[V]b\f[R], \f[V]best\f[R]: Select the best quality format that
\f[B]contains both\f[R] video and audio.
Equivalent to \f[V]best*[vcodec!=none][acodec!=none]\f[R]
.IP \[bu] 2
\f[V]bv\f[R], \f[V]bestvideo\f[R]: Select the best quality
\f[B]video-only\f[R] format.
Equivalent to \f[V]best*[acodec=none]\f[R]
.IP \[bu] 2
\f[V]bv*\f[R], \f[V]bestvideo*\f[R]: Select the best quality format that
\f[B]contains video\f[R].
It may also contain audio.
Equivalent to \f[V]best*[vcodec!=none]\f[R]
.IP \[bu] 2
\f[V]ba\f[R], \f[V]bestaudio\f[R]: Select the best quality
\f[B]audio-only\f[R] format.
Equivalent to \f[V]best*[vcodec=none]\f[R]
.IP \[bu] 2
\f[V]ba*\f[R], \f[V]bestaudio*\f[R]: Select the best quality format that
\f[B]contains audio\f[R].
It may also contain video.
Equivalent to \f[V]best*[acodec!=none]\f[R] (Do not
use! (https://github.com/yt-dlp/yt-dlp/issues/979#issuecomment-919629354))
.IP \[bu] 2
\f[V]w*\f[R], \f[V]worst*\f[R]: Select the worst quality format that
contains either a video or an audio
.IP \[bu] 2
\f[V]w\f[R], \f[V]worst\f[R]: Select the worst quality format that
contains both video and audio.
Equivalent to \f[V]worst*[vcodec!=none][acodec!=none]\f[R]
.IP \[bu] 2
\f[V]wv\f[R], \f[V]worstvideo\f[R]: Select the worst quality video-only
format.
Equivalent to \f[V]worst*[acodec=none]\f[R]
.IP \[bu] 2
\f[V]wv*\f[R], \f[V]worstvideo*\f[R]: Select the worst quality format
that contains video.
It may also contain audio.
Equivalent to \f[V]worst*[vcodec!=none]\f[R]
.IP \[bu] 2
\f[V]wa\f[R], \f[V]worstaudio\f[R]: Select the worst quality audio-only
format.
Equivalent to \f[V]worst*[vcodec=none]\f[R]
.IP \[bu] 2
\f[V]wa*\f[R], \f[V]worstaudio*\f[R]: Select the worst quality format
that contains audio.
It may also contain video.
Equivalent to \f[V]worst*[acodec!=none]\f[R]
.PP
For example, to download the worst quality video-only format you can use
\f[V]-f worstvideo\f[R].
It is, however, recommended not to use \f[V]worst\f[R] and related
options.
When your format selector is \f[V]worst\f[R], the format which is worst
in all respects is selected.
Most of the time, what you actually want is the video with the smallest
filesize instead.
So it is generally better to use \f[V]-S +size\f[R] or more rigorously,
\f[V]-S +size,+br,+res,+fps\f[R] instead of \f[V]-f worst\f[R].
See Sorting Formats for more details.
.PP
You can select the n\[aq]th best format of a type by using
\f[V]best<type>.<n>\f[R].
For example, \f[V]best.2\f[R] will select the 2nd best combined format.
Similarly, \f[V]bv*.3\f[R] will select the 3rd best format that contains
a video stream.
.PP
If you want to download multiple videos, and they don\[aq]t have the
same formats available, you can specify the order of preference using
slashes.
Note that formats on the left hand side are preferred; e.g.
\f[V]-f 22/17/18\f[R] will download format 22 if it\[aq]s available,
otherwise it will download format 17 if it\[aq]s available, otherwise it
will download format 18 if it\[aq]s available, otherwise it will
complain that no suitable formats are available for download.
.PP
If you want to download several formats of the same video use a comma as
a separator, e.g.
\f[V]-f 22,17,18\f[R] will download all these three formats, of course
if they are available.
Or a more sophisticated example combined with the precedence feature:
\f[V]-f 136/137/mp4/bestvideo,140/m4a/bestaudio\f[R].
.PP
You can merge the video and audio of multiple formats into a single file
using \f[V]-f <format1>+<format2>+...\f[R] (requires ffmpeg installed);
e.g.
\f[V]-f bestvideo+bestaudio\f[R] will download the best video-only
format, the best audio-only format and mux them together with ffmpeg.
.PP
\f[B]Deprecation warning\f[R]: Since the \f[I]below\f[R] described
behavior is complex and counter-intuitive, this will be removed and
multistreams will be enabled by default in the future.
A new operator will be instead added to limit formats to single
audio/video
.PP
Unless \f[V]--video-multistreams\f[R] is used, all formats with a video
stream except the first one are ignored.
Similarly, unless \f[V]--audio-multistreams\f[R] is used, all formats
with an audio stream except the first one are ignored.
E.g.
\f[V]-f bestvideo+best+bestaudio --video-multistreams --audio-multistreams\f[R]
will download and merge all 3 given formats.
The resulting file will have 2 video streams and 2 audio streams.
But \f[V]-f bestvideo+best+bestaudio --no-video-multistreams\f[R] will
download and merge only \f[V]bestvideo\f[R] and \f[V]bestaudio\f[R].
\f[V]best\f[R] is ignored since another format containing a video stream
(\f[V]bestvideo\f[R]) has already been selected.
The order of the formats is therefore important.
\f[V]-f best+bestaudio --no-audio-multistreams\f[R] will download only
\f[V]best\f[R] while \f[V]-f bestaudio+best --no-audio-multistreams\f[R]
will ignore \f[V]best\f[R] and download only \f[V]bestaudio\f[R].
.SS Filtering Formats
.PP
You can also filter the video formats by putting a condition in
brackets, as in \f[V]-f \[dq]best[height=720]\[dq]\f[R] (or
\f[V]-f \[dq][filesize>10M]\[dq]\f[R] since filters without a selector
are interpreted as \f[V]best\f[R]).
.PP
The following numeric meta fields can be used with comparisons
\f[V]<\f[R], \f[V]<=\f[R], \f[V]>\f[R], \f[V]>=\f[R], \f[V]=\f[R]
(equals), \f[V]!=\f[R] (not equals):
.IP \[bu] 2
\f[V]filesize\f[R]: The number of bytes, if known in advance
.IP \[bu] 2
\f[V]filesize_approx\f[R]: An estimate for the number of bytes
.IP \[bu] 2
\f[V]width\f[R]: Width of the video, if known
.IP \[bu] 2
\f[V]height\f[R]: Height of the video, if known
.IP \[bu] 2
\f[V]aspect_ratio\f[R]: Aspect ratio of the video, if known
.IP \[bu] 2
\f[V]tbr\f[R]: Average bitrate of audio and video in kbps
.IP \[bu] 2
\f[V]abr\f[R]: Average audio bitrate in kbps
.IP \[bu] 2
\f[V]vbr\f[R]: Average video bitrate in kbps
.IP \[bu] 2
\f[V]asr\f[R]: Audio sampling rate in Hertz
.IP \[bu] 2
\f[V]fps\f[R]: Frame rate
.IP \[bu] 2
\f[V]audio_channels\f[R]: The number of audio channels
.IP \[bu] 2
\f[V]stretched_ratio\f[R]: \f[V]width:height\f[R] of the video\[aq]s
pixels, if not square
.PP
Also filtering work for comparisons \f[V]=\f[R] (equals),
\f[V]\[ha]=\f[R] (starts with), \f[V]$=\f[R] (ends with), \f[V]*=\f[R]
(contains), \f[V]\[ti]=\f[R] (matches regex) and following string meta
fields:
.IP \[bu] 2
\f[V]url\f[R]: Video URL
.IP \[bu] 2
\f[V]ext\f[R]: File extension
.IP \[bu] 2
\f[V]acodec\f[R]: Name of the audio codec in use
.IP \[bu] 2
\f[V]vcodec\f[R]: Name of the video codec in use
.IP \[bu] 2
\f[V]container\f[R]: Name of the container format
.IP \[bu] 2
\f[V]protocol\f[R]: The protocol that will be used for the actual
download, lower-case (\f[V]http\f[R], \f[V]https\f[R], \f[V]rtsp\f[R],
\f[V]rtmp\f[R], \f[V]rtmpe\f[R], \f[V]mms\f[R], \f[V]f4m\f[R],
\f[V]ism\f[R], \f[V]http_dash_segments\f[R], \f[V]m3u8\f[R], or
\f[V]m3u8_native\f[R])
.IP \[bu] 2
\f[V]language\f[R]: Language code
.IP \[bu] 2
\f[V]dynamic_range\f[R]: The dynamic range of the video
.IP \[bu] 2
\f[V]format_id\f[R]: A short description of the format
.IP \[bu] 2
\f[V]format\f[R]: A human-readable description of the format
.IP \[bu] 2
\f[V]format_note\f[R]: Additional info about the format
.IP \[bu] 2
\f[V]resolution\f[R]: Textual description of width and height
.PP
Any string comparison may be prefixed with negation \f[V]!\f[R] in order
to produce an opposite comparison, e.g.
\f[V]!*=\f[R] (does not contain).
The comparand of a string comparison needs to be quoted with either
double or single quotes if it contains spaces or special characters
other than \f[V]._-\f[R].
.PP
\f[B]Note\f[R]: None of the aforementioned meta fields are guaranteed to
be present since this solely depends on the metadata obtained by the
particular extractor, i.e.
the metadata offered by the website.
Any other field made available by the extractor can also be used for
filtering.
.PP
Formats for which the value is not known are excluded unless you put a
question mark (\f[V]?\f[R]) after the operator.
You can combine format filters, so
\f[V]-f \[dq]bv[height<=?720][tbr>500]\[dq]\f[R] selects up to 720p
videos (or videos where the height is not known) with a bitrate of at
least 500 kbps.
You can also use the filters with \f[V]all\f[R] to download all formats
that satisfy the filter, e.g.
\f[V]-f \[dq]all[vcodec=none]\[dq]\f[R] selects all audio-only formats.
.PP
Format selectors can also be grouped using parentheses; e.g.
\f[V]-f \[dq](mp4,webm)[height<480]\[dq]\f[R] will download the best
pre-merged mp4 and webm formats with a height lower than 480.
.SS Sorting Formats
.PP
You can change the criteria for being considered the \f[V]best\f[R] by
using \f[V]-S\f[R] (\f[V]--format-sort\f[R]).
The general format for this is \f[V]--format-sort field1,field2...\f[R].
.PP
The available fields are:
.IP \[bu] 2
\f[V]hasvid\f[R]: Gives priority to formats that have a video stream
.IP \[bu] 2
\f[V]hasaud\f[R]: Gives priority to formats that have an audio stream
.IP \[bu] 2
\f[V]ie_pref\f[R]: The format preference
.IP \[bu] 2
\f[V]lang\f[R]: The language preference as determined by the extractor
(e.g.
original language preferred over audio description)
.IP \[bu] 2
\f[V]quality\f[R]: The quality of the format
.IP \[bu] 2
\f[V]source\f[R]: The preference of the source
.IP \[bu] 2
\f[V]proto\f[R]: Protocol used for download
(\f[V]https\f[R]/\f[V]ftps\f[R] > \f[V]http\f[R]/\f[V]ftp\f[R] >
\f[V]m3u8_native\f[R]/\f[V]m3u8\f[R] > \f[V]http_dash_segments\f[R]>
\f[V]websocket_frag\f[R] > \f[V]mms\f[R]/\f[V]rtsp\f[R] >
\f[V]f4f\f[R]/\f[V]f4m\f[R])
.IP \[bu] 2
\f[V]vcodec\f[R]: Video Codec (\f[V]av01\f[R] > \f[V]vp9.2\f[R] >
\f[V]vp9\f[R] > \f[V]h265\f[R] > \f[V]h264\f[R] > \f[V]vp8\f[R] >
\f[V]h263\f[R] > \f[V]theora\f[R] > other)
.IP \[bu] 2
\f[V]acodec\f[R]: Audio Codec (\f[V]flac\f[R]/\f[V]alac\f[R] >
\f[V]wav\f[R]/\f[V]aiff\f[R] > \f[V]opus\f[R] > \f[V]vorbis\f[R] >
\f[V]aac\f[R] > \f[V]mp4a\f[R] > \f[V]mp3\f[R] > \f[V]ac4\f[R] >
\f[V]eac3\f[R] > \f[V]ac3\f[R] > \f[V]dts\f[R] > other)
.IP \[bu] 2
\f[V]codec\f[R]: Equivalent to \f[V]vcodec,acodec\f[R]
.IP \[bu] 2
\f[V]vext\f[R]: Video Extension (\f[V]mp4\f[R] > \f[V]mov\f[R] >
\f[V]webm\f[R] > \f[V]flv\f[R] > other).
If \f[V]--prefer-free-formats\f[R] is used, \f[V]webm\f[R] is preferred.
.IP \[bu] 2
\f[V]aext\f[R]: Audio Extension (\f[V]m4a\f[R] > \f[V]aac\f[R] >
\f[V]mp3\f[R] > \f[V]ogg\f[R] > \f[V]opus\f[R] > \f[V]webm\f[R] >
other).
If \f[V]--prefer-free-formats\f[R] is used, the order changes to
\f[V]ogg\f[R] > \f[V]opus\f[R] > \f[V]webm\f[R] > \f[V]mp3\f[R] >
\f[V]m4a\f[R] > \f[V]aac\f[R]
.IP \[bu] 2
\f[V]ext\f[R]: Equivalent to \f[V]vext,aext\f[R]
.IP \[bu] 2
\f[V]filesize\f[R]: Exact filesize, if known in advance
.IP \[bu] 2
\f[V]fs_approx\f[R]: Approximate filesize
.IP \[bu] 2
\f[V]size\f[R]: Exact filesize if available, otherwise approximate
filesize
.IP \[bu] 2
\f[V]height\f[R]: Height of video
.IP \[bu] 2
\f[V]width\f[R]: Width of video
.IP \[bu] 2
\f[V]res\f[R]: Video resolution, calculated as the smallest dimension.
.IP \[bu] 2
\f[V]fps\f[R]: Framerate of video
.IP \[bu] 2
\f[V]hdr\f[R]: The dynamic range of the video (\f[V]DV\f[R] >
\f[V]HDR12\f[R] > \f[V]HDR10+\f[R] > \f[V]HDR10\f[R] > \f[V]HLG\f[R] >
\f[V]SDR\f[R])
.IP \[bu] 2
\f[V]channels\f[R]: The number of audio channels
.IP \[bu] 2
\f[V]tbr\f[R]: Total average bitrate in kbps
.IP \[bu] 2
\f[V]vbr\f[R]: Average video bitrate in kbps
.IP \[bu] 2
\f[V]abr\f[R]: Average audio bitrate in kbps
.IP \[bu] 2
\f[V]br\f[R]: Average bitrate in kbps,
\f[V]tbr\f[R]/\f[V]vbr\f[R]/\f[V]abr\f[R]
.IP \[bu] 2
\f[V]asr\f[R]: Audio sample rate in Hz
.PP
\f[B]Deprecation warning\f[R]: Many of these fields have (currently
undocumented) aliases, that may be removed in a future version.
It is recommended to use only the documented field names.
.PP
All fields, unless specified otherwise, are sorted in descending order.
To reverse this, prefix the field with a \f[V]+\f[R].
E.g.
\f[V]+res\f[R] prefers format with the smallest resolution.
Additionally, you can suffix a preferred value for the fields, separated
by a \f[V]:\f[R].
E.g.
\f[V]res:720\f[R] prefers larger videos, but no larger than 720p and the
smallest video if there are no videos less than 720p.
For \f[V]codec\f[R] and \f[V]ext\f[R], you can provide two preferred
values, the first for video and the second for audio.
E.g.
\f[V]+codec:avc:m4a\f[R] (equivalent to
\f[V]+vcodec:avc,+acodec:m4a\f[R]) sets the video codec preference to
\f[V]h264\f[R] > \f[V]h265\f[R] > \f[V]vp9\f[R] > \f[V]vp9.2\f[R] >
\f[V]av01\f[R] > \f[V]vp8\f[R] > \f[V]h263\f[R] > \f[V]theora\f[R] and
audio codec preference to \f[V]mp4a\f[R] > \f[V]aac\f[R] >
\f[V]vorbis\f[R] > \f[V]opus\f[R] > \f[V]mp3\f[R] > \f[V]ac3\f[R] >
\f[V]dts\f[R].
You can also make the sorting prefer the nearest values to the provided
by using \f[V]\[ti]\f[R] as the delimiter.
E.g.
\f[V]filesize\[ti]1G\f[R] prefers the format with filesize closest to 1
GiB.
.PP
The fields \f[V]hasvid\f[R] and \f[V]ie_pref\f[R] are always given
highest priority in sorting, irrespective of the user-defined order.
This behavior can be changed by using \f[V]--format-sort-force\f[R].
Apart from these, the default order used is:
\f[V]lang,quality,res,fps,hdr:12,vcodec,channels,acodec,size,br,asr,proto,ext,hasaud,source,id\f[R].
The extractors may override this default order, but they cannot override
the user-provided order.
.PP
Note that the default for hdr is \f[V]hdr:12\f[R]; i.e.
Dolby Vision is not preferred.
This choice was made since DV formats are not yet fully compatible with
most devices.
This may be changed in the future.
.PP
If your format selector is \f[V]worst\f[R], the last item is selected
after sorting.
This means it will select the format that is worst in all respects.
Most of the time, what you actually want is the video with the smallest
filesize instead.
So it is generally better to use
\f[V]-f best -S +size,+br,+res,+fps\f[R].
.PP
\f[B]Tip\f[R]: You can use the \f[V]-v -F\f[R] to see how the formats
have been sorted (worst to best).
.SS Format Selection examples
.IP
.nf
\f[C]
# Download and merge the best video-only format and the best audio-only format,
# or download the best combined format if video-only format is not available
$ yt-dlp -f \[dq]bv+ba/b\[dq]

# Download best format that contains video,
# and if it doesn\[aq]t already have an audio stream, merge it with best audio-only format
$ yt-dlp -f \[dq]bv*+ba/b\[dq]

# Same as above
$ yt-dlp

# Download the best video-only format and the best audio-only format without merging them
# For this case, an output template should be used since
# by default, bestvideo and bestaudio will have the same file name.
$ yt-dlp -f \[dq]bv,ba\[dq] -o \[dq]%(title)s.f%(format_id)s.%(ext)s\[dq]

# Download and merge the best format that has a video stream,
# and all audio-only formats into one file
$ yt-dlp -f \[dq]bv*+mergeall[vcodec=none]\[dq] --audio-multistreams

# Download and merge the best format that has a video stream,
# and the best 2 audio-only formats into one file
$ yt-dlp -f \[dq]bv*+ba+ba.2\[dq] --audio-multistreams


# The following examples show the old method (without -S) of format selection
# and how to use -S to achieve a similar but (generally) better result

# Download the worst video available (old method)
$ yt-dlp -f \[dq]wv*+wa/w\[dq]

# Download the best video available but with the smallest resolution
$ yt-dlp -S \[dq]+res\[dq]

# Download the smallest video available
$ yt-dlp -S \[dq]+size,+br\[dq]



# Download the best mp4 video available, or the best video if no mp4 available
$ yt-dlp -f \[dq]bv*[ext=mp4]+ba[ext=m4a]/b[ext=mp4] / bv*+ba/b\[dq]

# Download the best video with the best extension
# (For video, mp4 > mov > webm > flv. For audio, m4a > aac > mp3 ...)
$ yt-dlp -S \[dq]ext\[dq]



# Download the best video available but no better than 480p,
# or the worst video if there is no video under 480p
$ yt-dlp -f \[dq]bv*[height<=480]+ba/b[height<=480] / wv*+ba/w\[dq]

# Download the best video available with the largest height but no better than 480p,
# or the best video with the smallest resolution if there is no video under 480p
$ yt-dlp -S \[dq]height:480\[dq]

# Download the best video available with the largest resolution but no better than 480p,
# or the best video with the smallest resolution if there is no video under 480p
# Resolution is determined by using the smallest dimension.
# So this works correctly for vertical videos as well
$ yt-dlp -S \[dq]res:480\[dq]



# Download the best video (that also has audio) but no bigger than 50 MB,
# or the worst video (that also has audio) if there is no video under 50 MB
$ yt-dlp -f \[dq]b[filesize<50M] / w\[dq]

# Download the largest video (that also has audio) but no bigger than 50 MB,
# or the smallest video (that also has audio) if there is no video under 50 MB
$ yt-dlp -f \[dq]b\[dq] -S \[dq]filesize:50M\[dq]

# Download the best video (that also has audio) that is closest in size to 50 MB
$ yt-dlp -f \[dq]b\[dq] -S \[dq]filesize\[ti]50M\[dq]



# Download best video available via direct link over HTTP/HTTPS protocol,
# or the best video available via any protocol if there is no such video
$ yt-dlp -f \[dq](bv*+ba/b)[protocol\[ha]=http][protocol!*=dash] / (bv*+ba/b)\[dq]

# Download best video available via the best protocol
# (https/ftps > http/ftp > m3u8_native > m3u8 > http_dash_segments ...)
$ yt-dlp -S \[dq]proto\[dq]



# Download the best video with either h264 or h265 codec,
# or the best video if there is no such video
$ yt-dlp -f \[dq](bv*[vcodec\[ti]=\[aq]\[ha]((he|a)vc|h26[45])\[aq]]+ba) / (bv*+ba/b)\[dq]

# Download the best video with best codec no better than h264,
# or the best video with worst codec if there is no such video
$ yt-dlp -S \[dq]codec:h264\[dq]

# Download the best video with worst codec no worse than h264,
# or the best video with best codec if there is no such video
$ yt-dlp -S \[dq]+codec:h264\[dq]



# More complex examples

# Download the best video no better than 720p preferring framerate greater than 30,
# or the worst video (still preferring framerate greater than 30) if there is no such video
$ yt-dlp -f \[dq]((bv*[fps>30]/bv*)[height<=720]/(wv*[fps>30]/wv*)) + ba / (b[fps>30]/b)[height<=720]/(w[fps>30]/w)\[dq]

# Download the video with the largest resolution no better than 720p,
# or the video with the smallest resolution available if there is no such video,
# preferring larger framerate for formats with the same resolution
$ yt-dlp -S \[dq]res:720,fps\[dq]



# Download the video with smallest resolution no worse than 480p,
# or the video with the largest resolution available if there is no such video,
# preferring better codec and then larger total bitrate for the same resolution
$ yt-dlp -S \[dq]+res:480,codec,br\[dq]
\f[R]
.fi
.SH MODIFYING METADATA
.PP
The metadata obtained by the extractors can be modified by using
\f[V]--parse-metadata\f[R] and \f[V]--replace-in-metadata\f[R]
.PP
\f[V]--replace-in-metadata FIELDS REGEX REPLACE\f[R] is used to replace
text in any metadata field using Python regular
expression (https://docs.python.org/3/library/re.html#regular-expression-syntax).
Backreferences (https://docs.python.org/3/library/re.html?highlight=backreferences#re.sub)
can be used in the replace string for advanced use.
.PP
The general syntax of \f[V]--parse-metadata FROM:TO\f[R] is to give the
name of a field or an output template to extract data from, and the
format to interpret it as, separated by a colon \f[V]:\f[R].
Either a Python regular
expression (https://docs.python.org/3/library/re.html#regular-expression-syntax)
with named capture groups, a single field name, or a similar syntax to
the output template (only \f[V]%(field)s\f[R] formatting is supported)
can be used for \f[V]TO\f[R].
The option can be used multiple times to parse and modify various
fields.
.PP
Note that these options preserve their relative order, allowing
replacements to be made in parsed fields and vice versa.
Also, any field thus created can be used in the output template and will
also affect the media file\[aq]s metadata added when using
\f[V]--embed-metadata\f[R].
.PP
This option also has a few special uses:
.IP \[bu] 2
You can download an additional URL based on the metadata of the
currently downloaded video.
To do this, set the field \f[V]additional_urls\f[R] to the URL that you
want to download.
E.g.
\f[V]--parse-metadata \[dq]description:(?P<additional_urls>https?://www\[rs].vimeo\[rs].com/\[rs]d+)\[dq]\f[R]
will download the first vimeo video found in the description
.IP \[bu] 2
You can use this to change the metadata that is embedded in the media
file.
To do this, set the value of the corresponding field with a
\f[V]meta_\f[R] prefix.
For example, any value you set to \f[V]meta_description\f[R] field will
be added to the \f[V]description\f[R] field in the file - you can use
this to set a different \[dq]description\[dq] and \[dq]synopsis\[dq].
To modify the metadata of individual streams, use the \f[V]meta<n>_\f[R]
prefix (e.g.
\f[V]meta1_language\f[R]).
Any value set to the \f[V]meta_\f[R] field will overwrite all default
values.
.PP
\f[B]Note\f[R]: Metadata modification happens before format selection,
post-extraction and other post-processing operations.
Some fields may be added or changed during these steps, overriding your
changes.
.PP
For reference, these are the fields yt-dlp adds by default to the file
metadata:
.PP
.TS
tab(@);
lw(24.9n) lw(45.1n).
T{
Metadata fields
T}@T{
From
T}
_
T{
\f[V]title\f[R]
T}@T{
\f[V]track\f[R] or \f[V]title\f[R]
T}
T{
\f[V]date\f[R]
T}@T{
\f[V]upload_date\f[R]
T}
T{
\f[V]description\f[R], \f[V]synopsis\f[R]
T}@T{
\f[V]description\f[R]
T}
T{
\f[V]purl\f[R], \f[V]comment\f[R]
T}@T{
\f[V]webpage_url\f[R]
T}
T{
\f[V]track\f[R]
T}@T{
\f[V]track_number\f[R]
T}
T{
\f[V]artist\f[R]
T}@T{
\f[V]artist\f[R], \f[V]artists\f[R], \f[V]creator\f[R],
\f[V]creators\f[R], \f[V]uploader\f[R] or \f[V]uploader_id\f[R]
T}
T{
\f[V]composer\f[R]
T}@T{
\f[V]composer\f[R] or \f[V]composers\f[R]
T}
T{
\f[V]genre\f[R]
T}@T{
\f[V]genre\f[R] or \f[V]genres\f[R]
T}
T{
\f[V]album\f[R]
T}@T{
\f[V]album\f[R]
T}
T{
\f[V]album_artist\f[R]
T}@T{
\f[V]album_artist\f[R] or \f[V]album_artists\f[R]
T}
T{
\f[V]disc\f[R]
T}@T{
\f[V]disc_number\f[R]
T}
T{
\f[V]show\f[R]
T}@T{
\f[V]series\f[R]
T}
T{
\f[V]season_number\f[R]
T}@T{
\f[V]season_number\f[R]
T}
T{
\f[V]episode_id\f[R]
T}@T{
\f[V]episode\f[R] or \f[V]episode_id\f[R]
T}
T{
\f[V]episode_sort\f[R]
T}@T{
\f[V]episode_number\f[R]
T}
T{
\f[V]language\f[R] of each stream
T}@T{
the format\[aq]s \f[V]language\f[R]
T}
.TE
.PP
\f[B]Note\f[R]: The file format may not support some of these fields
.SS Modifying metadata examples
.IP
.nf
\f[C]
# Interpret the title as \[dq]Artist - Title\[dq]
$ yt-dlp --parse-metadata \[dq]title:%(artist)s - %(title)s\[dq]

# Regex example
$ yt-dlp --parse-metadata \[dq]description:Artist - (?P<artist>.+)\[dq]

# Set title as \[dq]Series name S01E05\[dq]
$ yt-dlp --parse-metadata \[dq]%(series)s S%(season_number)02dE%(episode_number)02d:%(title)s\[dq]

# Prioritize uploader as the \[dq]artist\[dq] field in video metadata
$ yt-dlp --parse-metadata \[dq]%(uploader|)s:%(meta_artist)s\[dq] --embed-metadata

# Set \[dq]comment\[dq] field in video metadata using description instead of webpage_url,
# handling multiple lines correctly
$ yt-dlp --parse-metadata \[dq]description:(?s)(?P<meta_comment>.+)\[dq] --embed-metadata

# Do not set any \[dq]synopsis\[dq] in the video metadata
$ yt-dlp --parse-metadata \[dq]:(?P<meta_synopsis>)\[dq]

# Remove \[dq]formats\[dq] field from the infojson by setting it to an empty string
$ yt-dlp --parse-metadata \[dq]video::(?P<formats>)\[dq] --write-info-json

# Replace all spaces and \[dq]_\[dq] in title and uploader with a \[ga]-\[ga]
$ yt-dlp --replace-in-metadata \[dq]title,uploader\[dq] \[dq][ _]\[dq] \[dq]-\[dq]
\f[R]
.fi
.SH EXTRACTOR ARGUMENTS
.PP
Some extractors accept additional arguments which can be passed using
\f[V]--extractor-args KEY:ARGS\f[R].
\f[V]ARGS\f[R] is a \f[V];\f[R] (semicolon) separated string of
\f[V]ARG=VAL1,VAL2\f[R].
E.g.
\f[V]--extractor-args \[dq]youtube:player-client=tv,mweb;formats=incomplete\[dq] --extractor-args \[dq]twitter:api=syndication\[dq]\f[R]
.PP
Note: In CLI, \f[V]ARG\f[R] can use \f[V]-\f[R] instead of \f[V]_\f[R];
e.g.
\f[V]youtube:player-client\[dq]\f[R] becomes
\f[V]youtube:player_client\[dq]\f[R]
.PP
The following extractors use this feature:
.SS youtube
.IP \[bu] 2
\f[V]lang\f[R]: Prefer translated metadata (\f[V]title\f[R],
\f[V]description\f[R] etc) of this language code (case-sensitive).
By default, the video primary language metadata is preferred, with a
fallback to \f[V]en\f[R] translated.
See
youtube.py (https://github.com/yt-dlp/yt-dlp/blob/c26f9b991a0681fd3ea548d535919cec1fbbd430/yt_dlp/extractor/youtube.py#L381-L390)
for list of supported content language codes
.IP \[bu] 2
\f[V]skip\f[R]: One or more of \f[V]hls\f[R], \f[V]dash\f[R] or
\f[V]translated_subs\f[R] to skip extraction of the m3u8 manifests, dash
manifests and auto-translated
subtitles (https://github.com/yt-dlp/yt-dlp/issues/4090#issuecomment-1158102032)
respectively
.IP \[bu] 2
\f[V]player_client\f[R]: Clients to extract video data from.
The currently available clients are \f[V]web\f[R], \f[V]web_safari\f[R],
\f[V]web_embedded\f[R], \f[V]web_music\f[R], \f[V]web_creator\f[R],
\f[V]mweb\f[R], \f[V]ios\f[R], \f[V]android\f[R], \f[V]android_vr\f[R],
\f[V]tv\f[R] and \f[V]tv_embedded\f[R].
By default, \f[V]tv,ios,web\f[R] is used, or \f[V]tv,web\f[R] is used
when authenticating with cookies.
The \f[V]web_music\f[R] client is added for \f[V]music.youtube.com\f[R]
URLs when logged-in cookies are used.
The \f[V]web_embedded\f[R] client is added for age-restricted videos but
only works if the video is embeddable.
The \f[V]tv_embedded\f[R] and \f[V]web_creator\f[R] clients are added
for age-restricted videos if account age-verification is required.
Some clients, such as \f[V]web\f[R] and \f[V]web_music\f[R], require a
\f[V]po_token\f[R] for their formats to be downloadable.
Some clients, such as \f[V]web_creator\f[R], will only work with
authentication.
Not all clients support authentication via cookies.
You can use \f[V]default\f[R] for the default clients, or you can use
\f[V]all\f[R] for all clients (not recommended).
You can prefix a client with \f[V]-\f[R] to exclude it, e.g.
\f[V]youtube:player_client=default,-ios\f[R]
.IP \[bu] 2
\f[V]player_skip\f[R]: Skip some network requests that are generally
needed for robust extraction.
One or more of \f[V]configs\f[R] (skip client configs),
\f[V]webpage\f[R] (skip initial webpage), \f[V]js\f[R] (skip js player),
\f[V]initial_data\f[R] (skip initial data/next ep request).
While these options can help reduce the number of requests needed or
avoid some rate-limiting, they could cause issues such as missing
formats or metadata.
See #860 (https://github.com/yt-dlp/yt-dlp/pull/860) and
#12826 (https://github.com/yt-dlp/yt-dlp/issues/12826) for more details
.IP \[bu] 2
\f[V]player_params\f[R]: YouTube player parameters to use for player
requests.
Will overwrite any default ones set by yt-dlp.
.IP \[bu] 2
\f[V]player_js_variant\f[R]: The player javascript variant to use for
signature and nsig deciphering.
The known variants are: \f[V]main\f[R], \f[V]tce\f[R], \f[V]tv\f[R],
\f[V]tv_es6\f[R], \f[V]phone\f[R], \f[V]tablet\f[R].
Only \f[V]main\f[R] is recommended as a possible workaround; the others
are for debugging purposes.
The default is to use what is prescribed by the site, and can be
selected with \f[V]actual\f[R]
.IP \[bu] 2
\f[V]comment_sort\f[R]: \f[V]top\f[R] or \f[V]new\f[R] (default) -
choose comment sorting mode (on YouTube\[aq]s side)
.IP \[bu] 2
\f[V]max_comments\f[R]: Limit the amount of comments to gather.
Comma-separated list of integers representing
\f[V]max-comments,max-parents,max-replies,max-replies-per-thread\f[R].
Default is \f[V]all,all,all,all\f[R]
.RS 2
.IP \[bu] 2
E.g.
\f[V]all,all,1000,10\f[R] will get a maximum of 1000 replies total, with
up to 10 replies per thread.
\f[V]1000,all,100\f[R] will get a maximum of 1000 comments, with a
maximum of 100 replies total
.RE
.IP \[bu] 2
\f[V]formats\f[R]: Change the types of formats to return.
\f[V]dashy\f[R] (convert HTTP to DASH), \f[V]duplicate\f[R] (identical
content but different URLs or protocol; includes \f[V]dashy\f[R]),
\f[V]incomplete\f[R] (cannot be downloaded completely - live dash and
post-live m3u8), \f[V]missing_pot\f[R] (include formats that require a
PO Token but are missing one)
.IP \[bu] 2
\f[V]innertube_host\f[R]: Innertube API host to use for all API
requests; e.g.
\f[V]studio.youtube.com\f[R], \f[V]youtubei.googleapis.com\f[R].
Note that cookies exported from one subdomain will not work on others
.IP \[bu] 2
\f[V]innertube_key\f[R]: Innertube API key to use for all API requests.
By default, no API key is used
.IP \[bu] 2
\f[V]raise_incomplete_data\f[R]: \f[V]Incomplete Data Received\f[R]
raises an error instead of reporting a warning
.IP \[bu] 2
\f[V]data_sync_id\f[R]: Overrides the account Data Sync ID used in
Innertube API requests.
This may be needed if you are using an account with
\f[V]youtube:player_skip=webpage,configs\f[R] or
\f[V]youtubetab:skip=webpage\f[R]
.IP \[bu] 2
\f[V]visitor_data\f[R]: Overrides the Visitor Data used in Innertube API
requests.
This should be used with \f[V]player_skip=webpage,configs\f[R] and
without cookies.
Note: this may have adverse effects if used improperly.
If a session from a browser is wanted, you should pass cookies instead
(which contain the Visitor ID)
.IP \[bu] 2
\f[V]po_token\f[R]: Proof of Origin (PO) Token(s) to use.
Comma seperated list of PO Tokens in the format
\f[V]CLIENT.CONTEXT+PO_TOKEN\f[R], e.g.
\f[V]youtube:po_token=web.gvs+XXX,web.player=XXX,web_safari.gvs+YYY\f[R].
Context can be any of \f[V]gvs\f[R] (Google Video Server URLs),
\f[V]player\f[R] (Innertube player request) or \f[V]subs\f[R]
(Subtitles)
.IP \[bu] 2
\f[V]pot_trace\f[R]: Enable debug logging for PO Token fetching.
Either \f[V]true\f[R] or \f[V]false\f[R] (default)
.IP \[bu] 2
\f[V]fetch_pot\f[R]: Policy to use for fetching a PO Token from
providers.
One of \f[V]always\f[R] (always try fetch a PO Token regardless if the
client requires one for the given context), \f[V]never\f[R] (never fetch
a PO Token), or \f[V]auto\f[R] (default; only fetch a PO Token if the
client requires one for the given context)
.SS youtubepot-webpo
.IP \[bu] 2
\f[V]bind_to_visitor_id\f[R]: Whether to use the Visitor ID instead of
Visitor Data for caching WebPO tokens.
Either \f[V]true\f[R] (default) or \f[V]false\f[R]
.SS youtubetab (YouTube playlists, channels, feeds, etc.)
.IP \[bu] 2
\f[V]skip\f[R]: One or more of \f[V]webpage\f[R] (skip initial webpage
download), \f[V]authcheck\f[R] (allow the download of playlists
requiring authentication when no initial webpage is downloaded.
This may cause unwanted behavior, see
#1122 (https://github.com/yt-dlp/yt-dlp/pull/1122) for more details)
.IP \[bu] 2
\f[V]approximate_date\f[R]: Extract approximate \f[V]upload_date\f[R]
and \f[V]timestamp\f[R] in flat-playlist.
This may cause date-based filters to be slightly off
.SS generic
.IP \[bu] 2
\f[V]fragment_query\f[R]: Passthrough any query in mpd/m3u8 manifest
URLs to their fragments if no value is provided, or else apply the query
string given as \f[V]fragment_query=VALUE\f[R].
Note that if the stream has an HLS AES-128 key, then the query
parameters will be passed to the key URI as well, unless the
\f[V]key_query\f[R] extractor-arg is passed, or unless an external key
URI is provided via the \f[V]hls_key\f[R] extractor-arg.
Does not apply to ffmpeg
.IP \[bu] 2
\f[V]variant_query\f[R]: Passthrough the master m3u8 URL query to its
variant playlist URLs if no value is provided, or else apply the query
string given as \f[V]variant_query=VALUE\f[R]
.IP \[bu] 2
\f[V]key_query\f[R]: Passthrough the master m3u8 URL query to its HLS
AES-128 decryption key URI if no value is provided, or else apply the
query string given as \f[V]key_query=VALUE\f[R].
Note that this will have no effect if the key URI is provided via the
\f[V]hls_key\f[R] extractor-arg.
Does not apply to ffmpeg
.IP \[bu] 2
\f[V]hls_key\f[R]: An HLS AES-128 key URI \f[I]or\f[R] key (as hex), and
optionally the IV (as hex), in the form of \f[V](URI|KEY)[,IV]\f[R];
e.g.
\f[V]generic:hls_key=ABCDEF1234567980,0xFEDCBA0987654321\f[R].
Passing any of these values will force usage of the native HLS
downloader and override the corresponding values found in the m3u8
playlist
.IP \[bu] 2
\f[V]is_live\f[R]: Bypass live HLS detection and manually set
\f[V]live_status\f[R] - a value of \f[V]false\f[R] will set
\f[V]not_live\f[R], any other value (or no value) will set
\f[V]is_live\f[R]
.IP \[bu] 2
\f[V]impersonate\f[R]: Target(s) to try and impersonate with the initial
webpage request; e.g.
\f[V]generic:impersonate=safari,chrome-110\f[R].
Use \f[V]generic:impersonate\f[R] to impersonate any available target,
and use \f[V]generic:impersonate=false\f[R] to disable impersonation
(default)
.SS vikichannel
.IP \[bu] 2
\f[V]video_types\f[R]: Types of videos to download - one or more of
\f[V]episodes\f[R], \f[V]movies\f[R], \f[V]clips\f[R],
\f[V]trailers\f[R]
.SS youtubewebarchive
.IP \[bu] 2
\f[V]check_all\f[R]: Try to check more at the cost of more requests.
One or more of \f[V]thumbnails\f[R], \f[V]captures\f[R]
.SS gamejolt
.IP \[bu] 2
\f[V]comment_sort\f[R]: \f[V]hot\f[R] (default), \f[V]you\f[R] (cookies
needed), \f[V]top\f[R], \f[V]new\f[R] - choose comment sorting mode (on
GameJolt\[aq]s side)
.SS hotstar
.IP \[bu] 2
\f[V]res\f[R]: resolution to ignore - one or more of \f[V]sd\f[R],
\f[V]hd\f[R], \f[V]fhd\f[R]
.IP \[bu] 2
\f[V]vcodec\f[R]: vcodec to ignore - one or more of \f[V]h264\f[R],
\f[V]h265\f[R], \f[V]dvh265\f[R]
.IP \[bu] 2
\f[V]dr\f[R]: dynamic range to ignore - one or more of \f[V]sdr\f[R],
\f[V]hdr10\f[R], \f[V]dv\f[R]
.SS instagram
.IP \[bu] 2
\f[V]app_id\f[R]: The value of the \f[V]X-IG-App-ID\f[R] header used for
API requests.
Default is the web app ID, \f[V]936619743392459\f[R]
.SS niconicochannelplus
.IP \[bu] 2
\f[V]max_comments\f[R]: Maximum number of comments to extract - default
is \f[V]120\f[R]
.SS tiktok
.IP \[bu] 2
\f[V]api_hostname\f[R]: Hostname to use for mobile API calls, e.g.
\f[V]api22-normal-c-alisg.tiktokv.com\f[R]
.IP \[bu] 2
\f[V]app_name\f[R]: Default app name to use with mobile API calls, e.g.
\f[V]trill\f[R]
.IP \[bu] 2
\f[V]app_version\f[R]: Default app version to use with mobile API calls
- should be set along with \f[V]manifest_app_version\f[R], e.g.
\f[V]34.1.2\f[R]
.IP \[bu] 2
\f[V]manifest_app_version\f[R]: Default numeric app version to use with
mobile API calls, e.g.
\f[V]2023401020\f[R]
.IP \[bu] 2
\f[V]aid\f[R]: Default app ID to use with mobile API calls, e.g.
\f[V]1180\f[R]
.IP \[bu] 2
\f[V]app_info\f[R]: Enable mobile API extraction with one or more app
info strings in the format of
\f[V]<iid>/[app_name]/[app_version]/[manifest_app_version]/[aid]\f[R],
where \f[V]iid\f[R] is the unique app install ID.
\f[V]iid\f[R] is the only required value; all other values and their
\f[V]/\f[R] separators can be omitted, e.g.
\f[V]tiktok:app_info=1234567890123456789\f[R] or
\f[V]tiktok:app_info=123,456/trill///1180,789//34.0.1/340001\f[R]
.IP \[bu] 2
\f[V]device_id\f[R]: Enable mobile API extraction with a genuine device
ID to be used with mobile API calls.
Default is a random 19-digit string
.SS rokfinchannel
.IP \[bu] 2
\f[V]tab\f[R]: Which tab to download - one of \f[V]new\f[R],
\f[V]top\f[R], \f[V]videos\f[R], \f[V]podcasts\f[R], \f[V]streams\f[R],
\f[V]stacks\f[R]
.SS twitter
.IP \[bu] 2
\f[V]api\f[R]: Select one of \f[V]graphql\f[R] (default),
\f[V]legacy\f[R] or \f[V]syndication\f[R] as the API for tweet
extraction.
Has no effect if logged in
.SS stacommu, wrestleuniverse
.IP \[bu] 2
\f[V]device_id\f[R]: UUID value assigned by the website and used to
enforce device limits for paid livestream content.
Can be found in browser local storage
.SS twitch
.IP \[bu] 2
\f[V]client_id\f[R]: Client ID value to be sent with GraphQL requests,
e.g.
\f[V]twitch:client_id=******************************\f[R]
.SS nhkradirulive (NHK らじる★らじる LIVE)
.IP \[bu] 2
\f[V]area\f[R]: Which regional variation to extract.
Valid areas are: \f[V]sapporo\f[R], \f[V]sendai\f[R], \f[V]tokyo\f[R],
\f[V]nagoya\f[R], \f[V]osaka\f[R], \f[V]hiroshima\f[R],
\f[V]matsuyama\f[R], \f[V]fukuoka\f[R].
Defaults to \f[V]tokyo\f[R]
.SS nflplusreplay
.IP \[bu] 2
\f[V]type\f[R]: Type(s) of game replays to extract.
Valid types are: \f[V]full_game\f[R], \f[V]full_game_spanish\f[R],
\f[V]condensed_game\f[R] and \f[V]all_22\f[R].
You can use \f[V]all\f[R] to extract all available replay types, which
is the default
.SS jiocinema
.IP \[bu] 2
\f[V]refresh_token\f[R]: The \f[V]refreshToken\f[R] UUID from browser
local storage can be passed to extend the life of your login session
when logging in with \f[V]token\f[R] as username and the
\f[V]accessToken\f[R] from browser local storage as password
.SS jiosaavn
.IP \[bu] 2
\f[V]bitrate\f[R]: Audio bitrates to request.
One or more of \f[V]16\f[R], \f[V]32\f[R], \f[V]64\f[R], \f[V]128\f[R],
\f[V]320\f[R].
Default is \f[V]128,320\f[R]
.SS afreecatvlive
.IP \[bu] 2
\f[V]cdn\f[R]: One or more CDN IDs to use with the API call for stream
URLs, e.g.
\f[V]gcp_cdn\f[R], \f[V]gs_cdn_pc_app\f[R], \f[V]gs_cdn_mobile_web\f[R],
\f[V]gs_cdn_pc_web\f[R]
.SS soundcloud
.IP \[bu] 2
\f[V]formats\f[R]: Formats to request from the API.
Requested values should be in the format of
\f[V]{protocol}_{codec}\f[R], e.g.
\f[V]hls_opus,http_aac\f[R].
The \f[V]*\f[R] character functions as a wildcard, e.g.
\f[V]*_mp3\f[R], and can be passed by itself to request all formats.
Known protocols include \f[V]http\f[R], \f[V]hls\f[R] and
\f[V]hls-aes\f[R]; known codecs include \f[V]aac\f[R], \f[V]opus\f[R]
and \f[V]mp3\f[R].
Original \f[V]download\f[R] formats are always extracted.
Default is
\f[V]http_aac,hls_aac,http_opus,hls_opus,http_mp3,hls_mp3\f[R]
.SS orfon (orf:on)
.IP \[bu] 2
\f[V]prefer_segments_playlist\f[R]: Prefer a playlist of program
segments instead of a single complete video when available.
If individual segments are desired, use
\f[V]--concat-playlist never --extractor-args \[dq]orfon:prefer_segments_playlist\[dq]\f[R]
.SS bilibili
.IP \[bu] 2
\f[V]prefer_multi_flv\f[R]: Prefer extracting flv formats over mp4 for
older videos that still provide legacy formats
.SS sonylivseries
.IP \[bu] 2
\f[V]sort_order\f[R]: Episode sort order for series extraction - one of
\f[V]asc\f[R] (ascending, oldest first) or \f[V]desc\f[R] (descending,
newest first).
Default is \f[V]asc\f[R]
.SS tver
.IP \[bu] 2
\f[V]backend\f[R]: Backend API to use for extraction - one of
\f[V]streaks\f[R] (default) or \f[V]brightcove\f[R] (deprecated)
.PP
\f[B]Note\f[R]: These options may be changed/removed in the future
without concern for backward compatibility
.SH INSTALLATION
.PP
You can install yt-dlp using the binaries,
pip (https://pypi.org/project/yt-dlp) or one using a third-party package
manager.
See the wiki (https://github.com/yt-dlp/yt-dlp/wiki/Installation) for
detailed instructions
.PP
\f[B]Note\f[R]: The manpages, shell completion (autocomplete) files etc.
are available inside the source
tarball (https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.tar.gz)
.SS UPDATE
.PP
You can use \f[V]yt-dlp -U\f[R] to update if you are using the release
binaries
.PP
If you installed with
pip (https://github.com/yt-dlp/yt-dlp/wiki/Installation#with-pip),
simply re-run the same command that was used to install the program
.PP
For other third-party package managers, see the
wiki (https://github.com/yt-dlp/yt-dlp/wiki/Installation#third-party-package-managers)
or refer to their documentation
.PP
.PP
There are currently three release channels for binaries:
\f[V]stable\f[R], \f[V]nightly\f[R] and \f[V]master\f[R].
.IP \[bu] 2
\f[V]stable\f[R] is the default channel, and many of its changes have
been tested by users of the \f[V]nightly\f[R] and \f[V]master\f[R]
channels.
.IP \[bu] 2
The \f[V]nightly\f[R] channel has releases scheduled to build every day
around midnight UTC, for a snapshot of the project\[aq]s new patches and
changes.
This is the \f[B]recommended channel for regular users\f[R] of yt-dlp.
The \f[V]nightly\f[R] releases are available from
yt-dlp/yt-dlp-nightly-builds (https://github.com/yt-dlp/yt-dlp-nightly-builds/releases)
or as development releases of the \f[V]yt-dlp\f[R] PyPI package (which
can be installed with pip\[aq]s \f[V]--pre\f[R] flag).
.IP \[bu] 2
The \f[V]master\f[R] channel features releases that are built after each
push to the master branch, and these will have the very latest fixes and
additions, but may also be more prone to regressions.
They are available from
yt-dlp/yt-dlp-master-builds (https://github.com/yt-dlp/yt-dlp-master-builds/releases).
.PP
When using \f[V]--update\f[R]/\f[V]-U\f[R], a release binary will only
update to its current channel.
\f[V]--update-to CHANNEL\f[R] can be used to switch to a different
channel when a newer version is available.
\f[V]--update-to [CHANNEL\[at]]TAG\f[R] can also be used to upgrade or
downgrade to specific tags from a channel.
.PP
You may also use \f[V]--update-to <repository>\f[R]
(\f[V]<owner>/<repository>\f[R]) to update to a channel on a completely
different repository.
Be careful with what repository you are updating to though, there is no
verification done for binaries from different repositories.
.PP
Example usage:
.IP \[bu] 2
\f[V]yt-dlp --update-to master\f[R] switch to the \f[V]master\f[R]
channel and update to its latest release
.IP \[bu] 2
\f[V]yt-dlp --update-to stable\[at]2023.07.06\f[R] upgrade/downgrade to
release to \f[V]stable\f[R] channel tag \f[V]2023.07.06\f[R]
.IP \[bu] 2
\f[V]yt-dlp --update-to 2023.10.07\f[R] upgrade/downgrade to tag
\f[V]2023.10.07\f[R] if it exists on the current channel
.IP \[bu] 2
\f[V]yt-dlp --update-to example/yt-dlp\[at]2023.09.24\f[R]
upgrade/downgrade to the release from the \f[V]example/yt-dlp\f[R]
repository, tag \f[V]2023.09.24\f[R]
.PP
\f[B]Important\f[R]: Any user experiencing an issue with the
\f[V]stable\f[R] release should install or update to the
\f[V]nightly\f[R] release before submitting a bug report:
.IP
.nf
\f[C]
# To update to nightly from stable executable/binary:
yt-dlp --update-to nightly

# To install nightly with pip:
python3 -m pip install -U --pre \[dq]yt-dlp[default]\[dq]
\f[R]
.fi
.SS DEPENDENCIES
.PP
Python versions 3.9+ (CPython) and 3.10+ (PyPy) are supported.
Other versions and implementations may or may not work correctly.
.PP
While all the other dependencies are optional, \f[V]ffmpeg\f[R] and
\f[V]ffprobe\f[R] are highly recommended
.SS Strongly recommended
.IP \[bu] 2
\f[B]ffmpeg\f[R] and \f[B]ffprobe\f[R] (https://www.ffmpeg.org) -
Required for merging separate video and audio files, as well as for
various post-processing tasks.
License depends on the build (https://www.ffmpeg.org/legal.html)
.RS 2
.PP
There are bugs in ffmpeg that cause various issues when used alongside
yt-dlp.
Since ffmpeg is such an important dependency, we provide custom
builds (https://github.com/yt-dlp/FFmpeg-Builds#ffmpeg-static-auto-builds)
with patches for some of these issues at
yt-dlp/FFmpeg-Builds (https://github.com/yt-dlp/FFmpeg-Builds).
See the readme (https://github.com/yt-dlp/FFmpeg-Builds#patches-applied)
for details on the specific issues solved by these builds
.PP
\f[B]Important\f[R]: What you need is ffmpeg \f[I]binary\f[R],
\f[B]NOT\f[R] the Python package of the same
name (https://pypi.org/project/ffmpeg)
.RE
.SS Networking
.IP \[bu] 2
\f[B]certifi\f[R] (https://github.com/certifi/python-certifi)* -
Provides Mozilla\[aq]s root certificate bundle.
Licensed under
MPLv2 (https://github.com/certifi/python-certifi/blob/master/LICENSE)
.IP \[bu] 2
\f[B]brotli\f[R] (https://github.com/google/brotli)* or
\f[B]brotlicffi\f[R] (https://github.com/python-hyper/brotlicffi) -
Brotli (https://en.wikipedia.org/wiki/Brotli) content encoding support.
Both licensed under MIT
1 (https://github.com/google/brotli/blob/master/LICENSE)
2 (https://github.com/python-hyper/brotlicffi/blob/master/LICENSE)
.IP \[bu] 2
\f[B]websockets\f[R] (https://github.com/aaugustin/websockets)* - For
downloading over websocket.
Licensed under
BSD-3-Clause (https://github.com/aaugustin/websockets/blob/main/LICENSE)
.IP \[bu] 2
\f[B]requests\f[R] (https://github.com/psf/requests)* - HTTP library.
For HTTPS proxy and persistent connections support.
Licensed under
Apache-2.0 (https://github.com/psf/requests/blob/main/LICENSE)
.SS Impersonation
.PP
The following provide support for impersonating browser requests.
This may be required for some sites that employ TLS fingerprinting.
.IP \[bu] 2
\f[B]curl_cffi\f[R] (https://github.com/lexiforest/curl_cffi)
(recommended) - Python binding for
curl-impersonate (https://github.com/lexiforest/curl-impersonate).
Provides impersonation targets for Chrome, Edge and Safari.
Licensed under
MIT (https://github.com/lexiforest/curl_cffi/blob/main/LICENSE)
.RS 2
.IP \[bu] 2
Can be installed with the \f[V]curl-cffi\f[R] group, e.g.
\f[V]pip install \[dq]yt-dlp[default,curl-cffi]\[dq]\f[R]
.IP \[bu] 2
Currently included in \f[V]yt-dlp.exe\f[R], \f[V]yt-dlp_linux\f[R] and
\f[V]yt-dlp_macos\f[R] builds
.RE
.SS Metadata
.IP \[bu] 2
\f[B]mutagen\f[R] (https://github.com/quodlibet/mutagen)* - For
\f[V]--embed-thumbnail\f[R] in certain formats.
Licensed under
GPLv2+ (https://github.com/quodlibet/mutagen/blob/master/COPYING)
.IP \[bu] 2
\f[B]AtomicParsley\f[R] (https://github.com/wez/atomicparsley) - For
\f[V]--embed-thumbnail\f[R] in \f[V]mp4\f[R]/\f[V]m4a\f[R] files when
\f[V]mutagen\f[R]/\f[V]ffmpeg\f[R] cannot.
Licensed under
GPLv2+ (https://github.com/wez/atomicparsley/blob/master/COPYING)
.IP \[bu] 2
\f[B]xattr\f[R] (https://github.com/xattr/xattr),
\f[B]pyxattr\f[R] (https://github.com/iustin/pyxattr) or
\f[B]setfattr\f[R] (http://savannah.nongnu.org/projects/attr) - For
writing xattr metadata (\f[V]--xattr\f[R]) on \f[B]Mac\f[R] and
\f[B]BSD\f[R].
Licensed under
MIT (https://github.com/xattr/xattr/blob/master/LICENSE.txt),
LGPL2.1 (https://github.com/iustin/pyxattr/blob/master/COPYING) and
GPLv2+ (http://git.savannah.nongnu.org/cgit/attr.git/tree/doc/COPYING)
respectively
.SS Misc
.IP \[bu] 2
\f[B]pycryptodomex\f[R] (https://github.com/Legrandin/pycryptodome)* -
For decrypting AES-128 HLS streams and various other data.
Licensed under
BSD-2-Clause (https://github.com/Legrandin/pycryptodome/blob/master/LICENSE.rst)
.IP \[bu] 2
\f[B]phantomjs\f[R] (https://github.com/ariya/phantomjs) - Used in
extractors where javascript needs to be run.
Licensed under
BSD-3-Clause (https://github.com/ariya/phantomjs/blob/master/LICENSE.BSD)
.IP \[bu] 2
\f[B]secretstorage\f[R] (https://github.com/mitya57/secretstorage)* -
For \f[V]--cookies-from-browser\f[R] to access the \f[B]Gnome\f[R]
keyring while decrypting cookies of \f[B]Chromium\f[R]-based browsers on
\f[B]Linux\f[R].
Licensed under
BSD-3-Clause (https://github.com/mitya57/secretstorage/blob/master/LICENSE)
.IP \[bu] 2
Any external downloader that you want to use with \f[V]--downloader\f[R]
.SS Deprecated
.IP \[bu] 2
\f[B]avconv\f[R] and \f[B]avprobe\f[R] (https://www.libav.org) - Now
\f[B]deprecated\f[R] alternative to ffmpeg.
License depends on the build (https://libav.org/legal)
.IP \[bu] 2
\f[B]sponskrub\f[R] (https://github.com/faissaloo/SponSkrub) - For using
the now \f[B]deprecated\f[R] sponskrub options.
Licensed under
GPLv3+ (https://github.com/faissaloo/SponSkrub/blob/master/LICENCE.md)
.IP \[bu] 2
\f[B]rtmpdump\f[R] (http://rtmpdump.mplayerhq.hu) - For downloading
\f[V]rtmp\f[R] streams.
ffmpeg can be used instead with \f[V]--downloader ffmpeg\f[R].
Licensed under GPLv2+ (http://rtmpdump.mplayerhq.hu)
.IP \[bu] 2
\f[B]mplayer\f[R] (http://mplayerhq.hu/design7/info.html) or
\f[B]mpv\f[R] (https://mpv.io) - For downloading
\f[V]rstp\f[R]/\f[V]mms\f[R] streams.
ffmpeg can be used instead with \f[V]--downloader ffmpeg\f[R].
Licensed under
GPLv2+ (https://github.com/mpv-player/mpv/blob/master/Copyright)
.PP
To use or redistribute the dependencies, you must agree to their
respective licensing terms.
.PP
The standalone release binaries are built with the Python interpreter
and the packages marked with \f[B]*\f[R] included.
.PP
If you do not have the necessary dependencies for a task you are
attempting, yt-dlp will warn you.
All the currently available dependencies are visible at the top of the
\f[V]--verbose\f[R] output
.SS COMPILE
.SS Standalone PyInstaller Builds
.PP
To build the standalone executable, you must have Python and
\f[V]pyinstaller\f[R] (plus any of yt-dlp\[aq]s optional dependencies if
needed).
The executable will be built for the same CPU architecture as the Python
used.
.PP
You can run the following commands:
.IP
.nf
\f[C]
python3 devscripts/install_deps.py --include pyinstaller
python3 devscripts/make_lazy_extractors.py
python3 -m bundle.pyinstaller
\f[R]
.fi
.PP
On some systems, you may need to use \f[V]py\f[R] or \f[V]python\f[R]
instead of \f[V]python3\f[R].
.PP
\f[V]python -m bundle.pyinstaller\f[R] accepts any arguments that can be
passed to \f[V]pyinstaller\f[R], such as \f[V]--onefile/-F\f[R] or
\f[V]--onedir/-D\f[R], which is further documented
here (https://pyinstaller.org/en/stable/usage.html#what-to-generate).
.PP
\f[B]Note\f[R]: Pyinstaller versions below 4.4 do not
support (https://github.com/pyinstaller/pyinstaller#requirements-and-tested-platforms)
Python installed from the Windows store without using a virtual
environment.
.PP
\f[B]Important\f[R]: Running \f[V]pyinstaller\f[R] directly \f[B]instead
of\f[R] using \f[V]python -m bundle.pyinstaller\f[R] is \f[B]not\f[R]
officially supported.
This may or may not work correctly.
.SS Platform-independent Binary (UNIX)
.PP
You will need the build tools \f[V]python\f[R] (3.9+), \f[V]zip\f[R],
\f[V]make\f[R] (GNU), \f[V]pandoc\f[R]* and \f[V]pytest\f[R]*.
.PP
After installing these, simply run \f[V]make\f[R].
.PP
You can also run \f[V]make yt-dlp\f[R] instead to compile only the
binary without updating any of the additional files.
(The build tools marked with \f[B]*\f[R] are not needed for this)
.SS Related scripts
.IP \[bu] 2
\f[B]\f[VB]devscripts/install_deps.py\f[B]\f[R] - Install dependencies
for yt-dlp.
.IP \[bu] 2
\f[B]\f[VB]devscripts/update-version.py\f[B]\f[R] - Update the version
number based on the current date.
.IP \[bu] 2
\f[B]\f[VB]devscripts/set-variant.py\f[B]\f[R] - Set the build variant
of the executable.
.IP \[bu] 2
\f[B]\f[VB]devscripts/make_changelog.py\f[B]\f[R] - Create a markdown
changelog using short commit messages and update \f[V]CONTRIBUTORS\f[R]
file.
.IP \[bu] 2
\f[B]\f[VB]devscripts/make_lazy_extractors.py\f[B]\f[R] - Create lazy
extractors.
Running this before building the binaries (any variant) will improve
their startup performance.
Set the environment variable \f[V]YTDLP_NO_LAZY_EXTRACTORS\f[R] to
something nonempty to forcefully disable lazy extractor loading.
.PP
Note: See their \f[V]--help\f[R] for more info.
.SS Forking the project
.PP
If you fork the project on GitHub, you can run your fork\[aq]s build
workflow to automatically build the selected version(s) as artifacts.
Alternatively, you can run the release workflow or enable the nightly
workflow to create full (pre-)releases.
.SH PLUGINS
.PP
Note that \f[B]all\f[R] plugins are imported even if not invoked, and
that \f[B]there are no checks\f[R] performed on plugin code.
\f[B]Use plugins at your own risk and only if you trust the code!\f[R]
.PP
Plugins can be of \f[V]<type>\f[R]s \f[V]extractor\f[R] or
\f[V]postprocessor\f[R].
- Extractor plugins do not need to be enabled from the CLI and are
automatically invoked when the input URL is suitable for it.
- Extractor plugins take priority over built-in extractors.
- Postprocessor plugins can be invoked using
\f[V]--use-postprocessor NAME\f[R].
.PP
Plugins are loaded from the namespace packages
\f[V]yt_dlp_plugins.extractor\f[R] and
\f[V]yt_dlp_plugins.postprocessor\f[R].
.PP
In other words, the file structure on the disk looks something like:
.IP
.nf
\f[C]
    yt_dlp_plugins/
        extractor/
            myplugin.py
        postprocessor/
            myplugin.py
\f[R]
.fi
.PP
yt-dlp looks for these \f[V]yt_dlp_plugins\f[R] namespace folders in
many locations (see below) and loads in plugins from \f[B]all\f[R] of
them.
Set the environment variable \f[V]YTDLP_NO_PLUGINS\f[R] to something
nonempty to disable loading plugins entirely.
.PP
See the wiki for some known
plugins (https://github.com/yt-dlp/yt-dlp/wiki/Plugins)
.SS Installing Plugins
.PP
Plugins can be installed using various methods and locations.
.IP "1." 3
\f[B]Configuration directories\f[R]: Plugin packages (containing a
\f[V]yt_dlp_plugins\f[R] namespace folder) can be dropped into the
following standard configuration locations:
.RS 4
.IP \[bu] 2
\f[B]User Plugins\f[R]
.RS 2
.IP \[bu] 2
\f[V]${XDG_CONFIG_HOME}/yt-dlp/plugins/<package name>/yt_dlp_plugins/\f[R]
(recommended on Linux/macOS)
.IP \[bu] 2
\f[V]${XDG_CONFIG_HOME}/yt-dlp-plugins/<package name>/yt_dlp_plugins/\f[R]
.IP \[bu] 2
\f[V]${APPDATA}/yt-dlp/plugins/<package name>/yt_dlp_plugins/\f[R]
(recommended on Windows)
.IP \[bu] 2
\f[V]${APPDATA}/yt-dlp-plugins/<package name>/yt_dlp_plugins/\f[R]
.IP \[bu] 2
\f[V]\[ti]/.yt-dlp/plugins/<package name>/yt_dlp_plugins/\f[R]
.IP \[bu] 2
\f[V]\[ti]/yt-dlp-plugins/<package name>/yt_dlp_plugins/\f[R]
.RE
.IP \[bu] 2
\f[B]System Plugins\f[R]
.RS 2
.IP \[bu] 2
\f[V]/etc/yt-dlp/plugins/<package name>/yt_dlp_plugins/\f[R]
.IP \[bu] 2
\f[V]/etc/yt-dlp-plugins/<package name>/yt_dlp_plugins/\f[R]
.RE
.RE
.IP "2." 3
\f[B]Executable location\f[R]: Plugin packages can similarly be
installed in a \f[V]yt-dlp-plugins\f[R] directory under the executable
location (recommended for portable installations):
.RS 4
.IP \[bu] 2
Binary: where \f[V]<root-dir>/yt-dlp.exe\f[R],
\f[V]<root-dir>/yt-dlp-plugins/<package name>/yt_dlp_plugins/\f[R]
.IP \[bu] 2
Source: where \f[V]<root-dir>/yt_dlp/__main__.py\f[R],
\f[V]<root-dir>/yt-dlp-plugins/<package name>/yt_dlp_plugins/\f[R]
.RE
.IP "3." 3
\f[B]pip and other locations in \f[VB]PYTHONPATH\f[B]\f[R]
.RS 4
.IP \[bu] 2
Plugin packages can be installed and managed using \f[V]pip\f[R].
See
yt-dlp-sample-plugins (https://github.com/yt-dlp/yt-dlp-sample-plugins)
for an example.
.RS 2
.IP \[bu] 2
Note: plugin files between plugin packages installed with pip must have
unique filenames.
.RE
.IP \[bu] 2
Any path in \f[V]PYTHONPATH\f[R] is searched in for the
\f[V]yt_dlp_plugins\f[R] namespace folder.
.RS 2
.IP \[bu] 2
Note: This does not apply for Pyinstaller builds.
.RE
.RE
.PP
\f[V].zip\f[R], \f[V].egg\f[R] and \f[V].whl\f[R] archives containing a
\f[V]yt_dlp_plugins\f[R] namespace folder in their root are also
supported as plugin packages.
.IP \[bu] 2
e.g.
\f[V]${XDG_CONFIG_HOME}/yt-dlp/plugins/mypluginpkg.zip\f[R] where
\f[V]mypluginpkg.zip\f[R] contains
\f[V]yt_dlp_plugins/<type>/myplugin.py\f[R]
.PP
Run yt-dlp with \f[V]--verbose\f[R] to check if the plugin has been
loaded.
.SS Developing Plugins
.PP
See the
yt-dlp-sample-plugins (https://github.com/yt-dlp/yt-dlp-sample-plugins)
repo for a template plugin package and the Plugin
Development (https://github.com/yt-dlp/yt-dlp/wiki/Plugin-Development)
section of the wiki for a plugin development guide.
.PP
All public classes with a name ending in \f[V]IE\f[R]/\f[V]PP\f[R] are
imported from each file for extractors and postprocessors respectively.
This respects underscore prefix (e.g.
\f[V]_MyBasePluginIE\f[R] is private) and \f[V]__all__\f[R].
Modules can similarly be excluded by prefixing the module name with an
underscore (e.g.
\f[V]_myplugin.py\f[R]).
.PP
To replace an existing extractor with a subclass of one, set the
\f[V]plugin_name\f[R] class keyword argument (e.g.
\f[V]class MyPluginIE(ABuiltInIE, plugin_name=\[aq]myplugin\[aq])\f[R]
will replace \f[V]ABuiltInIE\f[R] with \f[V]MyPluginIE\f[R]).
Since the extractor replaces the parent, you should exclude the subclass
extractor from being imported separately by making it private using one
of the methods described above.
.PP
If you are a plugin author, add
yt-dlp-plugins (https://github.com/topics/yt-dlp-plugins) as a topic to
your repository for discoverability.
.PP
See the Developer
Instructions (https://github.com/yt-dlp/yt-dlp/blob/master/CONTRIBUTING.md#developer-instructions)
on how to write and test an extractor.
.SH EMBEDDING YT-DLP
.PP
yt-dlp makes the best effort to be a good command-line program, and thus
should be callable from any programming language.
.PP
Your program should avoid parsing the normal stdout since they may
change in future versions.
Instead, they should use options such as \f[V]-J\f[R],
\f[V]--print\f[R], \f[V]--progress-template\f[R], \f[V]--exec\f[R] etc
to create console output that you can reliably reproduce and parse.
.PP
From a Python program, you can embed yt-dlp in a more powerful fashion,
like this:
.IP
.nf
\f[C]
from yt_dlp import YoutubeDL

URLS = [\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]]
with YoutubeDL() as ydl:
    ydl.download(URLS)
\f[R]
.fi
.PP
Most likely, you\[aq]ll want to use various options.
For a list of options available, have a look at
\f[V]yt_dlp/YoutubeDL.py\f[R] or \f[V]help(yt_dlp.YoutubeDL)\f[R] in a
Python shell.
If you are already familiar with the CLI, you can use
\f[V]devscripts/cli_to_api.py\f[R] (https://github.com/yt-dlp/yt-dlp/blob/master/devscripts/cli_to_api.py)
to translate any CLI switches to \f[V]YoutubeDL\f[R] params.
.PP
\f[B]Tip\f[R]: If you are porting your code from youtube-dl to yt-dlp,
one important point to look out for is that we do not guarantee the
return value of \f[V]YoutubeDL.extract_info\f[R] to be json
serializable, or even be a dictionary.
It will be dictionary-like, but if you want to ensure it is a
serializable dictionary, pass it through
\f[V]YoutubeDL.sanitize_info\f[R] as shown in the example below
.SS Embedding examples
.SS Extracting information
.IP
.nf
\f[C]
import json
import yt_dlp

URL = \[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]

# ℹ️ See help(yt_dlp.YoutubeDL) for a list of available options and public functions
ydl_opts = {}
with yt_dlp.YoutubeDL(ydl_opts) as ydl:
    info = ydl.extract_info(URL, download=False)

    # ℹ️ ydl.sanitize_info makes the info json-serializable
    print(json.dumps(ydl.sanitize_info(info)))
\f[R]
.fi
.SS Download using an info-json
.IP
.nf
\f[C]
import yt_dlp

INFO_FILE = \[aq]path/to/video.info.json\[aq]

with yt_dlp.YoutubeDL() as ydl:
    error_code = ydl.download_with_info_file(INFO_FILE)

print(\[aq]Some videos failed to download\[aq] if error_code
      else \[aq]All videos successfully downloaded\[aq])
\f[R]
.fi
.SS Extract audio
.IP
.nf
\f[C]
import yt_dlp

URLS = [\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]]

ydl_opts = {
    \[aq]format\[aq]: \[aq]m4a/bestaudio/best\[aq],
    # ℹ️ See help(yt_dlp.postprocessor) for a list of available Postprocessors and their arguments
    \[aq]postprocessors\[aq]: [{  # Extract audio using ffmpeg
        \[aq]key\[aq]: \[aq]FFmpegExtractAudio\[aq],
        \[aq]preferredcodec\[aq]: \[aq]m4a\[aq],
    }]
}

with yt_dlp.YoutubeDL(ydl_opts) as ydl:
    error_code = ydl.download(URLS)
\f[R]
.fi
.SS Filter videos
.IP
.nf
\f[C]
import yt_dlp

URLS = [\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]]

def longer_than_a_minute(info, *, incomplete):
    \[dq]\[dq]\[dq]Download only videos longer than a minute (or with unknown duration)\[dq]\[dq]\[dq]
    duration = info.get(\[aq]duration\[aq])
    if duration and duration < 60:
        return \[aq]The video is too short\[aq]

ydl_opts = {
    \[aq]match_filter\[aq]: longer_than_a_minute,
}

with yt_dlp.YoutubeDL(ydl_opts) as ydl:
    error_code = ydl.download(URLS)
\f[R]
.fi
.SS Adding logger and progress hook
.IP
.nf
\f[C]
import yt_dlp

URLS = [\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]]

class MyLogger:
    def debug(self, msg):
        # For compatibility with youtube-dl, both debug and info are passed into debug
        # You can distinguish them by the prefix \[aq][debug] \[aq]
        if msg.startswith(\[aq][debug] \[aq]):
            pass
        else:
            self.info(msg)

    def info(self, msg):
        pass

    def warning(self, msg):
        pass

    def error(self, msg):
        print(msg)


# ℹ️ See \[dq]progress_hooks\[dq] in help(yt_dlp.YoutubeDL)
def my_hook(d):
    if d[\[aq]status\[aq]] == \[aq]finished\[aq]:
        print(\[aq]Done downloading, now post-processing ...\[aq])


ydl_opts = {
    \[aq]logger\[aq]: MyLogger(),
    \[aq]progress_hooks\[aq]: [my_hook],
}

with yt_dlp.YoutubeDL(ydl_opts) as ydl:
    ydl.download(URLS)
\f[R]
.fi
.SS Add a custom PostProcessor
.IP
.nf
\f[C]
import yt_dlp

URLS = [\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]]

# ℹ️ See help(yt_dlp.postprocessor.PostProcessor)
class MyCustomPP(yt_dlp.postprocessor.PostProcessor):
    def run(self, info):
        self.to_screen(\[aq]Doing stuff\[aq])
        return [], info


with yt_dlp.YoutubeDL() as ydl:
    # ℹ️ \[dq]when\[dq] can take any value in yt_dlp.utils.POSTPROCESS_WHEN
    ydl.add_post_processor(MyCustomPP(), when=\[aq]pre_process\[aq])
    ydl.download(URLS)
\f[R]
.fi
.SS Use a custom format selector
.IP
.nf
\f[C]
import yt_dlp

URLS = [\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]]

def format_selector(ctx):
    \[dq]\[dq]\[dq] Select the best video and the best audio that won\[aq]t result in an mkv.
    NOTE: This is just an example and does not handle all cases \[dq]\[dq]\[dq]

    # formats are already sorted worst to best
    formats = ctx.get(\[aq]formats\[aq])[::-1]

    # acodec=\[aq]none\[aq] means there is no audio
    best_video = next(f for f in formats
                      if f[\[aq]vcodec\[aq]] != \[aq]none\[aq] and f[\[aq]acodec\[aq]] == \[aq]none\[aq])

    # find compatible audio extension
    audio_ext = {\[aq]mp4\[aq]: \[aq]m4a\[aq], \[aq]webm\[aq]: \[aq]webm\[aq]}[best_video[\[aq]ext\[aq]]]
    # vcodec=\[aq]none\[aq] means there is no video
    best_audio = next(f for f in formats if (
        f[\[aq]acodec\[aq]] != \[aq]none\[aq] and f[\[aq]vcodec\[aq]] == \[aq]none\[aq] and f[\[aq]ext\[aq]] == audio_ext))

    # These are the minimum required fields for a merged format
    yield {
        \[aq]format_id\[aq]: f\[aq]{best_video[\[dq]format_id\[dq]]}+{best_audio[\[dq]format_id\[dq]]}\[aq],
        \[aq]ext\[aq]: best_video[\[aq]ext\[aq]],
        \[aq]requested_formats\[aq]: [best_video, best_audio],
        # Must be + separated list of protocols
        \[aq]protocol\[aq]: f\[aq]{best_video[\[dq]protocol\[dq]]}+{best_audio[\[dq]protocol\[dq]]}\[aq]
    }


ydl_opts = {
    \[aq]format\[aq]: format_selector,
}

with yt_dlp.YoutubeDL(ydl_opts) as ydl:
    ydl.download(URLS)
\f[R]
.fi
.SH CHANGES FROM YOUTUBE-DL
.SS New features
.IP \[bu] 2
Forked from
\f[B]yt-dlc\[at]f9401f2\f[R] (https://github.com/blackjack4494/yt-dlc/commit/f9401f2a91987068139c5f757b12fc711d4c0cee)
and merged with
\f[B]youtube-dl\[at]a08f2b7\f[R] (https://github.com/ytdl-org/youtube-dl/commit/a08f2b7e4567cdc50c0614ee0a4ffdff49b8b6e6)
(exceptions (https://github.com/yt-dlp/yt-dlp/issues/21))
.IP \[bu] 2
\f[B]SponsorBlock Integration\f[R]: You can mark/remove sponsor sections
in YouTube videos by utilizing the
SponsorBlock (https://sponsor.ajay.app) API
.IP \[bu] 2
\f[B]Format Sorting\f[R]: The default format sorting options have been
changed so that higher resolution and better codecs will be now
preferred instead of simply using larger bitrate.
Furthermore, you can now specify the sort order using \f[V]-S\f[R].
This allows for much easier format selection than what is possible by
simply using \f[V]--format\f[R] (examples)
.IP \[bu] 2
\f[B]Merged with animelover1984/youtube-dl\f[R]: You get most of the
features and improvements from
animelover1984/youtube-dl (https://github.com/animelover1984/youtube-dl)
including \f[V]--write-comments\f[R], \f[V]BiliBiliSearch\f[R],
\f[V]BilibiliChannel\f[R], Embedding thumbnail in mp4/ogg/opus, playlist
infojson etc.
See #31 (https://github.com/yt-dlp/yt-dlp/pull/31) for details.
.IP \[bu] 2
\f[B]YouTube improvements\f[R]:
.RS 2
.IP \[bu] 2
Supports Clips, Stories (\f[V]ytstories:<channel UCID>\f[R]), Search
(including filters)\f[B]*\f[R], YouTube Music Search, Channel-specific
search, Search prefixes (\f[V]ytsearch:\f[R],
\f[V]ytsearchdate:\f[R])\f[B]*\f[R], Mixes, and Feeds (\f[V]:ytfav\f[R],
\f[V]:ytwatchlater\f[R], \f[V]:ytsubs\f[R], \f[V]:ythistory\f[R],
\f[V]:ytrec\f[R], \f[V]:ytnotif\f[R])
.IP \[bu] 2
Fix for n-sig based
throttling (https://github.com/ytdl-org/youtube-dl/issues/29326)
\f[B]*\f[R]
.IP \[bu] 2
Download livestreams from the start using \f[V]--live-from-start\f[R]
(\f[I]experimental\f[R])
.IP \[bu] 2
Channel URLs download all uploads of the channel, including shorts and
live
.IP \[bu] 2
Support for logging in with
OAuth (https://github.com/yt-dlp/yt-dlp/wiki/Extractors#logging-in-with-oauth)
.RE
.IP \[bu] 2
\f[B]Cookies from browser\f[R]: Cookies can be automatically extracted
from all major web browsers using
\f[V]--cookies-from-browser BROWSER[+KEYRING][:PROFILE][::CONTAINER]\f[R]
.IP \[bu] 2
\f[B]Download time range\f[R]: Videos can be downloaded partially based
on either timestamps or chapters using \f[V]--download-sections\f[R]
.IP \[bu] 2
\f[B]Split video by chapters\f[R]: Videos can be split into multiple
files based on chapters using \f[V]--split-chapters\f[R]
.IP \[bu] 2
\f[B]Multi-threaded fragment downloads\f[R]: Download multiple fragments
of m3u8/mpd videos in parallel.
Use \f[V]--concurrent-fragments\f[R] (\f[V]-N\f[R]) option to set the
number of threads used
.IP \[bu] 2
\f[B]Aria2c with HLS/DASH\f[R]: You can use \f[V]aria2c\f[R] as the
external downloader for DASH(mpd) and HLS(m3u8) formats
.IP \[bu] 2
\f[B]New and fixed extractors\f[R]: Many new extractors have been added
and a lot of existing ones have been fixed.
See the changelog or the list of supported sites
.IP \[bu] 2
\f[B]New MSOs\f[R]: Philo, Spectrum, SlingTV, Cablevision, RCN etc.
.IP \[bu] 2
\f[B]Subtitle extraction from manifests\f[R]: Subtitles can be extracted
from streaming media manifests.
See
commit/be6202f (https://github.com/yt-dlp/yt-dlp/commit/be6202f12b97858b9d716e608394b51065d0419f)
for details
.IP \[bu] 2
\f[B]Multiple paths and output templates\f[R]: You can give different
output templates and download paths for different types of files.
You can also set a temporary path where intermediary files are
downloaded to using \f[V]--paths\f[R] (\f[V]-P\f[R])
.IP \[bu] 2
\f[B]Portable Configuration\f[R]: Configuration files are automatically
loaded from the home and root directories.
See CONFIGURATION for details
.IP \[bu] 2
\f[B]Output template improvements\f[R]: Output templates can now have
date-time formatting, numeric offsets, object traversal etc.
See output template for details.
Even more advanced operations can also be done with the help of
\f[V]--parse-metadata\f[R] and \f[V]--replace-in-metadata\f[R]
.IP \[bu] 2
\f[B]Other new options\f[R]: Many new options have been added such as
\f[V]--alias\f[R], \f[V]--print\f[R], \f[V]--concat-playlist\f[R],
\f[V]--wait-for-video\f[R], \f[V]--retry-sleep\f[R],
\f[V]--sleep-requests\f[R], \f[V]--convert-thumbnails\f[R],
\f[V]--force-download-archive\f[R], \f[V]--force-overwrites\f[R],
\f[V]--break-match-filters\f[R] etc
.IP \[bu] 2
\f[B]Improvements\f[R]: Regex and other operators in
\f[V]--format\f[R]/\f[V]--match-filters\f[R], multiple
\f[V]--postprocessor-args\f[R] and \f[V]--downloader-args\f[R], faster
archive checking, more format selection options, merge
multi-video/audio, multiple \f[V]--config-locations\f[R],
\f[V]--exec\f[R] at different stages, etc
.IP \[bu] 2
\f[B]Plugins\f[R]: Extractors and PostProcessors can be loaded from an
external file.
See plugins for details
.IP \[bu] 2
\f[B]Self updater\f[R]: The releases can be updated using
\f[V]yt-dlp -U\f[R], and downgraded using \f[V]--update-to\f[R] if
required
.IP \[bu] 2
\f[B]Automated builds\f[R]: Nightly/master builds can be used with
\f[V]--update-to nightly\f[R] and \f[V]--update-to master\f[R]
.PP
See changelog or commits (https://github.com/yt-dlp/yt-dlp/commits) for
the full list of changes
.PP
Features marked with a \f[B]*\f[R] have been back-ported to youtube-dl
.SS Differences in default behavior
.PP
Some of yt-dlp\[aq]s default options are different from that of
youtube-dl and youtube-dlc:
.IP \[bu] 2
yt-dlp supports only Python 3.9+, and will remove support for more
versions as they become
EOL (https://devguide.python.org/versions/#python-release-cycle); while
youtube-dl still supports Python 2.6+ and
3.2+ (https://github.com/ytdl-org/youtube-dl/issues/30568#issue-1118238743)
.IP \[bu] 2
The options \f[V]--auto-number\f[R] (\f[V]-A\f[R]), \f[V]--title\f[R]
(\f[V]-t\f[R]) and \f[V]--literal\f[R] (\f[V]-l\f[R]), no longer work.
See removed options for details
.IP \[bu] 2
\f[V]avconv\f[R] is not supported as an alternative to \f[V]ffmpeg\f[R]
.IP \[bu] 2
yt-dlp stores config files in slightly different locations to
youtube-dl.
See CONFIGURATION for a list of correct locations
.IP \[bu] 2
The default output template is \f[V]%(title)s [%(id)s].%(ext)s\f[R].
There is no real reason for this change.
This was changed before yt-dlp was ever made public and now there are no
plans to change it back to \f[V]%(title)s-%(id)s.%(ext)s\f[R].
Instead, you may use \f[V]--compat-options filename\f[R]
.IP \[bu] 2
The default format sorting is different from youtube-dl and prefers
higher resolution and better codecs rather than higher bitrates.
You can use the \f[V]--format-sort\f[R] option to change this to any
order you prefer, or use \f[V]--compat-options format-sort\f[R] to use
youtube-dl\[aq]s sorting order.
Older versions of yt-dlp preferred VP9 due to its broader compatibility;
you can use \f[V]--compat-options prefer-vp9-sort\f[R] to revert to that
format sorting preference.
These two compat options cannot be used together
.IP \[bu] 2
The default format selector is \f[V]bv*+ba/b\f[R].
This means that if a combined video + audio format that is better than
the best video-only format is found, the former will be preferred.
Use \f[V]-f bv+ba/b\f[R] or \f[V]--compat-options format-spec\f[R] to
revert this
.IP \[bu] 2
Unlike youtube-dlc, yt-dlp does not allow merging multiple audio/video
streams into one file by default (since this conflicts with the use of
\f[V]-f bv*+ba\f[R]).
If needed, this feature must be enabled using
\f[V]--audio-multistreams\f[R] and \f[V]--video-multistreams\f[R].
You can also use \f[V]--compat-options multistreams\f[R] to enable both
.IP \[bu] 2
\f[V]--no-abort-on-error\f[R] is enabled by default.
Use \f[V]--abort-on-error\f[R] or
\f[V]--compat-options abort-on-error\f[R] to abort on errors instead
.IP \[bu] 2
When writing metadata files such as thumbnails, description or infojson,
the same information (if available) is also written for playlists.
Use \f[V]--no-write-playlist-metafiles\f[R] or
\f[V]--compat-options no-playlist-metafiles\f[R] to not write these
files
.IP \[bu] 2
\f[V]--add-metadata\f[R] attaches the \f[V]infojson\f[R] to
\f[V]mkv\f[R] files in addition to writing the metadata when used with
\f[V]--write-info-json\f[R].
Use \f[V]--no-embed-info-json\f[R] or
\f[V]--compat-options no-attach-info-json\f[R] to revert this
.IP \[bu] 2
Some metadata are embedded into different fields when using
\f[V]--add-metadata\f[R] as compared to youtube-dl.
Most notably, \f[V]comment\f[R] field contains the \f[V]webpage_url\f[R]
and \f[V]synopsis\f[R] contains the \f[V]description\f[R].
You can use \f[V]--parse-metadata\f[R] to modify this to your liking or
use \f[V]--compat-options embed-metadata\f[R] to revert this
.IP \[bu] 2
\f[V]playlist_index\f[R] behaves differently when used with options like
\f[V]--playlist-reverse\f[R] and \f[V]--playlist-items\f[R].
See #302 (https://github.com/yt-dlp/yt-dlp/issues/302) for details.
You can use \f[V]--compat-options playlist-index\f[R] if you want to
keep the earlier behavior
.IP \[bu] 2
The output of \f[V]-F\f[R] is listed in a new format.
Use \f[V]--compat-options list-formats\f[R] to revert this
.IP \[bu] 2
Live chats (if available) are considered as subtitles.
Use \f[V]--sub-langs all,-live_chat\f[R] to download all subtitles
except live chat.
You can also use \f[V]--compat-options no-live-chat\f[R] to prevent any
live chat/danmaku from downloading
.IP \[bu] 2
YouTube channel URLs download all uploads of the channel.
To download only the videos in a specific tab, pass the tab\[aq]s URL.
If the channel does not show the requested tab, an error will be raised.
Also, \f[V]/live\f[R] URLs raise an error if there are no live videos
instead of silently downloading the entire channel.
You may use \f[V]--compat-options no-youtube-channel-redirect\f[R] to
revert all these redirections
.IP \[bu] 2
Unavailable videos are also listed for YouTube playlists.
Use \f[V]--compat-options no-youtube-unavailable-videos\f[R] to remove
this
.IP \[bu] 2
The upload dates extracted from YouTube are in UTC.
.IP \[bu] 2
If \f[V]ffmpeg\f[R] is used as the downloader, the downloading and
merging of formats happen in a single step when possible.
Use \f[V]--compat-options no-direct-merge\f[R] to revert this
.IP \[bu] 2
Thumbnail embedding in \f[V]mp4\f[R] is done with mutagen if possible.
Use \f[V]--compat-options embed-thumbnail-atomicparsley\f[R] to force
the use of AtomicParsley instead
.IP \[bu] 2
Some internal metadata such as filenames are removed by default from the
infojson.
Use \f[V]--no-clean-infojson\f[R] or
\f[V]--compat-options no-clean-infojson\f[R] to revert this
.IP \[bu] 2
When \f[V]--embed-subs\f[R] and \f[V]--write-subs\f[R] are used
together, the subtitles are written to disk and also embedded in the
media file.
You can use just \f[V]--embed-subs\f[R] to embed the subs and
automatically delete the separate file.
See #630
(comment) (https://github.com/yt-dlp/yt-dlp/issues/630#issuecomment-893659460)
for more info.
\f[V]--compat-options no-keep-subs\f[R] can be used to revert this
.IP \[bu] 2
\f[V]certifi\f[R] will be used for SSL root certificates, if installed.
If you want to use system certificates (e.g.
self-signed), use \f[V]--compat-options no-certifi\f[R]
.IP \[bu] 2
yt-dlp\[aq]s sanitization of invalid characters in filenames is
different/smarter than in youtube-dl.
You can use \f[V]--compat-options filename-sanitization\f[R] to revert
to youtube-dl\[aq]s behavior
.IP \[bu] 2
[STRIKEOUT:yt-dlp tries to parse the external downloader outputs into
the standard progress output if possible (Currently implemented:
aria2c (https://github.com/yt-dlp/yt-dlp/issues/5931)).
You can use \f[V]--compat-options no-external-downloader-progress\f[R]
to get the downloader output as-is]
.IP \[bu] 2
yt-dlp versions between 2021.09.01 and 2023.01.02 applies
\f[V]--match-filters\f[R] to nested playlists.
This was an unintentional side-effect of
8f18ac (https://github.com/yt-dlp/yt-dlp/commit/8f18aca8717bb0dd49054555af8d386e5eda3a88)
and is fixed in
d7b460 (https://github.com/yt-dlp/yt-dlp/commit/d7b460d0e5fc710950582baed2e3fc616ed98a80).
Use \f[V]--compat-options playlist-match-filter\f[R] to revert this
.IP \[bu] 2
yt-dlp versions between 2021.11.10 and 2023.06.21 estimated
\f[V]filesize_approx\f[R] values for fragmented/manifest formats.
This was added for convenience in
f2fe69 (https://github.com/yt-dlp/yt-dlp/commit/f2fe69c7b0d208bdb1f6292b4ae92bc1e1a7444a),
but was reverted in
0dff8e (https://github.com/yt-dlp/yt-dlp/commit/0dff8e4d1e6e9fb938f4256ea9af7d81f42fd54f)
due to the potentially extreme inaccuracy of the estimated values.
Use \f[V]--compat-options manifest-filesize-approx\f[R] to keep
extracting the estimated values
.IP \[bu] 2
yt-dlp uses modern http client backends such as \f[V]requests\f[R].
Use \f[V]--compat-options prefer-legacy-http-handler\f[R] to prefer the
legacy http handler (\f[V]urllib\f[R]) to be used for standard http
requests.
.IP \[bu] 2
The sub-modules \f[V]swfinterp\f[R], \f[V]casefold\f[R] are removed.
.IP \[bu] 2
Passing \f[V]--simulate\f[R] (or calling \f[V]extract_info\f[R] with
\f[V]download=False\f[R]) no longer alters the default format selection.
See #9843 (https://github.com/yt-dlp/yt-dlp/issues/9843) for details.
.PP
For ease of use, a few more compat options are available:
.IP \[bu] 2
\f[V]--compat-options all\f[R]: Use all compat options (\f[B]Do NOT use
this!\f[R])
.IP \[bu] 2
\f[V]--compat-options youtube-dl\f[R]: Same as
\f[V]--compat-options all,-multistreams,-playlist-match-filter,-manifest-filesize-approx,-allow-unsafe-ext,-prefer-vp9-sort\f[R]
.IP \[bu] 2
\f[V]--compat-options youtube-dlc\f[R]: Same as
\f[V]--compat-options all,-no-live-chat,-no-youtube-channel-redirect,-playlist-match-filter,-manifest-filesize-approx,-allow-unsafe-ext,-prefer-vp9-sort\f[R]
.IP \[bu] 2
\f[V]--compat-options 2021\f[R]: Same as
\f[V]--compat-options 2022,no-certifi,filename-sanitization\f[R]
.IP \[bu] 2
\f[V]--compat-options 2022\f[R]: Same as
\f[V]--compat-options 2023,playlist-match-filter,no-external-downloader-progress,prefer-legacy-http-handler,manifest-filesize-approx\f[R]
.IP \[bu] 2
\f[V]--compat-options 2023\f[R]: Same as
\f[V]--compat-options 2024,prefer-vp9-sort\f[R]
.IP \[bu] 2
\f[V]--compat-options 2024\f[R]: Currently does nothing.
Use this to enable all future compat options
.PP
The following compat options restore vulnerable behavior from before
security patches:
.IP \[bu] 2
\f[V]--compat-options allow-unsafe-ext\f[R]: Allow files with any
extension (including unsafe ones) to be downloaded
(GHSA-79w7-vh3h-8g4j (https://github.com/yt-dlp/yt-dlp/security/advisories/GHSA-79w7-vh3h-8g4j))
.RS 2
.RS
.PP
:warning: Only use if a valid file download is rejected because its
extension is detected as uncommon
.PP
\f[B]This option can enable remote code execution!
Consider opening an
issue (https://github.com/yt-dlp/yt-dlp/issues/new/choose) instead!\f[R]
.RE
.RE
.SS Deprecated options
.PP
These are all the deprecated options and the current alternative to
achieve the same effect
.SS Almost redundant options
.PP
While these options are almost the same as their new counterparts, there
are some differences that prevents them being redundant
.IP
.nf
\f[C]
-j, --dump-json                  --print \[dq]%()j\[dq]
-F, --list-formats               --print formats_table
--list-thumbnails                --print thumbnails_table --print playlist:thumbnails_table
--list-subs                      --print automatic_captions_table --print subtitles_table
\f[R]
.fi
.SS Redundant options
.PP
While these options are redundant, they are still expected to be used
due to their ease of use
.IP
.nf
\f[C]
--get-description                --print description
--get-duration                   --print duration_string
--get-filename                   --print filename
--get-format                     --print format
--get-id                         --print id
--get-thumbnail                  --print thumbnail
-e, --get-title                  --print title
-g, --get-url                    --print urls
--match-title REGEX              --match-filters \[dq]title \[ti]= (?i)REGEX\[dq]
--reject-title REGEX             --match-filters \[dq]title !\[ti]= (?i)REGEX\[dq]
--min-views COUNT                --match-filters \[dq]view_count >=? COUNT\[dq]
--max-views COUNT                --match-filters \[dq]view_count <=? COUNT\[dq]
--break-on-reject                Use --break-match-filters
--user-agent UA                  --add-headers \[dq]User-Agent:UA\[dq]
--referer URL                    --add-headers \[dq]Referer:URL\[dq]
--playlist-start NUMBER          -I NUMBER:
--playlist-end NUMBER            -I :NUMBER
--playlist-reverse               -I ::-1
--no-playlist-reverse            Default
--no-colors                      --color no_color
\f[R]
.fi
.SS Not recommended
.PP
While these options still work, their use is not recommended since there
are other alternatives to achieve the same
.IP
.nf
\f[C]
--force-generic-extractor        --ies generic,default
--exec-before-download CMD       --exec \[dq]before_dl:CMD\[dq]
--no-exec-before-download        --no-exec
--all-formats                    -f all
--all-subs                       --sub-langs all --write-subs
--print-json                     -j --no-simulate
--autonumber-size NUMBER         Use string formatting, e.g. %(autonumber)03d
--autonumber-start NUMBER        Use internal field formatting like %(autonumber+NUMBER)s
--id                             -o \[dq]%(id)s.%(ext)s\[dq]
--metadata-from-title FORMAT     --parse-metadata \[dq]%(title)s:FORMAT\[dq]
--hls-prefer-native              --downloader \[dq]m3u8:native\[dq]
--hls-prefer-ffmpeg              --downloader \[dq]m3u8:ffmpeg\[dq]
--list-formats-old               --compat-options list-formats (Alias: --no-list-formats-as-table)
--list-formats-as-table          --compat-options -list-formats [Default] (Alias: --no-list-formats-old)
--youtube-skip-dash-manifest     --extractor-args \[dq]youtube:skip=dash\[dq] (Alias: --no-youtube-include-dash-manifest)
--youtube-skip-hls-manifest      --extractor-args \[dq]youtube:skip=hls\[dq] (Alias: --no-youtube-include-hls-manifest)
--youtube-include-dash-manifest  Default (Alias: --no-youtube-skip-dash-manifest)
--youtube-include-hls-manifest   Default (Alias: --no-youtube-skip-hls-manifest)
--geo-bypass                     --xff \[dq]default\[dq]
--no-geo-bypass                  --xff \[dq]never\[dq]
--geo-bypass-country CODE        --xff CODE
--geo-bypass-ip-block IP_BLOCK   --xff IP_BLOCK
\f[R]
.fi
.SS Developer options
.PP
These options are not intended to be used by the end-user
.IP
.nf
\f[C]
--test                           Download only part of video for testing extractors
--load-pages                     Load pages dumped by --write-pages
--youtube-print-sig-code         For testing youtube signatures
--allow-unplayable-formats       List unplayable formats also
--no-allow-unplayable-formats    Default
\f[R]
.fi
.SS Old aliases
.PP
These are aliases that are no longer documented for various reasons
.IP
.nf
\f[C]
--avconv-location                --ffmpeg-location
--clean-infojson                 --clean-info-json
--cn-verification-proxy URL      --geo-verification-proxy URL
--dump-headers                   --print-traffic
--dump-intermediate-pages        --dump-pages
--force-write-download-archive   --force-write-archive
--load-info                      --load-info-json
--no-clean-infojson              --no-clean-info-json
--no-split-tracks                --no-split-chapters
--no-write-srt                   --no-write-subs
--prefer-unsecure                --prefer-insecure
--rate-limit RATE                --limit-rate RATE
--split-tracks                   --split-chapters
--srt-lang LANGS                 --sub-langs LANGS
--trim-file-names LENGTH         --trim-filenames LENGTH
--write-srt                      --write-subs
--yes-overwrites                 --force-overwrites
\f[R]
.fi
.SS Sponskrub Options
.PP
Support for SponSkrub (https://github.com/faissaloo/SponSkrub) has been
deprecated in favor of the \f[V]--sponsorblock\f[R] options
.IP
.nf
\f[C]
--sponskrub                      --sponsorblock-mark all
--no-sponskrub                   --no-sponsorblock
--sponskrub-cut                  --sponsorblock-remove all
--no-sponskrub-cut               --sponsorblock-remove -all
--sponskrub-force                Not applicable
--no-sponskrub-force             Not applicable
--sponskrub-location             Not applicable
--sponskrub-args                 Not applicable
\f[R]
.fi
.SS No longer supported
.PP
These options may no longer work as intended
.IP
.nf
\f[C]
--prefer-avconv                  avconv is not officially supported by yt-dlp (Alias: --no-prefer-ffmpeg)
--prefer-ffmpeg                  Default (Alias: --no-prefer-avconv)
-C, --call-home                  Not implemented
--no-call-home                   Default
--include-ads                    No longer supported
--no-include-ads                 Default
--write-annotations              No supported site has annotations now
--no-write-annotations           Default
--compat-options seperate-video-versions  No longer needed
--compat-options no-youtube-prefer-utc-upload-date  No longer supported
\f[R]
.fi
.SS Removed
.PP
These options were deprecated since 2014 and have now been entirely
removed
.IP
.nf
\f[C]
-A, --auto-number                -o \[dq]%(autonumber)s-%(id)s.%(ext)s\[dq]
-t, -l, --title, --literal       -o \[dq]%(title)s-%(id)s.%(ext)s\[dq]
\f[R]
.fi
.SH CONTRIBUTING
.PP
See CONTRIBUTING.md for instructions on Opening an Issue and
Contributing code to the project
.SH WIKI
.PP
See the Wiki (https://github.com/yt-dlp/yt-dlp/wiki) for more
information
