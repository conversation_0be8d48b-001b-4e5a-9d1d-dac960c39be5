#!/usr/bin/env python3
"""
测试视频合成stdout问题修复
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def test_video_composition():
    """测试视频合成功能"""
    try:
        from app.services.video_service import VideoService
        
        print("🎬 测试视频合成stdout问题修复...")
        
        # 初始化服务
        video_service = VideoService()
        print("✅ VideoService初始化成功")
        
        # 检查测试文件
        test_video = "python-ai-service/storage/uploads"
        test_outputs = "python-ai-service/storage/outputs"
        
        # 查找测试文件
        uploads_dir = Path(test_video)
        outputs_dir = Path(test_outputs)
        
        if not uploads_dir.exists():
            print(f"❌ 上传目录不存在: {uploads_dir}")
            return False
            
        if not outputs_dir.exists():
            print(f"❌ 输出目录不存在: {outputs_dir}")
            return False
        
        # 查找视频文件
        video_files = list(uploads_dir.glob("*.mp4"))
        if not video_files:
            print("❌ 没有找到测试视频文件")
            return False
            
        test_video_path = str(video_files[0])
        print(f"📹 找到测试视频: {test_video_path}")
        
        # 查找音频文件
        audio_files = list(outputs_dir.glob("tts_*.wav"))
        if not audio_files:
            print("❌ 没有找到TTS音频文件")
            print("请先运行TTS测试生成音频文件")
            return False
            
        test_audio_path = str(audio_files[0])
        print(f"🎵 找到测试音频: {test_audio_path}")
        
        # 测试视频合成
        print("\n🔧 开始测试视频合成...")
        try:
            result = await video_service.compose_video(
                video_path=test_video_path,
                audio_path=test_audio_path,
                subtitle_path=None,
                output_format="mp4"
            )
            
            print("✅ 视频合成测试成功!")
            print(f"📋 结果:")
            print(f"  输出文件: {result['video_file_path']}")
            print(f"  时长: {result['duration']:.2f}秒")
            print(f"  文件大小: {result['file_size']} 字节")
            
            # 检查输出文件
            output_file = Path(result['video_file_path'])
            if output_file.exists() and output_file.stat().st_size > 0:
                print(f"✅ 输出文件验证成功: {output_file.stat().st_size} 字节")
                return True
            else:
                print("❌ 输出文件验证失败")
                return False
                
        except Exception as e:
            print(f"❌ 视频合成失败: {e}")
            
            # 检查是否是stdout错误
            if "'NoneType' object has no attribute 'stdout'" in str(e):
                print("🔍 检测到stdout错误，这正是我们要修复的问题")
                print("修复应该能够处理这个错误并使用FFmpeg备选方案")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_stdout_handling():
    """测试stdout处理"""
    try:
        print("\n🖥️ 测试stdout处理...")
        
        import sys
        from io import StringIO
        from contextlib import redirect_stdout, redirect_stderr
        
        # 模拟Windows服务环境
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        
        try:
            # 测试重定向
            safe_stdout = StringIO()
            safe_stderr = StringIO()
            
            with redirect_stdout(safe_stdout), redirect_stderr(safe_stderr):
                print("这是测试输出")
                print("这应该被重定向到StringIO", file=sys.stderr)
            
            # 检查重定向是否工作
            stdout_content = safe_stdout.getvalue()
            stderr_content = safe_stderr.getvalue()
            
            if "这是测试输出" in stdout_content:
                print("✅ stdout重定向工作正常")
            else:
                print("❌ stdout重定向失败")
                return False
                
            if "这应该被重定向到StringIO" in stderr_content:
                print("✅ stderr重定向工作正常")
            else:
                print("❌ stderr重定向失败")
                return False
                
            return True
            
        finally:
            # 确保恢复原始流
            sys.stdout = original_stdout
            sys.stderr = original_stderr
            
    except Exception as e:
        print(f"❌ stdout处理测试失败: {e}")
        return False

async def test_ffmpeg_availability():
    """测试FFmpeg可用性"""
    try:
        print("\n🎥 测试FFmpeg可用性...")
        
        import subprocess
        
        # 测试FFmpeg命令
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ FFmpeg可用")
                # 显示版本信息的第一行
                version_line = result.stdout.split('\n')[0]
                print(f"  版本: {version_line}")
                return True
            else:
                print("❌ FFmpeg命令执行失败")
                return False
                
        except FileNotFoundError:
            print("❌ FFmpeg未安装或不在PATH中")
            return False
        except subprocess.TimeoutExpired:
            print("❌ FFmpeg命令超时")
            return False
            
    except Exception as e:
        print(f"❌ FFmpeg测试失败: {e}")
        return False

async def main():
    print("🚀 开始视频合成stdout问题修复测试...")
    
    # 1. 测试stdout处理
    stdout_ok = await test_stdout_handling()
    
    # 2. 测试FFmpeg可用性
    ffmpeg_ok = await test_ffmpeg_availability()
    
    # 3. 测试视频合成
    composition_ok = await test_video_composition()
    
    print(f"\n📊 测试结果总结:")
    print(f"  stdout处理: {'✅ 正常' if stdout_ok else '❌ 失败'}")
    print(f"  FFmpeg可用性: {'✅ 可用' if ffmpeg_ok else '❌ 不可用'}")
    print(f"  视频合成: {'✅ 成功' if composition_ok else '❌ 失败'}")
    
    if stdout_ok and ffmpeg_ok and composition_ok:
        print("\n🎉 视频合成修复测试成功!")
        print("stdout问题已经解决，视频合成应该正常工作")
    elif stdout_ok and ffmpeg_ok:
        print("\n⚠️ 基础功能正常，但视频合成仍有问题")
        print("可能需要检查测试文件或其他配置")
    else:
        print("\n❌ 基础功能有问题")
        if not stdout_ok:
            print("- stdout处理有问题")
        if not ffmpeg_ok:
            print("- FFmpeg不可用，需要安装FFmpeg")

if __name__ == "__main__":
    asyncio.run(main())
