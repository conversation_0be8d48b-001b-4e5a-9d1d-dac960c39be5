"""
Subtitle service for video translation AI service
Enhanced with subtitle burn-in capabilities
"""

import os
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional
from .base_service import BaseService
from app.models.schemas import SubtitleGenerationRequest, SubtitleSegment


class SubtitleService(BaseService):
    """Subtitle generation service"""
    
    def __init__(self):
        super().__init__()
        self.output_dir = Path(self.settings.outputs_path)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def generate_subtitles_to_path(self, segments: List[Dict[str, Any]], output_path: str,
                                        max_chars_per_line: int = 50, max_lines: int = 2) -> dict:
        """Generate subtitles and save to specified path"""
        try:
            self.logger.info(f"Generating subtitles to {output_path}")

            # Ensure output directory exists
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Generate subtitles using existing method
            result = await self.generate_subtitles(segments, max_chars_per_line, max_lines)

            # Move the result to the specified path
            import shutil
            shutil.move(result["subtitle_file_path"], output_path)

            # Update result with new path
            result["subtitle_file_path"] = output_path

            return result

        except Exception as e:
            self.logger.error(f"Subtitle generation to path failed: {e}")
            raise

    async def generate_subtitles_async(self, segments: List[Dict[str, Any]],
                                     max_chars_per_line: int = 50, max_lines: int = 2) -> str:
        """Create async task for subtitle generation"""
        task_id = await self.create_task("subtitle_generation", {
            "segments": segments,
            "max_chars_per_line": max_chars_per_line,
            "max_lines": max_lines
        })
        return task_id
    
    async def process_subtitle_generation(self, task_id: str, request: SubtitleGenerationRequest):
        """Process subtitle generation task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Generate subtitles
            result = await self.generate_subtitles(
                request.segments,
                request.max_chars_per_line,
                request.max_lines
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "subtitles": result["subtitles"],
                "subtitle_file_path": result["subtitle_file_path"]
            })
            
        except Exception as e:
            self.logger.error(f"Subtitle generation failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def generate_subtitles(self, segments: List[Dict[str, Any]], 
                               max_chars_per_line: int = 50, max_lines: int = 2) -> Dict[str, Any]:
        """Generate subtitles from text segments"""
        try:
            subtitles = []
            
            for i, segment in enumerate(segments):
                # Split text into lines if needed
                text = segment.get("text", "").strip()
                if not text:
                    continue
                
                lines = self._split_text_into_lines(text, max_chars_per_line, max_lines)
                
                subtitle = SubtitleSegment(
                    start_time=segment.get("start", 0.0),
                    end_time=segment.get("end", 0.0),
                    text="\n".join(lines),
                    index=i + 1
                )
                subtitles.append(subtitle)
            
            # Generate SRT file
            srt_path = await self._generate_srt_file(subtitles)
            
            return {
                "subtitles": [subtitle.dict() for subtitle in subtitles],
                "subtitle_file_path": str(srt_path)
            }
            
        except Exception as e:
            self.logger.error(f"Subtitle generation failed: {e}")
            raise
    
    def _split_text_into_lines(self, text: str, max_chars_per_line: int, max_lines: int) -> List[str]:
        """Split text into lines for subtitle display"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line) + len(word) + 1 <= max_chars_per_line:
                if current_line:
                    current_line += " " + word
                else:
                    current_line = word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
                
                # Check max lines limit
                if len(lines) >= max_lines:
                    break
        
        if current_line and len(lines) < max_lines:
            lines.append(current_line)
        
        return lines
    
    async def _generate_srt_file(self, subtitles: List[SubtitleSegment]) -> Path:
        """Generate SRT subtitle file"""
        srt_filename = f"subtitles_{self.generate_task_id()[:8]}.srt"
        srt_path = self.output_dir / srt_filename
        
        with open(srt_path, "w", encoding="utf-8") as f:
            for subtitle in subtitles:
                # Format timestamps
                start_time = self._format_timestamp(subtitle.start_time)
                end_time = self._format_timestamp(subtitle.end_time)
                
                # Write SRT format
                f.write(f"{subtitle.index}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{subtitle.text}\n\n")
        
        self.logger.info(f"SRT file generated: {srt_path}")
        return srt_path
    
    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp for SRT format"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    async def burn_subtitles_to_video(self, video_path: str, subtitle_path: str,
                                     output_path: str, subtitle_style: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Burn subtitles into video using FFmpeg

        Args:
            video_path: Input video file path
            subtitle_path: SRT subtitle file path
            output_path: Output video file path
            subtitle_style: Subtitle styling options

        Returns:
            Dict with result information
        """
        try:
            self.logger.info(f"Burning subtitles into video: {video_path} + {subtitle_path} -> {output_path}")

            # Ensure output directory exists
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Convert paths to absolute paths
            abs_video_path = os.path.abspath(video_path)
            abs_subtitle_path = os.path.abspath(subtitle_path)
            abs_output_path = os.path.abspath(output_path)

            # Validate input files
            if not os.path.exists(abs_video_path):
                raise FileNotFoundError(f"Video file not found: {abs_video_path}")
            if not os.path.exists(abs_subtitle_path):
                raise FileNotFoundError(f"Subtitle file not found: {abs_subtitle_path}")

            # Build subtitle filter with styling
            subtitle_filter = self._build_subtitle_filter(abs_subtitle_path, subtitle_style)

            # Build FFmpeg command
            ffmpeg_cmd = [
                'ffmpeg', '-y',  # Overwrite output file
                '-i', abs_video_path,  # Input video
                '-vf', subtitle_filter,  # Video filter with subtitles
                '-c:a', 'copy',  # Copy audio stream
                '-c:v', 'libx264',  # Re-encode video (required for subtitle burn-in)
                '-preset', 'medium',  # Encoding preset (balance between speed and quality)
                '-crf', '23',  # Constant Rate Factor (quality setting)
                abs_output_path  # Output file
            ]

            self.logger.info(f"Executing FFmpeg command: {' '.join(ffmpeg_cmd)}")

            # Execute FFmpeg command
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                timeout=1800,  # 30 minutes timeout
                cwd=os.getcwd()
            )

            if result.returncode == 0:
                self.logger.info(f"Subtitle burn-in successful: {abs_output_path}")

                # Get output file info
                file_size = os.path.getsize(abs_output_path)

                return {
                    "success": True,
                    "output_path": abs_output_path,
                    "file_size": file_size,
                    "message": "Subtitles burned into video successfully"
                }
            else:
                self.logger.error(f"FFmpeg failed with return code: {result.returncode}")
                self.logger.error(f"FFmpeg stderr: {result.stderr}")
                raise subprocess.CalledProcessError(result.returncode, ffmpeg_cmd, result.stderr)

        except Exception as e:
            self.logger.error(f"Subtitle burn-in failed: {e}")
            raise

    def _build_subtitle_filter(self, subtitle_path: str, style: Optional[Dict[str, Any]] = None) -> str:
        """
        Build FFmpeg subtitle filter with styling options

        Args:
            subtitle_path: Path to subtitle file
            style: Styling options

        Returns:
            FFmpeg subtitle filter string
        """
        # Default subtitle style (Netflix-like)
        default_style = {
            'font_name': 'Arial',
            'font_size': 24,
            'primary_color': '&Hffffff',  # White text
            'outline_color': '&H000000',  # Black outline
            'back_color': '&*********',   # Semi-transparent background
            'outline': 2,                 # Outline width
            'shadow': 1,                  # Shadow
            'alignment': 2,               # Bottom center
            'margin_v': 30                # Vertical margin from bottom
        }

        # Merge with user-provided style
        if style:
            default_style.update(style)

        # Escape the subtitle path for FFmpeg (handle spaces and special characters)
        # Convert Windows paths to forward slashes and escape colons
        escaped_path = subtitle_path.replace('\\', '/').replace(':', '\\:')

        # Build subtitle filter
        # Using subtitles filter which supports SRT files
        subtitle_filter = f"subtitles='{escaped_path}'"

        # Add style parameters using force_style
        style_params = []

        if 'font_name' in default_style:
            style_params.append(f"FontName={default_style['font_name']}")
        if 'font_size' in default_style:
            style_params.append(f"FontSize={default_style['font_size']}")
        if 'primary_color' in default_style:
            style_params.append(f"PrimaryColour={default_style['primary_color']}")
        if 'outline_color' in default_style:
            style_params.append(f"OutlineColour={default_style['outline_color']}")
        if 'back_color' in default_style:
            style_params.append(f"BackColour={default_style['back_color']}")
        if 'outline' in default_style:
            style_params.append(f"Outline={default_style['outline']}")
        if 'shadow' in default_style:
            style_params.append(f"Shadow={default_style['shadow']}")
        if 'alignment' in default_style:
            style_params.append(f"Alignment={default_style['alignment']}")
        if 'margin_v' in default_style:
            style_params.append(f"MarginV={default_style['margin_v']}")

        if style_params:
            force_style = ','.join(style_params)
            subtitle_filter += f":force_style='{force_style}'"

        return subtitle_filter

    async def create_dual_subtitles(self, original_segments: List[Dict[str, Any]],
                                   translated_segments: List[Dict[str, Any]],
                                   output_path: str) -> Dict[str, Any]:
        """
        Create dual-language subtitles (original + translation)

        Args:
            original_segments: Original language segments
            translated_segments: Translated segments
            output_path: Output SRT file path

        Returns:
            Dict with result information
        """
        try:
            self.logger.info(f"Creating dual subtitles: {output_path}")

            # Ensure output directory exists
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Combine segments
            dual_subtitles = []

            for i, (orig, trans) in enumerate(zip(original_segments, translated_segments)):
                # Create dual-language text
                original_text = orig.get("text", "").strip()
                translated_text = trans.get("text", "").strip()

                # Format: Original text on top, translation below
                dual_text = f"{original_text}\n{translated_text}"

                subtitle = SubtitleSegment(
                    start_time=orig.get("start", 0.0),
                    end_time=orig.get("end", 0.0),
                    text=dual_text,
                    index=i + 1
                )
                dual_subtitles.append(subtitle)

            # Generate SRT file
            with open(output_path, 'w', encoding='utf-8') as f:
                for subtitle in dual_subtitles:
                    # Format timestamps
                    start_time = self._format_timestamp(subtitle.start_time)
                    end_time = self._format_timestamp(subtitle.end_time)

                    # Write SRT format
                    f.write(f"{subtitle.index}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{subtitle.text}\n\n")

            self.logger.info(f"Dual subtitles created: {output_path}")

            return {
                "success": True,
                "subtitle_file_path": output_path,
                "subtitle_count": len(dual_subtitles),
                "message": "Dual-language subtitles created successfully"
            }

        except Exception as e:
            self.logger.error(f"Dual subtitle creation failed: {e}")
            raise

    async def process_task(self, task_id: str, *args, **kwargs):
        """Process subtitle task"""
        pass