"""
Subtitle service for video translation AI service
"""

from pathlib import Path
from typing import List, Dict, Any
from .base_service import BaseService
from app.models.schemas import SubtitleGenerationRequest, SubtitleSegment


class SubtitleService(BaseService):
    """Subtitle generation service"""
    
    def __init__(self):
        super().__init__()
        self.output_dir = Path(self.settings.outputs_path)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def generate_subtitles_async(self, segments: List[Dict[str, Any]], 
                                     max_chars_per_line: int = 50, max_lines: int = 2) -> str:
        """Create async task for subtitle generation"""
        task_id = await self.create_task("subtitle_generation", {
            "segments": segments,
            "max_chars_per_line": max_chars_per_line,
            "max_lines": max_lines
        })
        return task_id
    
    async def process_subtitle_generation(self, task_id: str, request: SubtitleGenerationRequest):
        """Process subtitle generation task"""
        try:
            await self.update_task_status(task_id, "processing", 0.0)
            
            # Generate subtitles
            result = await self.generate_subtitles(
                request.segments,
                request.max_chars_per_line,
                request.max_lines
            )
            
            await self.update_task_status(task_id, "completed", 100.0, {
                "subtitles": result["subtitles"],
                "subtitle_file_path": result["subtitle_file_path"]
            })
            
        except Exception as e:
            self.logger.error(f"Subtitle generation failed for task {task_id}: {e}")
            await self.update_task_status(task_id, "failed", 0.0, error=str(e))
    
    async def generate_subtitles(self, segments: List[Dict[str, Any]], 
                               max_chars_per_line: int = 50, max_lines: int = 2) -> Dict[str, Any]:
        """Generate subtitles from text segments"""
        try:
            subtitles = []
            
            for i, segment in enumerate(segments):
                # Split text into lines if needed
                text = segment.get("text", "").strip()
                if not text:
                    continue
                
                lines = self._split_text_into_lines(text, max_chars_per_line, max_lines)
                
                subtitle = SubtitleSegment(
                    start_time=segment.get("start", 0.0),
                    end_time=segment.get("end", 0.0),
                    text="\n".join(lines),
                    index=i + 1
                )
                subtitles.append(subtitle)
            
            # Generate SRT file
            srt_path = await self._generate_srt_file(subtitles)
            
            return {
                "subtitles": [subtitle.dict() for subtitle in subtitles],
                "subtitle_file_path": str(srt_path)
            }
            
        except Exception as e:
            self.logger.error(f"Subtitle generation failed: {e}")
            raise
    
    def _split_text_into_lines(self, text: str, max_chars_per_line: int, max_lines: int) -> List[str]:
        """Split text into lines for subtitle display"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line) + len(word) + 1 <= max_chars_per_line:
                if current_line:
                    current_line += " " + word
                else:
                    current_line = word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
                
                # Check max lines limit
                if len(lines) >= max_lines:
                    break
        
        if current_line and len(lines) < max_lines:
            lines.append(current_line)
        
        return lines
    
    async def _generate_srt_file(self, subtitles: List[SubtitleSegment]) -> Path:
        """Generate SRT subtitle file"""
        srt_filename = f"subtitles_{self.generate_task_id()[:8]}.srt"
        srt_path = self.output_dir / srt_filename
        
        with open(srt_path, "w", encoding="utf-8") as f:
            for subtitle in subtitles:
                # Format timestamps
                start_time = self._format_timestamp(subtitle.start_time)
                end_time = self._format_timestamp(subtitle.end_time)
                
                # Write SRT format
                f.write(f"{subtitle.index}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{subtitle.text}\n\n")
        
        self.logger.info(f"SRT file generated: {srt_path}")
        return srt_path
    
    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp for SRT format"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process subtitle task"""
        pass 