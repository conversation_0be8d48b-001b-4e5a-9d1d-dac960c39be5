package com.translation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * DeepL翻译服务
 * 高质量翻译，支持多种语言
 */
@Service
public class DeepLTranslationService {
    private static final Logger logger = LoggerFactory.getLogger(DeepLTranslationService.class);
    
    @Value("${deepl.api.key:}")
    private String apiKey;
    
    private final OkHttpClient client;
    private final ObjectMapper objectMapper;
    
    // DeepL API URLs
    private static final String FREE_API_URL = "https://api-free.deepl.com/v2/translate";
    private static final String PRO_API_URL = "https://api.deepl.com/v2/translate";
    
    public DeepLTranslationService() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 检查服务是否已配置
     */
    public boolean isConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() && 
               !"your-deepl-api-key".equals(apiKey);
    }
    
    /**
     * 翻译文本
     */
    public String translateText(String text, String sourceLanguage, String targetLanguage) throws IOException {
        if (!isConfigured()) {
            throw new IOException("DeepL翻译服务未配置：请设置 DEEPL_API_KEY 环境变量");
        }
        
        logger.info("开始DeepL翻译，源语言: {}, 目标语言: {}", sourceLanguage, targetLanguage);
        
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 转换语言代码
        String sourceLang = convertLanguageCode(sourceLanguage);
        String targetLang = convertLanguageCode(targetLanguage);
        
        // 判断使用免费版还是专业版API
        String apiUrl = apiKey.endsWith(":fx") ? FREE_API_URL : PRO_API_URL;
        
        RequestBody requestBody = new FormBody.Builder()
                .add("text", text)
                .add("source_lang", sourceLang)
                .add("target_lang", targetLang)
                .build();
        
        Request request = new Request.Builder()
                .url(apiUrl)
                .post(requestBody)
                .addHeader("Authorization", "DeepL-Auth-Key " + apiKey)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "";
                logger.error("DeepL翻译调用失败: {} - {}", response.code(), errorBody);
                throw new IOException("DeepL翻译调用失败: " + response.code());
            }
            
            String responseBody = response.body().string();
            logger.debug("DeepL翻译API响应: {}", responseBody);
            
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (jsonNode.has("translations") && jsonNode.get("translations").isArray()) {
                JsonNode translations = jsonNode.get("translations");
                if (translations.size() > 0) {
                    String result = translations.get(0).get("text").asText();
                    logger.info("DeepL翻译完成，原文长度: {}, 译文长度: {}", text.length(), result.length());
                    return result;
                }
            }
            
            throw new IOException("DeepL翻译返回格式错误");
        }
    }
    
    /**
     * 转换语言代码为DeepL格式
     */
    private String convertLanguageCode(String languageCode) {
        if (languageCode == null || languageCode.trim().isEmpty()) {
            return "EN"; // DeepL默认使用英语
        }
        
        switch (languageCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
                return "ZH";
            case "zh-tw":
                return "ZH-TW";
            case "en":
            case "en-us":
                return "EN-US";
            case "en-gb":
                return "EN-GB";
            case "ja":
                return "JA";
            case "ko":
                return "KO";
            case "fr":
                return "FR";
            case "de":
                return "DE";
            case "es":
                return "ES";
            case "ru":
                return "RU";
            case "pt":
                return "PT-PT";
            case "pt-br":
                return "PT-BR";
            case "it":
                return "IT";
            case "nl":
                return "NL";
            case "pl":
                return "PL";
            default:
                return languageCode.toUpperCase();
        }
    }
    
    /**
     * 获取支持的语言列表
     */
    public String getSupportedLanguages() throws IOException {
        if (!isConfigured()) {
            throw new IOException("DeepL翻译服务未配置");
        }
        
        String apiUrl = apiKey.endsWith(":fx") ? 
            "https://api-free.deepl.com/v2/languages" : 
            "https://api.deepl.com/v2/languages";
        
        Request request = new Request.Builder()
                .url(apiUrl)
                .get()
                .addHeader("Authorization", "DeepL-Auth-Key " + apiKey)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("获取DeepL支持语言失败: " + response.code());
            }
            
            return response.body().string();
        }
    }
    
    /**
     * 检查API使用情况
     */
    public String getUsage() throws IOException {
        if (!isConfigured()) {
            throw new IOException("DeepL翻译服务未配置");
        }
        
        String apiUrl = apiKey.endsWith(":fx") ? 
            "https://api-free.deepl.com/v2/usage" : 
            "https://api.deepl.com/v2/usage";
        
        Request request = new Request.Builder()
                .url(apiUrl)
                .get()
                .addHeader("Authorization", "DeepL-Auth-Key " + apiKey)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("获取DeepL使用情况失败: " + response.code());
            }
            
            return response.body().string();
        }
    }
}