version: '3.8'

services:
  translation-service:
    build: .
    container_name: translation-service
    ports:
      - "8081:8081"
    environment:
      # 从.env文件读取环境变量
      - BAIDU_TRANSLATE_APP_ID=${BAIDU_TRANSLATE_APP_ID}
      - BAIDU_TRANSLATE_SECRET_KEY=${BAIDU_TRANSLATE_SECRET_KEY}
      - GOOGLE_TRANSLATE_API_KEY=${GOOGLE_TRANSLATE_API_KEY}
      - DEEPL_API_KEY=${DEEPL_API_KEY}
      - SERVER_PORT=8081
      - LOGGING_LEVEL=INFO
      - HTTP_CONNECT_TIMEOUT=10000
      - HTTP_READ_TIMEOUT=30000
      - HTTP_WRITE_TIMEOUT=30000
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/translation/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - translation-network

networks:
  translation-network:
    driver: bridge