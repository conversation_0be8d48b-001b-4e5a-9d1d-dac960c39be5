"""
Configuration module for video translation AI service
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Application Settings
    app_name: str = Field(default="Video Translation AI Service", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Server Settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=4, env="WORKERS")
    
    # Database Settings
    database_url: str = Field(default="sqlite:///./storage/video_translation.db", env="DATABASE_URL")
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Message Queue Settings
    celery_broker_url: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    celery_result_backend: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    
    # AI Service Settings
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_base_url: Optional[str] = Field(default=None, env="OPENAI_BASE_URL")
    openai_model: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    google_cloud_credentials_path: Optional[str] = Field(default=None, env="GOOGLE_CLOUD_CREDENTIALS_PATH")
    
    # Storage Settings
    storage_path: str = Field(default="./storage", env="STORAGE_PATH")
    temp_path: str = Field(default="./storage/temp", env="TEMP_PATH")
    uploads_path: str = Field(default="./storage/uploads", env="UPLOADS_PATH")
    outputs_path: str = Field(default="./storage/outputs", env="OUTPUTS_PATH")
    max_file_size: str = Field(default="500MB", env="MAX_FILE_SIZE")
    
    # Audio Processing Settings
    audio_sample_rate: int = Field(default=16000, env="AUDIO_SAMPLE_RATE")
    audio_channels: int = Field(default=1, env="AUDIO_CHANNELS")
    audio_format: str = Field(default="wav", env="AUDIO_FORMAT")
    
    # Speech Recognition Settings
    whisper_model: str = Field(default="base", env="WHISPER_MODEL")
    whisper_language: str = Field(default="auto", env="WHISPER_LANGUAGE")
    
    # Translation Settings
    translation_service: str = Field(default="openai", env="TRANSLATION_SERVICE")
    target_language: str = Field(default="zh-CN", env="TARGET_LANGUAGE")
    source_language: str = Field(default="en", env="SOURCE_LANGUAGE")
    
    # TTS Settings
    tts_service: str = Field(default="edge-tts", env="TTS_SERVICE")
    tts_voice: str = Field(default="zh-CN-XiaoxiaoNeural", env="TTS_VOICE")
    tts_speed: float = Field(default=1.0, env="TTS_SPEED")
    tts_pitch: int = Field(default=0, env="TTS_PITCH")
    
    # Security Settings
    secret_key: str = Field(default="your-default-secret-key-change-in-production", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS Settings
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="CORS_ORIGINS"
    )
    cors_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE"],
        env="CORS_METHODS"
    )
    cors_headers: List[str] = Field(default=["*"], env="CORS_HEADERS")
    
    # Pydantic v2/pydantic-settings 新增配置方式
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore" # 忽略.env中未定义的字段
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create storage directories if they don't exist
        for path in [self.storage_path, self.temp_path, self.uploads_path, self.outputs_path]:
            os.makedirs(path, exist_ok=True)


# Global settings instance
settings = Settings() 