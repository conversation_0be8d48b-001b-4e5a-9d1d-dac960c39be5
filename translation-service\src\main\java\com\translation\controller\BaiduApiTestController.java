package com.translation.controller;

import com.translation.service.BaiduTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 百度API测试控制器
 * 用于测试百度API令牌获取功能
 */
@RestController
@RequestMapping("/api/baidu")
@CrossOrigin(origins = "*")
public class BaiduApiTestController {
    private static final Logger logger = LoggerFactory.getLogger(BaiduApiTestController.class);
    
    @Autowired
    private BaiduTokenService baiduTokenService;
    
    /**
     * 获取百度API访问令牌
     */
    @GetMapping("/token")
    public ResponseEntity<Map<String, Object>> getToken() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!baiduTokenService.isConfigured()) {
                response.put("success", false);
                response.put("error", "百度API服务未配置，请设置环境变量 BAIDU_API_CLIENT_ID 和 BAIDU_API_CLIENT_SECRET");
                return ResponseEntity.badRequest().body(response);
            }
            
            String token = baiduTokenService.getAccessToken();
            response.put("success", true);
            response.put("access_token", token);
            response.put("message", "百度API令牌获取成功");
            
            logger.info("百度API令牌获取成功");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取百度API令牌失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 检查百度API服务配置状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> response = new HashMap<>();
        
        boolean configured = baiduTokenService.isConfigured();
        response.put("configured", configured);
        response.put("service", "百度API令牌服务");
        
        if (configured) {
            response.put("message", "百度API服务已配置");
        } else {
            response.put("message", "百度API服务未配置，请设置环境变量");
            response.put("required_env", new String[]{"BAIDU_API_CLIENT_ID", "BAIDU_API_CLIENT_SECRET"});
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 测试百度API连接
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testConnection() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            baiduTokenService.testGetToken();
            response.put("success", true);
            response.put("message", "百度API连接测试成功");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("百度API连接测试失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}