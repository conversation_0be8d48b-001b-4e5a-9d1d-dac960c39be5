"""
Base service class for video translation AI service
"""

import uuid
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from datetime import datetime

from app.core.logging import get_logger
from app.core.config import settings


class BaseService(ABC):
    """Base service class with common functionality"""
    
    # 全局任务存储（所有服务共享）
    _global_tasks: Dict[str, Dict[str, Any]] = {}
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.settings = settings
    
    def generate_task_id(self) -> str:
        """Generate unique task ID"""
        return str(uuid.uuid4())
    
    def get_timestamp(self) -> datetime:
        """Get current timestamp"""
        return datetime.now()
    
    async def create_task(self, task_type: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Create a new task"""
        task_id = self.generate_task_id()
        self.logger.info(f"Created task {task_id} of type {task_type}")
        
        # 存储任务到全局任务字典
        self._global_tasks[task_id] = {
            "task_id": task_id,
            "task_type": task_type,
            "status": "pending",
            "progress": 0.0,
            "created_at": self.get_timestamp(),
            "updated_at": self.get_timestamp(),
            "result": None,
            "error": None,
            "metadata": metadata or {}
        }
        
        return task_id
    
    async def update_task_status(self, task_id: str, status: str, progress: float = 0.0, result: Optional[Dict[str, Any]] = None, error: Optional[str] = None):
        """Update task status"""
        self.logger.info(f"Task {task_id} status updated to {status} (progress: {progress}%)")
        
        # 更新全局任务字典
        if task_id in self._global_tasks:
            self._global_tasks[task_id].update({
                "status": status,
                "progress": progress,
                "updated_at": self.get_timestamp(),
                "result": result,
                "error": error
            })
    
    async def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task information"""
        return self._global_tasks.get(task_id)
    
    @abstractmethod
    async def process_task(self, task_id: str, *args, **kwargs):
        """Process task - to be implemented by subclasses"""
        pass 