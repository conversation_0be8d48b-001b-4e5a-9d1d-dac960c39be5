#!/usr/bin/env python3
"""
测试完整流程中的TTS问题
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def test_tts_in_full_pipeline_context():
    """在完整流程上下文中测试TTS"""
    try:
        print("🔄 测试完整流程中的TTS调用...")
        
        from app.services.tts_service import TTSService
        
        tts_service = TTSService()
        print("✅ TTS服务初始化成功")
        
        # 模拟完整流程中的翻译文本
        # 这应该是从英文翻译成中文的结果
        test_translation = "这是一个测试视频的中文翻译。我们正在验证文本转语音功能是否能够正确生成中文音频。"
        voice = "zh-CN-XiaoxiaoNeural"
        
        print(f"翻译文本: {test_translation}")
        print(f"使用语音: {voice}")
        
        # 使用与完整流程相同的调用方式
        print("\n🎤 开始TTS合成...")
        result = await tts_service.synthesize_speech(
            test_translation,
            voice,
            1.0,
            0,
            "edge-tts"
        )
        
        print(f"\n📋 TTS结果:")
        print(f"  音频文件路径: {result['audio_file_path']}")
        print(f"  时长: {result['duration']:.2f}秒")
        print(f"  文本: {result['text']}")
        print(f"  服务: {result.get('service', 'unknown')}")
        print(f"  是否回退: {result.get('fallback', False)}")
        
        if result.get('fallback'):
            print(f"  ⚠️ 回退原因: {result.get('message', 'Unknown')}")
            
            # 检查回退文件
            fallback_file = Path(result['audio_file_path'])
            if fallback_file.exists():
                print(f"  📄 回退文件存在: {fallback_file}")
                if fallback_file.suffix == '.txt':
                    print("  📝 这是一个文本回退文件")
                    try:
                        with open(fallback_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            print(f"  内容: {content[:100]}...")
                    except Exception as e:
                        print(f"  读取失败: {e}")
                else:
                    print("  🎵 这是一个音频文件")
            else:
                print("  ❌ 回退文件不存在")
            
            return False
        else:
            # 检查生成的音频文件
            audio_file = Path(result['audio_file_path'])
            if audio_file.exists():
                file_size = audio_file.stat().st_size
                print(f"  📁 文件大小: {file_size} 字节")
                
                if file_size > 0:
                    print("  ✅ TTS音频生成成功")
                    
                    # 验证音频文件
                    try:
                        if audio_file.suffix == '.wav':
                            import wave
                            with wave.open(str(audio_file), 'rb') as wav_file:
                                frames = wav_file.getnframes()
                                rate = wav_file.getframerate()
                                duration = frames / rate
                                print(f"  🎵 音频时长: {duration:.2f}秒")
                                print(f"  📊 采样率: {rate}Hz")
                                
                                if frames > 0:
                                    print("  ✅ 音频文件有效")
                                    return True
                                else:
                                    print("  ❌ 音频文件为空")
                                    return False
                    except Exception as e:
                        print(f"  ❌ 音频文件验证失败: {e}")
                        return False
                else:
                    print("  ❌ 音频文件为空")
                    return False
            else:
                print("  ❌ 音频文件不存在")
                return False
        
    except Exception as e:
        print(f"❌ 完整流程TTS测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tts_with_retry_mechanism():
    """测试带重试机制的TTS（模拟完整流程）"""
    try:
        print("\n🔄 测试带重试机制的TTS...")
        
        from app.services.tts_service import TTSService
        
        tts_service = TTSService()
        
        # 模拟完整流程中的重试逻辑
        test_text = "这是重试机制测试文本。"
        voice = "zh-CN-XiaoxiaoNeural"
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                print(f"  尝试 {attempt + 1}/{max_retries}...")
                
                result = await tts_service.synthesize_speech(
                    test_text,
                    voice,
                    1.0,
                    0,
                    "edge-tts"
                )
                
                if result and not result.get("fallback"):
                    print(f"  ✅ 第{attempt + 1}次尝试成功")
                    
                    # 验证文件
                    audio_file = Path(result['audio_file_path'])
                    if audio_file.exists() and audio_file.stat().st_size > 0:
                        print(f"  📁 音频文件: {audio_file.name} ({audio_file.stat().st_size} 字节)")
                        return True
                    else:
                        print(f"  ❌ 音频文件无效")
                        return False
                        
                elif result and result.get("fallback"):
                    print(f"  ⚠️ 第{attempt + 1}次尝试返回回退: {result.get('message', 'Unknown')}")
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 3
                        print(f"  等待{wait_time}秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print("  接受回退结果")
                        return False
                else:
                    print(f"  ❌ 第{attempt + 1}次尝试返回无效结果")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2)
                        continue
                    else:
                        return False
                        
            except Exception as e:
                print(f"  ❌ 第{attempt + 1}次尝试异常: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    continue
                else:
                    return False
        
        return False
        
    except Exception as e:
        print(f"❌ 重试机制测试失败: {e}")
        return False

async def check_edge_tts_status():
    """检查Edge TTS状态"""
    try:
        print("\n🌐 检查Edge TTS状态...")
        
        import edge_tts
        
        # 测试网络连接
        try:
            voices = await edge_tts.list_voices()
            chinese_voices = [v for v in voices if 'zh-CN' in v['Locale']]
            print(f"✅ 网络连接正常，找到{len(chinese_voices)}个中文语音")
        except Exception as e:
            print(f"❌ 网络连接失败: {e}")
            return False
        
        # 测试简单合成
        try:
            communicate = edge_tts.Communicate("测试", "zh-CN-XiaoxiaoNeural")
            test_file = "edge_tts_health_check.wav"
            await communicate.save(test_file)
            
            if os.path.exists(test_file) and os.path.getsize(test_file) > 0:
                print("✅ Edge TTS合成测试成功")
                os.remove(test_file)
                return True
            else:
                print("❌ Edge TTS合成测试失败")
                return False
                
        except Exception as e:
            print(f"❌ Edge TTS合成测试异常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Edge TTS状态检查失败: {e}")
        return False

async def main():
    print("🔍 开始完整流程TTS问题诊断...")
    
    # 1. 检查Edge TTS状态
    edge_ok = await check_edge_tts_status()
    
    # 2. 测试完整流程中的TTS调用
    pipeline_tts_ok = await test_tts_in_full_pipeline_context()
    
    # 3. 测试重试机制
    retry_ok = await test_tts_with_retry_mechanism()
    
    print(f"\n📊 诊断结果总结:")
    print(f"  Edge TTS状态: {'✅ 正常' if edge_ok else '❌ 异常'}")
    print(f"  完整流程TTS: {'✅ 成功' if pipeline_tts_ok else '❌ 失败'}")
    print(f"  重试机制: {'✅ 正常' if retry_ok else '❌ 异常'}")
    
    print(f"\n🎯 问题分析:")
    
    if not edge_ok:
        print("❌ Edge TTS服务不可用")
        print("  解决方案:")
        print("  - 检查网络连接")
        print("  - 尝试使用VPN")
        print("  - 检查防火墙设置")
    elif not pipeline_tts_ok:
        print("❌ 完整流程中TTS调用失败")
        print("  可能原因:")
        print("  - 翻译文本格式问题")
        print("  - TTS服务配置问题")
        print("  - 网络间歇性问题")
        print("  解决方案:")
        print("  - 增加更多重试")
        print("  - 改进错误处理")
        print("  - 使用本地TTS作为备选")
    elif not retry_ok:
        print("❌ 重试机制有问题")
        print("  解决方案:")
        print("  - 调整重试间隔")
        print("  - 增加重试次数")
        print("  - 改进重试逻辑")
    else:
        print("✅ TTS功能正常")
        print("  问题可能在于:")
        print("  - 完整流程的调用时序")
        print("  - 视频合成时的音频处理")
        print("  - 文件路径问题")

if __name__ == "__main__":
    asyncio.run(main())
