#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析字幕时间分布
检查字幕之间的时间间隔和覆盖率
"""

import re

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    hours, minutes, seconds = time_str.strip().split(':')
    seconds_parts = seconds.split('.')
    seconds = int(seconds_parts[0])
    milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
    return int(hours) * 3600 + int(minutes) * 60 + seconds + milliseconds / 1000

def read_srt_file(srt_path: str):
    """读取SRT字幕文件"""
    subtitles = []
    
    with open(srt_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                # 尝试解析第一行为数字索引
                index = int(lines[0])
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                # 检查时间行格式
                if ' --> ' in time_line:
                    # 解析时间
                    start_time_str, end_time_str = time_line.split(' --> ')
                    start_time = parse_srt_time(start_time_str)
                    end_time = parse_srt_time(end_time_str)
                    
                    subtitles.append((index, start_time, end_time, text.strip()))
            except ValueError:
                continue
    
    return subtitles

def analyze_subtitle_timing(srt_path: str, total_duration: float):
    """分析字幕时间分布"""
    print(f"📝 分析字幕文件: {srt_path}")
    print(f"🎬 视频总时长: {total_duration/60:.1f}分钟 ({total_duration:.2f}秒)")
    
    subtitles = read_srt_file(srt_path)
    print(f"📊 字幕条数: {len(subtitles)}")
    
    if not subtitles:
        print("❌ 没有找到有效字幕")
        return
    
    # 计算字幕覆盖时间
    total_subtitle_duration = 0
    gaps = []
    
    # 按开始时间排序
    subtitles.sort(key=lambda x: x[1])
    
    for i, (index, start_time, end_time, text) in enumerate(subtitles):
        duration = end_time - start_time
        total_subtitle_duration += duration
        
        # 计算与前一个字幕的间隔
        if i > 0:
            prev_end = subtitles[i-1][2]
            gap = start_time - prev_end
            if gap > 0:
                gaps.append(gap)
    
    # 统计信息
    coverage_rate = (total_subtitle_duration / total_duration) * 100
    avg_subtitle_duration = total_subtitle_duration / len(subtitles)
    
    print(f"📊 字幕总覆盖时间: {total_subtitle_duration/60:.1f}分钟 ({total_subtitle_duration:.2f}秒)")
    print(f"📊 字幕覆盖率: {coverage_rate:.2f}%")
    print(f"📊 平均字幕时长: {avg_subtitle_duration:.2f}秒")
    
    if gaps:
        total_gap_time = sum(gaps)
        avg_gap = sum(gaps) / len(gaps)
        max_gap = max(gaps)
        min_gap = min(gaps)
        
        print(f"📊 字幕间隙总时间: {total_gap_time/60:.1f}分钟 ({total_gap_time:.2f}秒)")
        print(f"📊 平均间隙: {avg_gap:.2f}秒")
        print(f"📊 最大间隙: {max_gap:.2f}秒")
        print(f"📊 最小间隙: {min_gap:.2f}秒")
        
        # 分析大间隙
        large_gaps = [g for g in gaps if g > 5]  # 大于5秒的间隙
        if large_gaps:
            print(f"⚠️  发现 {len(large_gaps)} 个大间隙（>5秒）")
            print(f"⚠️  大间隙总时间: {sum(large_gaps)/60:.1f}分钟")
    
    # 分析第一个和最后一个字幕
    first_start = subtitles[0][1]
    last_end = subtitles[-1][2]
    
    print(f"📊 第一个字幕开始时间: {first_start:.2f}秒")
    print(f"📊 最后一个字幕结束时间: {last_end:.2f}秒")
    
    if first_start > 1:
        print(f"⚠️  视频开始有 {first_start:.2f}秒 静音")
    
    if last_end < total_duration - 1:
        end_silence = total_duration - last_end
        print(f"⚠️  视频结尾有 {end_silence:.2f}秒 静音")

def main():
    """主函数"""
    print("🎯 字幕时间分布分析器")
    
    srt_path = "VideoLingo20250607_2(1)/VideoLingo20250607/translation-service-python/out/output_chinese.srt"
    total_duration = 4242.68  # 从之前的测试得到
    
    analyze_subtitle_timing(srt_path, total_duration)

if __name__ == "__main__":
    main() 