#!/usr/bin/env python3
"""
测试音频修复的脚本
"""

import os
import subprocess
from pathlib import Path

def check_audio_file(audio_path):
    """检查音频文件"""
    print(f"检查音频文件: {audio_path}")
    
    if not os.path.exists(audio_path):
        print(f"❌ 文件不存在")
        return False
    
    file_size = os.path.getsize(audio_path)
    print(f"✅ 文件存在，大小: {file_size} 字节")
    
    if audio_path.endswith('.txt'):
        print("📄 这是文本文件（TTS回退）")
        return False
    
    # 检查是否为有效的音频文件
    try:
        import wave
        with wave.open(audio_path, 'rb') as wav_file:
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            duration = frames / sample_rate if sample_rate > 0 else 0
            
            print(f"🎵 音频信息:")
            print(f"  帧数: {frames}")
            print(f"  采样率: {sample_rate} Hz")
            print(f"  声道数: {channels}")
            print(f"  时长: {duration:.2f} 秒")
            
            if frames > 0:
                print("✅ 音频文件有效")
                return True
            else:
                print("❌ 音频文件为空")
                return False
                
    except Exception as e:
        print(f"❌ 音频文件检查失败: {e}")
        
        # 尝试用FFprobe检查
        try:
            cmd = ['ffprobe', '-v', 'quiet', '-show_streams', '-select_streams', 'a', audio_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and 'codec_type=audio' in result.stdout:
                print("✅ FFprobe确认音频文件有效")
                return True
            else:
                print("❌ FFprobe确认音频文件无效")
                return False
        except Exception as e2:
            print(f"❌ FFprobe检查也失败: {e2}")
            return False

def test_recent_files():
    """测试最近的文件"""
    outputs_dir = Path("python-ai-service/storage/outputs")
    
    if not outputs_dir.exists():
        print("❌ outputs目录不存在")
        return
    
    # 查找最近的TTS文件
    tts_files = list(outputs_dir.glob("tts_*.wav"))
    tts_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    print(f"找到 {len(tts_files)} 个TTS音频文件:")
    
    for i, tts_file in enumerate(tts_files[:3]):  # 只检查最近的3个
        print(f"\n--- 文件 {i+1}: {tts_file.name} ---")
        is_valid = check_audio_file(str(tts_file))
        
        if is_valid:
            print("🎉 这个音频文件可以用于视频合成")
        else:
            print("⚠️ 这个音频文件可能导致视频没有声音")

if __name__ == "__main__":
    test_recent_files()
