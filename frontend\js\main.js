/**
 * 主应用程序逻辑
 */

// 全局变量
let currentTasks = new Map(); // 存储当前任务信息
let serviceStatus = 'checking'; // 服务状态

/**
 * 应用程序初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('视频翻译AI服务前端已加载');
    
    // 初始化应用程序
    initializeApp();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 检查服务状态
    checkServiceHealth();
    
    // 语音选择已简化为默认本地TTS
    
    // 定期检查服务状态
    setInterval(checkServiceHealth, 30000); // 每30秒检查一次
});

/**
 * 初始化应用程序
 */
function initializeApp() {
    // 初始化Bootstrap组件
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    console.log('应用程序初始化完成');
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 健康检查按钮
    document.getElementById('healthCheck').addEventListener('click', function(e) {
        e.preventDefault();
        checkServiceHealth();
    });
    
    // 完整翻译流程表单
    document.getElementById('fullPipelineForm').addEventListener('submit', handleFullPipelineSubmit);
    
    // 音频提取表单
    document.getElementById('audioExtractionForm').addEventListener('submit', handleAudioExtractionSubmit);
    
    // 语音识别表单
    document.getElementById('speechRecognitionForm').addEventListener('submit', handleSpeechRecognitionSubmit);
    
    // 文本翻译表单
    document.getElementById('translationForm').addEventListener('submit', handleTranslationSubmit);
    
    console.log('事件监听器绑定完成');
}

/**
 * 检查服务健康状态
 */
async function checkServiceHealth() {
    const statusElement = document.getElementById('serviceStatus');
    
    try {
        updateServiceStatus('checking', '正在检查服务状态...', 'warning');
        
        const health = await apiClient.healthCheck();
        
        updateServiceStatus('online', `服务正常运行 - ${health.service} v${health.version}`, 'success');
        serviceStatus = 'online';
        
    } catch (error) {
        console.error('服务健康检查失败:', error);
        updateServiceStatus('offline', `服务不可用: ${error.message}`, 'danger');
        serviceStatus = 'offline';
    }
}

// 语音选择功能已删除，直接使用本地TTS

/**
 * 更新服务状态显示
 */
function updateServiceStatus(status, message, type) {
    const statusElement = document.getElementById('serviceStatus');
    const iconMap = {
        checking: 'fas fa-spinner fa-spin',
        online: 'fas fa-check-circle',
        offline: 'fas fa-times-circle'
    };
    
    statusElement.innerHTML = `
        <i class="${iconMap[status]} text-${type} me-2"></i>
        <span class="text-${type}">${message}</span>
    `;
}

/**
 * 处理完整翻译流程提交
 */
async function handleFullPipelineSubmit(e) {
    e.preventDefault();
    
    const videoFileInput = document.getElementById('videoFile');
    const videoFile = videoFileInput.files[0];
    
    if (!videoFile) {
        showNotification('请选择视频文件', 'warning');
        return;
    }
    
    console.log('选择的视频文件:', videoFile.name, '大小:', videoFile.size, 'bytes');
    
    if (serviceStatus !== 'online') {
        showNotification('服务当前不可用，请稍后重试', 'danger');
        return;
    }
    
    try {
        // 显示加载状态
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在上传文件...';
        submitBtn.disabled = true;
        
        // 首先上传文件
        showNotification('正在上传视频文件...', 'info');
        const uploadResult = await fileUploadManager.uploadFile(videoFile, (progress) => {
            submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>上传中... ${Math.round(progress)}%`;
        });
        
        showNotification('文件上传成功，开始处理...', 'success');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在处理...';
        
        // 准备翻译请求数据
        const requestData = {
            video_file_path: uploadResult.file_path, // 使用服务器返回的文件路径
            source_language: document.getElementById('sourceLanguage').value,
            target_language: document.getElementById('targetLanguage').value,
            include_subtitles: document.getElementById('includeSubtitles').checked,
                            tts_voice: "local"  // 使用本地TTS
        };
        
        console.log('发送完整翻译请求:', requestData);
        
        // 发送请求
        const response = await apiClient.fullPipeline(requestData);
        
        console.log('完整翻译响应:', response);
        
        if (response.task_id) {
            showNotification('翻译任务已启动', 'success');
            addTaskToList(response.task_id, 'full_pipeline', 'pending');
            startTaskPolling(response.task_id);
        }
        
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
    } catch (error) {
        console.error('完整翻译失败:', error);
        showNotification(`翻译失败: ${error.message}`, 'danger');
        
        // 恢复按钮状态
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-play"></i> 开始完整翻译';
        submitBtn.disabled = false;
    }
}

/**
 * 处理音频提取提交
 */
async function handleAudioExtractionSubmit(e) {
    e.preventDefault();
    
    const videoFileInput = document.getElementById('audioVideoFile');
    const videoFile = videoFileInput.files[0];
    
    if (!videoFile) {
        showNotification('请选择视频文件', 'warning');
        return;
    }
    
    try {
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';
        submitBtn.disabled = true;
        
        // 上传文件
        const uploadResult = await fileUploadManager.uploadFile(videoFile, (progress) => {
            submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>上传中... ${Math.round(progress)}%`;
        });
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提取中...';
        
        const requestData = {
            video_file_path: uploadResult.file_path,
            output_format: 'wav',
            sample_rate: 16000,
            channels: 1
        };
        
        const response = await apiClient.extractAudio(requestData);
        
        if (response.task_id) {
            showNotification('音频提取任务已启动', 'success');
            addTaskToList(response.task_id, 'audio_extraction', 'pending');
            startTaskPolling(response.task_id);
        }
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
    } catch (error) {
        console.error('音频提取失败:', error);
        showNotification(`音频提取失败: ${error.message}`, 'danger');
        
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-extract"></i> 提取音频';
        submitBtn.disabled = false;
    }
}

/**
 * 处理语音识别提交
 */
async function handleSpeechRecognitionSubmit(e) {
    e.preventDefault();
    
    const audioFileInput = document.getElementById('audioFile');
    const audioFile = audioFileInput.files[0];
    
    if (!audioFile) {
        showNotification('请选择音频文件', 'warning');
        return;
    }
    
    try {
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';
        submitBtn.disabled = true;
        
        // 上传音频文件
        const uploadResult = await fileUploadManager.uploadFile(audioFile, (progress) => {
            submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>上传中... ${Math.round(progress)}%`;
        });
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>识别中...';
        
        const requestData = {
            audio_file_path: uploadResult.file_path,
            language: 'auto',
            model: 'base'
        };
        
        const response = await apiClient.speechRecognition(requestData);
        
        if (response.task_id) {
            showNotification('语音识别任务已启动', 'success');
            addTaskToList(response.task_id, 'speech_recognition', 'pending');
            startTaskPolling(response.task_id);
        }
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
    } catch (error) {
        console.error('语音识别失败:', error);
        showNotification(`语音识别失败: ${error.message}`, 'danger');
        
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-comments"></i> 识别语音';
        submitBtn.disabled = false;
    }
}

/**
 * 处理文本翻译提交
 */
async function handleTranslationSubmit(e) {
    e.preventDefault();
    
    const textToTranslate = document.getElementById('textToTranslate').value.trim();
    
    if (!textToTranslate) {
        showNotification('请输入要翻译的文本', 'warning');
        return;
    }
    
    try {
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>翻译中...';
        submitBtn.disabled = true;
        
        const requestData = {
            text: textToTranslate,
            source_language: 'en',
            target_language: 'zh-CN',
            service: 'openai'
        };
        
        const response = await apiClient.translateText(requestData);
        
        if (response.task_id) {
            showNotification('文本翻译任务已启动', 'success');
            addTaskToList(response.task_id, 'translation', 'pending');
            startTaskPolling(response.task_id);
        }
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
    } catch (error) {
        console.error('文本翻译失败:', error);
        showNotification(`文本翻译失败: ${error.message}`, 'danger');
        
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-globe"></i> 翻译文本';
        submitBtn.disabled = false;
    }
}

/**
 * 添加任务到列表
 */
function addTaskToList(taskId, taskType, status) {
    const task = {
        task_id: taskId,
        task_type: taskType,
        status: status,
        progress: 0,
        created_at: new Date().toISOString()
    };
    
    currentTasks.set(taskId, task);
    updateCurrentTaskDisplay(task);
    updateTaskHistory();
}

/**
 * 开始任务轮询
 */
function startTaskPolling(taskId) {
    taskPollingManager.startPolling(taskId, (taskInfo) => {
        if (taskInfo.error) {
            showNotification(`任务 ${taskId} 状态更新失败: ${taskInfo.error}`, 'danger');
            return;
        }
        
        // 更新任务信息
        currentTasks.set(taskId, taskInfo);
        updateCurrentTaskDisplay(taskInfo);
        updateTaskHistory();
        
        // 如果任务完成，显示结果
        if (taskInfo.status === 'completed') {
            showNotification(`任务 ${taskId} 已完成`, 'success');
            displayTaskResult(taskInfo);
        } else if (taskInfo.status === 'failed') {
            showNotification(`任务 ${taskId} 失败: ${taskInfo.error || '未知错误'}`, 'danger');
        }
    });
}

/**
 * 更新当前任务显示
 */
function updateCurrentTaskDisplay(task) {
    const currentTaskElement = document.getElementById('currentTask');
    const taskIdElement = document.getElementById('currentTaskId');
    const taskStatusElement = document.getElementById('currentTaskStatus');
    const taskProgressElement = document.getElementById('currentTaskProgress');
    const taskTypeElement = document.getElementById('currentTaskType');
    const taskCreatedElement = document.getElementById('currentTaskCreated');
    
    // 显示当前任务区域
    currentTaskElement.style.display = 'block';
    
    // 更新任务信息
    taskIdElement.textContent = task.task_id;
    taskStatusElement.textContent = getStatusText(task.status);
    taskStatusElement.className = `badge bg-${getStatusColor(task.status)}`;
    
    const progress = task.progress || 0;
    taskProgressElement.style.width = `${progress}%`;
    taskProgressElement.textContent = `${Math.round(progress)}%`;
    taskProgressElement.className = `progress-bar bg-${getStatusColor(task.status)}`;
    
    taskTypeElement.textContent = getTaskTypeText(task.task_type);
    taskCreatedElement.textContent = new Date(task.created_at).toLocaleString('zh-CN');
}

/**
 * 更新任务历史
 */
function updateTaskHistory() {
    const taskHistoryElement = document.getElementById('taskHistory');
    
    if (currentTasks.size === 0) {
        taskHistoryElement.innerHTML = `
            <div class="col-12 text-muted text-center">
                <i class="fas fa-info-circle"></i> 暂无任务记录
            </div>
        `;
        return;
    }
    
    const tasksArray = Array.from(currentTasks.values());
    tasksArray.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    
    taskHistoryElement.innerHTML = tasksArray.map(task => `
        <div class="col-md-6 mb-3">
            <div class="card task-card ${task.status}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-${getStatusColor(task.status)}">${getStatusText(task.status)}</span>
                        <small class="text-muted">${task.task_id}</small>
                    </div>
                    <div class="mb-2">
                        <strong>${getTaskTypeText(task.task_type)}</strong>
                    </div>
                    <div class="progress mb-2" style="height: 6px;">
                        <div class="progress-bar bg-${getStatusColor(task.status)}" 
                             style="width: ${task.progress || 0}%"></div>
                    </div>
                    <small class="text-muted">
                        创建时间: ${new Date(task.created_at).toLocaleString('zh-CN')}
                    </small>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * 显示任务结果
 */
function displayTaskResult(task) {
    const resultArea = document.getElementById('resultArea');
    const resultContent = document.getElementById('resultContent');
    
    resultArea.style.display = 'block';
    
    // Check for fallback information
    if (task.result?.fallback_info?.has_fallbacks) {
        const fallbackInfo = task.result.fallback_info;
        
        // Create a user-friendly display with fallback warnings
        let displayHTML = `
            <div class="alert alert-warning">
                <h5>⚠️ 任务完成（有服务限制）</h5>
                <p>${fallbackInfo.note}</p>
                <ul>
                    ${fallbackInfo.messages.map(msg => `<li>${msg}</li>`).join('')}
                </ul>
            </div>
        `;
        
        // Add result details
        displayHTML += '<div class="mt-3"><h6>任务结果:</h6>';
        
        const result = task.result;
        
        if (result.translated_video) {
            displayHTML += `<p><strong>翻译视频:</strong> <a href="${result.translated_video}" target="_blank" class="btn btn-sm btn-primary">下载视频</a></p>`;
        }
        
        if (result.transcript) {
            displayHTML += `<p><strong>语音识别结果:</strong> ${result.transcript}</p>`;
        }
        
        if (result.translation) {
            displayHTML += `<p><strong>翻译结果:</strong> ${result.translation}</p>`;
        }
        
        if (result.tts_fallback) {
            displayHTML += `<p><strong>TTS音频:</strong> <span class="text-warning">由于TTS服务不可用，已保存为文本文件</span> <a href="${result.tts_audio}" target="_blank" class="btn btn-sm btn-outline-secondary">查看文本</a></p>`;
        } else if (result.tts_audio) {
            displayHTML += `<p><strong>TTS音频:</strong> <a href="${result.tts_audio}" target="_blank" class="btn btn-sm btn-success">播放音频</a></p>`;
        }
        
        if (result.subtitle_file) {
            displayHTML += `<p><strong>字幕文件:</strong> <a href="${result.subtitle_file}" target="_blank" class="btn btn-sm btn-info">下载字幕</a></p>`;
        }
        
        if (result.duration) {
            displayHTML += `<p><strong>时长:</strong> ${result.duration.toFixed(2)} 秒</p>`;
        }
        
        displayHTML += '</div>';
        
        // Add raw JSON for debugging (collapsible)
        displayHTML += `
            <div class="mt-3">
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#rawResult">
                    显示原始数据
                </button>
                <div class="collapse mt-2" id="rawResult">
                    <pre class="bg-light p-2 rounded"><code>${JSON.stringify(task.result, null, 2)}</code></pre>
                </div>
            </div>
        `;
        
        resultContent.innerHTML = displayHTML;
        
    } else {
        // Normal result display
        let displayHTML = '<div class="alert alert-success"><h5>✅ 任务完成</h5></div>';
        
        const result = task.result;
        
        if (result?.translated_video) {
            displayHTML += `<p><strong>翻译视频:</strong> <a href="${result.translated_video}" target="_blank" class="btn btn-sm btn-primary">下载视频</a></p>`;
        }
        
        // Add other result details similar to above...
        displayHTML += `<pre class="bg-light p-2 rounded"><code>${JSON.stringify(task.result || task, null, 2)}</code></pre>`;
        
        resultContent.innerHTML = displayHTML;
    }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        failed: '失败',
        cancelled: '已取消'
    };
    return statusMap[status] || status;
}

/**
 * 获取状态颜色
 */
function getStatusColor(status) {
    const colorMap = {
        pending: 'secondary',
        processing: 'info',
        completed: 'success',
        failed: 'danger',
        cancelled: 'warning'
    };
    return colorMap[status] || 'secondary';
}

/**
 * 获取任务类型文本
 */
function getTaskTypeText(taskType) {
    const typeMap = {
        audio_extraction: '音频提取',
        speech_recognition: '语音识别',
        translation: '文本翻译',
        subtitle_generation: '字幕生成',
        tts_generation: '语音合成',
        video_composition: '视频合成',
        full_pipeline: '完整翻译流程'
    };
    return typeMap[taskType] || taskType;
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    const toast = document.getElementById('notificationToast');
    const toastTitle = document.getElementById('toastTitle');
    const toastBody = document.getElementById('toastBody');
    const toastIcon = document.getElementById('toastIcon');
    
    // 设置图标和颜色
    const iconMap = {
        success: 'fas fa-check-circle text-success',
        danger: 'fas fa-exclamation-circle text-danger',
        warning: 'fas fa-exclamation-triangle text-warning',
        info: 'fas fa-info-circle text-info'
    };
    
    toastIcon.className = iconMap[type] || iconMap.info;
    toastTitle.textContent = type === 'danger' ? '错误' : type === 'warning' ? '警告' : type === 'success' ? '成功' : '通知';
    toastBody.textContent = message;
    
    // 显示Toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    console.log(`[${type.toUpperCase()}] ${message}`);
}

/**
 * 页面卸载时清理资源
 */
window.addEventListener('beforeunload', function() {
    taskPollingManager.stopAllPolling();
}); 