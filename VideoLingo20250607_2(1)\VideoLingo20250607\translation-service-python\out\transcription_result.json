{"segments": [{"text": " Hey everyone, welcome to this crash course on Phaser Editor V4. Today we'll explore how you can quickly and easily build Phaser 3 games without needing to write a ton of code manually.", "start": 0.009, "end": 8.456}, {"text": " If you're new to Phaser or game development in general, you're in the right place. Phaser Editor V4 is a powerful tool for building HTML5 games visually, letting you focus on creating rather than getting lost in code. In this video, I'll guide you step-by-step through setting up a project, using the editor, and creating a simple game. Here's what we'll cover today. What is the Phaser Editor? How to quickly set up a brand new project. We'll do a high-level overview of the workbench and the various views in the UI. How the editor auto-generates code based on the actions you take in the GUI.", "start": 8.899, "end": 36.101}, {"text": " how to run and test your games locally, adding keyboard input and moving game objects and reaction to these events, working with arcade physics and collisions, handling object overlaps, using prefabs for usable game components, controlling object layers, and rendering order, adding new assets and creating new animations for your game.", "start": 36.101, "end": 53.951}, {"text": " To follow along, make sure you have access to Phaser Editor v4. I'll link the documentation in the description for more detailed setup instructions. Before we dive in, just a quick note. This tutorial uses Phaser Editor v4. Some experience with JavaScript and the Phaser 3 framework will help, but even if you're totally new, you'll still be able to follow along easily. We won't be covering installation in this video, but don't worry, I've linked the docs and resources in the description. Now let's get into it. So in this crash course, we'll be creating a very basic Phaser 3 game.", "start": 54.172, "end": 82.568}, {"text": " And the goal of this game is just to help teach us the basics of using the Phaser Editor to create a Phaser 3 game. So in our game, we'll create a very simple title scene where the player can click to start our next scene. So we'll learn about scene transitions. And then we'll see how we can move our player, so our dinosaur, around our game. And we can collide with blocks. And then we'll be able to pick up food items in our game. And so we'll see how we can add different variations of our food items. And we can also load in sprite sheets and create basic animations.", "start": 82.995, "end": 109.855}, {"text": " And so this is a very basic game, but it allows us to grasp the basics of creating Phaser 3 games using the Phaser Editor. So what is Phaser Editor? At a high level, Phaser Editor is a game development tool that was designed to help you build Phaser 3 games faster. It can assist with filling out your game scenes, manage your assets, and even generating code. A more formal description is Phaser Editor is a powerful visual development tool for creating 2D games using the Phaser game engine.", "start": 109.957, "end": 139.667}, {"text": " With its intuitive interface and extensive set of features, it allows developers of all skill levels to quickly and easily create high-quality games for desktop and mobile platforms. Whether you're a beginner or an experienced developer, Phaser Editor can enhance your Phaser 3 game development experience.", "start": 139.838, "end": 154.838}, {"text": " So some of the main features of the phaser editor are the scene editor is a visual tool to allow you to create phaser scenes or levels by positioning images and other types of game object. The tool allows you to drag and drop various phaser game object types into your scene. So this could be images, text, and much more. The scene editor is a scene preview is rendered directly with phaser. And so you can be sure what you see is what will be in your final game.", "start": 155.333, "end": 179.821}, {"text": " Next up is your physics editor. So this tool allows you to easily create and modify your arcade physics game objects. So you'll be able to visually modify the bodies for your game objects and you'll no longer have to do this manually through code. You'll also be able to do things like add collision and overlap checks through the GUI.", "start": 180.794, "end": 196.391}, {"text": " Next, we have our Animations Editor. And so this is a tool that allows you to manually or automatically create your sprite animations from your texture frames, and you no longer have to dynamically generate your JSON files for your various animations. Through the GUI, you can modify your configuration for things like your frame rate, your delay, your repeat, time scale, and much more. Next up is your Asset Packed Editor.", "start": 197.176, "end": 219.087}, {"text": " So as you add new assets to your project, the phaser editor will allow you to import these into an asset pack file. And the asset pack file can then be used to load all of your game assets for one central location. The asset pack editor allows you to view these files and see what type of game assets are included. And you can modify that JSON configuration.", "start": 219.258, "end": 238.012}, {"text": " Finally, we have our prefab system. Prefabs are a key concept in the Phaser Editor, and at a high level, they allow you to create reusable game objects, where you can control their appearance, their properties, and settings, and store in one central location. And then you can create multiple instances of that object by using that prefab. Now let's open Phaser Editor and start creating our project. Once you have the editor open, click on New Project to create a brand new Phaser Editor project. On the next screen, you'll be able to choose a starting template for your project.", "start": 238.66, "end": 268.66}, {"text": " And so this is going to scaffold your Phaser 3 game project. Your three built-in project templates here are included offline as far as the Phaser editor install process. The starters, examples, and more advanced framework examples will need to be downloaded from online. And so to get started, we'll be using this basic JavaScript template. Once you choose a template, you'll be prompted for where you'd like to save your project at on your machine. So you'll need to make a new folder. I'm just going to call this Hello World.", "start": 268.985, "end": 297.739}, {"text": " Once you make a new project folder,", "start": 298.575, "end": 299.991}, {"text": " you want to open up that project folder, and now the phaser editor is going to set up your project template and scaffold all the project files you need for running your game locally. Once everything's set up, you're presented with the welcome page. From here, we can choose open a scene to select from one of the two scenes that are included as part of our project template. Let's open up our level scene. And now our editor will be updated with our phaser scene and the game objects that currently exist in it. Once you open up your phaser editor project, in the workbench or the IDE,", "start": 300.043, "end": 329.633}, {"text": " you'll be presented with a number of different views and menu options that are available to you. At a high level, on the left-hand side here, on the outline view, this is going to show you all the content of your active editor. But it's going to be in a hierarchy like this. And so we're able to see when we have a scene open, so we're in the scene editor, we're going to see our various game objects that are included in our scene. And so as an example, if I add additional game objects, we'll see they now appear in our outline over here.", "start": 329.957, "end": 359.326}, {"text": " And so as we change to our various editors, this will be updated with the content of that editor. So when we're editing things like asset packs, we'll see the assets that make up that asset pack. Next, on the right-hand side, we have the inspector panel. And so your inspector view allows you to see all of the properties for the object that you currently have selected and active in the editor. And so as an example, currently we don't have anything selected, so it defaults to our scene. So for our scene, we're going to see a bunch of options that we can use for modifying our scene properties.", "start": 360.009, "end": 389.94}, {"text": " Versus if I select like this image game object here, our panel updates, and now we see all of the properties for our game object. And now we can do things like modify our variable name, we can update the position of our game object, and much more. And so depending on which editor you're in, your panel will reflect the properties of the object they are currently selecting. So on the bottom of your screen, you'll see the files view. Your files view will display a list of all of your project files. And like any other file editor, you can do basic actions like add new files,", "start": 390.299, "end": 418.49}, {"text": " delete, rename, and even move files around in your project. When you open up one of your files, it's going to open up in the IDE here, and they'll be able to modify it. Like any other object in the phaser editor, when you select one of your files, our inspector panel is going to update to display information related to that file. Next to that, we have our blocks view. And so this is going to show you elements that can be used to build objects in the current active editor.", "start": 418.729, "end": 442.193}, {"text": " And so when we have our scene editor open, this is going to show us the various built-in Phaser 3 game object types that we can add to our scene. And it's going to show any images or other assets that we've loaded, and we can drag those into our scene as well. If you have a different active editor, like the asset pack editor, it's going to show you the various files that you can import into your asset pack. Next up, in the top right-hand corner, we have our main menu. And so this will allow you to open up different projects, open a new project window, and various things like that.", "start": 442.5, "end": 470.503}, {"text": " You can also do things like change the default layout of your scene, choose a theme color, and much more. Next to that, we have the support chat button for contacting the Phaser Studio team. In the top left-hand corner, we have a couple of shortcuts for creating new content. The play button, which will allow you to run your project and view it in your browser. Then we have the quick dialogue for opening up a scene. And then we have the open the project inside Visual Studio Code. So when you click on this button, it's gonna open up your project with the current active file in Visual Studio Code.", "start": 470.74199999999996, "end": 499.053}, {"text": " So when you click that button, VS Code should now open up if you have it installed locally, and you'll be able to see the code that makes up your project. Finally, at the top of our screen, we're going to see a couple different tools that are going to change depending on which editor we have open. So as an example, on the welcome screen, there's no tools up here. If we're on the asset-packed editor, we have different options for importing files. And now when we're in our scene editor, now we have tools for working in our scene. So for our scene editor tools, the first one is the translate tool.", "start": 499.25800000000004, "end": 526.954}, {"text": " The translate tool allows you to select a game object and then you can drag it around your scene and it's going to update the X and Y position of that game object. Next, we have our scale tool. The scale tool allows you to drag and scale up your game objects on the X and Y coordinates. And as we modify these properties, we're going to scale up our game objects and we'll see that's reflected here. The next tool is the rotate tool. This allows you to select a game object and then you can rotate it and modify its angle of your game object. Next is the origin tool.", "start": 527.1759999999999, "end": 555.23}, {"text": " This allows you to modify the origin for your game objects. By default, for most game objects, the origin is in the center of the game object, so it's 0.5 for both X and Y. And so your origin is how Phaser places game objects in your scene. It uses that point as the starting point for your game object. So for our X and Y value here, it's based on this origin. So if I place my origin up in this top left-hand corner, and I do something like modify my position to be 0, 0,", "start": 555.5029999999999, "end": 582.227}, {"text": " we'll see now it's using this as our starting point for our game object. If I place our origin back in the middle, we'll now see that that position is now at 0, 0, and it's using that as the origin for our game object. Next up, we have the Select All Objects Within a Region tool. This allows you to drag and drop a region", "start": 582.483, "end": 599.991}, {"text": " of where you like to select game objects, and then this allows you to do the multi-select. Finally, we have the pan tool. This allows you to navigate around your viewport by just clicking and dragging on your scene. And so if you ever need to see the shortcuts, if you hover over your object here, you'll be able to see what the shortcut is for that tool. And on that note, many of the properties in our GUI, when you hover over them, it'll give you a tool tip of how that's actually used by the phaser editor.", "start": 600.009, "end": 625.708}, {"text": " So as we're working in our project and we're moving things around our scene, under the hood, the phaser editor is dynamically generating code for our phaser project. And so to see this code, we actually need to open up the .js file that's associated with our level. So in the GUI, when we create new files for our project, typically there's going to be two files.", "start": 629.019, "end": 647.449}, {"text": " One will be the .scene file, and this is the file that's used by the editor to present it inside the GUI here. And then your .js or .ts files are going to be the code that's generated from the editor based on the actions we're taking. So when we use our project template, this automatically created the level.js and level.scene files. But if I wanted to make a new scene as an example, if I do new, I do scene file. I'm just going to call this test.", "start": 647.739, "end": 672.193}, {"text": " we'll see first our test.scene file is created, and now we see our scene in our GUI, and then our test.js file is also created. So in the code that's generated, there's really two main parts. The first part is the code that's generated by the compiler, and then the second part is the safe areas where you can add your own code. And so throughout our code, we're going to see these two different code blocks. The first will be the start of compiled code and the end of compiled code, and then we'll have the start of where users can do something and the end of where users can do something.", "start": 672.722, "end": 700.759}, {"text": " Anywhere that is not within these blocks of where it says start user, end user, you don't want to add code there because the compiled code will be automatically generated anytime we take an action. And so anything you add can be overridden. When you add your code within these blocks here where it says start user, end user, these areas the editor will not modify and your code will persist between every time it's compiled. So to see an example of this, if we open up our level.js file,", "start": 700.9300000000001, "end": 726.101}, {"text": " So inside here, we have code that is tied to our game objects that are in our level scene. We currently have two game objects. One is this image game object, and the other one is this text game object. So in our code, we have two variables. We have dino, so this is referencing our image game object, and then welcome is referencing that text game object. So currently, they have their own X and Y position, so we see if our image is 329 by 235. So if we select our image game object, we see that our position reflects that.", "start": 726.442, "end": 754.667}, {"text": " So if I take our dinosaur and I drag it like over here, so now we see we've modified those properties. If we come back to our level.js file, we'll see now our code has been updated to reflect that change. And so this is important as you add your own custom code to your project, you'll want to make sure you put it in the right areas so then that way it doesn't get overridden.", "start": 754.855, "end": 774.172}, {"text": " To run and test your game locally, you'll need to run a dev server. By default, the phaser editor provides a built-in HTTP server that can do this. So we use the basic JavaScript project template. If you choose the play button here in the top left-hand corner, this is going to start that development server and is going to launch your game in your web browser. Once this happens, you should see our local host port with editor external, and we should see our phaser game here. So when you have that development server running, if you move the game objects around your scene and you hit save, if you come back to your browser,", "start": 776.596, "end": 806.4590000000001}, {"text": " We'll need to refresh, and we should see our game is now updated with the changes that we made. So one thing to note is when you use the default JavaScript project template, our built-in HTTP server works without any issues. However, if you're using a different project template that's based on modern web technologies like Webpack, Parcel, or Vite, in these cases, you'll need to run a development server manually. When you click the Play button up here, if Phaser Editor does not detect that there's a development server running based on the configuration you provide, it's going to show a dialog.", "start": 806.766, "end": 836.049}, {"text": " That's going to give you the option to start that dev server, and then you'll be able to launch your game in the browser for you. The Phaser framework supports a variety of input types. This can be keyboard, mouse, gamepad, and even touch on mobile.", "start": 836.288, "end": 851.715}, {"text": " And the first type of input we'll take a look at will be click events. So click events in your Phaser game is typically a mechanism where you can allow a player to interact with your Phaser scene or your game objects directly, and when they click on them, we can do something in our game. And by default, our Phaser template here has support for click events. So if we come over to our browser where our game's running, if we click on our dinosaur game object, we'll see our text is updated with new text here.", "start": 851.92, "end": 876.578}, {"text": " And how this works is when you choose a game object in the scene editor, you can define a hit area for that game object. Your hit area is the area of the game object that the player can actually click on, and then we can listen for that click event and other various event types. For your hit area, you can choose a variety of different shapes, and so by default, generally we want to use a rectangle for most of your image game objects.", "start": 876.7660000000001, "end": 899.991}, {"text": " And when you choose rectangle, the default width and height will be the size of that image that you loaded in your scene. Versus if we choose this text game object here, we'll see right now our hit area is none. And so when you add new game objects to your scene, generally there's not a hit area defined for that object and you need to manually define it. So in this scene here, we just add another image game object, dino1. If we jump over to our level.js file,", "start": 900.009, "end": 924.906}, {"text": " If we go to where we have our definition for dyno one, we'll see we have our game object here. And one of the key differences between the original dyno and this new one is the set interactive method here. For the phaser framework to enable input for game objects, we need to call that method. And the phaser editor, once we define that hit area, is going to add code to do that for us automatically. So now that we've added this rectangle hit area,", "start": 925.179, "end": 950.401}, {"text": " If we come back to our level.js, we'll see now setInteract has been called with the definition of that rectangle we provided. The second piece for your click events is you need to listen for that event and react to it. And down here in this create method, this is where custom code that was part of the template was added to listen for this event. So the phaser framework has a variety of built-in events that are emitted", "start": 950.862, "end": 975.367}, {"text": " for all kinds of different things in the system. And so when we listen for our input events, this could be pointer down, pointer up, and much more. We can now provide a callback function that we want to run when this happens. And so this is a good example of how we can take the existing phaser editor code that's generated and enhance it with more functionality. We can add in an event listener and modify our other game objects we created in our scene editor. So as an example, let's just copy this block of code. We'll go ahead and paste it.", "start": 975.538, "end": 1004.241}, {"text": " And we're going to listen for the pointer down event on our dyno1 variable. So we're going to do this. We'll do dyno1. We'll do on pointer down. But instead of saying hello world, we'll just say hi. So if we come back to our level scene, in order for us to use and run that code, we need to update our variable scope. So for the JavaScript code that's created, that's tied to our variable definition over here in our inspector. So by default, when we add in our new game objects, the scope will always be local to our class where it's created.", "start": 1004.633, "end": 1034.343}, {"text": " And what that means is we'll only be able to use it in the local context of where our code's added here in this editor create method. What that means is we won't be able to use it anywhere else in our class unless we change that property.", "start": 1034.701, "end": 1048.882}, {"text": " So if we take a look at the original dyno, we'll see this has the scope of class. And so we'll want to update the scope here, our variable scope for our new dyno. So let's change this and we'll make this class variable. And what that means is it's going to be available to any of our logic inside our level class here. So we take a look at our code that was generated. We'll see now we have this new dyno1 property, and that's going to allow us to reference it down here in our create method.", "start": 1049.121, "end": 1076.135}, {"text": " And so to see an example, if we don't change that, we're going to get an error in our game. So if we save our changes, come back to our browser, let's refresh our game. And so we're going to open up our developer tools. So if we right click and do inspect, let's go to console to see our developer console. And we'll see now we have an error tied to the code that was generated in our level.js file. So it says cannot read properties that are undefined on. And this is because back in our level code,", "start": 1076.442, "end": 1101.459}, {"text": " We don't actually have a property called dino1, and so this is undefined, and this causes our error. Now if we change this back from local to class, now we have this property in our code, and now our code should run properly. And so now if we click on our second dino, we say hi, and if we click on our first one, it says hello world.", "start": 1101.766, "end": 1120.128}, {"text": " So as I mentioned, there is a variety of built-in events we can listen for. So on the phaser framework documentation website, we have a list of supported events and the ones we're interested in right now are going to be our phaser input events. So for our input, we have support for things like clicking and dragging an object around our scene. We can drop an object onto another object. We can listen for our pointer down, pointer up and pointer over events. So then that way we can have like buttons where if our mouse hovers over it, we", "start": 1120.674, "end": 1148.558}, {"text": " apply an effect, if our mouse leaves it, it changes, and much more. And besides our basic input events, we also have events for like our gamepad and for our keyboard. So then we can listen for when these keys are pressed and then react accordingly. And so because all these events are supported in the phaser framework, that means they're all supported inside the phaser editor and we just need to have code to react to them.", "start": 1148.9850000000001, "end": 1170.93}, {"text": " So the next type of input we'll take a look at is going to be our keyboard events. So what we're going to do is we're going to add support for listening for our arrow keys, and then we're going to move our game objects around in our scene. So to add support for keyboard events in our built-in blocks, one of the types is input. When we expand this, we have the option to add a keyboard key to our scene. Let's drag this block onto our scene, and we'll see there's a new folder added called input. And so by default, our variable name will be keyboard key. Let's change this to up key. Now for our", "start": 1171.152, "end": 1199.991}, {"text": " key it has a default scope of class and we'll need this to be class because we'll need to listen for input and react to it in our update method for our scene. Next we can choose which key we like to list for input on and so we're going to do our up key. So once we choose that let's save our scene jump over to level.js and we'll see here in our editor create method our up key variable was created and we're listening for our input plug-in", "start": 1200.009, "end": 1224.326}, {"text": " our keyboard manager, and we're doing add key, which is going to allow us to now listen for input when a key is pressed, when it's held down, and when it's released. This is going to get assigned to our up key property that we added for our class. So now to react to our key being pressed, we actually need to add a new method to our code, and we're going to add the update method.", "start": 1224.923, "end": 1243.046}, {"text": " And so this method here is one of the built-in methods that are available on your phaser scene class. And this method is going to be called every tick or every game update or every update that's made to your game. And so what we can do inside this method now is we can check to see if our key's being pressed. And so to do that, we're just going to add an if statement. So we'll do if. We're going to reference our up key. So if our up key is down, that means it's being pressed down right now by the player.", "start": 1243.387, "end": 1268.677}, {"text": " And so now we can take one of our game objects and we can update its position based on this key being pressed. So let's reference our dino game object and we'll reference our Y property. So when we press the up key, we want our game object to move up on our scene. So inside your scenes, your top left-hand corner here is going to be position 0, 0. So 0 for X and 0 for Y.", "start": 1269.138, "end": 1292.09}, {"text": " And as we move right across our scene, this is going to increase our x value. And we move left, we're going to decrease our x value. And when we move down, that's going to increase our y value. And if we move up, we want to decrement that. So what that means is for when we want to move, when the up key is pressed, we actually want to subtract a value from our y. So we're just going to do minus equals 4. So if we save our code, if we make sure our scene is saved, let's come back to our browser. Let's refresh our scene.", "start": 1292.381, "end": 1320.452}, {"text": " And now if we hold our up key, we can see now we can move our game object and it's moving up in our scene. So now we're going to do the same things for the rest of our arrow keys. And so let's drag in a couple more keyboard blocks. Next, let's do our down key. So we want to choose down for our key code. Next, we'll do left. So we'll do left key. Let's look for left. Finally, we have right key. Let's save.", "start": 1320.759, "end": 1350.06}, {"text": " jump over to our level.js file, and now we want to do the same thing, but we want to reference our other keys that were created. So if we take a quick look up in our editor create method, we'll see we have our other keys got defined based on the key codes we specified. So let's come back to our update method. So we'll do else if, let's copy this here. So if our up key, so if our down key is down,", "start": 1350.299, "end": 1376.0149999999999}, {"text": " Now we want to go ahead and add to our Y value. Now let's copy this whole block of code here and let's do our left and right keys. So if our left key is down, we want to subtract from our X value. If our right key is down, we want to increment our X value. So if we save, let's come back to our browser. Now if we press our arrow keys, we can now move our game object around in our scene.", "start": 1376.237, "end": 1405.23}, {"text": " So now we can move our objects around our scene by using our keyboard events. The next thing we're going to take a look at is how we can have our game objects interact with each other by using the physics system. So out of the box, Phaser Editor supports the arcade physics system from Phaser,", "start": 1408.763, "end": 1423.114}, {"text": " which allows us to do things like to check for collisions between two different game objects, we can check when they overlap, and much more. And so the Phaser framework has support for a variety of physics systems, such as Arcade Physics, Impact Physics, and Matter.js. The Arcade Physics is perfect for simple games where we want to just check for collisions and different things like that.", "start": 1423.507, "end": 1444.241}, {"text": " And so to enable our game for physics, we need to update our game configuration to tell Phaser which physics system we like to use. For our game configuration, this is going to be contained in our main .js file here. And inside the code, we're going to have this new Phaser game where we create a new Phaser game instance and we provide our configuration for our game. Inside here, this is where we can provide which our physics system we like to use. And so if we add the physics property, this is going to be an object.", "start": 1444.497, "end": 1471.203}, {"text": " Our first property we want to add is default. And this is going to be a string of which system we want to use by default. And we're going to do arcade. Next, we provide a property of which physics system we're using. So in this case, it'll be arcade. And now we want to provide our configuration for this. The first we're going to do is debug. We're going to set this to true. This is going to allow us to see the outline of our physics bodies. And then that way makes it very easy to work with the physics system while we're designing our game. You'll want to turn this to false once you're finished with your game.", "start": 1471.527, "end": 1499.991}, {"text": " Next, we can define our default gravity that will be applied to our X and Y properties. So we're going to set this to zero for both. Then let's go ahead and save. So now that we've enabled our physics system for our game, we need to update our game objects to actually use this. And so there's a few different ways we can do this. The first is we can add a physics body to an existing game object.", "start": 1500.009, "end": 1522.09}, {"text": " And this is the physics body is how we check for those collisions. The second way is we can change our default object type. So when we drag an image over by default, it's going to be an image game object. So if we click on our dyno, we can change this to an arcade image. Now an arcade image is an extension of your image game object, but it has more properties that are going to be tied to the physics. So real quick, if we take a look at our inspector,", "start": 1522.329, "end": 1546.954}, {"text": " we'll see now we have new areas tied to our physics body and for how our physics should interact with our game object. Versus if we choose our other dino, we see those properties do not exist in our inspector. And now the third way for adding our physics is we can use our built-in object types and choose our arcade image or arcade sprite and drag those onto our scene. And now it lets us select that texture we like to use. And by default, that type will now be arcade image and we don't have to manually change that.", "start": 1547.398, "end": 1576.869}, {"text": " So after we've updated our type of our game object to be arcade image, if we take a look at our panel, by default, we'll have an arcade physics body. And so if we save, let's open up our web browser again. We'll refresh. We'll see now we have this purple box around our dino. And what that purple box represents, that is our arcade physics body. So by default, this is going to use the width and height", "start": 1577.568, "end": 1601.578}, {"text": " of our game object. And so our image is currently these dimensions. And that's why our purple box is around our game object instead of matching the dinosaur directly. And so that physics body, this purple outline, this is now how we can check for collisions with other game objects that have physics enabled for them as well. So if we come back to our scene, if we choose our second dino, let's go ahead and update its type as well. And we're going to change the type to be the arcade image.", "start": 1601.903, "end": 1629.991}, {"text": " we'll do replace let's do save let's come back and now we have our two game objects that are enabled for physics so just like our input events once we have our physics body we actually need to tell the editor what we want to check for collisions with and so we could check for one game object we could check for collisions with a group of game objects and we can even check for collisions with the outside bounds of our canvas element here to do that choose our dyno for our player", "start": 1630.094, "end": 1657.295}, {"text": " Next, we're going to go to our panel for our arcade physics body collision. And so there's a bunch of properties on here that's going to affect how the physics is applied to our body. And we're just going to check this box here with collide world bounds. If we save and come back to our browser, now if we try to move our game object outside of our canvas element, we'll see now our physics system is now preventing our game object from leaving. For checking for collisions between our game object and another one, we need to use what's called a collider.", "start": 1657.449, "end": 1687.346}, {"text": " So from our blocks, and they're built in under arcade, let's drag the collider block in. Now what this block allows us to do is we can choose which two game objects or groups we want to check for collisions between, and then we can provide a callback function that we like to be invoked when this happens. So for object one, let's choose our dino, and then for object two, let's choose our dino one. All right, so if we save and come back to our browser,", "start": 1687.568, "end": 1714.565}, {"text": " Let's refresh, and if we try moving our game object to our other game object, we see our collision's not working. And so what's happening here is for our collisions to work properly, we need to use the physics system for moving our game objects. So currently in our level.js file, we're manually setting our x and y values, and we're incrementing and decrementing them without checking if anything's actually colliding.", "start": 1715.196, "end": 1737.739}, {"text": " And so when we do this, we're overwriting that physics system and saying, hey, we still want to move our game object, and that's why we can move through our other game object. Instead of using our dot y and dot x properties, we want to update our velocity for our game objects so then that way we can move them in our scene. So to use the physics system for moving, we're going to reference our dino game object. Let's do set velocity, and we'll do set velocity", "start": 1738.029, "end": 1764.019}, {"text": " X or Y, depending on which property we're looking at. So in our up key, because we're looking at Y, we'll do velocity Y. And now we can provide the value for velocity we like to set. So let's do minus 200. So similar to how we did Y here, when we move up, we want to set this to a negative value. And if we move down, we'll want to set this to a positive. So let's copy this. Let's paste it. And we'll do a positive 200. Let's comment out our code here before we do our Y values. Let's save.", "start": 1764.343, "end": 1791.749}, {"text": " If we refresh our scene, let's come down below our dino, and now if we try moving up, we'll see now we're pushing that other game object.", "start": 1792.432, "end": 1799.991}, {"text": " when we press our up or down key once, we'll see our object keeps moving in that direction because we're setting that velocity. And so what happens is our physics system is calling your update method multiple times. And once we set the velocity for our game object, unless we modify our physics in any way, our velocity is going to be consistent and it'll be a constant value and we'll just keep moving back and forth. And so we actually need to tell the phaser framework that, hey, we shouldn't be updating this if our key's not being held.", "start": 1800.009, "end": 1829.906}, {"text": " To fix that, we're just going to add an else block after our if else check here. And we'll just set our velocity back to zero if neither our up or down key is being pressed. So if we refresh, now if we let go of our key, our game object stops moving. And now we can push our other game object when we collide with it. So now let's do the same change for our x property. So let's copy this here. We'll comment this out. Let's go ahead and paste it. And we'll do set velocity x.", "start": 1830.589, "end": 1859.65}, {"text": " We'll copy that. Let's paste it. And we're going to make this positive. Then finally, let's have our else block. So then we'll reset it. And we'll change this to x. And we'll save. So while we're here, one thing we might want to consider doing is we're repeating this value multiple times. And so storing this in a property or a variable in our file probably makes sense. So then that way, we have to change it in one location. We don't have to change it in multiple locations.", "start": 1860.282, "end": 1886.084}, {"text": " So to do that, let's come up here to our code where it says we can write more code safely. And let's add a new property to our class. And we're just going to call this player velocity. And we're going to give it a default value of 200. So now that we've added this property, let's go ahead and update our code down here. So we're going to do this, player velocity. We'll times it by negative 1. And we have our negative value. Let's copy this. We'll paste it here, paste it here. And we'll times it by negative 1.", "start": 1886.783, "end": 1916.51}, {"text": " All right, so if we save, we should be able to test our changes. So I've come back to our browser refresh. Now we can still move our game object around just like before, but now we're using the physics to do so. And now we can do those collision checks and we can interact with our other game objects.", "start": 1916.715, "end": 1931.22}, {"text": " All right, and so one thing to know is once we touch that other game object, what's happening is the force from our physics game object here colliding with that other one now applies a force to that other game object. And because there's nothing stopping it or slowing it down, that game object keeps moving as well. And we never updated that game object so it can't leave our scene, and that's why it's able to exit while our game object cannot. If we want to make it so that game object can actually move, what we can do inside the editor is let's choose our Dino 2,", "start": 1931.647, "end": 1960.828}, {"text": " We come down to the properties for our physics body. When we have a collision, we can check this box here for immovable. And what that's going to do is now when we interact with that game object, we're not actually able to move it. And now it's like we're colliding with a static object that we can't push. And so this is like perfect for like walls or other characters that we don't want to be able to move around our game.", "start": 1961.254, "end": 1983.916}, {"text": " So besides using the physics system for checking for collisions between our game objects, we can also use it for checking for overlaps. And an overlap is a way for us to allow our game object to move through the other game object, but then still use our physics system to be notified when our physics body is overlapping with the other one. And this is perfect for like pickups in our game so the player can grab an item and pick it up. If we have events and we have zones where the player can move through and it triggers something else in the game, overlaps are perfect for this.", "start": 1987.671, "end": 2017.585}, {"text": " And so for our game, let's say we have a hungry dinosaur and he wants to eat some food. And so we want to be able to pick these up in our game. To do this, let's come back to our scene editor and let's add a new arcade image game object to our scene. So let's drag over our arcade image block. We'll choose our food item. And so if we select that image, oh, our image is a little bit big. So let's scale it down. So we're just going to scale this to 0.4.", "start": 2017.79, "end": 2041.51}, {"text": " Now that our food item is a little bit smaller, we want to go ahead and add a collider for our player and our object that we want to have our overlap. So from our arcade blocks, let's add another collider into our scene. On this collider, let's choose our dino. And then for our second object, let's choose our arcade image. So now from our collider, one other option we have available is this overlap here. So if we click this and we do Save, come back to our browser,", "start": 2042.637, "end": 2071.681}, {"text": " should happen now is even though this food object has a physics body when we collide with it we're not actually moving it or pushing it around instead we're just kind of overlapping with it and in order to do something with this we now need to listen for our callback for once our player collides with that game object and so here's we can provide the callback that we like to invoke all right and then so for our callback what we can do is now we can provide a method on our", "start": 2072.073, "end": 2099.991}, {"text": " our level class that we'd like to be invoked. To do that, we're going to do this to reference the context of this current scene, and then we're going to do eat fruit. So if we save, we need to add this method to our class. So if we come into our level.js file, under our update method, let's add a new method to our class, and we're going to call this eat fruit. All right, and then inside this method, let's just do console.log, and we're going to say hit. And all we're doing is we just want to see something happen", "start": 2100.009, "end": 2129.872}, {"text": " when we collide with our food item. So we refresh our browser, we'll see as soon as our physics body overlaps with our food item, our callback is being invoked. And so what we'll see is this is being invoked multiple times per second. So every update tick to our game loop is going to call this callback while these two game objects are overlapping. And so this is important to note. So if you have logic, you only want to run once, you'll need to handle this in your code to stop the overlap check from happening.", "start": 2130.196, "end": 2158.336}, {"text": " And so now that we see that this is working, we want to clean up our food game object and have it be removed from our scene. And to do that, in our collider, when we call our collide callback, two of the arguments we're going to receive are going to be the two game objects that the overlap, the collision, is happening for.", "start": 2158.831, "end": 2178.899}, {"text": " And depending on the order we add these objects here, that will be the order of our arguments. And so because our first object is our dino, our first argument will reference our dino image game object, and then our second one's going to reference our food. So if we come over to our level.js file, let's add those two arguments to our method, and we'll add in dino, and then we'll add in food. All right, so below our console log, let's reference our food variable. And so for our food game object, we want to disable the body.", "start": 2179.224, "end": 2208.148}, {"text": " And by disabling our body, that's going to allow us to stop checking for our collisions while we do anything else in our code. So if we come back to our browser and refresh, what we should see now is our hit gets called one time, even though our game object is still now overlapping with the other one. So now what we can do is now we can destroy this game object and remove it from our game to show that our dinosaur has now ate it.", "start": 2208.643, "end": 2233.234}, {"text": " So if we do food.destroy, this is going to tell Phaser to destroy this game object and to clean it up. Alright, so if we refresh in our browser, if we overlap with our food item, we see it's removed from our scene. Alright, so in our game, after eating the one food item, our dinosaur is still hungry. And so let's add another food pickup to our game. So from our blocks, let's choose an arcade image. We'll choose our food item.", "start": 2233.695, "end": 2257.568}, {"text": " And oh, we'll see our food item is now back at its original size. And now we have to manually scale this to match our other food item. As we add more instances of our game option to our scene, we're going to have to repeat this pattern of where we need to keep modifying those settings individually. But then let's say once we do that, we're like, oh, we actually want to change another setting on one of our food items. Let's say we want to add a tent to it so we can change its color. So as an example, let's say I want to do like this, uh,", "start": 2257.9610000000002, "end": 2286.766}, {"text": " this blue color here. So if I copy this and I apply this for all of our tents, we'll say now we have a unique item, but now I need to apply that to all of our other game objects as well. And so in order to avoid all this, we can use what's called a prefab inside phaser editor. And so a prefab is a way for us to create like a", "start": 2287.244, "end": 2306.408}, {"text": " snapshot of all of the properties we set in one of our game objects. And then we can create instances of that prefab. And then anytime we make a change to our prefab, it'll be reflected across those game objects. So to see an example of this, let's let's clean up all these fruit objects here. And for this fruit object here, we want to create a prefab. And there's a few ways to do this. When we already have an existing game object, we can right click on that object. We can go to prefab and we can do create prefab with object.", "start": 2306.596, "end": 2335.776}, {"text": " What this will do, it's going to open a modal where we can define where we want our prefab to be saved. I'm just going to save mine in the source folder for the time being. And we're just going to call this food prefab. We'll hit create. So if we save, the first thing that happens is our game object is now green over in our outline. And we'll see if we expand this, it's referencing this prefab instance. What that means is we've now created this new food prefab scene and food prefab JS file. And so our food prefab that we just created", "start": 2336.237, "end": 2365.862}, {"text": " This is just going to be a class that extends whatever game object type that object was. And so when we do this, this allows us to modify that game object directly. And now we can apply those changes to our scene. So as an example, let's say we don't actually like that tent on our food. Let's reset it back to white. So if we copy that, apply that to our values, if we save our food items now back to being purple, we jump over to our level scene.", "start": 2366.015, "end": 2393.387}, {"text": " Oh, we see our food item is now purple. And so this is where our prefabs really shine is because now any changes we make here,", "start": 2393.609, "end": 2399.991}, {"text": " we can apply that to any instance in any of our scenes. So now if we want to have the multiple food pickups, instead of dragging over our arcade image, we now have this new prefab block. So if we drag over our food image, we'll see now it's at the size of our other game object instance, and it has the same properties that have been applied to it. So now we can drag multiple instances over to our scene. And if we want to make a change and affect all of them, we can come to our food prefab", "start": 2400.009, "end": 2428.2}, {"text": " And let's say maybe we want our food to be larger. So let's do 1.2, 1.2 for our scaling. If we save, come back to our level, we'll see all instances of our game object have been modified. And so I'm just going to revert our changes to our scale and our prefab. So we'll go back to 0.4. And the other part of the prefabs, what's really cool is when we create these instances, what we're going to see on our properties is there's this little lock icon.", "start": 2428.439, "end": 2455.435}, {"text": " What that means is when this property is locked, the instance of the class that gets created is going to use the default properties from that class. When it's unlocked, that means whatever value we set in the GUI, this instance will have that value. And that's why we can move this object around in our scene. And it's not moving these other ones around because they also are unlocked.", "start": 2455.657, "end": 2480.077}, {"text": " If we lock this position, it's going to go to the default position of the prefab instance here. So 00. And if I move this around, we'll see this only moves the one where that lock icon is set. And so let's revert this back to 00. So it's in our top left corner. And we'll save. We'll come back to our level scene. So now if we unlock it, we can drag this around and it won't be modified.", "start": 2480.35, "end": 2504.206}, {"text": " And so we're able to do this for any of the properties on our instance. So maybe I want this food object to be larger, so we could do 0.6. And now our other ones will stay at the size they're set. And we can also do things like modify our alpha or our tent, and it won't affect our other instances. And so prefabs are really powerful, and they're perfect for when you want to create more than one instance of your game object. All right, so now we've added multiple prefabs to our scene. If we come back to our browser and test,", "start": 2504.445, "end": 2533.66}, {"text": " What should happen is now we can eat our one food item, but the new ones we added, we're not actually able to pick up. And the reason for that is in our collider, we're currently only targeting one of our food game objects that we added. What we need to do is we need to check for a collision between our player and all of our food game objects. And so we need to pass a group or an array of game objects we want to check for collisions with.", "start": 2534.087, "end": 2560.572}, {"text": " And so to pass an array of game objects that we like to check for collisions with, we can use another built-in block. So under our built-in blocks, let's go to group and let's add a list to our game scene. This is going to add a new section called list. And so the lists are a way for you to create what's an array. And then in your array, you can provide any game objects that you would like. And then we'll have that available to us in our code. So I'm just going to call this a variable food and we'll do save. So if we jump over to our level.js file, we should see a new property added to our class called food.", "start": 2560.811, "end": 2590.538}, {"text": " so we'll see under our list we have our food array and it is empty so now to actually add objects to our list we can select one of our game objects and now from our property drop down here we can do list and now if we choose list we can choose food if we do save come back to our level.js we'll see now our food prefab item it has been added to our food array and we'll be able to reference that on our code all right so to add our game objects to our list if we choose our game object", "start": 2590.896, "end": 2617.466}, {"text": " From our inspector panel, we'll do list, and now we can choose our food list that we'd like to add our object to. And you can also select multiple game objects and do this as well. So if we save, let's come back to our collider. Now for our object 2, instead of doing a specific instance of our food, let's choose our food list. We'll do select. All right, so if we save, come back to our browser. Let's refresh. Now if we try picking up our food objects, we'll see we can now pick up each one, and our callback method is now being invoked.", "start": 2617.875, "end": 2645.913}, {"text": " The next feature for our game we're going to implement is we're going to add a score mechanic. So as our hungry dinosaur picks up the food elements and eats them, we'll increment our score, and we're going to display that to our player. So to add this mechanic, we're going to add a new text game object to our scene. So under our built-in types, under string, let's add in text. So for our text game object, we're going to call our variable score. Next, we're going to position this in our top left-hand corner. And let's update our text. We're going to say score.", "start": 2649.189, "end": 2677.2780000000002}, {"text": " then we'll do zero so we'll start with zero let's update our size so for our size let's do 48 pixels and we're going to give a little bit of spacing between the edge of the game so for our position let's come over 10 pixels and we'll come down 10 pixels so if we save and come back to our browser we refresh we'll see our score text is now visible to the player and now when we pick up one of our food objects we need to actually update", "start": 2677.671, "end": 2699.991}, {"text": " update our text on our string here. So if we come over to our level.js file, first, let's add a new variable to keep track of our player score. So under our custom code where we had player velocity, let's add in score. And we're going to add a default value of 0. And now in our eat fruit method, let's get rid of our console log. After we destroy the game object, let's increment our score. So we'll do this. We'll reference score. And then we're going to do plus equals 1.", "start": 2700.009, "end": 2727.79}, {"text": " After we update our score variable, we need to actually update our score text. So for our score text, we need to reference our score game object that was created. So on our score game object is currently has our scope set to local. So let's change this to be class. And then that way we can actually get a property added to our class that we can reference. So if we come up to our properties, we now see we have a score property and it's going to collide with our property that we added here.", "start": 2728.524, "end": 2754.309}, {"text": " And so this is something to keep in mind is as you add your variable names here in the inspector, those will also be your property names that get added to your class. Since it is the same as the score property we added, let's do score text. We'll do save. Now if we come back to our level.js file, we'll see now we have a score text property. So back in our afruit method, let's reference our score text game object. We're going to use the set text method to update the text on our game object. For this, we'll want to make sure we keep our word score.", "start": 2754.718, "end": 2784.582}, {"text": " And now we just want to add in our score value. So we'll do this.score. All right, so we save and come back to our browser. Now, when we go to pick up one of our food items, oh, we have an error. So it says, can I read properties of undefined set text? So what's happening here is when we are inside our method here, we're currently trying to reference this scope of our level scene currently.", "start": 2784.974, "end": 2809.77}, {"text": " And so we're trying to increment our score and our score text properties on this class. However, in our level scene, if we go to our collider, when we call our callback here, by default, we're going to lose the context of where this is being called from. So to see an example, before we do disable body, I'm just going to do console.log, and we're going to do this. If you come back to our browser, let's try picking up a food item.", "start": 2810.196, "end": 2836.63}, {"text": " And we'll see now our this is being called in context of just our function here, our callback function. If we want to use the context of our whole class, we need to pass that in. So in our callback context here, we want to pass in this to reference our scene. If we come back to our browser, now if we pick up our food item, we'll see our context is referring to our level scene. And now that we have our correct scope, we'll see our food item is destroyed and our score text is updated.", "start": 2837.159, "end": 2866.613}, {"text": " And so now we should be able to pick up all of our game objects in our scene. Now that we've added our score mechanic to our game, I want to take a moment to talk about our scene layering and our rendering order. So as we move our game objects around our scene, one of the things we might have noticed is that our Dino game object appears behind our text for both of our UI elements. And what's happening here is by default, <PERSON>r is going to render our game objects in the order that we add them to our scene.", "start": 2866.971, "end": 2896.817}, {"text": " And with phaser editor, this is going to be based on how they're added to our hierarchy for our game objects here. Because our score text is above our dyno and our welcome text is above our dyno, that means they're going to be rendered over top of our player's game object. If we want to change that, we need to change the order of these objects in our hierarchy. As an example, if I take our dyno,", "start": 2897.159, "end": 2924.48}, {"text": " and we go to layout and we do sort, if we do move up, it's going to move the dino up in our hierarchy. If we do save and come back to our scene, we'll see now our dino game object is rendered on top of our other game object, but for our UI text, it's still rendered behind. This is important to note because as you're adding your game objects to your scene, the order that these are added is going to affect how they appear to the player.", "start": 2924.787, "end": 2952.159}, {"text": " And so there are other ways to control the rendering order. And one of the ways we can do this is by the depth on our game objects. Through Phaser Editor, an easier way to manage this is we can use what's a layer. And a layer is a way for us to group our game objects together. And then we can move our layers around to be in the order we'd like them to be displayed in our game. And so to see an example of this, under our built-in blocks, under Group, let's drag in Layer. This is going to add a new layer to our scene.", "start": 2952.705, "end": 2979.889}, {"text": " And for this layer, let's just call this UI. Now for our layers, we need to actually add our game objects to this layer. And by default, all of our game objects are going to have one parent. And this parent is going to be our scene by default. What we can do is on our objects,", "start": 2980.196, "end": 2999.991}, {"text": " If I choose our welcome text here, if I do right click, I can go to parent and now I can do move to parent. This will allow us to choose a different parent we like for our game object. And we're going to choose our UI layer here. What this will do in our hierarchy is it's going to nest that game object under our layer. And that's going to show that the parent of this game object is this game object here. We can then do the same thing for our score text. So if we do parent, we do move to parent. Let's do our UI layer.", "start": 3000.333, "end": 3029.514}, {"text": " And now our layer, because of the hierarchy, is on top of our other game objects. Those two game objects will always appear on top of our other ones. And so we could add the rest of our game objects to another layer, and then that way it's very easy to sort and move these around. But if we come back to our browser, now if we move our game object on top of our text, we'll see it appears behind it. And for our UI, our game object appears behind it.", "start": 3029.787, "end": 3053.814}, {"text": " So for our game, our hungry dino would like to have something to eat besides the fruit. To do this, we need to add new assets to our project. So to add assets to our phaser editor project, one way to do this is if we click on our assets folder here, we'll see in our inspector panel, this now opens up a place for us to add files. And so we can drag and drop files here, and it's going to copy them over to our project.", "start": 3056.852, "end": 3077.585}, {"text": " We're able to browse files from clicking the button here. Finally, if we right-click on our folder, we can also do Add Files, and this will also open a modal where we can add files to our project. So for my dino, I'm going to choose to give him a burger. So I have this burger asset here that is a sprite sheet. So in the description of the video, there'll be a link to where you can download this burger sprite sheet. And just a big shout-out to Cast Creates Games. This creator created this awesome sprite sheet and made it available for free on itch.io, and so there'll be a link to that in the description down below.", "start": 3077.892, "end": 3105.794}, {"text": " All right, and so all we've done so far is we've just added a new asset to our project folder. To actually use our asset inside Phaser Editor and to make it available in our Phaser game, we need to tell Phaser Editor what to do with this asset. So after we load it in our new sprite sheet, we'll see a new option has appeared in our inspector for our file.", "start": 3106.749, "end": 3124.326}, {"text": " So if we select our file, we have this asset pack entry here. This is how Phaser Editor will know to parse the file that we just added. And since we're importing a sprite sheet, we want to choose this option here. If we just want to do a raw image, we can do import as image. So if we do import a sprite sheet, we're not given the option of which asset pack we would like to add these to. For the time being, we're just going to choose asset, assetpack.json. Once we do that, our editor is now updated", "start": 3124.787, "end": 3150.503}, {"text": " since we've stored a reference to this in our asset pack. So now if we choose open burger sprite sheet at asset pack, it's now going to open our asset pack JSON file. So in this view here, this is our asset pack editor. In this editor, we can see all of the files our project is aware of", "start": 3150.64, "end": 3167.619}, {"text": " and what it's actually trying to load. So currently it's loading our dyno image and the new burger sprite sheet that we just added. And so our asset pack JSON, this is just a JSON configuration file that the editor and phaser is going to use to know how to load in our assets. So as an example, if I right click on our asset pack JSON file and I do open with and we do text editor, we're going to see our raw JSON that's being used. So we'll see we have two files. We have our burger sprite sheet with some information because it's a sprite sheet on how to parse it.", "start": 3167.79, "end": 3197.466}, {"text": " Then we have our dino PNG, and since it's just an image, it's just going to load that image. And then what you can do is now you can select one of your assets, and now in our inspector, we see metadata tied to that asset. So this includes things like the cache key of where this asset's going to be stored, the URL of where the data needs to be retrieved from for this asset, and then some additional information depending on the asset type. So like images, we have our width and height. Our sprite sheet, we have a sprite configuration to define how big our sprites actually are.", "start": 3197.756, "end": 3225.333}, {"text": " And so for now, we can leave our configuration alone. And if we did have a different size sprite, we would need to update our frame within height here. So let's close out of our asset pack JSON, and we'll come back to our level scene. Now that we've updated our assets to be in our asset pack, we have this sprite sheet option available to us in our blocks. And if we expand our sprite sheet, we can now select one of our individual frames for like what we would like to add to our scene. And so as an example, I can select this frame here to create an image game object in our scene.", "start": 3226.254, "end": 3254.189}, {"text": " So to use our burger in our game, let's make a new prefab instance. So from our source folder, let's right click. Let's do new. We'll do new prefab file for our prefab. We're going to call this burger prefab. We'll hit create. And what this is going to do is this is going to open our default prefab view. And so what this does, this will set up our prefab instance. But now we need to tell phaser editor what we like to use as our base. Based on the block that we drag into our prefab scene, that will be used as the root. And that's what our prefab will extend.", "start": 3254.906, "end": 3283.524}, {"text": " So if I drag our burger into our burger prefab scene, we'll see now we have our variable is an image game object, and if we save, now our burger prefab.js file is created, and this will extend that game object type. So our burger's a little bit small, so let's go ahead and scale it up by three.", "start": 3283.848, "end": 3299.991}, {"text": " Our X will do three. Y will do three. And let's position this back at zero, zero. So it's up in our top corner. All right. So one last thing we want to do for our burger is we need to add our collider. So we want to change this from an image. Let's do a arcade image. So we'll do arcade image. Let's do replace for our type. Now we'll have our body. And now if we save, let's come back to our level scene. Let's drag an instance of our burger. And we're going to replace our food item up here. Let's delete that. We'll add our burger up here.", "start": 3300.009, "end": 3329.36}, {"text": " And then finally, we just need to add this to our list. So we need to add this to our food item list, and even though this is a different prefab, it's still a phaser game object, so our player should be able to pick it up. So now if we jump over to our browser, let's refresh our scene. We'll see our new burger is now in our scene, and it has our physics body, and when our player picks it up, our score increments.", "start": 3330.162, "end": 3348.114}, {"text": " So besides our burger asset, we'll add one more asset to our game, and we're going to add some blocks. So then that way we have something for our player to collide with as obstacles. So before we do that, let's clean up our scene a little bit. We're going to get rid of our second dino. Then let's get rid of our second text here, and we'll save. Let's jump over to our level.js file. Since we deleted some of our game objects, for any manual code we added referencing those game objects, we'll need to clean that up. So we can clean up this dino1.", "start": 3350.896, "end": 3377.108}, {"text": " And since we're no longer updating our text here, we can remove this as well. Finally, since we removed our text, let's remove our hit area for our dinosaur so it's no longer clickable. So if we save, let's go ahead and add in another asset. So let's come to our assets folder. For our second asset, we're going to use a block that's available as part of the pixel platforming blocks from <PERSON>. So I'm going to drag that block over into our file here. And so I'm doing a single image for a single block. So now when our inspector updates, we want to import this as an image since it's a single image.", "start": 3377.79, "end": 3407.295}, {"text": " And so for this asset, we'll want to choose our asset pack JSON file as well. So now if we view our asset pack JSON file, we should see we have our new image loaded here under our image section. So let's jump back to our level scene and let's drag in an instance of our tile. So we've placed our block into our scene. It's very small, so we want to scale this up. So I'm going to scale the X and Y properties by five. And we actually want to make this have a physics body. So let's go into our type. We'll do image. Let's change it to an arcade image and we'll do replace.", "start": 3407.568, "end": 3436.271}, {"text": " We're going to want to create multiple instances of our blocks, so let's make a prefab. So if we right-click, let's do prefab. We're going to do create prefab with object. Under our source folder, we'll do block prefab. So now that we have our blocks, let's create a couple instances of this. I'm just going to drag one around our scene. I'm going to copy and paste and make another one. And we'll do one up here. And we'll just move our player over here. And let's make one more block.", "start": 3436.613, "end": 3461.698}, {"text": " All right, so now that we have our four blocks, we need to enable these to have a collision with our player. So just like what we do with our food, let's create a new list to hold these objects. So under our group block, let's add another list for our name. We're just going to call this walls. Then for each of our blocks, we want to add that to our new list. So let's select all four blocks for our list. We're going to add them to our wall list. So now we need to add our collision. So if we expand arcade, let's go to collider.", "start": 3462.261, "end": 3488.848}, {"text": " And we want to change this from Dino 1. We're going to change this to our new wall list object. So we do select. Let's do save. We come over to our scene. We refresh our browser. Let's try move and we'll see our objects are movable. So let's fix that. So under our source folder, let's open up our block prefab scene file. If we click on our game object, let's go down to our body and let's make it immovable. And we'll also disable pushable because we just want this to be static.", "start": 3489.309, "end": 3516.715}, {"text": " So if we save, let's come back to our scene. If we refresh, now if our dinosaur tries to move through our block, we're not actually able to. So our player's a little bit big, so let's make our dinosaur just a little bit smaller. So let's click on our dino, and we'll scale him down to be 0.75. All right, so if we save and refresh, we'll see we have our dinosaur. He's just a little bit smaller, but everything's still functioning properly.", "start": 3517.09, "end": 3539.087}, {"text": " So right now our game is looking a little bit static, where we have these game objects that can move around, but there's no animations. And when we add in our burger asset, since it was a sprite sheet, we have a bunch of frames for creating an animation of where our burger is idle and moving around. So to spice up our game a little bit, we'll add in our burger animation. So to add animations in the phaser editor, we need to create what's called an animations file. This animations file will allow us to use the animations editor to add animations to our game.", "start": 3541.971, "end": 3570.8450000000003}, {"text": " To add this file, we'll go to our assets folder, right click, we're going to do new, and let's do animations file. For the name, I'm just going to leave this as animations. We'll hit create. All right, and this will open up our animations JSON file, and this will be our animations editor. And so we can tell this is the animations editor, since at the top of our UI, we have this add animation button. So to add animations, we need to choose our frames that we'd like to use. So we're going to select our burger frame 6 all the way through 11. If we hold our shift key, we can select multiple frames. Now over in the inspect", "start": 3571.067, "end": 3599.991}, {"text": " we're in this option to build an animation. So we're going to click build one animation. Now, when we do this, we need to enter in a prefix for the name of our new animation. We're going to call this idle. So once we do this, this is going to add a brand new animation and it's going to be shown over here in our outline. It'll show which frames make up our animation and we can preview the animation here in the editor.", "start": 3600.009, "end": 3622.688}, {"text": " And so then after we get our animation, when we select it over in our inspector, we'll be able to modify the configuration settings for that particular animation. We can do things like modify our frame rate. So if it's moving too fast, we could slow it down. We can also do things like update or repeat. And so this controls the number of times an animation will play. So we could do one and our animation will play one time when we instruct our code to play our animation. For our idle animation, we want this to be negative one. So then that way it repeats indefinitely.", "start": 3623.148, "end": 3652.039}, {"text": " And you can do other things with your configuration. Once we create our animations JSON file, now we need to tell Phaser Editor that we need to load this as part of our game. So if we click on our animations JSON file, we want to do import as animation, and we want to add that to our asset pack. If we come back to our level scene, for us to actually play our animation, we're going to need to update our game objects type so that we can actually play animations.", "start": 3652.5, "end": 3675.538}, {"text": " So in Phaser, by default, your image game objects are really meant to display one image or one texture. And then you'd use your sprite game objects for being able to play animations. And so for our burger, we're going to need to update our game object type. So if we go into our burger prefab, let's choose our burger type here. We want to change our variable type from an arcade image to an arcade sprite. So once we do this, we need to now tell Phaser that we'd like to play an animation for our sprite.", "start": 3676.049, "end": 3704.002}, {"text": " So if we open up our burgerprefab.js file, in our constructor we can add additional code. Here we'll want to tell Phaser that we want to play our animation by default. To do that, we need to reference this to reference our sprite game object. Then we'll use the play method. Alright, so then for our play method, now we need to provide either an animation configuration or the name or the key of the animation we'd like to play. Now for our animation key, if we open up our animations.json file,", "start": 3704.326, "end": 3730.384}, {"text": " This idle burger right here, this key, this is the value we'll want to use. So let's copy our idle burger sprite sheet. We come back to our prefab. Inside our play method, let's add that. And we'll save. Now if we come back to our level scene, let's jump over to our browser. If we save and refresh our browser, now we'll see our burger is animated and we can still pick up our game object. All right, so while we're working on importing our assets, one of the things we had to keep choosing was our asset pack JSON file.", "start": 3730.794, "end": 3760.657}, {"text": " Now, in our template, we currently have two asset pack files. One is this asset pack JSON, and this other one is this preload asset pack JSON. So in your phaser editor and your phaser projects, you can have as many asset pack JSON files as you would like. And it just allows you to pack up different assets and then load them through these various configurations. In our existing template, we have two of these because of how our project's currently set up.", "start": 3761.101, "end": 3783.063}, {"text": " So while we're testing our game, occasionally you might see this preload scene pop up while we're loading our assets. And so in Phaser, you can have one or as many scenes as you like, and we can also do that in the Phaser editor. And so how our project's set up is our preload scene is like our loading screen, where we're going to show a loading bar while we load in all the assets we're going to use for our game. And then once we have all those assets, we transition to or over our level scene where now the player can play our game.", "start": 3783.268, "end": 3811.544}, {"text": " And for your preload scene, if there's any assets you would like to use, you'll need to make sure those are ready before we start our preload scene. Otherwise, we'd have broken images when we try to reference them. And that's why we have two asset packs here is in our main.js file. We currently have this boot scene. And so this is a custom phaser scene that was added here and not as one of our scenes here. And all this scene is doing is it's referencing our preload asset pack.", "start": 3811.817, "end": 3839.0190000000002}, {"text": " And in our preload asset pack, all we have is that image that we're using in our preloader. Then in our preload scene, we're loading in our other asset pack JSON file, and this has all of our other assets. And so this is another thing to note about Phaser is once you load in your assets, they're put into the cache and you can use them in any scene. Even though in our preload scene, we're loading in our food item, we're able to use it in our level scene because Phaser pulls it from the cache and we only have to load it one time.", "start": 3839.445, "end": 3867.295}, {"text": " And so this is a good pattern for when you're going to have larger games, so then that way you have a nice screen to show players while you're trying to load everything, instead of trying to load everything at once, and then they would see a black screen, since nothing would be visible to the player. So the last thing we're going to do for our Hungry Dinosaur game is we're going to add a very simple title screen. And so in our title screen, we're just going to display some text that has the name of our game, and once the player clicks on it, we'll transition to our level scene. So previously we created this test scene here,", "start": 3867.568, "end": 3897.244}, {"text": " So let's clean that up real quick. So we're going to right click on that.", "start": 3897.5, "end": 3899.991}, {"text": " going to delete. Now we want to create a brand new scene, so we'll do scenes, we'll do new, let's do a scene file, and we're going to call this title. Once we have our new scene, we're just going to add a simple text game object. So if we go under our string, let's add in a text. So for our text, we're just going to do hungry dinosaur. For our font size, let's make this a little bit bigger, so let's do 64 pixels, and we want to go ahead and center this in our screen.", "start": 3900.009, "end": 3927.722}, {"text": " So currently our origin on our game object is the top left hand corner. We want this to be in the center. So let's update our origin. So we'll do 0.5 and we'll do 0.5. So now for our title text, we'll want this to be centered in our screen. So one of the ways we can do this is we can use some of our layout tools. So if we right click on our game object and we go to layout, we have a bunch of different layout options available to us. And one of the nice ones is this is aligned to borders.", "start": 3928.097, "end": 3951.8}, {"text": " What this allows us to do is we can take a game object and have it line up nicely with one of the borders in our game. And so like we can do our left and right borders and we'll see the end of our game object aligns with that border. If we do layout and we do center, it's going to center our game object between the two edges of our game. Likewise, if we do layout, we do align to border. If we do border top or bottom border, we get the same thing, both the top and bottom of our game object. And if we just do border middle,", "start": 3952.073, "end": 3979.889}, {"text": " we now have our game object centered directly in our canvas element. And so with your layout tools, there's much more you can do with it. And so you have the option for doing things like creating a grid layout. So if you have a bunch of game objects that you want to lay out on a grid, it has built-in tools to do that for you. All right, so if we save and come back to our browser, if we refresh, we still have our level scene. And so anytime we add a brand new scene to our game, we need to update our phaser configuration so phaser is aware of that scene.", "start": 3980.333, "end": 4008.507}, {"text": " To do this, we need to open up our main.js file. So wherever you define your phaser game configuration, and that's going to be a main.js here, we automatically add in the scenes that we're using for our game. And so now we want to add in a new one for our title scene. So let's copy this logic here, place it here. Let's update our key to be title. And we're going to update our class to be title.", "start": 4008.899, "end": 4031.698}, {"text": " So to use our title class, we need to import that into our code. So let's do import title from scenes title.js. Once we do this, this is just telling the phaser game configuration we have another scene and it's going to create an instance of our scene. But if we actually want to start our scene, we need to reference that in our code here. So as an example, our boot scene, once this comes up and everything's ready to go, we call scene.start and we start our preload scene.", "start": 4032.159, "end": 4060.589}, {"text": " So if we go into our preload scene, let's open up preload.js. We should see at the bottom here, after we finish everything, we go into our create method and we start our level scene. So now we actually want to start our title scene. And then that way, once our preload scene is done, we'll go into our title. So if we refresh our game in our browser, we'll see now we have our new text, Hungry Dinosaur. And now we just need that mechanism to start our game scene. And so to do that, we just need to listen for our click event on our scene.", "start": 4061.118, "end": 4090.128}, {"text": " Let's open up our title.js file. If we go into our create method after our editor create, let's add in our logic for our event listener. So we're going to do this to reference our phaser scene. We're going to do input to reference our input plugin, and then we're going to call the once method. So what this method does, it allows us to add a one-time event listener for a given event. This will allow us to listen for our click event in our scene one time and then do something with it.", "start": 4091.067, "end": 4117.551}, {"text": " Versus if we do on, this callback will be invoked every time that event is emitted. So since we only want to click one time and go to our level scene, we're just going to use the once method. Now we need our event. So if we do phaser, we're going to do input events. And now we want to do pointer down. So when we get this event, our callback that we provide will be invoked. So in our callback function, now we just want to start our level scene.", "start": 4118.131, "end": 4144.753}, {"text": " So what we were doing in our preload scene before, this scene.start, we're going to do the same thing in our title scene, but now we want to change this to level. So after that change, if we come back to our browser, let's refresh our scene. If we click our scene, our level now starts, and our player can still collect our items. So one last change we're going to do for our game is we're going to update the title of our game that's shown in our browser. So currently it shows my game.", "start": 4145.145, "end": 4172.21}, {"text": " So for our project template, if we go into our index.html file, our title element here is where we can update that. So we changed my game, we're going to change this to Hungry Dinosaur. We do save, go back to our browser, and now we have our new title for our game. All right, that's it for this crash course on Phaser Editor V4. Today we've covered everything from setting up your first project to adding physics, animation,", "start": 4172.551, "end": 4199.991}, {"text": " and object interactions. With Phaser Editor, you can quickly create and test your games visually without needing to dive deep into coding, though having that option is always there for you. If you follow along, you now have a solid foundation to start building your own Phaser 3 game using Phaser Editor v4. Remember, game development is all about experimenting and learning, so don't be afraid to try out new features and expand on what we've covered today. As always, I'd love to hear your thoughts and see what you create. Drop your questions in the comments below,", "start": 4200.009, "end": 4228.234}, {"text": " And if you found this tutorial helpful, don't forget to like this video and subscribe for more game development content. Also, check the description for the links to the documentation and other helpful resources for Phaser Editor. Thanks for watching and happy game making. See you in the next tutorial.", "start": 4228.507, "end": 4242.381}], "language": "en"}