#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频音频内容
检查生成的视频是否真的包含音频数据
"""

import os
from moviepy import VideoFileClip
import numpy as np

def test_video_audio(video_path):
    """测试视频的音频内容"""
    print(f"🎵 测试视频: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"❌ 文件不存在: {video_path}")
        return False
    
    try:
        # 加载视频
        video = VideoFileClip(video_path)
        
        # 检查是否有音频
        if video.audio is None:
            print("❌ 视频没有音频轨道")
            video.close()
            return False
        
        print(f"✅ 视频有音频轨道")
        print(f"📊 视频时长: {video.duration:.2f}秒")
        print(f"📊 音频时长: {video.audio.duration:.2f}秒")
        print(f"📊 音频采样率: {video.audio.fps}Hz")
        
        # 提取前10秒的音频数据进行分析
        test_duration = min(10, video.duration)
        audio_clip = video.audio.subclipped(0, test_duration)
        
        # 转换为numpy数组
        audio_array = audio_clip.to_soundarray()
        print(f"📊 音频数组形状: {audio_array.shape}")
        
        # 检查音频数据
        max_amplitude = np.max(np.abs(audio_array))
        rms_amplitude = np.sqrt(np.mean(audio_array**2))
        
        print(f"📊 最大振幅: {max_amplitude:.6f}")
        print(f"📊 RMS振幅: {rms_amplitude:.6f}")
        
        if max_amplitude < 0.001:
            print("⚠️  音频振幅非常小，可能是静音")
        elif max_amplitude < 0.01:
            print("⚠️  音频振幅较小，可能音量很低")
        else:
            print("✅ 音频振幅正常")
        
        # 检查是否全为零
        if np.all(audio_array == 0):
            print("❌ 音频数据全为零（静音）")
        else:
            non_zero_samples = np.count_nonzero(audio_array)
            total_samples = audio_array.size
            print(f"✅ 非零音频样本: {non_zero_samples}/{total_samples} ({non_zero_samples/total_samples*100:.2f}%)")
        
        # 清理资源
        audio_clip.close()
        video.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 视频音频内容测试器")
    
    videos_to_test = [
        "chinese_video_high_quality.mp4",
        "chinese_video_full_complete.mp4"
    ]
    
    for video in videos_to_test:
        print("\n" + "="*50)
        test_video_audio(video)

if __name__ == "__main__":
    main() 