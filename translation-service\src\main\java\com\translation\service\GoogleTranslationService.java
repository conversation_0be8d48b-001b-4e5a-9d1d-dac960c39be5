package com.translation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * Google翻译服务
 */
@Service
public class GoogleTranslationService {
    private static final Logger logger = LoggerFactory.getLogger(GoogleTranslationService.class);
    
    @Value("${google.translate.api.key:}")
    private String apiKey;
    
    @Value("${google.translate.project.id:}")
    private String projectId;
    
    private final OkHttpClient client;
    private final ObjectMapper objectMapper;
    
    private static final String TRANSLATE_URL = "https://translation.googleapis.com/language/translate/v2";
    
    public GoogleTranslationService() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 检查服务是否已配置
     */
    public boolean isConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() && 
               !"your-google-translate-api-key".equals(apiKey);
    }
    
    /**
     * 翻译文本
     */
    public String translateText(String text, String sourceLanguage, String targetLanguage) throws IOException {
        if (!isConfigured()) {
            throw new IOException("Google翻译服务未配置：请设置 GOOGLE_TRANSLATE_API_KEY 环境变量");
        }
        
        logger.info("开始Google翻译，源语言: {}, 目标语言: {}", sourceLanguage, targetLanguage);
        
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 转换语言代码
        String from = convertLanguageCode(sourceLanguage);
        String to = convertLanguageCode(targetLanguage);
        
        // 构建请求URL
        String url = TRANSLATE_URL + "?key=" + apiKey;
        
        // 构建请求体
        RequestBody requestBody = new FormBody.Builder()
                .add("q", text)
                .add("source", from)
                .add("target", to)
                .add("format", "text")
                .build();
        
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Google翻译调用失败: " + response.code());
            }
            
            String responseBody = response.body().string();
            logger.debug("Google翻译API响应: {}", responseBody);
            
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (jsonNode.has("error")) {
                JsonNode error = jsonNode.get("error");
                String errorMsg = error.has("message") ? error.get("message").asText() : "未知错误";
                logger.error("Google翻译API错误: {}", errorMsg);
                throw new IOException("Google翻译失败: " + errorMsg);
            }
            
            if (jsonNode.has("data") && jsonNode.get("data").has("translations")) {
                JsonNode translations = jsonNode.get("data").get("translations");
                if (translations.isArray() && translations.size() > 0) {
                    String result = translations.get(0).get("translatedText").asText();
                    logger.info("Google翻译完成，原文长度: {}, 译文长度: {}", text.length(), result.length());
                    return result;
                }
            }
            
            throw new IOException("Google翻译返回格式错误");
        }
    }
    
    /**
     * 转换语言代码为Google格式
     */
    private String convertLanguageCode(String languageCode) {
        if (languageCode == null || languageCode.trim().isEmpty()) {
            return "auto"; // 自动检测
        }
        
        switch (languageCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
                return "zh-CN";
            case "zh-tw":
                return "zh-TW";
            case "en":
            case "en-us":
                return "en";
            case "ja":
                return "ja";
            case "ko":
                return "ko";
            case "fr":
                return "fr";
            case "de":
                return "de";
            case "es":
                return "es";
            case "ru":
                return "ru";
            default:
                return languageCode; // 保持原样
        }
    }
    
    /**
     * 检测语言
     */
    public String detectLanguage(String text) throws IOException {
        if (!isConfigured()) {
            throw new IOException("Google翻译服务未配置");
        }
        
        String url = "https://translation.googleapis.com/language/translate/v2/detect?key=" + apiKey;
        
        RequestBody requestBody = new FormBody.Builder()
                .add("q", text)
                .build();
        
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Google语言检测调用失败: " + response.code());
            }
            
            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (jsonNode.has("data") && jsonNode.get("data").has("detections")) {
                JsonNode detections = jsonNode.get("data").get("detections");
                if (detections.isArray() && detections.size() > 0) {
                    JsonNode firstDetection = detections.get(0);
                    if (firstDetection.isArray() && firstDetection.size() > 0) {
                        return firstDetection.get(0).get("language").asText();
                    }
                }
            }
            
            return "en"; // 默认英文
        }
    }
}