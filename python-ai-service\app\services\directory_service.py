"""
目录管理服务 - 为每个任务创建结构化的目录
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import shutil

class DirectoryService:
    """管理任务目录结构的服务"""
    
    def __init__(self, base_storage_path: str = "storage"):
        self.base_storage_path = Path(base_storage_path)
        self.outputs_dir = self.base_storage_path / "outputs"
        
        # 确保基础目录存在
        self.outputs_dir.mkdir(parents=True, exist_ok=True)
    
    def create_task_directory(self, original_filename: str, task_id: str) -> Dict[str, str]:
        """
        为任务创建结构化目录
        
        Args:
            original_filename: 原始文件名
            task_id: 任务ID
            
        Returns:
            包含所有目录路径的字典
        """
        # 生成目录名：时间戳_文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        clean_filename = self._clean_filename(original_filename)
        task_dir_name = f"{timestamp}_{clean_filename}"
        
        # 创建主任务目录
        task_dir = self.outputs_dir / task_dir_name
        task_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        subdirs = {
            'source': task_dir / 'source',           # 原始上传文件
            'audio': task_dir / 'audio',             # 提取的音频
            'transcription': task_dir / 'transcription', # 语音识别结果
            'translation': task_dir / 'translation',  # 翻译结果
            'tts': task_dir / 'tts',                 # TTS生成的音频
            'subtitles': task_dir / 'subtitles',     # 字幕文件
            'output': task_dir / 'output',           # 最终输出
            'logs': task_dir / 'logs',               # 任务日志
            'temp': task_dir / 'temp'                # 临时文件
        }
        
        # 创建所有子目录
        for subdir in subdirs.values():
            subdir.mkdir(exist_ok=True)
        
        # 转换为字符串路径
        paths = {
            'task_dir': str(task_dir),
            'task_name': task_dir_name
        }
        
        for key, path in subdirs.items():
            paths[key] = str(path)
        
        # 创建元数据文件
        metadata = {
            'task_id': task_id,
            'original_filename': original_filename,
            'created_at': datetime.now().isoformat(),
            'task_name': task_dir_name,
            'status': 'created',
            'paths': paths
        }
        
        metadata_file = task_dir / 'metadata.json'
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        return paths
    
    def get_task_paths(self, task_name: str) -> Optional[Dict[str, str]]:
        """
        获取任务的目录路径
        
        Args:
            task_name: 任务目录名
            
        Returns:
            目录路径字典，如果不存在返回None
        """
        task_dir = self.outputs_dir / task_name
        if not task_dir.exists():
            return None
        
        metadata_file = task_dir / 'metadata.json'
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                return metadata.get('paths', {})
            except Exception:
                pass
        
        # 如果元数据文件不存在，重建路径
        return self._rebuild_paths(task_dir)
    
    def update_task_metadata(self, task_name: str, updates: Dict[str, Any]) -> bool:
        """
        更新任务元数据
        
        Args:
            task_name: 任务目录名
            updates: 要更新的数据
            
        Returns:
            是否更新成功
        """
        task_dir = self.outputs_dir / task_name
        metadata_file = task_dir / 'metadata.json'
        
        if not metadata_file.exists():
            return False
        
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            metadata.update(updates)
            metadata['updated_at'] = datetime.now().isoformat()
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception:
            return False
    
    def copy_file_to_task_dir(self, source_path: str, task_name: str, 
                             target_subdir: str, new_filename: Optional[str] = None) -> str:
        """
        将文件复制到任务目录的指定子目录
        
        Args:
            source_path: 源文件路径
            task_name: 任务目录名
            target_subdir: 目标子目录名
            new_filename: 新文件名（可选）
            
        Returns:
            目标文件路径
        """
        paths = self.get_task_paths(task_name)
        if not paths or target_subdir not in paths:
            raise ValueError(f"Invalid task name or subdirectory: {task_name}/{target_subdir}")
        
        source = Path(source_path)
        if not source.exists():
            raise FileNotFoundError(f"Source file not found: {source_path}")
        
        target_dir = Path(paths[target_subdir])
        target_filename = new_filename or source.name
        target_path = target_dir / target_filename
        
        shutil.copy2(source, target_path)
        return str(target_path)
    
    def get_file_path(self, task_name: str, subdir: str, filename: str) -> str:
        """
        获取任务目录中文件的完整路径
        
        Args:
            task_name: 任务目录名
            subdir: 子目录名
            filename: 文件名
            
        Returns:
            完整文件路径
        """
        paths = self.get_task_paths(task_name)
        if not paths or subdir not in paths:
            raise ValueError(f"Invalid task name or subdirectory: {task_name}/{subdir}")
        
        return str(Path(paths[subdir]) / filename)
    
    def list_task_files(self, task_name: str, subdir: str) -> list:
        """
        列出任务目录中指定子目录的所有文件
        
        Args:
            task_name: 任务目录名
            subdir: 子目录名
            
        Returns:
            文件列表
        """
        paths = self.get_task_paths(task_name)
        if not paths or subdir not in paths:
            return []
        
        subdir_path = Path(paths[subdir])
        if not subdir_path.exists():
            return []
        
        return [f.name for f in subdir_path.iterdir() if f.is_file()]
    
    def cleanup_task_directory(self, task_name: str, keep_output: bool = True) -> bool:
        """
        清理任务目录（删除临时文件）
        
        Args:
            task_name: 任务目录名
            keep_output: 是否保留输出文件
            
        Returns:
            是否清理成功
        """
        paths = self.get_task_paths(task_name)
        if not paths:
            return False
        
        try:
            # 删除临时文件
            temp_dir = Path(paths['temp'])
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
                temp_dir.mkdir()
            
            # 如果不保留输出，删除中间文件
            if not keep_output:
                for subdir in ['audio', 'transcription', 'translation', 'tts']:
                    if subdir in paths:
                        subdir_path = Path(paths[subdir])
                        if subdir_path.exists():
                            shutil.rmtree(subdir_path)
                            subdir_path.mkdir()
            
            return True
        except Exception:
            return False
    
    def _clean_filename(self, filename: str) -> str:
        """清理文件名，移除扩展名和特殊字符"""
        # 移除扩展名
        name = Path(filename).stem
        
        # 替换特殊字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            name = name.replace(char, '_')
        
        # 限制长度
        if len(name) > 50:
            name = name[:50]
        
        return name
    
    def _rebuild_paths(self, task_dir: Path) -> Dict[str, str]:
        """重建任务目录的路径字典"""
        subdirs = ['source', 'audio', 'transcription', 'translation', 
                  'tts', 'subtitles', 'output', 'logs', 'temp']
        
        paths = {
            'task_dir': str(task_dir),
            'task_name': task_dir.name
        }
        
        for subdir in subdirs:
            subdir_path = task_dir / subdir
            subdir_path.mkdir(exist_ok=True)
            paths[subdir] = str(subdir_path)
        
        return paths
