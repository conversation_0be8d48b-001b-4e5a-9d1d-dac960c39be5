1
00:00:00,009 --> 00:00:08,455
大家好，欢迎收看Phaser Editor V4速成课程。今天我们将学习如何快速轻松地构建Phaser 3游戏，无需手动编写大量代码。

2
00:00:08,898 --> 00:00:36,100
大家好，欢迎来到Phaser Editor V4速成课程。如果你是Phaser或游戏开发的新手，来对地方了。Phaser Editor V4是一款强大的可视化HTML5游戏开发工具，让你专注于创作而非迷失在代码中。

本视频将带你逐步完成：项目设置、编辑器使用和简单游戏制作。今天我们将学习：什么是Phaser Editor？如何快速创建新项目；工作台和各类UI视图的概览；编辑器如何根据GUI操作自动生成代码。

3
00:00:36,100 --> 00:00:53,951
如果你是Phaser或游戏开发的新手，那你就来对地方了。Phaser Editor V4是一款强大的可视化HTML5游戏开发工具，让你专注于创作而无需深陷代码之中。在本视频中，我将一步步指导你完成项目设置、编辑器使用以及制作简单游戏的全过程。今天我们将涵盖以下内容：什么是Phaser Editor？如何快速创建全新项目。我们将概览工作台和用户界面中的各种视图。了解编辑器如何根据你在图形界面中的操作自动生成代码。

如何本地运行和测试游戏，添加键盘输入并移动游戏对象及其事件响应，使用街机物理系统和碰撞检测，处理对象重叠，使用预制件作为可复用游戏组件，控制对象层级与渲染顺序，为游戏添加新资源并创建新动画。

4
00:00:54,171 --> 00:01:22,567
要跟着操作，请确保你已安装Phaser Editor v4。我会在视频描述中附上文档链接，提供更详细的设置说明。在开始之前，请注意：本教程使用的是Phaser Editor v4。虽然具备JavaScript和Phaser 3框架基础会更有帮助，但即使你是完全的新手，也能轻松跟上。本视频不会讲解安装步骤，不过别担心，我已经在描述中附上了文档和资源链接。现在让我们正式开始。在这个快速入门教程中，我们将创建一个非常基础的Phaser 3游戏。

5
00:01:22,995 --> 00:01:49,855
这个游戏的目标是帮助我们学习使用Phaser Editor创建Phaser 3游戏的基础知识。在我们的游戏中，我们将创建一个简单的标题场景，玩家可以点击进入下一个场景，这样我们就能学习场景切换。然后我们会学习如何让玩家（也就是我们的恐龙）在游戏中移动，并与障碍物发生碰撞。我们还将实现拾取食物的功能，了解如何添加不同种类的食物道具。此外，我们还会学习加载精灵图集和创建基础动画的方法。

6
00:01:49,956 --> 00:02:19,667
这是一款非常基础的游戏，但它能帮助我们掌握使用Phaser Editor创建Phaser 3游戏的基本方法。那么什么是Phaser Editor？简单来说，Phaser Editor是一款旨在加速Phaser 3游戏开发的工具。它能协助构建游戏场景、管理资源资产，甚至自动生成代码。更正式的定义是：Phaser Editor是一款强大的可视化开发工具，专为使用Phaser游戏引擎创建2D游戏而设计。

7
00:02:19,837 --> 00:02:34,837
凭借直观的界面和丰富的功能集，它能让各级开发者快速轻松地为桌面和移动平台创建高质量游戏。无论你是初学者还是资深开发者，Phaser Editor都能提升你的Phaser 3游戏开发体验。

8
00:02:35,332 --> 00:02:59,820
Phaser编辑器的主要功能包括场景编辑器，这是一个可视化工具，能让你通过摆放图片和其他游戏对象来创建Phaser场景或关卡。该工具支持拖放各类Phaser游戏对象到场景中，如图片、文本等多种元素。场景预览直接由Phaser引擎渲染，因此你可以确信所见即所得——编辑界面展示的就是最终游戏效果。

9
00:03:00,794 --> 00:03:16,390
接下来是物理编辑器。这个工具能让你轻松创建和修改街机物理游戏对象。你可以直观地调整游戏对象的物理体，无需再通过代码手动操作。还能通过图形界面添加碰撞检测和重叠检查等功能。

10
00:03:17,175 --> 00:03:39,086
接下来是我们的动画编辑器。这款工具可以让你手动或自动从纹理帧创建精灵动画，你不再需要为各种动画动态生成JSON文件了。通过图形界面，你可以修改帧率、延迟、重复、时间缩放等配置参数。紧接着是资源包编辑器。

11
00:03:39,258 --> 00:03:58,012
接下来是我们的动画编辑器。这个工具可以让你手动或自动从纹理帧创建精灵动画，无需再为各种动画动态生成JSON文件。通过图形界面，你可以修改帧率、延迟、重复、时间缩放等配置参数。

接下来是资源包编辑器。当你向项目添加新资源时，Phaser编辑器可将其导入资源包文件。这个资源包文件可以从统一位置加载所有游戏资源。通过资源包编辑器，你可以查看文件内容，了解包含哪些类型的游戏资源，并能修改对应的JSON配置。

12
00:03:58,659 --> 00:04:28,660
最后要介绍的是预制件系统。预制件是Phaser编辑器的核心概念，简而言之，它能让您创建可重复使用的游戏对象，统一控制其外观、属性和设置，并集中存储。之后您就可以通过该预制件创建多个对象实例。现在让我们打开Phaser编辑器开始创建项目。启动编辑器后，点击"新建项目"来创建一个全新的Phaser编辑器项目。在接下来的界面中，您可以为项目选择初始模板。

13
00:04:28,985 --> 00:04:57,738
这样就会搭建好你的Phaser 3游戏项目框架。编辑器安装过程中已内置了三个离线项目模板。而入门示例和更高级的框架范例需要从网上下载。我们将使用这个基础的JavaScript模板开始。选择模板后，系统会提示你选择项目在电脑上的保存位置。你需要新建一个文件夹，我准备将其命名为"Hello World"。

14
00:04:58,574 --> 00:04:59,990
创建新项目文件夹后，

15
00:05:00,043 --> 00:05:29,632
创建新项目文件夹后，
你需要打开这个项目文件夹，
此时Phaser编辑器会自动生成项目模板
并搭建运行游戏所需的全部本地文件。

初始化完成后会显示欢迎页面，
从这里我们可以选择"打开场景"，
从项目模板自带的两个场景中任选其一。
让我们打开关卡场景。

现在编辑器会更新显示Phaser场景界面
以及当前场景中存在的游戏对象。

当你在工作台或IDE中
打开Phaser编辑器项目时，

16
00:05:29,956 --> 00:05:59,326
打开项目文件夹后，Phaser编辑器会自动配置项目模板，并搭建本地运行游戏所需的所有项目文件。初始化完成后，您会看到欢迎页面。从这里我们可以选择"打开场景"，从项目模板包含的两个场景中任选其一。现在让我们打开关卡场景，编辑器界面随即会更新显示当前的Phaser场景及其包含的游戏对象。

当您在Phaser编辑器的工作台或集成开发环境中打开项目时，系统会提供多种视图和菜单选项。左侧的大纲视图会以层级结构展示当前活动编辑器的全部内容。例如当我们打开场景编辑器时，就能看到场景中包含的各类游戏对象。比如我若添加新的游戏对象，它们就会立即显示在这个大纲视图中。

17
00:06:00,009 --> 00:06:29,939
当我们切换到不同的编辑器时，这里会实时更新显示对应编辑器的内容。比如在编辑资源包时，就能看到组成该资源包的所有资源。

右侧是检视面板。通过检视视图，您可以查看当前在编辑器中选中对象的所有属性。例如现在没有选中任何对象，默认显示的就是场景属性。在这里可以看到一系列用于修改场景属性的选项。

18
00:06:30,298 --> 00:06:58,490
而当我选中这个图像游戏对象时，右侧面板会实时更新，此时显示的就是该游戏对象的所有属性参数。我们可以进行诸如修改变量名、调整游戏对象位置等多项操作。根据当前所处的编辑器不同，右侧面板会动态显示所选对象的对应属性。

屏幕底部是文件视图区域，这里会列出项目的所有文件。和其他文件编辑器一样，您可以执行新建文件等基本操作。

19
00:06:58,728 --> 00:07:22,192
而如果我选择这个图像游戏对象，面板就会更新，现在我们就能看到游戏对象的所有属性。我们可以修改变量名称，调整游戏对象的位置等等。根据你当前所处的编辑器不同，面板会实时显示所选对象的对应属性。屏幕底部是文件视图区，这里会列出项目的所有文件。和其他文件编辑器一样，你可以进行添加新文件、删除、重命名甚至移动文件等基本操作。当你打开某个文件时，它会在集成开发环境中打开以供编辑。和Phaser编辑器中的其他对象一样，选中文件时检查器面板会同步显示该文件的相关信息。旁边是积木视图区，这里会显示当前编辑器中可用于构建对象的元素组件。

20
00:07:22,500 --> 00:07:50,502
当我们打开场景编辑器时，这里会显示可添加到场景中的各类Phaser 3内置游戏对象类型。同时也会展示已加载的图片或其他资源，我们可以将这些元素直接拖入场景中。若当前激活的是其他编辑器（如资源包编辑器），则会显示可导入资源包的各种文件类型。

在界面右上角是我们的主菜单栏，通过它可以打开不同项目、新建项目窗口以及执行其他类似操作。

21
00:07:50,741 --> 00:08:19,052
你可以更改场景的默认布局、选择主题颜色等。旁边是联系Phaser Studio团队的支持聊天按钮。左上角有几个创建新内容的快捷方式：播放按钮可运行项目并在浏览器中查看；快速对话框用于打开场景；还有在Visual Studio Code中打开项目的选项——点击该按钮会以当前活动文件在VS Code中打开你的项目。

22
00:08:19,258 --> 00:08:46,953
当你点击该按钮时，如果本地已安装VS Code，它就会自动打开，你可以查看构成项目的代码。最后在屏幕顶部，我们会看到一些随当前编辑器变化的不同工具。例如在欢迎界面时，这里不会显示任何工具；在资源包编辑器中，会出现导入文件的相关选项；而现在处于场景编辑器时，则会显示场景编辑工具。场景编辑器的第一个工具是平移工具。

23
00:08:47,175 --> 00:09:15,230
移动工具允许你选中游戏对象后，在场景中拖动它，这会实时更新该游戏对象的X和Y坐标位置。

接下来是缩放工具。通过缩放工具，你可以沿X和Y轴拖拽来放大游戏对象。当我们调整这些属性时，游戏对象的尺寸会随之改变，这些变化会实时显示在这里。

第三个是旋转工具。使用它可以选中游戏对象并进行旋转，从而改变游戏对象的角度。

最后是原点工具。

24
00:09:15,502 --> 00:09:42,226
这个工具可以修改游戏对象的原点位置。默认情况下，大多数游戏对象的原点位于中心位置，即X和Y轴均为0.5。原点是Phaser在场景中放置游戏对象的基准点，它决定了游戏对象的起始定位位置。我们看到的X和Y坐标值都是基于这个原点计算的。比如当我把原点移到左上角，并将位置坐标设为(0,0)时...

25
00:09:42,482 --> 00:09:59,990
这样你就能修改游戏对象的原点位置。默认情况下，大多数游戏对象的原点位于中心位置，因此X和Y值都是0.5。这个原点决定了Phaser如何在场景中放置游戏对象，它会将该点作为游戏对象的起始定位点。我们在这里设置的X和Y坐标值，就是基于这个原点来计算的。如果我将原点移到左上角，然后把位置坐标设为0,0，

现在可以看到游戏对象以这个点作为起始定位点了。如果我把原点重新放回中心位置，就能看到此时0,0坐标对应的是中心点，游戏对象会以该点作为原点。接下来介绍"区域内全选对象"工具，这个功能允许你拖拽选取一个区域，

26
00:10:00,009 --> 00:10:25,707
现在可以看到，系统将这个点作为我们游戏对象的起始点。如果我把原点放回中间位置，就能看到当前坐标归零，系统将其作为游戏对象的原点。接下来介绍区域选择工具，通过拖拽划定区域可以批量选中游戏对象。最后是平移工具，只需在场景中点击拖动即可自由调整视口视角。如需查看快捷键，将鼠标悬停在工具图标上就会显示对应快捷操作。值得一提的是，图形界面中大多数属性选项在悬停时都会显示工具提示，说明该功能在Phaser编辑器中的具体用法。

27
00:10:29,019 --> 00:10:47,448
在我们操作项目并移动场景中的物体时，Phaser编辑器实际上正在为我们的Phaser项目动态生成代码。要查看这些代码，我们需要打开与关卡相关联的.js文件。在图形界面中，当我们为项目创建新文件时，通常会产生两个文件。

28
00:10:47,739 --> 00:11:12,192
在我们操作项目并移动场景中的元素时，Phaser编辑器实际上正在为我们的Phaser项目动态生成代码。要查看这些代码，我们需要打开与关卡关联的.js文件。在图形界面中创建新项目文件时，通常会产生两个文件：

一个是.scene文件，这是编辑器用来在图形界面中呈现场景的文件。而.js或.ts文件则是编辑器根据我们的操作自动生成的代码。使用项目模板时，系统会自动创建level.js和level.scene文件。但假设我要新建一个场景作为示例，只需点击"新建"，选择"场景文件"，我将这个新场景命名为"test"。

29
00:11:12,721 --> 00:11:40,759
首先会看到test.scene文件被创建
现在场景已显示在图形界面中
同时test.js文件也已生成

生成的代码主要包含两部分
第一部分是编译器生成的代码
第二部分是供用户添加自定义代码的安全区域

在代码中我们会看到这两种区块
首先是"编译代码开始"和"编译代码结束"标记
然后是"用户代码开始"和"用户代码结束"标记

30
00:11:40,930 --> 00:12:06,100
首先我们会看到test.scene文件被创建，现在GUI中显示了场景，test.js文件也已生成。生成的代码主要分为两部分：第一部分是编译器生成的代码，第二部分是可供添加自定义代码的安全区域。在整个代码中，我们将看到这两种不同的代码块：一种是"编译代码开始/结束"标记，另一种是"用户代码开始/结束"标记。

所有未标注"用户代码开始/结束"的区域都不建议添加代码，因为每次执行操作时编译代码都会自动重新生成，您添加的内容可能会被覆盖。而在标注"用户代码开始/结束"的区块内添加代码，编辑器将不会修改这些区域，您的代码在每次编译时都会保留。举个例子，如果我们打开level.js文件...

31
00:12:06,442 --> 00:12:34,667
在这里，我们能看到与关卡场景中游戏对象绑定的代码。目前有两个游戏对象：一个是这个图像游戏对象，另一个是文本游戏对象。代码中对应两个变量：dino指向我们的图像游戏对象，welcome则指向文本游戏对象。它们各自拥有X和Y坐标位置，比如图像坐标显示为329×235——当我们选中图像游戏对象时，就能看到这个位置数值与之对应。

32
00:12:34,855 --> 00:12:54,172
如果我拖动这个恐龙到这里，可以看到属性值已经改变。
回到level.js文件，会发现代码也同步更新了。
在项目中添加自定义代码时，这点很重要，
要确保代码放在正确位置，避免被覆盖。

33
00:12:56,596 --> 00:13:26,459
要在本地运行和测试游戏，你需要启动一个开发服务器。默认情况下，Phaser编辑器自带了一个内置的HTTP服务器可以实现这个功能。我们使用的是基础JavaScript项目模板。如果点击左上角的这个播放按钮，就会启动开发服务器，并在你的网页浏览器中打开游戏。启动成功后，你会看到本地主机端口显示为editor external，这里就能看到我们的Phaser游戏了。当开发服务器运行时，如果你在场景中移动游戏对象并点击保存，再回到浏览器查看时

34
00:13:26,765 --> 00:13:56,048
要本地运行和测试游戏，您需要启动开发服务器。默认情况下，Phaser编辑器提供了内置的HTTP服务器来实现这一功能。我们使用的是基础JavaScript项目模板。点击左上角的播放按钮，就会启动开发服务器并在浏览器中打开您的游戏。启动后，您会看到显示本地主机端口及编辑器外部链接，这里就能看到我们的Phaser游戏了。

当开发服务器运行时，如果您在场景中移动游戏对象并点击保存，返回浏览器后需要刷新页面，就能看到游戏已更新为修改后的版本。需要注意的是，使用默认JavaScript项目模板时，内置HTTP服务器能无故障运行。但如果您使用的是基于现代Web技术（如Webpack、Parcel或Vite）的其他项目模板，则需要手动启动开发服务器。

点击上方播放按钮时，如果Phaser编辑器根据您的配置检测不到正在运行的开发服务器，就会弹出提示对话框。

35
00:13:56,288 --> 00:14:11,715
这样你就有选项可以启动开发服务器，然后就能在浏览器中运行游戏了。Phaser框架支持多种输入类型，包括键盘、鼠标、游戏手柄，甚至在移动设备上的触摸操作。

36
00:14:11,919 --> 00:14:36,577
我们将首先了解的是点击事件。在Phaser游戏中，点击事件通常是一种让玩家直接与场景或游戏对象互动的机制，点击时会触发游戏中的某些操作。默认情况下，这个Phaser模板已支持点击事件功能。现在我们切换到运行游戏的浏览器窗口，如果点击恐龙游戏对象，就能看到这里的文本内容被更新了。

37
00:14:36,766 --> 00:14:59,990
其运作原理是：当你在场景编辑器中选择一个游戏对象时，可以为其定义点击区域。点击区域就是玩家实际可点击的游戏对象范围，这样我们就能监听点击事件及其他各类事件。点击区域支持多种形状选择，默认情况下，建议对大多数图像类游戏对象使用矩形作为点击区域。

38
00:15:00,009 --> 00:15:24,905
其运作原理是：当你在场景编辑器中选择一个游戏对象时，可以为该对象定义点击区域。点击区域就是玩家实际可以点击的游戏对象区域，我们可以监听该点击事件及其他各类事件类型。点击区域支持多种形状选择，默认情况下，图像类游戏对象通常建议使用矩形。

当你选择矩形时，默认宽度和高度会自动匹配场景中加载的图片尺寸。比如我们选中这个文本游戏对象，可以看到当前点击区域显示为"无"。因此当向场景添加新游戏对象时，通常需要手动定义点击区域。例如当前场景中我们刚添加的dino1图像对象，如果切换到level.js文件查看...

39
00:15:25,178 --> 00:15:50,400
当我们查看dino1的定义处时，
可以看到这里的游戏对象。
原版恐龙与新恐龙的关键区别之一
就是这个setInteractive方法。
要让Phaser框架为游戏对象启用输入功能，
我们必须调用这个方法。
而Phaser编辑器在我们定义碰撞区域后，
会自动添加相关代码。
现在我们已经添加了这个矩形碰撞区域，

40
00:15:50,861 --> 00:16:15,366
回到level.js文件，我们会看到setInteract方法已被调用，并使用了我们定义的矩形参数。实现点击事件的第二个关键点是需要监听该事件并作出响应。在这个create方法下方，模板自带的代码已经添加了对此事件的监听。Phaser框架内置了多种可触发的事件类型

41
00:16:15,538 --> 00:16:44,240
如果我们回到level.js文件，现在可以看到setInteract方法已被调用，并传入了我们定义的矩形参数。实现点击事件的第二步是需要监听该事件并作出响应。在这个create方法底部，模板自带的代码已经添加了对此事件的监听。Phaser框架内置了多种系统事件，可以响应各种不同的操作。当我们监听输入事件时，可能是pointer down（指针按下）、pointer up（指针抬起）等多种类型。现在我们可以提供一个回调函数，在事件触发时执行特定操作。这个例子很好地展示了如何基于Phaser Editor生成的代码进行功能扩展。我们既能添加事件监听器，也能修改在场景编辑器中创建的其他游戏对象。例如，我们直接复制这段代码块，然后将其粘贴到指定位置。

42
00:16:44,633 --> 00:17:14,343
And we're going to listen for the pointer down event on our dyno1 variable. So we're going to do this. We'll do dyno1. We'll do on pointer down. But instead of saying hello world, we'll just say hi. So if we come back to our level scene, in order for us to use and run that code, we need to update our variable scope. So for the JavaScript code that's created, that's tied to our variable definition over here in our inspector. So by default, when we add in our new game objects, the scope will always be local to our class where it's created.

43
00:17:14,701 --> 00:17:28,882
这意味着我们只能在这段编辑器创建方法中添加的代码本地上下文中使用它。也就是说，除非我们更改该属性，否则无法在类的其他任何地方使用它。

44
00:17:29,121 --> 00:17:56,134
如果我们看一下原始的dyno对象，可以看到它的作用域是类级别的。因此我们需要在这里更新新dyno变量的作用域。让我们修改这个设置，将其设为类变量。这意味着这个变量将对我们这个Level类中的所有逻辑代码都可用。

现在查看生成的代码，可以看到我们有了这个新的dyno1属性，这将允许我们在下面的create方法中引用它。

45
00:17:56,442 --> 00:18:21,459
来看一个例子，如果我们不做修改，游戏中就会出现错误。保存更改后，回到浏览器刷新游戏。我们需要打开开发者工具，右键选择"检查"，切换到控制台查看日志。现在就能看到与level.js生成代码相关的报错信息，提示"无法读取未定义的属性"。这是因为在关卡代码中

46
00:18:21,766 --> 00:18:40,127
来看一个例子，如果我们不做修改，游戏中就会出现错误。保存更改后返回浏览器，刷新游戏界面。接着打开开发者工具，右键点击选择"检查"，切换到控制台查看日志。现在我们会发现level.js文件生成的代码报错了，提示"无法读取未定义的属性"。这是因为在关卡代码中，

我们实际上并没有名为dino1的属性，所以它是未定义的，这就导致了错误。现在如果我们把local改回class，代码中就存在这个属性了，程序应该能正常运行。此时点击第二只恐龙会显示"hi"，点击第一只则会显示"hello world"。

47
00:18:40,673 --> 00:19:08,557
正如我提到的，我们可以监听多种内置事件。在Phaser框架的官方文档网站上，列出了所有支持的事件类型，目前我们重点关注的是Phaser输入事件。针对输入系统，我们支持诸如点击场景中的对象、拖拽对象等操作。还可以实现将对象拖放到另一个对象上的功能。我们能够监听指针按下（pointer down）、指针抬起（pointer up）以及指针悬停（pointer over）等事件。通过这些功能，我们可以创建类似按钮的交互效果——比如当鼠标悬停在按钮上时，

48
00:19:08,985 --> 00:19:30,930
如我所言，我们可以监听多种内置事件。在Phaser框架文档网站上，列出了所有支持的事件类型，目前我们重点关注的是Phaser输入事件。针对输入系统，我们支持诸如点击场景中的对象并拖拽、将对象放置到另一个对象上等操作。还能监听指针按下、抬起以及悬停事件，这样就能实现按钮效果——比如鼠标悬停时施加特效，移开时样式变化等等。

除了基础输入事件外，我们还支持游戏手柄和键盘事件。当这些按键被按下时，我们可以监听并做出相应反应。由于Phaser框架支持所有这些事件，意味着它们在Phaser编辑器中同样可用，我们只需编写代码来响应这些事件即可。

49
00:19:31,152 --> 00:19:59,990
接下来我们要看的是键盘输入事件。我们将添加对方向键的监听功能，然后让场景中的游戏对象随之移动。在内置模块中添加键盘事件支持时，输入类型是其中之一。展开这个选项后，我们可以选择为场景添加键盘按键。把这个模块拖到场景中，会看到新增了一个名为"input"的文件夹。默认情况下变量名是"keyboard key"，我们把它改成"up key"。现在针对我们的...

50
00:20:00,009 --> 00:20:24,326
接下来我们要探讨的输入类型是键盘事件。我们将添加对方向键的监听功能，然后在场景中移动游戏对象。在内置模块中添加键盘事件支持时，输入类型是其中之一。展开这个选项后，我们可以选择向场景添加键盘按键。把这个模块拖到场景中，会看到新增了一个名为"input"的文件夹。默认情况下变量名是"keyboard key"，我们将其改为"up key"。

这个按键默认作用域是类级别，我们需要保持这个设置，因为要在场景的更新方法中监听输入并作出响应。接下来可以选择要监听的按键，这里我们选择上方向键。选择完成后保存场景，切换到level.js文件，在编辑器的create方法中可以看到已创建了up key变量，并开始监听输入插件。

51
00:20:24,923 --> 00:20:43,046
它默认的作用域是类级别，我们需要保持这个设置，因为我们将在场景的update方法中监听输入并作出响应。接下来我们可以选择要监听的按键，这里我们选择上方向键。选定后保存场景，切换到level.js文件，可以看到在编辑器create方法中已经创建了上方向键变量，并且我们正在监听输入插件。

我们的键盘管理器正在执行add key操作，这将允许我们监听按键按下、按住和释放三种状态。这个监听器会被赋值给我们为类添加的up key属性。现在为了响应按键操作，我们需要在代码中添加一个新方法——update方法。

52
00:20:43,386 --> 00:21:08,676
这里的方法是Phaser场景类自带的内置方法之一。这个方法会在每次游戏更新时被调用。现在我们可以在这个方法中检查按键是否被按下。我们只需添加一个if判断语句：如果上方向键处于按下状态，就表示玩家当前正按着这个键。

53
00:21:09,137 --> 00:21:32,089
现在我们可以选择一个游戏对象，并根据按键状态来更新它的位置。让我们引用恐龙游戏对象及其Y属性。当我们按下上键时，希望游戏对象在场景中向上移动。在场景坐标系中，左上角的位置是(0,0) - X轴为0，Y轴也为0。

54
00:21:32,381 --> 00:22:00,451
当我们向右移动时，场景中的X值会增加。向左移动则会减小X值。向下移动会增加Y值，而向上移动则需要减小Y值。这意味着当按下上键时，我们实际上要从Y值中减去一个数值。这里我们直接减去4。保存代码后，确保场景已保存，回到浏览器刷新场景即可。

55
00:22:00,759 --> 00:22:30,059
当我们向右移动时，这会增加x值。向左移动则会减小x值。向下移动时，y值会增加。而向上移动时，y值需要递减。这意味着当按下上方向键时，我们需要从y值中减去一个数值。这里我们直接使用"-=4"来实现。保存代码后，确保场景已保存，让我们回到浏览器刷新场景。

现在按住上方向键，可以看到游戏对象正在场景中向上移动。接下来我们将对其他方向键进行相同操作。再拖入几个键盘控制模块，先设置下方向键，选择对应的键值代码。然后是左方向键，找到对应的按键设置。最后设置右方向键，保存修改。

56
00:22:30,298 --> 00:22:56,014
切换到level.js文件，现在我们要做同样的事情，但这次要引用我们创建的其他按键。快速查看编辑器中的create方法，可以看到其他按键已经根据我们指定的键码定义好了。现在回到update方法，使用else if语句，这里复制一下。如果是上方向键，如果是下方向键被按下，

57
00:22:56,237 --> 00:23:25,230
现在我们要继续增加Y值。让我们复制这段完整代码，然后处理左右方向键。如果左键按下，我们要减少X值；如果右键按下，我们要增加X值。保存后回到浏览器，现在按方向键就能在场景中移动游戏对象了。

58
00:23:28,762 --> 00:23:43,114
现在我们可以通过键盘事件在场景中移动物体了。接下来我们要看看如何利用物理系统让游戏对象之间产生互动。Phaser Editor默认支持Phaser的街机物理系统，

59
00:23:43,507 --> 00:24:04,240
现在我们可以通过键盘事件在场景中移动物体。接下来我们要学习的是如何利用物理系统让游戏对象之间产生互动。Phaser Editor默认支持Phaser的街机物理系统，

这个系统能让我们检测两个不同游戏对象之间的碰撞，检查它们何时重叠等等。Phaser框架支持多种物理系统，比如街机物理、Impact物理和Matter.js。街机物理非常适合简单的游戏，我们只需要检测碰撞等基本交互。

60
00:24:04,497 --> 00:24:31,202
为了让游戏支持物理效果，我们需要更新游戏配置，告诉Phaser我们想使用哪种物理系统。在我们的游戏配置中，这些设置会放在这个主.js文件里。在代码内部，我们会新建一个Phaser游戏实例，并为游戏提供配置参数。在这里面，我们可以指定要使用的物理系统。如果我们添加physics属性，它将是一个对象。

61
00:24:31,527 --> 00:24:59,990
我们需要添加的第一个属性是default。这个字符串属性用于指定默认使用的物理系统，这里我们选择"arcade"。

接下来，我们要为使用的物理系统提供配置属性。在这个例子中就是arcade系统。现在我们需要为其添加配置参数。

第一个要设置的参数是debug，我们将其设为true。这将允许我们看到物理体的轮廓边框。这样在设计游戏时，使用物理系统会变得非常方便。不过游戏完成后，记得要将其设为false。

62
00:25:00,009 --> 00:25:22,089
我们要添加的第一个属性是default（默认设置）。这将是一个字符串，用于指定我们默认使用的物理系统。我们选择arcade（街机模式）。接下来，我们提供正在使用的物理系统属性，这里同样是arcade。现在我们要为其提供配置。首先设置debug（调试）属性为true（开启），这将显示物理体的轮廓边框，方便我们在设计游戏时使用物理系统。游戏完成后记得将其设为false（关闭）。

接下来可以定义默认重力值，分别应用于X和Y轴属性。我们把两者都设为0。现在保存设置。既然已经为游戏启用了物理系统，就需要更新游戏对象来实际使用它。有几种不同的实现方式：第一种是为现有游戏对象添加物理体。

63
00:25:22,328 --> 00:25:46,953
接下来，我们可以定义默认的重力参数，它将作用于X和Y属性。这里我们把两个值都设为零，然后保存设置。既然已经为游戏启用了物理系统，现在需要更新游戏对象来实际应用它。具体有几种实现方式：第一种是为现有游戏对象添加物理刚体——通过这个刚体组件我们就能检测碰撞。

第二种方式是修改默认对象类型。当我们拖入图像时，默认创建的是普通图像对象。如果选中恐龙对象，可以将其改为街机图像类型。街机图像是普通图像对象的扩展版本，但附加了更多与物理系统联动的属性。快速查看检视器面板的话，

64
00:25:47,397 --> 00:26:16,868
这就是物理体，我们用它来检测碰撞。第二种方法是我们可以更改默认对象类型。当我们拖入一个图像时，默认会是图像游戏对象。如果我们点击恐龙图像，可以将其改为街机图像。街机图像是图像游戏对象的扩展，但具有更多与物理相关的属性。快速看一下检查器面板，

现在能看到新增了与物理体相关的区域，以及物理如何与游戏对象交互的设置。而选择另一只恐龙时，检查器里就没有这些属性。第三种添加物理的方式是使用内置对象类型，选择街机图像或街机精灵并拖入场景。现在可以选择想要使用的纹理贴图，默认类型就是街机图像，无需手动修改。

65
00:26:17,567 --> 00:26:41,577
现在我们将游戏对象类型更新为街机图像后，查看面板会发现默认已带有街机物理体。保存后重新打开网页浏览器，刷新页面，就能看到恐龙周围出现了紫色方框。这个紫色方框代表的就是街机物理体，默认会采用当前图像的宽高作为碰撞体积。

66
00:26:41,903 --> 00:27:09,990
在我们将游戏对象类型更新为"街机图像"后，查看面板时会发现默认已附带一个街机物理体。保存后重新打开网页浏览器并刷新，现在可以看到恐龙周围出现了紫色方框。这个紫色方框代表的就是我们的街机物理体。

默认情况下，物理体会采用游戏对象的宽高尺寸。由于当前图像就是这样的尺寸，所以紫色方框会包裹整个游戏对象，而不是精确贴合恐龙轮廓。这个紫色轮廓的物理体，就是我们用来检测与其他启用了物理功能的游戏对象发生碰撞的依据。

回到场景中，选择第二只恐龙，我们同样将其类型更新为"街机图像"。

67
00:27:10,094 --> 00:27:37,295
我们将执行替换操作，点击保存后返回，现在我们的两个游戏对象都已启用物理系统。就像处理输入事件一样，在设置好物理碰撞体后，我们需要告知编辑器要检测哪些碰撞对象。我们可以检测与单个游戏对象的碰撞，也可以检测与一组游戏对象的碰撞，甚至还能检测与画布元素边界的碰撞。要实现这些，请先选择玩家控制的恐龙角色。

68
00:27:37,449 --> 00:28:07,346
接下来，我们进入街机物理引擎的碰撞面板。这里有许多属性会影响物理效果在物体上的应用。我们只需勾选"碰撞世界边界"这个选项。保存后回到浏览器，现在如果尝试将游戏对象移出画布元素外，就能看到物理系统会阻止它越界。要检测游戏对象之间的碰撞，我们需要使用所谓的碰撞器。

69
00:28:07,567 --> 00:28:34,565
接下来，我们进入街机物理引擎的碰撞面板。这里有许多属性会影响物理效果如何作用于我们的物体。我们只需勾选这个"碰撞世界边界"的选项。保存后回到浏览器，现在如果我们尝试将游戏对象移出画布范围，就会发现物理系统会阻止它越界。要检测游戏对象之间的碰撞，我们需要使用所谓的碰撞器。

从街机物理模块中拖入碰撞器功能块。这个功能块允许我们选择需要检测碰撞的两个游戏对象或组，并设置碰撞发生时触发的回调函数。第一个对象选择我们的恐龙角色，第二个对象也选择同一个恐龙角色。保存后回到浏览器...

70
00:28:35,195 --> 00:28:57,739
刷新页面后，如果我们尝试将一个游戏对象移动到另一个游戏对象上，会发现碰撞检测没有生效。这是因为要让碰撞系统正常工作，我们必须使用物理系统来移动游戏对象。目前在我们的level.js文件中，我们只是手动设置x和y坐标值，通过增减这些数值来移动对象，而没有实际检测是否发生了碰撞。

71
00:28:58,028 --> 00:29:24,019
让我们回顾一下，如果尝试将游戏对象移动到另一个游戏对象上，会发现碰撞检测失效了。这是因为要让碰撞系统正常工作，我们必须使用物理系统来移动游戏对象。目前在我们的level.js文件中，我们直接手动设置了x和y值，并在没有检查实际碰撞的情况下进行增减操作。

这样做实际上覆盖了物理系统的功能，强行移动了游戏对象，导致我们能穿透其他物体。正确的做法不是直接修改x/y属性，而是应该通过更新游戏对象的速度属性来实现场景中的移动。要使用物理系统进行移动，我们需要引用dino游戏对象，调用setVelocity方法，现在就来设置速度参数。

72
00:29:24,343 --> 00:29:51,749
根据我们当前操作的属性，选择X轴或Y轴。在向上按键时，由于涉及Y轴，我们将设置velocityY。现在可以设定我们想要的移动速度值，这里设为-200。就像之前处理Y轴那样，向上移动时设为负值，向下则设为正值。复制这段代码，粘贴后改为+200。在设置Y值前，先注释掉这里的代码。保存修改。

73
00:29:52,432 --> 00:29:59,990
如果我们刷新场景，来到恐龙下方，现在尝试向上移动，就会看到我们正在推动另一个游戏对象。

74
00:30:00,009 --> 00:30:29,905
当我们按下上下键一次时，会看到物体持续朝该方向移动，因为我们设置了速度值。实际上物理系统会多次调用更新方法。一旦为游戏对象设置了速度，除非我们修改物理参数，否则速度将保持不变，物体会持续来回移动。因此我们需要告诉Phaser框架：当按键未被按住时，就不应该更新这个速度值。

75
00:30:30,588 --> 00:30:59,650
为了解决这个问题，我们只需在这个条件判断后添加一个else代码块。当上下方向键都没有被按下时，我们将速度重置为零。现在刷新后，松开按键时游戏对象就会停止移动。这样我们就能在碰撞时推动另一个游戏对象了。接下来让我们对x轴属性做同样的修改。这里复制这段代码，先注释掉原有部分，粘贴新代码，然后使用setVelocityX方法。

76
00:31:00,281 --> 00:31:26,084
我们复制这段代码，粘贴过来。这里要改成正值。最后添加else语句块，用来重置数值。把这里改成x轴，然后保存。趁现在，我们可以考虑把重复出现的数值提取出来，存储在文件的一个属性或变量中会更合理。这样只需在一处修改，而不用多处改动。

77
00:31:26,782 --> 00:31:56,509
我们复制这段代码。粘贴过来。然后将其设为正值。最后添加else代码块。接着重置它。把这个改成x。保存一下。既然在这里，我们可以考虑这个值被重复使用了多次。把它存储在文件的一个属性或变量中会更合理。这样只需在一个地方修改，不必多处改动。

为此，我们回到代码中"可以安全编写更多代码"的位置。给类添加一个新属性。就命名为playerVelocity吧。默认值设为200。添加完属性后，来更新下面的代码。改成这样：playerVelocity乘以-1。得到负值。复制这段。粘贴到这里，还有这里。都乘以-1。

78
00:31:56,714 --> 00:32:11,220
好的，我们来保存并测试修改效果。我回到浏览器刷新页面，现在依然可以像之前那样移动游戏对象，但这次我们是通过物理系统实现的。这样一来，我们就能进行碰撞检测，并与其他游戏对象互动了。

79
00:32:11,646 --> 00:32:40,827
好的，现在需要注意一点：当我们碰到另一个游戏对象时，实际上是我们这个物理游戏对象的碰撞力作用在了那个对象上。由于没有任何阻力使其停下或减速，那个游戏对象也会持续移动。而我们从未更新过那个游戏对象，所以它不会受到场景边界限制，这就是为什么它能离开场景而我们的游戏对象不能。如果我们想让那个游戏对象真正移动起来，可以在编辑器里选择Dino 2进行设置。

80
00:32:41,253 --> 00:33:03,915
好的，这里需要注意一点：当我们碰到另一个游戏对象时，实际上是我们这个物理游戏对象在碰撞时对那个对象施加了力。由于没有任何阻力，那个游戏对象也会持续移动。而我们从未更新过那个游戏对象，所以它不会离开场景，这就是为什么它能离开而我们的游戏对象不能。如果我们想让那个游戏对象真正移动起来，可以在编辑器中选择Dino 2，

我们找到物理体的属性设置。当发生碰撞时，可以勾选这里的"不可移动"选项。这样当我们与该游戏对象互动时，就无法推动它了。现在就像撞上了无法推动的静态物体，这非常适合用作游戏中的墙壁或其他不希望被移动的角色。

81
00:33:07,671 --> 00:33:37,585
除了用物理系统检测游戏对象间的碰撞外，我们还能用它来检测重叠。重叠功能允许游戏对象穿过其他对象，同时仍能通过物理系统获知何时发生了重叠。这非常适合游戏中的拾取物机制，让玩家能触碰并拾取物品。当我们需要设置事件触发区域，或是让玩家通过特定区域激活游戏事件时，重叠检测就是理想选择。

82
00:33:37,789 --> 00:34:01,509
在我们的游戏中，假设有一只饥饿的恐龙想要吃食物。我们需要实现拾取功能。为此，让我们回到场景编辑器，添加一个新的街机图像游戏对象。拖动街机图像块到场景中，选择食物素材。选中图像后——哦，图像有点大，我们把它缩小到0.4倍。

83
00:34:02,636 --> 00:34:31,681
现在我们的食物道具已经缩小了一些，接下来要为玩家和需要重叠的对象添加碰撞器。从Arcade模块中，我们再往场景里添加一个碰撞器。在这个碰撞器上，选择我们的恐龙角色。然后第二个对象选择我们的Arcade图片道具。现在碰撞器这里还有个重叠选项可用。如果我们点击这个并保存，回到浏览器界面，

84
00:34:32,072 --> 00:34:59,990
现在发生的情况是，尽管这个食物对象具有物理属性，当我们与之碰撞时，实际上并不会推动或移动它，而只是与其重叠。为了处理这种情况，我们需要监听玩家与游戏对象碰撞时的回调函数。在这里，我们可以提供想要调用的回调函数。那么对于这个回调函数，我们现在可以在我们的

85
00:35:00,009 --> 00:35:29,871
现在的情况是：虽然这个食物对象具有物理实体，但当我们与之碰撞时，实际上并没有移动或推动它，而是与之发生了重叠。为了处理这种情况，我们需要监听玩家与游戏对象碰撞时的回调函数。这里我们可以提供一个想要调用的回调函数。

在我们的关卡类中，我们需要添加一个待调用的方法。为此，我们将引用当前场景的上下文，然后调用"eatFruit"方法。保存后，我们需要在类中添加这个方法。

进入level.js文件，在update方法下方，让我们为类添加一个新方法，命名为"eatFruit"。在这个方法内部，我们只需使用console.log输出"hit"信息。目前我们只想观察是否有反应发生。

86
00:35:30,195 --> 00:35:58,335
我们想要调用的关卡类方法。为此，我们需要引用当前场景的上下文，然后调用eatFruit方法。保存后，我们需要在类中添加这个方法。现在进入level.js文件，在update方法下方添加一个新方法，命名为eatFruit。好的，在这个方法内部，我们先简单写个console.log打印"hit"。这样做的目的是为了观察碰撞效果。

刷新浏览器后可以看到，当物理体与食物对象重叠时，回调函数就会被触发。注意这里每秒会触发多次回调。只要这两个游戏对象处于重叠状态，游戏循环的每次更新都会调用这个回调函数。这一点非常重要：如果你的逻辑只需要执行一次，就需要在代码中处理这种情况，停止重叠检测的持续触发。

87
00:35:58,831 --> 00:36:18,898
既然我们已经确认碰撞检测生效了，接下来就需要清理食物游戏对象，将其从场景中移除。为此，在碰撞回调函数中，我们会接收到两个关键参数——它们正是发生重叠碰撞的两个游戏对象实例。

88
00:36:19,224 --> 00:36:48,148
现在既然看到这个功能已经生效，我们就要清理食物游戏对象并将其从场景中移除。具体操作是：在碰撞器中调用碰撞回调函数时，会接收到两个参数，分别是发生重叠碰撞的两个游戏对象。

这些参数的顺序取决于我们在此处添加对象的顺序。由于第一个对象是我们的恐龙，第一个参数将引用恐龙图像游戏对象，第二个参数则引用食物对象。现在转到level.js文件，将这两个参数添加到方法中，先添加dino，再添加food。

好的，在console.log下方，我们来引用food变量。对于食物游戏对象，我们需要禁用其物理体。

89
00:36:48,643 --> 00:37:13,233
根据我们在这里添加这些对象的顺序，这将决定参数的排列顺序。由于第一个对象是恐龙，第一个参数将引用我们的恐龙图像游戏对象，第二个参数则引用食物对象。现在来到level.js文件，让我们为方法添加这两个参数：先添加dino，再添加food。好的，在console.log下方，我们来引用food变量。对于这个食物游戏对象，我们需要禁用其物理体。

通过禁用物理体，我们就能在代码执行其他操作时停止碰撞检测。回到浏览器刷新后，现在应该能看到碰撞只触发了一次——即使游戏对象仍在重叠状态。接下来我们可以销毁这个游戏对象并将其从游戏中移除，以此显示恐龙已经将其吃掉。

90
00:37:13,695 --> 00:37:37,568
如果我们执行food.destroy，这将通知Phaser销毁该游戏对象并清理它。好的，如果我们在浏览器中刷新，当与食物物品重叠时，我们会看到它从场景中移除了。好的，所以在我们的游戏中，吃完一个食物后，恐龙仍然饥饿。那么让我们在游戏中再添加一个食物拾取物。从我们的积木中，选择一个街机图像。我们将选择我们的食物物品。

91
00:37:37,961 --> 00:38:06,766
我们能看到食物现在恢复了原始大小
现在需要手动调整尺寸来匹配另一个食物
随着在场景中添加更多游戏选项实例
我们将不得不重复这种逐个修改设置的模式
但假设我们做完这些后
突然又想更改某个食物的其他设置
比如说想给它添加一个帐篷效果来改变颜色
举个例子 假设我想这样做 呃

92
00:38:07,244 --> 00:38:26,407
哦，我们看到食物道具现在恢复了原始尺寸。现在需要手动调整它的大小以匹配另一个食物道具。随着我们在场景中添加更多游戏选项实例，我们将不得不重复这种逐个修改设置的繁琐操作。假设我们完成调整后，突然又想修改某个食物道具的其他属性——比如给它添加帐篷并改变颜色。举个例子，我打算这样做...

选用这里的蓝色。如果我复制这个颜色并应用到所有帐篷上，现在我们就有了独特的道具，但还需要把这个改动同步到其他所有游戏对象上。为了避免这种重复劳动，我们可以使用Phaser编辑器中的"预制体"功能。预制体让我们能够创建一种...

93
00:38:26,596 --> 00:38:55,775
这里的蓝色部分。如果我复制这个颜色并应用到所有帐篷上，现在我们就有了独特的物品，但还需要把这个颜色应用到其他游戏对象上。为了避免重复操作，我们可以使用Phaser编辑器中的预制体(prefab)功能。预制体可以让我们创建一个游戏对象所有属性设置的快照，然后基于这个预制体创建多个实例。这样当我们修改预制体时，所有相关游戏对象都会同步更新。

来看具体操作：首先清理掉这些水果对象。选中这个水果对象，右键点击选择"预制体"→"用对象创建预制体"。创建预制体有几种方法，对于已存在的游戏对象，我们可以直接这样操作。

94
00:38:56,237 --> 00:39:25,862
这是我们为某个游戏对象设置的所有属性的快照。然后我们可以基于这个预设体创建实例。之后任何时候我们对预设体做出修改，都会同步到所有相关游戏对象上。

来看个具体例子：我们先清理掉这些水果对象。对于这个水果对象，我们要创建一个预设体。有几种方法可以实现：当已有现成游戏对象时，我们可以右键点击该对象，选择"预设体"，然后点击"用对象创建预设体"。

这个操作会弹出一个对话框让我们选择预设体保存位置。我暂时把它保存在源文件夹里，命名为"food prefab"，点击创建。保存后首先会看到，场景大纲中的游戏对象变成了绿色。展开后可以看到它现在引用的是这个预设体实例。

这意味着我们已经创建了新的food prefab场景和food prefab JS文件。我们刚创建的food预设体现在...

95
00:39:26,014 --> 00:39:53,387
这将创建一个继承自该游戏对象类型的类。通过这种方式，我们可以直接修改该游戏对象。现在我们可以将这些更改应用到场景中。举个例子，假设我们不喜欢食物上的帐篷图案。让我们把它重置回白色。如果我们复制这个设置，应用到数值上，保存后食物就会变回紫色。这时我们切换到关卡场景...

96
00:39:53,608 --> 00:39:59,990
这只是一个继承该游戏对象类型的类。通过这种方式，我们可以直接修改那个游戏对象。现在就能将这些改动应用到场景中。举个例子，假设我们不喜欢食物上的那个色调，可以把它重置回白色。如果我们复制这个值并应用到参数中，保存后食物就会变回紫色，这时切换到关卡场景——

（场景切换）哦，我们看到食物现在变成紫色了。这正是预制体大显身手的地方，因为现在我们在这里所做的任何修改，

97
00:40:00,009 --> 00:40:28,199
我们看到食物道具变成了紫色。这时预制件的优势就显现出来了——我们在此处所做的任何修改，
都能应用到所有场景中的实例上。现在如果需要多个食物拾取物，不必再拖拽街机图像，直接使用这个新的预制件方块即可。拖入食物图像后，可以看到它与其他游戏对象实例尺寸一致，且继承了所有相同的属性设置。现在我们可以向场景中拖入多个实例。若想统一修改所有实例，只需调整食物预制件即可。

98
00:40:28,438 --> 00:40:55,434
假设我们希望食物尺寸更大些。将缩放比例设为1.2, 1.2。保存后返回场景，可以看到所有游戏对象实例都已更新。现在我将撤销对预制体和缩放比例的修改，恢复为0.4。预制体另一个很酷的功能是：当我们创建这些实例时，属性面板上会出现一个小锁图标。

99
00:40:55,657 --> 00:41:20,077
假设我们希望食物看起来更大些。那么我们把缩放比例设为1.2。保存后回到场景，会发现所有游戏对象实例都已同步修改。现在我要把预制体的缩放值复原回0.4。预制体还有个很酷的特性：当我们创建实例时，属性面板上会出现一个小锁图标。

这个锁图标表示：当属性被锁定时，实例将使用该类的默认属性值；解锁后，我们通过界面设置的值将仅作用于当前实例。正因如此，我们才能在场景中单独移动这个对象——其他实例之所以不受影响，正是因为它们的坐标属性也处于解锁状态。

100
00:41:20,349 --> 00:41:44,206
这意味着当我们锁定这个属性时，创建的类实例将使用该类的默认属性。解锁后，实例则会采用我们在图形界面中设置的数值。这就是为什么我们能在场景中移动这个对象，而其他对象保持不动——因为它们也处于解锁状态。

如果我们锁定这个位置，它就会恢复到预设实例的默认位置（0,0）。当我移动它时，只有带锁定图标的实例会随之移动。现在让我们把它重置回（0,0）坐标，这样它就会回到左上角。保存后返回关卡场景，现在解锁后就可以自由拖动这个对象而不会影响预设值。

101
00:41:44,445 --> 00:42:13,659
这样我们就可以对实例的任何属性进行修改。比如我想把这个食物对象放大些，可以设为0.6。其他实例会保持原有尺寸不变。我们还能调整透明度或色调等属性，都不会影响其他实例。预制体功能非常强大，特别适合需要创建多个相同游戏对象的情况。好了，现在我们已经往场景里添加了多个预制体。如果回到浏览器测试一下，

102
00:42:14,086 --> 00:42:40,572
现在的情况应该是：我们可以吃掉最初的那个食物，但新增的那些食物却无法拾取。原因在于我们的碰撞检测目前只针对最初添加的那个食物游戏对象。我们需要做的是检测玩家与所有食物游戏对象之间的碰撞。因此，必须传入一个游戏对象组或数组，用于检查与哪些对象可能发生碰撞。

103
00:42:40,811 --> 00:43:10,538
为了传递一组需要检测碰撞的游戏对象，我们可以使用另一个内置功能块。在"内置块"菜单下，进入"组"选项，向游戏场景添加一个列表。这将新增一个名为"列表"的板块。列表功能可以创建数组结构，你可以在数组中添加任意需要的游戏对象，这些对象就能在我们的代码中使用了。我将这个变量命名为"food"然后保存。现在跳转到level.js文件，就能看到类中新增了一个名为food的属性。

104
00:43:10,896 --> 00:43:37,465
因此，在列表下方我们可以看到food数组目前是空的。现在要向列表中添加对象，我们可以先选中一个游戏对象，然后从属性下拉菜单中选择"list"选项。选择"food"后保存，再回到level.js文件，就能看到我们的食物预设项已被添加到food数组中，后续可以在代码中引用这些对象。

好的，总结一下添加游戏对象到列表的操作：选中游戏对象后...

105
00:43:37,875 --> 00:44:05,913
在检视面板中，我们选择列表功能，现在可以选择要添加对象的食物列表。你也可以同时选择多个游戏对象进行添加。保存后回到碰撞器设置，这次为对象2选择食物列表而非特定食物实例。点击选择后保存，返回浏览器刷新页面。现在当我们拾取食物对象时，可以看到每个都能被拾取，回调方法也被成功触发。

（注：根据视频教程类内容的翻译特点，做了以下处理：
1. "inspector panel"译为游戏开发领域通用的"检视面板"
2. 将长句拆分为符合中文表达习惯的短句结构
3. "callback method"保留专业术语译为"回调方法"
4. "being invoked"译为"被触发"符合中文技术文档表达习惯
5. 使用"可以看到"等口语化表达保持教程亲切感）

106
00:44:09,188 --> 00:44:37,278
接下来我们要为游戏实现计分功能。当饥饿的恐龙拾取并吃掉食物时，我们会增加分数，并将分数显示给玩家看。要实现这个功能，我们需要在场景中添加一个新的文本游戏对象。在内置类型的字符串选项下，我们添加文本组件。将这个文本游戏对象的变量命名为"score"。然后将其定位在屏幕左上角，并更新文本内容为"分数"。

107
00:44:37,670 --> 00:44:59,990
然后我们将初始值设为0
把字体大小调整为48像素
并在游戏边缘留出一些间距
位置方面，我们向右移动10像素
再向下移动10像素
保存后返回浏览器刷新
就能看到分数显示对玩家可见了
现在当我们拾取食物对象时
需要实时更新这个分数显示

108
00:45:00,009 --> 00:45:27,789
更新我们这里的文本字符串。现在转到level.js文件，首先添加一个新变量来记录玩家分数。在自定义代码区域（之前定义玩家速度的地方），我们添加score变量，并设置默认值为0。然后在eat fruit方法中，删除console.log语句，在销毁游戏对象后增加分数。具体操作是：引用score变量，然后执行加1操作（score += 1）。

109
00:45:28,523 --> 00:45:54,309
更新字符串中的文本显示。现在我们来到level.js文件，首先添加一个新变量来记录玩家分数。在自定义代码区域（之前定义玩家速度的地方），我们添加score变量并设置默认值为0。

在eat fruit方法中，删除console.log语句。销毁游戏对象后，我们要增加分数。具体操作是引用score变量，然后使用+=1进行递增。

更新分数变量后，需要同步更新分数文本显示。为此要引用已创建的score游戏对象。目前该对象的scope设置为local，我们需要改为class级别。这样就能添加一个可供引用的类属性。回到属性面板，现在可以看到score属性，它会与我们在此处添加的属性产生关联。

110
00:45:54,717 --> 00:46:24,581
在我们更新分数变量后，实际上需要更新分数文本。对于分数文本，我们需要引用已创建的分数游戏对象。当前我们的分数游戏对象作用域设置为局部，现在让我们将其改为类作用域。这样我们就能为类添加一个可供引用的属性。

当我们查看属性面板时，现在可以看到有一个分数属性，它会与我们在此处添加的属性产生冲突。

这里需要注意：当你在检查器中添加变量名称时，这些名称也会成为添加到类中的属性名称。由于它与我们添加的分数属性同名，让我们将其命名为分数文本。点击保存后，回到level.js文件，现在就能看到分数文本属性了。

回到afruit方法中，让我们引用分数文本游戏对象。我们将使用setText方法来更新游戏对象上的文本内容。为此，我们要确保保留"分数"这个词。

111
00:46:24,974 --> 00:46:49,769
现在我们要把分数值加进去。所以我们写上this.score。好的，保存后回到浏览器。现在当我们拾取食物时，哦，出现了一个错误。提示说：无法读取未定义的set text属性？问题在于，当我们在方法内部时，当前试图引用的this指向的是level场景的作用域。

112
00:46:50,195 --> 00:47:16,630
现在我们只需要添加分数值。我们使用this.score来操作。好的，保存后回到浏览器。当我们拾取食物时，哎呀，出现了一个错误。提示说：无法读取未定义的set text属性？问题在于，在这个方法内部，我们试图引用level scene的this作用域。

我们原本想在这个类中递增分数和更新分数文本属性。但回到level scene查看碰撞器，当我们在这里调用回调函数时，默认会丢失原始调用上下文。举个例子，在执行disable body之前，我先用console.log输出this。回到浏览器，让我们再试一次拾取食物。

113
00:47:17,159 --> 00:47:46,612
因此我们尝试在这个类中递增分数和更新分数文本属性。但在关卡场景中，如果查看碰撞器，当我们在此处调用回调时，默认会丢失调用来源的上下文。举个例子，在执行disable body前，我先用console.log输出这个值。回到浏览器，让我们试着拾取一个食物道具。

现在可以看到这里的this仅指向当前回调函数的上下文。若想使用整个类的上下文，我们需要显式传入。所以在回调上下文这里，我们要传入this来引用当前场景。刷新浏览器后再次拾取食物，就能看到上下文已正确指向关卡场景。现在作用域正确了，食物道具会被销毁，同时分数文本也成功更新。

114
00:47:46,971 --> 00:48:16,817
现在我们应该能够拾取场景中的所有游戏对象了。既然已经为游戏添加了计分机制，我想花点时间谈谈场景分层和渲染顺序的问题。当我们移动场景中的游戏对象时，可能已经注意到恐龙游戏对象会显示在两个UI元素的文字后面。这是因为默认情况下，Phaser会按照我们将游戏对象添加到场景中的顺序进行渲染。

115
00:48:17,159 --> 00:48:44,480
现在我们应该能够选中场景中的所有游戏对象了。既然已经为游戏添加了计分机制，我想花点时间谈谈场景分层和渲染顺序的问题。当我们移动场景中的游戏对象时，可能已经注意到恐龙游戏对象会显示在两个UI元素的文字后面。这是因为默认情况下，Phaser会按照我们向场景添加对象的顺序进行渲染。

而在Phaser编辑器中，这个顺序取决于它们在游戏对象层级结构中的排列位置。由于我们的分数文本和欢迎文本都排在恐龙对象上方，所以它们会渲染在玩家游戏对象之上。如果想要改变这个顺序，就需要调整层级结构中这些对象的排列位置。举个例子，如果我将恐龙对象...

116
00:48:44,786 --> 00:49:12,159
使用Phaser编辑器时，这取决于游戏对象在层级结构中的添加顺序。由于分数文本和欢迎文本都位于恐龙对象上方，它们会渲染在玩家游戏对象之上。若要调整显示顺序，我们需要改变层级结构中这些对象的排列。例如，如果我选中恐龙对象，

然后进入布局选项进行排序，选择"上移"就会将恐龙在层级中提升。保存后返回场景，现在恐龙游戏对象会显示在其他游戏对象上方，但UI文本仍会显示在后方。这一点非常重要，因为当你向场景添加游戏对象时，它们的添加顺序将直接影响玩家看到的显示效果。

117
00:49:12,704 --> 00:49:39,889
因此还有其他方法可以控制渲染顺序。其中一种方式是通过游戏对象的深度值来实现。在Phaser编辑器中，更简便的管理方法是使用"图层"功能。图层可以将我们的游戏对象分组管理，然后通过调整图层的上下顺序，就能控制它们在游戏中的显示层级。让我们实际操作一下：在"内置模块"下的"分组"选项中，拖入一个"图层"组件，这将在场景中添加一个新的图层。

118
00:49:40,195 --> 00:49:59,990
因此，我们可以将这个图层命名为"UI"。对于图层管理，我们需要将游戏对象实际添加至该图层中。默认情况下，所有游戏对象都会有一个父级容器，这个父级默认就是场景本身。我们可以在对象属性中

119
00:50:00,333 --> 00:50:29,514
如果我选中这里的欢迎文本，右键点击后可以选择"父级"，然后执行"移动到父级"操作。这样就能为游戏对象选择一个新的父级对象。这里我们将选择UI图层作为父级。在层级结构中，这会将游戏对象嵌套在该图层下方，表明该游戏对象的父级就是这里的这个游戏对象。我们可以对分数文本进行同样的操作：选择"父级"→"移动到父级"，然后指定UI图层即可。

120
00:50:29,786 --> 00:50:53,813
现在由于层级关系，我们的UI层位于其他游戏对象之上。这两个游戏对象将始终显示在其他对象前面。我们可以将剩余的游戏对象添加到另一个图层，这样就能轻松进行排序和移动。但如果我们回到浏览器视图，现在将游戏对象移动到文字上方时，可以看到它显示在文字后面。对于UI界面来说，游戏对象就显示在其下方了。

121
00:50:56,851 --> 00:51:17,585
在我们的游戏中，饥饿的恐龙除了水果还想吃点别的。为此，我们需要向项目添加新素材。要在Phaser编辑器中添加素材，一种方法是点击这里的素材文件夹，然后在检查器面板中会看到添加文件的选项。我们可以直接将文件拖放到这里，它们就会被复制到项目中。

122
00:51:17,891 --> 00:51:45,793
我们可以点击这里的按钮浏览文件。最后，如果右键点击文件夹，还能选择"添加文件"选项，同样会弹出添加文件的对话框。我决定给我的恐龙准备一个汉堡——这里有个汉堡素材是精灵表。视频简介里会提供这个汉堡精灵表的下载链接。特别感谢Cast Creates Games这位创作者，他制作了这个超棒的精灵表并在itch.io上免费分享，下方简介中也会附上链接。

123
00:51:46,748 --> 00:52:04,326
好的，目前我们只是往项目文件夹里添加了一个新素材。要想在Phaser编辑器里使用这个素材，并让它能在Phaser游戏中生效，我们需要告诉Phaser编辑器如何处理它。当我们加载完这个新精灵表后，就会在文件的检查器面板里看到新增的选项。

124
00:52:04,786 --> 00:52:30,503
好的，选中文件后，这里会出现资产包条目。这就是Phaser编辑器识别我们刚添加文件的方式。由于我们导入的是精灵表，需要选择这个选项。若只需导入普通图片，可选择"作为图片导入"。选择导入精灵表时，系统不会询问要添加到哪个资产包，我们暂时选择assetpack.json文件。完成操作后，编辑器就会立即更新。

125
00:52:30,639 --> 00:52:47,619
因为我们已在资源包中存储了对此的引用。现在如果我们选择在资源包中打开汉堡精灵表，它就会打开我们的资源包JSON文件。在这个视图中，这是我们的资源包编辑器。通过这个编辑器，我们可以看到项目所知道的所有文件

126
00:52:47,789 --> 00:53:17,465
既然我们已经将引用存储在了资源包中。现在如果在资源包中选择打开汉堡精灵表，就会打开我们的资源包JSON文件。在这个视图中，可以看到我们的资源包编辑器。通过这个编辑器，我们可以查看项目所知道的所有文件

以及它实际尝试加载的内容。当前它正在加载我们的恐龙图片和刚添加的新汉堡精灵表。这个资源包JSON文件，实际上就是一个JSON配置文件，编辑器和Phaser将用它来了解如何加载我们的资源。举个例子，如果我右键点击资源包JSON文件，选择"用文本编辑器打开"，就能看到正在使用的原始JSON内容。可以看到这里有两个文件：一个是我们的汉堡精灵表，包含了一些解析信息（因为它是精灵表）；

127
00:53:17,755 --> 00:53:45,333
当前它正在加载我们的恐龙图像和刚添加的汉堡精灵表。我们的资源包JSON文件，本质上是一个JSON配置文件，编辑器和Phaser引擎将据此了解如何加载资源。例如，右键点击资源包JSON文件选择用文本编辑器打开，就能看到原始JSON配置内容。可以看到这里定义了两个文件：一个是汉堡精灵表及其解析参数（因为是精灵表类型），另一个是恐龙PNG图片（作为普通图像直接加载）。

现在你可以选中任意资源，在检查器中就能看到与之关联的元数据。这些数据包括：资源存储时使用的缓存键名、资源数据的获取URL地址，以及根据资源类型而定的附加信息。比如图像资源会显示宽高尺寸，精灵表资源则包含定义精灵实际大小的配置参数。

128
00:53:46,253 --> 00:54:14,188
现在我们可以保持配置不变。如果精灵尺寸不同，就需要在这里更新帧的高度设置。让我们关闭资产包的JSON文件，回到关卡场景。

现在我们已经将资产更新到资产包中，在方块选项中就能看到这个精灵表功能了。展开精灵表后，可以选择任意单帧画面添加到场景中。比如我可以选中这个帧，在场景中创建一个图像游戏对象。

129
00:54:14,905 --> 00:54:43,523
要在游戏中使用汉堡素材，我们新建一个预制体实例。在源文件夹上右键点击，选择新建，创建一个预制体文件。我们将它命名为"汉堡预制体"，点击创建。这会打开默认的预制体视图界面，帮我们建立预制体实例。

现在需要告知Phaser编辑器我们想使用什么作为基础。根据我们拖入预制体场景的区块类型，该区块将被用作根节点，我们的预制体将继承这个根节点。

130
00:54:43,847 --> 00:54:59,990
要在游戏中使用我们的汉堡，让我们创建一个新的预制体实例。从源文件夹右键点击，选择新建，为预制体创建新的预制体文件。我们将它命名为"burger prefab"，点击创建。这将打开默认的预制体视图，设置我们的预制体实例。现在需要告诉Phaser编辑器我们想使用什么作为基础。根据我们拖入预制体场景的方块，这将被用作根节点，也就是我们预制体要继承的对象。

如果我将汉堡拖入汉堡预制体场景，现在可以看到我们的变量是一个图像游戏对象。保存后，burger prefab.js文件就创建完成了，它将继承该游戏对象类型。我们的汉堡有点小，所以让我们把它放大三倍。

131
00:55:00,009 --> 00:55:29,360
X轴设为3，Y轴也设为3。然后把它移回原点(0,0)位置，这样它就会出现在左上角。好的，最后我们要给汉堡添加碰撞体。我们需要把这个图像类型改为物理图像 - 选择Arcade Image类型，点击替换。现在就有了物理实体。保存后回到关卡场景，拖入一个汉堡实例，替换掉这里的食物道具。删除原有食物，把我们的汉堡添加到这里。

132
00:55:30,161 --> 00:55:48,114
最后，我们只需将其添加到列表中。需要把这个加入食物物品清单，虽然这是个不同的预设体，但它仍是Phaser游戏对象，所以玩家应该能拾取它。现在切换到浏览器，刷新场景，就能看到新汉堡出现在场景中，带有物理实体，当玩家拾取时，分数会增加。

133
00:55:50,896 --> 00:56:17,108
除了汉堡素材外，我们还要为游戏添加另一个素材——障碍方块。这样玩家就有可以碰撞的障碍物了。不过在添加之前，我们先清理一下场景：删掉第二只恐龙，移除这里的第二个文本，然后保存。接着切换到level.js文件，由于我们删除了一些游戏对象，需要清理那些引用这些对象的手动代码，比如这个dino1就可以删掉了。

134
00:56:17,789 --> 00:56:47,295
既然我们不再需要更新这里的文本，这部分也可以删除了。最后，由于移除了文本，我们把恐龙的点击区域也去掉，这样它就不再可点击了。保存后，我们来添加另一个素材。进入素材文件夹，第二个素材我们将使用Kenny提供的像素平台积木块中的一个方块。我把这个方块拖到文件里来，这里我选用的是单个方块的图片。现在当检查器更新时，我们要将其作为单张图片导入。

135
00:56:47,568 --> 00:57:16,271
对于这个资源，我们还需要选择对应的资源包JSON文件。现在查看资源包JSON文件时，应该能在图片部分看到新加载的图像。让我们回到关卡场景，拖入一个瓦片实例。现在我们已经把方块放进了场景，它看起来很小，所以需要放大尺寸。我将把X和Y属性值都调整为5倍。实际上我们还需要为它添加物理实体，所以进入类型设置，选择图片类型，将其更改为街机图像模式，然后点击替换。

136
00:57:16,612 --> 00:57:41,697
我们需要创建多个方块实例，所以先制作一个预设体。右键点击选择"创建预设体"，在源文件夹下命名为"方块预设"。现在有了预设体，让我们创建几个实例。我只需在场景中拖拽一个，再复制粘贴出另一个。这里再放一个，把玩家角色移到这边。最后再添加一个方块。

137
00:57:42,260 --> 00:58:08,847
好的，现在我们已经有了四个方块
需要让它们能与玩家角色发生碰撞
就像之前处理食物那样
我们新建一个列表来存放这些物体

在"group block"下添加一个新列表
命名为"walls"
然后把每个方块都加入这个新列表

选中全部四个方块
将它们添加到墙壁列表中
现在需要添加碰撞组件

展开arcade菜单
找到collider选项

138
00:58:09,309 --> 00:58:36,715
好的，现在我们有四个方块了，需要让它们能与玩家发生碰撞。就像处理食物那样，我们先新建一个列表来存放这些对象。在"group block"下方添加一个新列表，命名为"walls"。然后把所有四个方块都添加进这个墙壁列表。

接下来设置碰撞检测。展开arcade菜单，找到collider选项。我们需要把"Dino 1"改成新建的墙壁列表对象。选择后保存，回到场景刷新浏览器。现在尝试移动会发现物体可以动了，这需要修正。

打开资源文件夹里的block prefab场景文件，选中游戏对象，找到body属性设为不可移动。同时关闭pushable选项，这样就能保持静态了。

139
00:58:37,090 --> 00:58:59,086
如果我们保存后返回场景，刷新页面，现在当恐龙试图穿过方块时，就无法实现了。我们的角色体型有点大，让我们把恐龙调小一些。点击恐龙对象，将缩放比例调整为0.75。好的，保存并刷新后，可以看到恐龙变小了些，但所有功能仍正常运行。

140
00:59:01,971 --> 00:59:30,845
目前我们的游戏看起来有点静态，虽然有可以移动的游戏对象，但缺少动画效果。之前导入的汉堡素材是一个精灵图集，包含多帧图像可以制作汉堡待机和移动的动画。为了让游戏更有趣，我们将添加汉堡动画。

在Phaser编辑器中添加动画需要先创建动画文件。这个动画文件能让我们使用动画编辑器为游戏添加动画效果。

141
00:59:31,067 --> 00:59:59,990
现在我们的游戏看起来有点静态，虽然有可以移动的游戏对象，但缺少动画效果。当我们导入汉堡素材时，由于它是精灵图集，我们有一系列帧可以用来创建汉堡待机和移动的动画。为了让游戏更生动，我们将添加汉堡动画。

要在Phaser编辑器中添加动画，我们需要创建一个动画文件。这个动画文件将允许我们使用动画编辑器为游戏添加动画效果。

要添加这个文件，我们进入资源文件夹，右键点击选择新建，然后创建动画文件。文件名我就保留默认的"animations"，点击创建。这会打开我们的动画JSON文件，也就是动画编辑器界面。我们可以通过顶部UI中的"添加动画"按钮确认这是动画编辑器。

要添加动画，我们需要选择要使用的帧。我们将选中汉堡的第6帧到第11帧。按住Shift键可以多选。现在在检查面板...

142
01:00:00,009 --> 01:00:22,688
我们当前处于创建动画的选项界面。点击"构建一个动画"按钮后，需要为这个新动画输入名称前缀。我们将它命名为"idle"。操作完成后，一个全新的动画就会被添加到左侧大纲中，这里会显示组成动画的帧序列，并且可以直接在编辑器中预览动画效果。

143
01:00:23,148 --> 01:00:52,039
我们正在这个选项中创建动画。点击"创建动画"后，需要为新动画输入名称前缀。这里我们命名为"idle"。完成后，系统会添加一个全新的动画，并显示在概要视图中。这里会展示构成动画的帧序列，我们可以在编辑器中预览动画效果。

创建好动画后，在检查器面板选中它，就能修改该动画的配置参数。比如可以调整帧率 - 如果动画太快可以调慢速度。还能设置更新或循环播放选项，这个参数控制动画播放次数。设为1时，代码调用播放指令后动画只播放一次。对于待机动画，我们需要设为-1，这样动画就会无限循环播放。

144
01:00:52,500 --> 01:01:15,538
在配置中你还可以进行其他操作。创建好动画JSON文件后，我们需要告知Phaser编辑器将其作为游戏资源加载。选中动画JSON文件后，选择"导入为动画"选项，并将其添加到资源包中。回到关卡场景界面，要播放动画还需将游戏对象类型更新为支持动画播放的类型。

145
01:01:16,048 --> 01:01:44,001
在Phaser中，默认情况下，图像游戏对象仅用于显示单张图片或纹理。而精灵游戏对象则用于播放动画效果。因此我们需要为汉堡对象更新游戏对象类型。进入汉堡预设文件后，在此处选择汉堡类型，将变量类型从"arcade image"更改为"arcade sprite"。完成修改后，现在需要告知Phaser我们希望为这个精灵播放动画。

146
01:01:44,326 --> 01:02:10,384
如果我们打开burgerprefab.js文件，在构造函数中就可以添加额外代码。这里我们需要告诉Phaser默认播放动画。为此，我们需要通过this来引用精灵游戏对象，然后调用play方法。接下来在play方法中，我们需要提供动画配置或想要播放的动画名称/键值。现在要设置动画键值，如果我们打开animations.json文件，

147
01:02:10,793 --> 01:02:40,657
这里显示的"idle burger"键名，就是我们需要的值。让我们复制这个闲置汉堡精灵表的键名。回到预制件文件中，在play方法里添加这个键名，然后保存。现在回到关卡场景，切换到浏览器窗口，保存并刷新后就能看到汉堡动画效果了，而且游戏对象仍然可以拾取。

既然我们在处理资源导入，有个需要反复选择的就是资源包的JSON文件。

148
01:02:41,101 --> 01:03:03,063
现在我们的模板中有两个资源包文件。一个是这个asset pack JSON，另一个是preload asset pack JSON。在Phaser编辑器和Phaser项目中，你可以根据需要创建任意多个资源包JSON文件。这让你能够将不同资源打包，然后通过这些配置来加载它们。在当前的项目设置中，我们的模板里有两个这样的文件。

149
01:03:03,268 --> 01:03:31,543
在我们测试游戏时，偶尔会看到这个预加载场景在加载资源时弹出。在Phaser中，你可以设置一个或多个场景，在Phaser编辑器中同样可以实现。我们项目的设置方式是：预加载场景相当于加载界面，这里会显示进度条来加载游戏所需的所有资源。当所有资源加载完成后，就会切换到关卡场景，这时玩家就可以开始游戏了。

150
01:03:31,817 --> 01:03:59,019
在预加载场景中，若需使用任何资源，请确保这些资源在预加载场景启动前已准备就绪。否则，当我们引用它们时会出现图像损坏的情况。这就是为什么我们在main.js文件中设置了两组资源包。目前我们有一个启动场景（boot scene），这是一个自定义的Phaser场景，它被添加在此处而非我们的常规场景列表中。该场景的唯一作用就是引用我们的预加载资源包。

151
01:03:59,445 --> 01:04:27,295
在我们的预加载资源包中，只有预加载器使用的那张图片。然后在预加载场景中，我们会加载另一个资源包的JSON文件，这里面包含了所有其他资源。关于Phaser需要注意的另一点是：一旦加载了资源，它们就会被存入缓存，可以在任何场景中使用。虽然我们在预加载场景中加载了食物素材，但在关卡场景中仍能使用它，因为Phaser会从缓存中调用，我们只需加载一次即可。

152
01:04:27,568 --> 01:04:57,244
因此，对于开发大型游戏来说，这是一个很好的模式。这样在加载所有内容时，你可以向玩家展示一个友好的界面，而不是一次性加载所有内容导致玩家只能看到黑屏（因为没有任何内容对玩家可见）。我们为《饥饿恐龙》游戏要做的最后一件事是添加一个非常简单的标题界面。在这个标题界面中，我们只需显示包含游戏名称的文本，当玩家点击后，就会跳转到我们的关卡场景。之前我们在这里创建了这个测试场景，

153
01:04:57,500 --> 01:04:59,990
让我们快速清理一下。我们右键点击它。

154
01:05:00,009 --> 01:05:27,722
删除这个。现在我们要创建一个全新的场景，选择场景菜单，新建一个场景文件，命名为"标题"。创建新场景后，只需添加一个简单的文本游戏对象。在字符串选项下，添加文本内容"饥饿的恐龙"。将字体大小调整为64像素，并在屏幕中央对齐显示。

155
01:05:28,097 --> 01:05:51,800
当前游戏对象的原点位于左上角。我们需要将其调整到中心位置。让我们更新原点坐标，设置为0.5和0.5。现在要让标题文本在屏幕居中显示，我们可以使用布局工具来实现。右键点击游戏对象选择布局选项，会出现多种布局方式可供选择。其中有个很实用的功能是"对齐到边界"选项。

156
01:05:52,072 --> 01:06:19,889
这样我们就能让游戏物体与游戏边界完美对齐。比如选择左右边界时，可以看到游戏物体的边缘会与边界对齐。如果选择"布局-居中"，游戏物体就会在游戏两侧边缘之间居中显示。同理，选择"布局-对齐边界"后，无论是顶部边界还是底部边界，游戏物体的上下边缘都会与边界对齐。而如果选择"边界中间"，

157
01:06:20,333 --> 01:06:48,507
这样我们的游戏对象就能完美居中于画布元素中了。通过布局工具，您还能实现更多功能。比如您可以选择创建网格布局 - 当您需要将多个游戏对象按网格排列时，系统已内置了相应工具来自动完成。好，如果我们保存后返回浏览器，刷新页面，依然会看到关卡场景。请注意：每次向游戏添加全新场景时，都需要更新Phaser配置，以便Phaser能识别该场景。

（注：根据字幕翻译特点，我对原文进行了以下处理：
1. 将长句拆分为符合中文表达习惯的短句
2. "canvas element"译为专业术语"画布元素"
3. "grid layout"保留专业表述"网格布局"
4. 将"it has built-in tools to do that for you"意译为"系统已内置了相应工具来自动完成"，更符合中文技术文档表述
5. 最后一句添加"请注意"作为提示语，使语气更自然）

158
01:06:48,898 --> 01:07:11,697
现在我们需要打开main.js文件。无论你在哪里定义Phaser游戏配置（这里就是main.js文件），我们都要自动添加游戏中使用的场景。现在我们要为标题场景新增一个配置。让我们复制这里的逻辑，粘贴到这里。将键名更新为title，类名也更新为title。

159
01:07:12,159 --> 01:07:40,588
要使用标题场景类，我们需要将其导入代码中。让我们输入：import title from scenes/title.js。这样做是告诉Phaser游戏配置我们新增了一个场景，它会创建该场景的实例。但如果要真正启动这个场景，我们需要在代码中进行引用。例如，在启动场景准备就绪后，我们调用scene.start来加载预载场景。

160
01:07:41,117 --> 01:08:10,128
因此，要使用我们的标题场景类，需要将其导入代码中。让我们输入：从场景文件夹的title.js导入Title类。这样做之后，就是在告诉Phaser游戏配置我们新增了一个场景，系统会创建该场景的实例。但如果我们想真正启动这个场景，还需要在代码中进行引用。以启动场景为例，当它加载完成并准备就绪后，我们调用scene.start方法来启动预加载场景。

现在进入预加载场景，打开preload.js文件。可以看到在底部完成所有操作后，会进入create方法并启动关卡场景。目前我们需要改为启动标题场景。这样当预加载场景完成后，就会跳转到标题界面。如果在浏览器中刷新游戏，现在就能看到新添加的"饥饿恐龙"文字。接下来只需要添加启动游戏场景的交互机制，为此我们需要监听场景上的点击事件。

161
01:08:11,067 --> 01:08:37,551
让我们打开title.js文件。进入create方法后，在编辑器创建代码下方，添加事件监听器的逻辑代码。我们将通过this来引用Phaser场景，用input来引用输入插件，然后调用once方法。这个方法的作用是让我们能够为指定事件添加一次性监听器，这样就能在场景中监听一次点击事件并执行相应操作。

162
01:08:38,131 --> 01:09:04,752
而如果我们使用on方法，每次触发该事件时都会调用回调函数。由于我们只需要点击一次就跳转到关卡场景，这里我们只需使用once方法即可。现在我们需要指定事件类型。输入phaser.input.events，然后选择POINTER_DOWN事件。当这个事件被触发时，我们提供的回调函数就会被执行。在回调函数中，我们只需启动我们的关卡场景。

163
01:09:05,145 --> 01:09:32,210
之前我们在预加载场景中所做的scene.start操作，现在要在标题场景中重复同样的步骤，只不过这次需要将其改为level场景。完成修改后，回到浏览器刷新场景，点击画面时就能启动关卡场景，玩家依然可以收集物品。最后我们要对游戏做的一个调整是更新浏览器中显示的游戏标题。目前显示的是"我的游戏"。

164
01:09:32,551 --> 01:09:59,990
在我们之前的预加载场景中，通过scene.start实现的功能，现在我们要在标题场景做同样操作，但这次要切换至关卡场景。完成修改后，回到浏览器刷新场景。点击场景后，关卡现在能正常启动，玩家仍可收集物品。

最后我们要修改游戏在浏览器中显示的标题。当前显示的是"my game"。进入项目模板的index.html文件，在title元素处进行修改。我们将"my game"改为"Hungry Dinosaur"，保存后返回浏览器，现在游戏就显示新标题了。

好了，这就是本次Phaser Editor V4速成课程的全部内容。今天我们涵盖了从创建首个项目到添加物理系统、动画效果...

165
01:10:00,009 --> 01:10:28,234
以及对象交互功能。通过Phaser Editor，您无需深入编码即可直观地快速创建和测试游戏，当然您随时可以选择手动编码。跟随本教程后，您现在已掌握使用Phaser Editor v4构建Phaser 3游戏的基础知识。请记住，游戏开发重在实践与学习，大胆尝试新功能并拓展今天所学内容吧。一如既往，期待听到您的想法并欣赏您的作品，欢迎在评论区留言提问。

166
01:10:28,506 --> 01:10:42,381
如果你觉得这个教程有帮助，别忘了点赞视频并订阅以获取更多游戏开发内容。同时，查看描述中的链接，可以找到Phaser Editor的文档和其他有用资源。感谢观看，祝游戏开发愉快。下个教程见。

