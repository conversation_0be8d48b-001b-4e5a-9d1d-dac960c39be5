#!/usr/bin/env python3
"""
修复最新的视频翻译任务
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "python-ai-service"))

async def fix_latest_task():
    """修复最新的视频翻译任务"""
    try:
        print("🔧 开始修复最新的视频翻译任务...")
        
        from app.services.tts_service import TTSService
        from app.services.video_service import VideoService
        
        # 最新任务目录
        task_dir = Path("python-ai-service/storage/outputs/20250719_1601_8da79b5e-208d-483c-81d6-cb3ce4bd0bd9_test_30s")
        
        print(f"📁 任务目录: {task_dir.absolute()}")
        
        if not task_dir.exists():
            print(f"❌ 任务目录不存在: {task_dir}")
            return False
        
        # 读取翻译文本
        translation_file = task_dir / "translation/translated.txt"
        if not translation_file.exists():
            print(f"❌ 翻译文件不存在: {translation_file}")
            return False
            
        with open(translation_file, 'r', encoding='utf-8') as f:
            translated_text = f.read().strip()
        
        print(f"📝 翻译文本: {translated_text[:100]}...")
        
        # Step 1: 生成TTS
        print("\n🎵 Step 1: 生成TTS...")
        tts_service = TTSService()
        tts_output = task_dir / "tts/chinese.wav"
        
        try:
            result = await tts_service.synthesize_speech_to_path(
                text=translated_text,
                voice="zh-CN-XiaoxiaoNeural",
                output_path=str(tts_output),
                speed=1.0,
                pitch=0,
                service="edge-tts"
            )
            
            if result and not result.get("fallback"):
                print(f"✅ TTS生成成功: {tts_output}")
                tts_success = True
            else:
                print(f"❌ TTS失败: {result}")
                tts_success = False
                
        except Exception as e:
            print(f"❌ TTS异常: {e}")
            tts_success = False
        
        if not tts_success:
            print("⚠️ TTS失败，将使用原视频音频")
            tts_output = translation_file  # 使用文本文件作为fallback
        
        # Step 2: 重新合成视频（带字幕烧录）
        print("\n🎬 Step 2: 重新合成视频...")
        
        video_service = VideoService()
        
        source_video = task_dir / "source/original.mp4"
        subtitle_file = task_dir / "subtitles/translated.srt"
        output_video = task_dir / "output/final_video_fixed.mp4"
        
        print(f"   源视频: {source_video}")
        print(f"   TTS音频: {tts_output}")
        print(f"   字幕文件: {subtitle_file}")
        print(f"   输出视频: {output_video}")
        
        # 检查文件是否存在
        if not source_video.exists():
            print(f"❌ 源视频不存在: {source_video}")
            return False
        
        if not subtitle_file.exists():
            print(f"❌ 字幕文件不存在: {subtitle_file}")
            return False
        
        # 字幕样式
        subtitle_style = {
            'font_name': 'Arial',
            'font_size': 28,
            'primary_color': '&Hffffff',  # 白色文字
            'outline_color': '&H000000',  # 黑色描边
            'back_color': '&H80000000',   # 半透明背景
            'outline': 2,                 # 描边宽度
            'shadow': 1,                  # 阴影
            'alignment': 2,               # 底部居中
            'margin_v': 40                # 底部边距
        }
        
        try:
            # 使用带字幕烧录的视频合成
            video_result = await video_service.compose_video_to_path(
                video_path=str(source_video),
                audio_path=str(tts_output),
                output_path=str(output_video),
                subtitle_path=str(subtitle_file),
                output_format="mp4",
                burn_subtitles=True,  # 启用字幕烧录
                subtitle_style=subtitle_style
            )
            
            print(f"✅ 视频合成成功: {output_video}")
            print(f"   文件大小: {video_result.get('file_size', 0) / (1024*1024):.1f} MB")
            print(f"   视频时长: {video_result.get('duration', 0):.1f} 秒")
            
            if video_result.get('subtitles_burned'):
                print(f"   ✅ 字幕已烧录到视频中")
            
        except Exception as e:
            print(f"❌ 视频合成失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print(f"\n🎉 修复完成!")
        print(f"📍 修复后的视频位置: {output_video}")
        print(f"🎬 现在视频应该包含:")
        if tts_success:
            print(f"   ✅ 中文TTS语音")
        else:
            print(f"   ⚠️ 原始音频（TTS失败）")
        print(f"   ✅ 烧录的中文字幕")
        
        print(f"\n🚀 要播放修复后的视频，请运行:")
        print(f"   start \"{output_video.absolute()}\"")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(fix_latest_task())
