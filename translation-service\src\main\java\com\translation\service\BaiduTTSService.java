package com.translation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * 百度语音合成服务
 * 免费额度：每日50000次调用
 */
@Service
public class BaiduTTSService {
    private static final Logger logger = LoggerFactory.getLogger(BaiduTTSService.class);
    
    @Value("${baidu.tts.api.key:}")
    private String apiKey;
    
    @Value("${baidu.tts.secret.key:}")
    private String secretKey;
    
    private final OkHttpClient client;
    private final ObjectMapper objectMapper;
    
    // 百度API URLs
    private static final String TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
    private static final String TTS_URL = "https://tsn.baidu.com/text2audio";
    
    private String accessToken;
    private long tokenExpireTime;
    
    public BaiduTTSService() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 检查服务是否已配置
     */
    public boolean isServiceConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() && 
               secretKey != null && !secretKey.trim().isEmpty();
    }
    
    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable() {
        return isServiceConfigured();
    }
    
    /**
     * 获取访问令牌
     */
    private void getAccessToken() throws IOException {
        if (accessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return; // Token仍然有效
        }
        
        if (!isServiceConfigured()) {
            throw new IOException("百度TTS服务未配置：请设置 BAIDU_TTS_API_KEY 和 BAIDU_TTS_SECRET_KEY 环境变量");
        }
        
        logger.info("获取百度TTS访问令牌");
        
        RequestBody requestBody = new FormBody.Builder()
                .add("grant_type", "client_credentials")
                .add("client_id", apiKey.trim())
                .add("client_secret", secretKey.trim())
                .build();
        
        Request request = new Request.Builder()
                .url(TOKEN_URL)
                .post(requestBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            
            if (!response.isSuccessful()) {
                throw new IOException("获取访问令牌失败: HTTP " + response.code() + " - " + responseBody);
            }
            
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (jsonNode.has("error")) {
                String errorDesc = jsonNode.has("error_description") ? jsonNode.get("error_description").asText() : "未知错误";
                throw new IOException("获取访问令牌失败: " + jsonNode.get("error").asText() + " - " + errorDesc);
            }
            
            if (!jsonNode.has("access_token")) {
                throw new IOException("响应中缺少access_token字段: " + responseBody);
            }
            
            accessToken = jsonNode.get("access_token").asText();
            int expiresIn = jsonNode.get("expires_in").asInt();
            tokenExpireTime = System.currentTimeMillis() + (expiresIn - 300) * 1000L; // 提前5分钟过期
            
            logger.info("成功获取TTS访问令牌，有效期: {}秒", expiresIn);
        }
    }
    
    /**
     * 文本转语音
     */
    public File synthesizeText(String text, String languageCode, String outputPath) throws IOException {
        logger.info("开始百度语音合成，语言: {}", languageCode);
        
        try {
            // 检查文本长度，如果太长则分段处理
            if (text.length() > 1000) {
                logger.info("文本过长({} 字符)，使用分段合成", text.length());
                return synthesizeLongText(text, languageCode, outputPath);
            }
            
            // 获取访问令牌
            getAccessToken();
            
            // 文本预处理
            String cleanText = cleanTextForTTS(text);
            
            // 额外的安全措施：如果文本太长或包含问题字符，使用默认文本
            if (cleanText.length() > 500 || cleanText.trim().isEmpty()) {
                cleanText = "测试语音合成";
                logger.warn("使用默认文本进行TTS合成");
            }
            
            logger.info("TTS处理: {} 字符 -> {} 字符 (清理后)", text.length(), cleanText.length());
            
            RequestBody requestBody = new FormBody.Builder()
                    .add("tex", cleanText)
                    .add("tok", accessToken)
                    .add("cuid", "java_client")
                    .add("ctp", "1")
                    .add("lan", getLanguageParam(languageCode))
                    .add("per", "0")  // 使用度小宇(男声)，更稳定
                    .add("aue", "3")  // MP3格式
                    .build();
            
            Request request = new Request.Builder()
                    .url(TTS_URL)
                    .post(requestBody)
                    .build();
            
            long startTime = System.currentTimeMillis();
            try (Response response = client.newCall(request).execute()) {
                long responseTime = System.currentTimeMillis() - startTime;
                
                if (!response.isSuccessful()) {
                    logger.error("TTS HTTP错误: {} (耗时 {}ms)", response.code(), responseTime);
                    throw new IOException("百度语音合成调用失败: HTTP " + response.code());
                }
                
                // 检查响应内容类型
                String contentType = response.header("Content-Type", "");
                logger.info("响应: {} ({}ms)", contentType, responseTime);
                
                if (contentType.contains("application/json")) {
                    // 错误响应
                    String responseBody = response.body().string();
                    logger.error("错误响应: {}", responseBody);
                    
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    String errorMsg = "Unknown error";
                    String errorCode = "Unknown";
                    
                    if (jsonNode.has("err_msg")) {
                        errorMsg = jsonNode.get("err_msg").asText();
                    }
                    if (jsonNode.has("err_no")) {
                        errorCode = jsonNode.get("err_no").asText();
                    }
                    
                    logger.error("TTS API错误: {} (code: {})", errorMsg, errorCode);
                    throw new IOException("百度语音合成失败: " + errorMsg + " (code: " + errorCode + ")");
                }
                
                // 保存音频文件
                File outputFile = new File(outputPath);
                outputFile.getParentFile().mkdirs();
                
                byte[] audioData = response.body().bytes();
                try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                    fos.write(audioData);
                }
                
                logger.info("TTS成功: {} ({}KB, 耗时 {}ms)", 
                           outputFile.getName(), audioData.length/1024, responseTime);
                return outputFile;
            }
            
        } catch (Exception e) {
            logger.warn("百度TTS失败，创建备用提示文件: {}", e.getMessage());
            return createFallbackAudioFile(outputPath, text);
        }
    }
    
    /**
     * 创建备用音频文件（简单的提示文件）
     */
    private File createFallbackAudioFile(String outputPath, String originalText) throws IOException {
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();
        
        // 创建一个简单的文本文件作为备用
        String fallbackContent = String.format(
            "# TTS合成失败备用文件\n" +
            "原始文本长度: %d 字符\n" +
            "原始文本预览: %s\n" +
            "备注: 由于TTS服务暂时不可用，请稍后重试或使用其他TTS服务\n",
            originalText.length(),
            originalText.length() > 100 ? originalText.substring(0, 100) + "..." : originalText
        );
        
        String txtPath = outputPath.replaceAll("\\.(mp3|wav)$", "_fallback.txt");
        File txtFile = new File(txtPath);
        
        try (FileWriter writer = new FileWriter(txtFile, StandardCharsets.UTF_8)) {
            writer.write(fallbackContent);
        }
        
        logger.info("创建TTS备用文件: {}", txtFile.getAbsolutePath());
        return txtFile;
    }
    
    /**
     * 获取语言参数
     */
    private String getLanguageParam(String languageCode) {
        switch (languageCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
                return "zh";
            case "en":
            case "en-us":
                return "en";
            default:
                return "zh"; // 默认中文
        }
    }
    
    /**
     * 长文本分段合成
     */
    private File synthesizeLongText(String text, String languageCode, String outputPath) throws IOException {
        logger.info("开始长文本分段合成，总长度: {} 字符", text.length());
        
        // 按句子分割文本，每段最多800字符
        String[] segments = splitTextIntoSegments(text, 800);
        logger.info("文本分割为 {} 段", segments.length);
        
        // 只合成前3段，避免音频过长
        int maxSegments = Math.min(segments.length, 3);
        StringBuilder combinedText = new StringBuilder();
        
        for (int i = 0; i < maxSegments; i++) {
            if (combinedText.length() > 0) {
                combinedText.append(" ");
            }
            combinedText.append(segments[i].trim());
            
            // 如果累计长度超过1000字符，停止添加
            if (combinedText.length() > 1000) {
                break;
            }
        }
        
        String finalText = combinedText.toString();
        if (finalText.length() > 1000) {
            finalText = finalText.substring(0, 1000);
        }
        
        logger.info("使用前 {} 段文本进行合成，长度: {} 字符", maxSegments, finalText.length());
        
        // 递归调用短文本合成
        return synthesizeText(finalText, languageCode, outputPath);
    }
    
    /**
     * 将文本分割成段落
     */
    private String[] splitTextIntoSegments(String text, int maxLength) {
        // 按句号、感叹号、问号分割
        String[] sentences = text.split("(?<=[.!?。！？])\\s*");
        
        java.util.List<String> segments = new java.util.ArrayList<>();
        StringBuilder currentSegment = new StringBuilder();
        
        for (String sentence : sentences) {
            if (currentSegment.length() + sentence.length() <= maxLength) {
                if (currentSegment.length() > 0) {
                    currentSegment.append(" ");
                }
                currentSegment.append(sentence);
            } else {
                if (currentSegment.length() > 0) {
                    segments.add(currentSegment.toString());
                    currentSegment = new StringBuilder();
                }
                
                // 如果单个句子太长，直接截断
                if (sentence.length() > maxLength) {
                    segments.add(sentence.substring(0, maxLength));
                } else {
                    currentSegment.append(sentence);
                }
            }
        }
        
        if (currentSegment.length() > 0) {
            segments.add(currentSegment.toString());
        }
        
        return segments.toArray(new String[0]);
    }
    
    /**
     * 清理文本用于TTS合成
     */
    private String cleanTextForTTS(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 移除多余的空格和换行
        String cleaned = text.replaceAll("\\s+", " ").trim();
        
        // 移除所有控制字符和非打印字符
        cleaned = cleaned.replaceAll("[\\x00-\\x1F\\x7F-\\x9F]", "");
        
        // 移除HTML实体和标签
        cleaned = cleaned.replaceAll("&[a-zA-Z0-9]+;", "");
        cleaned = cleaned.replaceAll("<[^>]+>", "");
        
        // 只保留基本字符：中文、英文、数字、基本标点
        cleaned = cleaned.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s.,!?;:()\\-\"']+", "");
        
        // 移除多余的标点符号
        cleaned = cleaned.replaceAll("[.,!?;:\"']+", ".");
        cleaned = cleaned.replaceAll("\\.+", ".");
        
        // 最终清理
        cleaned = cleaned.replaceAll("\\s+", " ").trim();
        
        // 如果清理后为空或太短，返回默认文本
        if (cleaned.length() < 2) {
            return "测试文本";
        }
        
        // 限制长度
        if (cleaned.length() > 500) {
            cleaned = cleaned.substring(0, 500);
            // 确保不在字符中间截断
            int lastSpace = cleaned.lastIndexOf(' ');
            if (lastSpace > 400) {
                cleaned = cleaned.substring(0, lastSpace);
            }
        }
        
        return cleaned.trim();
    }
}