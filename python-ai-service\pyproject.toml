[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "video-translation-ai-service"
version = "1.0.0"
description = "AI-powered video translation service with speech recognition, translation, and TTS"
authors = [
    {name = "AI Video Translation Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "moviepy>=1.0.3",
    "pydub>=0.25.1",
    "librosa>=0.10.1",
    "soundfile>=0.12.1",
    "openai-whisper>=20231117",
    "torch>=2.1.0",
    "torchaudio>=2.1.0",
    "openai>=1.3.7",
    "google-cloud-translate>=3.12.1",
    "edge-tts>=6.1.9",
    "gTTS>=2.4.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.0",
    "redis>=5.0.1",
    "celery>=5.3.4",
    "kombu>=5.3.4",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.2",
    "httpx>=0.25.2",
    "aiofiles>=23.2.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]

[project.urls]
Homepage = "https://github.com/example/video-translation-ai-service"
Repository = "https://github.com/example/video-translation-ai-service.git"
Issues = "https://github.com/example/video-translation-ai-service/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=85",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
] 