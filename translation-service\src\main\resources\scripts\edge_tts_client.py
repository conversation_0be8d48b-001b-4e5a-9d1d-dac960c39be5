#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Edge TTS客户端脚本
用于调用Microsoft Edge的文本转语音服务
"""

import argparse
import asyncio
import json
import sys
import os
from typing import List, Dict

try:
    import edge_tts
except ImportError:
    print("错误: 请安装edge-tts库")
    print("运行: pip install edge-tts")
    sys.exit(1)


async def synthesize_text(text: str, voice: str, rate: str, output_file: str) -> bool:
    """
    合成文本为语音
    
    Args:
        text: 要合成的文本
        voice: 语音名称
        rate: 语速
        output_file: 输出文件路径
    
    Returns:
        bool: 是否成功
    """
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 创建TTS通信对象
        communicate = edge_tts.Communicate(text, voice, rate=rate)
        
        # 保存音频文件
        await communicate.save(output_file)
        
        # 检查文件是否成功创建
        if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
            print(f"语音合成成功: {output_file}")
            return True
        else:
            print(f"语音合成失败: 输出文件为空或不存在")
            return False
            
    except Exception as e:
        print(f"语音合成失败: {str(e)}")
        return False


async def get_available_voices() -> List[Dict[str, str]]:
    """
    获取可用的语音列表
    
    Returns:
        List[Dict]: 语音列表
    """
    try:
        voices = await edge_tts.list_voices()
        
        # 过滤并格式化语音信息
        formatted_voices = []
        for voice in voices:
            # 只包含中文、英文和日文语音
            if any(lang in voice['Locale'] for lang in ['zh-', 'en-', 'ja-']):
                formatted_voice = {
                    'name': voice['FriendlyName'],
                    'voice': voice['ShortName'],
                    'language': voice['Locale'],
                    'gender': voice['Gender'],
                    'locale': voice['Locale']
                }
                formatted_voices.append(formatted_voice)
        
        return formatted_voices
        
    except Exception as e:
        print(f"获取语音列表失败: {str(e)}")
        return []


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='Edge TTS客户端')
    parser.add_argument('--text', type=str, help='要合成的文本')
    parser.add_argument('--voice', type=str, default='zh-CN-XiaoxiaoNeural', help='语音名称')
    parser.add_argument('--rate', type=str, default='+0%', help='语速')
    parser.add_argument('--output', type=str, help='输出文件路径')
    parser.add_argument('--list-voices', action='store_true', help='列出可用的语音')
    
    args = parser.parse_args()
    
    if args.list_voices:
        # 列出可用语音
        try:
            voices = asyncio.run(get_available_voices())
            print(json.dumps(voices, ensure_ascii=False, indent=2))
        except Exception as e:
            print(f"获取语音列表失败: {str(e)}")
            sys.exit(1)
    
    elif args.text and args.output:
        # 合成语音
        try:
            success = asyncio.run(synthesize_text(args.text, args.voice, args.rate, args.output))
            if not success:
                sys.exit(1)
        except Exception as e:
            print(f"语音合成失败: {str(e)}")
            sys.exit(1)
    
    else:
        parser.print_help()
        sys.exit(1)


if __name__ == '__main__':
    main()