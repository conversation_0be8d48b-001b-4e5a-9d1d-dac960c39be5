#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import torch
import whisperx

def test_whisperx():
    print("🧪 WhisperX 测试开始...")
    
    # 检查CUDA可用性
    print(f"🔍 CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"🎮 GPU设备: {torch.cuda.get_device_name()}")
        print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🚀 使用设备: {device}")
    
    # 检查音频文件
    audio_path = "out/temp_audio.wav"
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return False
        
    file_size = os.path.getsize(audio_path) / (1024*1024)  # MB
    print(f"📁 音频文件: {audio_path} ({file_size:.1f}MB)")
    
    try:
        print("📥 正在加载WhisperX模型...")
        model = whisperx.load_model("large-v3", device, compute_type="float16" if device == "cuda" else "int8")
        print("✅ 模型加载成功")
        
        print("📝 开始转录测试（仅前30秒）...")
        # 只转录前30秒进行测试
        import librosa
        audio, sr = librosa.load(audio_path, sr=16000, duration=30)
        result = model.transcribe(audio)
        
        print(f"✅ 转录测试完成，检测到语言: {result.get('language', 'unknown')}")
        print(f"📊 转录片段数: {len(result.get('segments', []))}")
        
        if result.get('segments'):
            first_segment = result['segments'][0]
            print(f"📝 第一句内容: {first_segment.get('text', '')[:100]}...")
        
        # 清理资源
        del model
        torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ WhisperX测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_whisperx()
    if success:
        print("🎉 WhisperX测试成功！")
    else:
        print("💥 WhisperX测试失败！") 