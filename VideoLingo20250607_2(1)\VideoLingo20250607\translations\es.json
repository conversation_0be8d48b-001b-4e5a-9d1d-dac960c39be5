{"a. Download or Upload Video": "<PERSON><PERSON> o subir video", "Delete and Reselect": "Eliminar y volver a seleccionar", "Enter YouTube link:": "Ingrese el enlace de YouTube:", "Resolution": "Resolución", "Download Video": "Des<PERSON><PERSON> video", "Or upload video": "O subir video", "Youtube Settings": "Configuración de Youtube", "Cookies Path": "Ruta del archivo de Cookies", "LLM Configuration": "Configuración de LLM", "API_KEY": "Clave API", "BASE_URL": "URL base", "MODEL": "<PERSON><PERSON>", "Openai format, will add /v1/chat/completions automatically": "Formato OpenAI, se agregará /v1/chat/completions automáticamente", "click to check API validity": "haga clic para verificar la validez de la API", "API Key is valid": "La clave API es válida", "API Key is invalid": "La clave API no es válida", "Recog Lang": "Idioma de reconocimiento", "Subtitles Settings": "Configuración de subtítulos", "Target Lang": "Idioma objetivo", "Input any language in natural language, as long as llm can understand": "Ingrese cualquier idioma en lenguaje natural, siempre que LLM pueda entenderlo", "Vocal separation enhance": "Mejora de separación vocal", "Burn-in Subtitles": "Incrustar subtítulos", "Whether to burn subtitles into the video, will increase processing time": "Si se deben incrustar los subtítulos en el video, aumentará el tiempo de procesamiento", "Video Resolution": "Resolución de video", "Recommended for videos with loud background noise, but will increase processing time": "Recomendado para videos con ruido de fondo fuerte, pero aumentará el tiempo de procesamiento", "Dubbing Settings": "Configuración de doblaje", "TTS Method": "Método TTS", "SiliconFlow API Key": "Clave API de SiliconFlow", "Mode Selection": "Selección de modo", "Preset": "Preestablecido", "Refer_stable": "Referencia estable", "Refer_dynamic": "Referencia dinámica", "OpenAI Voice": "Voz de OpenAI", "Fish TTS Character": "Personaje Fish TTS", "Azure Voice": "Voz de Azure", "Please refer to Github homepage for GPT_SoVITS configuration": "Consulte la página principal de Github para la configuración de GPT_SoVITS", "SoVITS Character": "Personaje SoVITS", "Refer Mode": "<PERSON>do de referencia", "Mode 1: Use provided reference audio only": "Modo 1: Usar solo el audio de referencia proporcionado", "Mode 2: Use first audio from video as reference": "Modo 2: Usar el primer audio del video como referencia", "Mode 3: Use each audio from video as reference": "Modo 3: Usar cada audio del video como referencia", "Configure reference audio mode for GPT-SoVITS": "Configurar modo de audio de referencia para GPT-SoVITS", "Edge TTS Voice": "Voz Edge TTS", "=====NOTE=====": "Lo siguiente es el contenido de st.py", "b. Translate and Generate Subtitles": "b. <PERSON><PERSON><PERSON> y generar subtítulos", "This stage includes the following steps:": "Esta etapa incluye los siguientes pasos:", "WhisperX word-level transcription": "Transcripción a nivel de palabra WhisperX", "Sentence segmentation using NLP and LLM": "Segmentación de oraciones usando NLP y LLM", "Summarization and multi-step translation": "Resumen y traducción en múltiples pasos", "Cutting and aligning long subtitles": "Cortar y alinear subtítulos largos", "Generating timeline and subtitles": "Generar línea de tiempo y subtítulos", "Merging subtitles into the video": "Fusionar subtítulos en el video", "Start Processing Subtitles": "Comenzar procesamiento de subtítulos", "Download All Srt Files": "Descargar todos los archivos Srt", "Archive to 'history'": "Archivar en 'history'", "Using Whisper for transcription...": "Usando Whisper para transcripción...", "Splitting long sentences...": "Dividiendo oraciones largas...", "Summarizing and translating...": "Resumiendo y traduciendo...", "Processing and aligning subtitles...": "Procesando y alineando subtítulos...", "Merging subtitles to video...": "Fusionando subtítulos al video...", "⚠️ PAUSE_BEFORE_TRANSLATE. Go to `output/log/terminology.json` to edit terminology. Then press ENTER to continue...": "⚠️ PAUSA_ANTES_DE_TRADUCIR. Vaya a `output/log/terminology.json` para editar la terminología. Luego presione ENTER para continuar...", "Subtitle processing complete! 🎉": "¡Procesamiento de subtítulos completado! 🎉", "c. Dubbing": "<PERSON><PERSON>", "Generate audio tasks and chunks": "Generar tareas y fragmentos de audio", "Extract reference audio": "Extraer audio de referencia", "Generate and merge audio files": "Generar y fusionar archivos de audio", "Merge final audio into video": "Fusionar audio final en el video", "Start Audio Processing": "Comenzar procesamiento de audio", "Audio processing is complete! You can check the audio files in the `output` folder.": "¡El procesamiento de audio está completo! Puede verificar los archivos de audio en la carpeta `output`.", "Delete dubbing files": "Eliminar archivos de doblaje", "Generate audio tasks": "Generar tareas de audio", "Extract refer audio": "Extraer audio de referencia", "Generate all audio": "Generar todo el audio", "Merge full audio": "Fusionar audio completo", "Merge dubbing to the video": "Fusionar doblaje al video", "Audio processing complete! 🎇": "¡Procesamiento de audio completado! 🎇", "Hello, welcome to VideoLingo. If you encounter any issues, feel free to get instant answers with our Free QA Agent <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">here</a>! You can also try out our SaaS website at <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a> for free!": "<PERSON><PERSON>, bienvenido a VideoLingo. Si encuentra algún problema, no dude en obtener respuestas instantáneas con nuestro Agente de preguntas y respuestas gratuito <a href=\"https://share.fastgpt.in/chat/share?shareId=066w11n3r9aq6879r4z0v9rh\" target=\"_blank\">aquí</a>. ¡También puede probar gratis nuestro sitio web SaaS en <a href=\"https://videolingo.io\" target=\"_blank\">videolingo.io</a>!", "WhisperX Runtime": "Entorno de WhisperX", "Local runtime requires >8GB GPU, cloud runtime requires 302ai API key, elevenlabs runtime requires ElevenLabs API key": "El entorno local requiere GPU >8GB, el entorno en la nube requiere clave API 302ai, el entorno elevenlabs requiere clave API ElevenLabs", "WhisperX 302ai API": "API 302ai de WhisperX", "=====NOTE2=====": "A continuación está en install.py", "🚀 Starting Installation": "🚀 Iniciando instalación", "Do you need to auto-configure PyPI mirrors? (Recommended if you have difficulty accessing pypi.org)": "¿Necesita configurar automáticamente los espejos PyPI? (Recomendado si tiene dificultades para acceder a pypi.org)", "🎮 NVIDIA GPU detected, installing CUDA version of PyTorch...": "🎮 GPU NVIDIA detectada, instalando versión CUDA de PyTorch...", "🍎 MacOS detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "🍎 MacOS detectado, instalando versión CPU de PyTorch... Nota: puede ser lento durante la transcripción de whisperX.", "💻 No NVIDIA GPU detected, installing CPU version of PyTorch... Note: it might be slow during whisperX transcription.": "💻 No se detectó GPU NVIDIA, instalando versión CPU de PyTorch... Nota: puede ser lento durante la transcripción de whisperX.", "❌ Failed to install requirements:": "❌ Error al instalar los requisitos:", "✅ FFmpeg is already installed": "✅ FFmpeg ya está instalado", "❌ FFmpeg not found\n\n": "❌ FFmpeg no encontrado\n\n", "🛠️ Install using:": "🛠️ Instalar usando:", "💡 Note:": "💡 Nota:", "🔄 After installing FFmpeg, please run this installer again:": "🔄 Después de instalar FFmpeg, ejecute este instalador nuevamente:", "Install Chocolatey first (https://chocolatey.org/)": "Instale Chocolatey primero (https://chocolatey.org/)", "Install Homebrew first (https://brew.sh/)": "Instale Homebrew primero (https://brew.sh/)", "Use your distribution's package manager": "Use el gestor de paquetes de su distribución", "FFmpeg is required. Please install it and run the installer again.": "Se requiere FFmpeg. Por favor instálelo y ejecute el instalador nuevamente.", "Installation completed": "Instalación completada", "Now I will run this command to start the application:": "Ahora ejecutaré este comando para iniciar la aplicación:", "Note: First startup may take up to 1 minute": "Nota: El primer inicio puede tardar hasta 1 minuto", "If the application fails to start:": "Si la aplicación no se inicia:", "Check your network connection": "Compruebe su conexión de red", "Re-run the installer: [bold]python install.py[/bold]": "Vuelva a ejecutar el instalador: [bold]python install.py[/bold]", "Installing requirements using `pip install -r requirements.txt`": "Instalando dependencias usando `pip install -r requirements.txt`", "Detected NVIDIA GPU(s)": "GPU(s) NVIDIA detectada(s)", "No NVIDIA GPU detected": "No se detectó GPU NVIDIA", "No NVIDIA GPU detected or NVIDIA drivers not properly installed": "No se detectó GPU NVIDIA o los controladores NVIDIA no están instalados correctamente", "LLM JSON Format Support": "Soporte de formato JSON para LLM", "Enable if your LLM supports JSON mode output": "Activar si su LLM admite salida en modo JSON"}